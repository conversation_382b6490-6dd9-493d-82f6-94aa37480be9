import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Progress } from '@/components/ui/progress';
import { 
  Search, 
  Download, 
  Grid3X3, 
  List, 
  CheckCircle2, 
  Loader2, 
  ImageIcon, 
  PlayCircle, 
  ZoomIn, 
  Star,
  Heart,
  Eye,
  X,
  Filter,
  RefreshCw
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';

interface StockItem {
  id: string;
  type: 'photo' | 'video';
  url: string;
  thumbnailUrl: string;
  title: string;
  tags: string;
  photographer?: string;
  user?: string;
  source: 'pexels' | 'pixabay';
  width: number;
  height: number;
  duration?: number;
  views?: number;
  likes?: number;
  downloads?: number;
}

interface SearchFilters {
  mediaType: 'photo' | 'video' | 'all';
  category: string;
  orientation: 'all' | 'landscape' | 'portrait' | 'square';
  minWidth: string;
  minHeight: string;
  safeSearch: boolean;
}

const CATEGORIES = [
  'All Categories', 'Nature', 'Business', 'Technology', 'Education', 
  'Health', 'Fashion', 'Food', 'Travel', 'Architecture', 'Sports', 'Music'
];

const SUGGESTED_SEARCHES = [
  'technology', 'education', 'business meeting', 'nature landscape',
  'city skyline', 'people working', 'abstract background', 'office space'
];

const StockMediaManager: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [items, setItems] = useState<StockItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [previewItem, setPreviewItem] = useState<StockItem | null>(null);
  const [importing, setImporting] = useState<Set<string>>(new Set());
  const [importProgress, setImportProgress] = useState(0);
  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(1);
  const { toast } = useToast();

  const [filters, setFilters] = useState<SearchFilters>({
    mediaType: 'all',
    category: 'All Categories',
    orientation: 'all',
    minWidth: '',
    minHeight: '',
    safeSearch: true
  });

  const searchTimeoutRef = useRef<NodeJS.Timeout>();
  const loadMoreRef = useRef<HTMLDivElement>(null);

  // Debounced search function
  const debouncedSearch = useCallback((query: string, resetPage = true) => {
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    searchTimeoutRef.current = setTimeout(() => {
      if (query.trim()) {
        performSearch(query, resetPage ? 1 : page);
      } else {
        setItems([]);
        setHasMore(true);
      }
    }, 500);
  }, [page]);

  const performSearch = async (query: string, searchPage: number = 1) => {
    if (!query.trim()) return;

    setLoading(true);
    try {
      const params = new URLSearchParams({
        query: query.trim(),
        page: searchPage.toString(),
        perPage: '20'
      });

      // Add filters to params
      if (filters.category !== 'All Categories') {
        params.append('category', filters.category.toLowerCase());
      }
      if (filters.orientation !== 'all') {
        params.append('orientation', filters.orientation);
      }
      if (filters.minWidth) {
        params.append('minWidth', filters.minWidth);
      }
      if (filters.minHeight) {
        params.append('minHeight', filters.minHeight);
      }
      params.append('safeSearch', filters.safeSearch.toString());

      const results: StockItem[] = [];

      // Search photos if needed
      if (filters.mediaType === 'photo' || filters.mediaType === 'all') {
        try {
          const [pexelsPhotos, pixabayPhotos] = await Promise.all([
            fetch(`/api/pexels/photos?${params}`).then(r => r.ok ? r.json() : { photos: [] }),
            fetch(`/api/pixabay/photos?${params}`).then(r => r.ok ? r.json() : [])
          ]);

          // Process Pexels photos
          if (pexelsPhotos.photos) {
            results.push(...pexelsPhotos.photos.map((photo: any) => ({
              id: `pexels-photo-${photo.id}`,
              type: 'photo' as const,
              url: photo.src.large,
              thumbnailUrl: photo.src.medium,
              title: photo.alt || `Photo by ${photo.photographer}`,
              tags: photo.alt || '',
              photographer: photo.photographer,
              source: 'pexels' as const,
              width: photo.width,
              height: photo.height
            })));
          }

          // Process Pixabay photos
          if (Array.isArray(pixabayPhotos)) {
            results.push(...pixabayPhotos.map((photo: any) => ({
              id: `pixabay-photo-${photo.id}`,
              type: 'photo' as const,
              url: photo.largeImageURL || photo.url,
              thumbnailUrl: photo.webformatURL || photo.thumbnail,
              title: photo.tags || `Photo by ${photo.user || photo.photographer}`,
              tags: photo.tags || '',
              user: photo.user || photo.photographer,
              source: 'pixabay' as const,
              width: photo.imageWidth || photo.width,
              height: photo.imageHeight || photo.height,
              views: photo.views,
              likes: photo.likes,
              downloads: photo.downloads
            })));
          }
        } catch (error) {
          console.error('Error fetching photos:', error);
        }
      }

      // Search videos if needed
      if (filters.mediaType === 'video' || filters.mediaType === 'all') {
        try {
          const [pexelsVideos, pixabayVideos] = await Promise.all([
            fetch(`/api/pexels/videos?${params}`).then(r => r.ok ? r.json() : []),
            fetch(`/api/pixabay/videos?${params}`).then(r => r.ok ? r.json() : [])
          ]);

          // Process Pexels videos
          if (Array.isArray(pexelsVideos)) {
            results.push(...pexelsVideos.map((video: any) => ({
              id: `pexels-video-${video.id}`,
              type: 'video' as const,
              url: video.video_files?.[0]?.link || video.url,
              thumbnailUrl: video.image || video.thumbnail,
              title: `Video by ${video.user?.name || video.photographer}`,
              tags: video.tags || '',
              photographer: video.user?.name || video.photographer,
              source: 'pexels' as const,
              width: video.width,
              height: video.height,
              duration: video.duration
            })));
          }

          // Process Pixabay videos
          if (Array.isArray(pixabayVideos)) {
            results.push(...pixabayVideos.map((video: any) => ({
              id: `pixabay-video-${video.id}`,
              type: 'video' as const,
              url: video.videos?.large?.url || video.url,
              thumbnailUrl: video.picture_id || video.thumbnail,
              title: video.tags || `Video by ${video.user}`,
              tags: video.tags || '',
              user: video.user,
              source: 'pixabay' as const,
              width: video.videos?.large?.width || video.width,
              height: video.videos?.large?.height || video.height,
              duration: video.duration,
              views: video.views,
              likes: video.likes
            })));
          }
        } catch (error) {
          console.error('Error fetching videos:', error);
        }
      }

      // Shuffle results for variety
      const shuffledResults = results.sort(() => Math.random() - 0.5);

      if (searchPage === 1) {
        setItems(shuffledResults);
        setPage(1);
      } else {
        setItems(prev => [...prev, ...shuffledResults]);
      }

      setHasMore(shuffledResults.length >= 20);
      setPage(searchPage);
    } catch (error) {
      console.error('Search error:', error);
      toast({
        title: "Search Error",
        description: "Failed to search stock media. Please try again.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleImportItems = async (itemsToImport: StockItem[]) => {
    if (itemsToImport.length === 0) return;

    setImportProgress(0);
    const importingIds = new Set(itemsToImport.map(item => item.id));
    setImporting(importingIds);

    try {
      let successCount = 0;
      const failedItems: string[] = [];
      
      for (let i = 0; i < itemsToImport.length; i++) {
        const item = itemsToImport[i];
        
        try {
          // Extract proper ID from composite ID
          const actualId = item.id.replace(/^(pexels|pixabay)-(photo|video)-/, '');
          
          const response = await fetch('/api/stock/import', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              id: actualId,
              type: item.type,
              url: item.url,
              thumbnailUrl: item.thumbnailUrl,
              title: item.title,
              tags: item.tags,
              photographer: item.photographer || item.user,
              source: item.source,
              width: item.width,
              height: item.height,
              duration: item.duration,
              metadata: {
                views: item.views,
                likes: item.likes,
                downloads: item.downloads,
                author: item.photographer || item.user,
                size: 0
              }
            })
          });
          
          if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
          }

          const importedMedia = await response.json();
          console.log('Successfully imported:', importedMedia);
          
          successCount++;
          
          // Show individual success toast for single imports
          if (itemsToImport.length === 1) {
            toast({
              title: "Media Imported",
              description: `"${item.title}" has been added to your Media Library.`
            });
          }
          
        } catch (error: any) {
          console.error(`Failed to import ${item.title}:`, error);
          failedItems.push(item.title);
          
          // Show individual error toast for single imports
          if (itemsToImport.length === 1) {
            toast({
              title: "Import Failed",
              description: `Failed to import "${item.title}". ${error.message || 'Please try again.'}`,
              variant: "destructive"
            });
          }
        }

        setImportProgress(((i + 1) / itemsToImport.length) * 100);
      }

      // Show bulk import results
      if (itemsToImport.length > 1) {
        if (successCount === itemsToImport.length) {
          toast({
            title: "Import Complete",
            description: `Successfully imported all ${successCount} items to your Media Library.`
          });
        } else if (successCount > 0) {
          toast({
            title: "Partial Import",
            description: `Imported ${successCount} of ${itemsToImport.length} items. ${failedItems.length} failed.`
          });
        } else {
          toast({
            title: "Import Failed",
            description: "No items were imported. Please check your connection and try again.",
            variant: "destructive"
          });
        }
      }

      // Clear selections after import attempt
      setSelectedItems(new Set());
      
      // Refresh media library to show new imports
      if (successCount > 0) {
        // Trigger a refetch of media library if available
        setTimeout(() => {
          window.dispatchEvent(new CustomEvent('mediaLibraryUpdate'));
        }, 500);
      }
      
    } catch (error: any) {
      console.error('Import error:', error);
      toast({
        title: "Import Error",
        description: error.message || "Failed to import selected items. Please try again.",
        variant: "destructive"
      });
    } finally {
      setImporting(new Set());
      setImportProgress(0);
    }
  };

  const handleSelectItem = (itemId: string) => {
    const newSelected = new Set(selectedItems);
    if (newSelected.has(itemId)) {
      newSelected.delete(itemId);
    } else {
      newSelected.add(itemId);
    }
    setSelectedItems(newSelected);
  };

  const handleSelectAll = () => {
    if (selectedItems.size === items.length) {
      setSelectedItems(new Set());
    } else {
      setSelectedItems(new Set(items.map(item => item.id)));
    }
  };

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // Search effect
  useEffect(() => {
    if (searchQuery.trim()) {
      debouncedSearch(searchQuery, true);
    }
  }, [searchQuery, filters, debouncedSearch]);

  // Intersection observer for infinite scroll
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && hasMore && !loading && searchQuery.trim()) {
          debouncedSearch(searchQuery, false);
          setPage(prev => prev + 1);
        }
      },
      { threshold: 0.1 }
    );

    if (loadMoreRef.current) {
      observer.observe(loadMoreRef.current);
    }

    return () => observer.disconnect();
  }, [hasMore, loading, searchQuery, debouncedSearch]);

  const renderItem = (item: StockItem) => (
    <Card key={item.id} className="group relative overflow-hidden hover:shadow-lg transition-all">
      <div className="relative">
        <img
          src={item.thumbnailUrl}
          alt={item.title}
          className="w-full h-48 object-cover"
          loading="lazy"
        />
        
        {item.type === 'video' && (
          <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-20">
            <PlayCircle className="h-12 w-12 text-white" />
          </div>
        )}

        {item.duration && (
          <Badge variant="secondary" className="absolute bottom-2 right-2">
            {formatDuration(item.duration)}
          </Badge>
        )}

        {/* Overlay controls */}
        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all flex items-center justify-center opacity-0 group-hover:opacity-100">
          <div className="flex gap-2">
            <Button
              size="sm"
              variant="secondary"
              onClick={() => setPreviewItem(item)}
            >
              <ZoomIn className="h-4 w-4" />
            </Button>
            <Button
              size="sm"
              variant="secondary"
              onClick={() => handleImportItems([item])}
              disabled={importing.has(item.id)}
            >
              {importing.has(item.id) ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Download className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>

        {/* Selection checkbox */}
        <div className="absolute top-2 left-2">
          <Button
            size="sm"
            variant={selectedItems.has(item.id) ? "default" : "secondary"}
            onClick={() => handleSelectItem(item.id)}
            className="h-6 w-6 p-0"
          >
            {selectedItems.has(item.id) && <CheckCircle2 className="h-4 w-4" />}
          </Button>
        </div>

        {/* Source badge */}
        <Badge variant="outline" className="absolute top-2 right-2 capitalize">
          {item.source}
        </Badge>
      </div>

      <CardContent className="p-3">
        <h4 className="font-medium text-sm line-clamp-2 mb-1">{item.title}</h4>
        <p className="text-xs text-muted-foreground mb-2">
          {item.photographer || item.user}
        </p>
        
        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <span>{item.width} × {item.height}</span>
          <div className="flex items-center gap-2">
            {item.views && (
              <span className="flex items-center gap-1">
                <Eye className="h-3 w-3" />
                {item.views.toLocaleString()}
              </span>
            )}
            {item.likes && (
              <span className="flex items-center gap-1">
                <Heart className="h-3 w-3" />
                {item.likes.toLocaleString()}
              </span>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className="space-y-6">
      {/* Search and Filters */}
      <div className="space-y-4">
        <div className="flex gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search for photos and videos..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          <Button
            variant="outline"
            onClick={() => setSearchQuery('')}
            disabled={!searchQuery}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Clear
          </Button>
        </div>

        {/* Media Type Tabs */}
        <Tabs value={filters.mediaType} onValueChange={(value) => 
          setFilters(prev => ({ ...prev, mediaType: value as any }))
        }>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="all">All Media</TabsTrigger>
            <TabsTrigger value="photo">Photos</TabsTrigger>
            <TabsTrigger value="video">Videos</TabsTrigger>
          </TabsList>
        </Tabs>

        {/* Quick suggestions when no search */}
        {!searchQuery && (
          <div className="space-y-2">
            <p className="text-sm font-medium">Suggested searches:</p>
            <div className="flex flex-wrap gap-2">
              {SUGGESTED_SEARCHES.map(suggestion => (
                <Button
                  key={suggestion}
                  variant="outline"
                  size="sm"
                  onClick={() => setSearchQuery(suggestion)}
                >
                  {suggestion}
                </Button>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Results */}
      {items.length > 0 && (
        <div className="space-y-4">
          {/* Results header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <p className="text-sm text-muted-foreground">
                {items.length} results found
              </p>
              <div className="flex items-center gap-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={handleSelectAll}
                >
                  {selectedItems.size === items.length ? 'Deselect All' : 'Select All'}
                </Button>
                {selectedItems.size > 0 && (
                  <Button
                    size="sm"
                    onClick={() => handleImportItems(items.filter(item => selectedItems.has(item.id)))}
                    disabled={importing.size > 0}
                  >
                    Import Selected ({selectedItems.size})
                  </Button>
                )}
              </div>
            </div>

            <div className="flex items-center gap-2">
              <Button
                size="sm"
                variant={viewMode === 'grid' ? 'default' : 'outline'}
                onClick={() => setViewMode('grid')}
              >
                <Grid3X3 className="h-4 w-4" />
              </Button>
              <Button
                size="sm"
                variant={viewMode === 'list' ? 'default' : 'outline'}
                onClick={() => setViewMode('list')}
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Import progress */}
          {importing.size > 0 && (
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>Importing {importing.size} items...</span>
                <span>{Math.round(importProgress)}%</span>
              </div>
              <Progress value={importProgress} />
            </div>
          )}

          {/* Items grid */}
          <div className={viewMode === 'grid' 
            ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4"
            : "space-y-4"
          }>
            {items.map(renderItem)}
          </div>

          {/* Load more trigger */}
          {hasMore && (
            <div ref={loadMoreRef} className="flex justify-center py-8">
              {loading && <Loader2 className="h-6 w-6 animate-spin" />}
            </div>
          )}
        </div>
      )}

      {/* Empty state */}
      {!loading && searchQuery && items.length === 0 && (
        <div className="text-center py-12">
          <ImageIcon className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="font-medium mb-2">No results found</h3>
          <p className="text-muted-foreground mb-4">
            Try adjusting your search terms or filters
          </p>
          <Button variant="outline" onClick={() => setSearchQuery('')}>
            Clear search
          </Button>
        </div>
      )}

      {/* Loading state */}
      {loading && items.length === 0 && (
        <div className="text-center py-12">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-muted-foreground">Searching stock media...</p>
        </div>
      )}

      {/* Preview Dialog */}
      <Dialog open={!!previewItem} onOpenChange={() => setPreviewItem(null)}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle className="flex items-center justify-between">
              <span>{previewItem?.title}</span>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setPreviewItem(null)}
              >
                <X className="h-4 w-4" />
              </Button>
            </DialogTitle>
          </DialogHeader>
          
          {previewItem && (
            <div className="space-y-4">
              <div className="relative">
                {previewItem.type === 'video' ? (
                  <video
                    src={previewItem.url}
                    controls
                    className="w-full max-h-96 object-contain"
                    poster={previewItem.thumbnailUrl}
                  />
                ) : (
                  <img
                    src={previewItem.url}
                    alt={previewItem.title}
                    className="w-full max-h-96 object-contain"
                  />
                )}
              </div>
              
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <p><strong>Source:</strong> {previewItem.source}</p>
                  <p><strong>Creator:</strong> {previewItem.photographer || previewItem.user}</p>
                  <p><strong>Dimensions:</strong> {previewItem.width} × {previewItem.height}</p>
                  {previewItem.duration && (
                    <p><strong>Duration:</strong> {formatDuration(previewItem.duration)}</p>
                  )}
                </div>
                <div>
                  {previewItem.views && <p><strong>Views:</strong> {previewItem.views.toLocaleString()}</p>}
                  {previewItem.likes && <p><strong>Likes:</strong> {previewItem.likes.toLocaleString()}</p>}
                  {previewItem.downloads && <p><strong>Downloads:</strong> {previewItem.downloads.toLocaleString()}</p>}
                  <p><strong>Tags:</strong> {previewItem.tags}</p>
                </div>
              </div>
              
              <div className="flex justify-end gap-2">
                <Button
                  onClick={() => {
                    handleImportItems([previewItem]);
                    setPreviewItem(null);
                  }}
                  disabled={importing.has(previewItem.id)}
                >
                  {importing.has(previewItem.id) ? (
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  ) : (
                    <Download className="h-4 w-4 mr-2" />
                  )}
                  Import to Library
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default StockMediaManager;