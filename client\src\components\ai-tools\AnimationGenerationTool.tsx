import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';
import { Loader2, Download, Clock } from 'lucide-react';
import { Slider } from '@/components/ui/slider';
import { Progress } from '@/components/ui/progress';

interface AnimationGenerationToolProps {
  onAnimationGenerated?: (url: string) => void;
  defaultPrompt?: string;
  heading?: string;
  description?: string;
}

export function AnimationGenerationTool({
  onAnimationGenerated,
  defaultPrompt = '',
  heading = 'Talking Avatar Generation',
  description = 'Generate talking avatars using SadTalker open-source model running on RunPod H100.'
}: AnimationGenerationToolProps) {
  const { toast } = useToast();
  const [prompt, setPrompt] = useState(defaultPrompt);
  const [frames, setFrames] = useState(24);
  const [fps, setFps] = useState(8);
  const [width, setWidth] = useState(512);
  const [height, setHeight] = useState(512);
  const [generating, setGenerating] = useState(false);
  const [jobId, setJobId] = useState<string | null>(null);
  const [progress, setProgress] = useState(0);
  const [estimatedTimeMin, setEstimatedTimeMin] = useState(0);
  const [checkingStatus, setCheckingStatus] = useState(false);
  const [generatedAnimation, setGeneratedAnimation] = useState<{
    url: string;
    width: number;
    height: number;
    frames: number;
    fps: number;
  } | null>(null);
  
  // Poll job status if there's an active job
  useEffect(() => {
    if (jobId && generating) {
      const statusTimer = setInterval(() => {
        checkJobStatus(jobId);
      }, 10000); // Check every 10 seconds
      
      return () => clearInterval(statusTimer);
    }
  }, [jobId, generating]);
  
  const handleGenerate = async () => {
    if (!prompt.trim()) {
      toast({
        title: 'Prompt is required',
        description: 'Please enter a prompt for animation generation',
        variant: 'destructive',
      });
      return;
    }
    
    setGenerating(true);
    setProgress(0);
    setGeneratedAnimation(null);
    
    try {
      const response = await apiRequest(
        'POST', 
        '/api/ai-tools/animation-generation', 
        {
          prompt,
          frames,
          fps,
          width,
          height
        }
      );
      
      const data = await response.json();
      
      if (data.status === 'processing' && data.estimatedTime) {
        setJobId(data.jobId || 'unknown');
        setEstimatedTimeMin(data.estimatedTime);
        toast({
          title: 'Animation generation started',
          description: `This will take approximately ${data.estimatedTime} minutes to complete`,
        });
      } else if (data.url) {
        // In case we get an immediate response (unlikely for animations)
        handleAnimationComplete(data);
      } else {
        toast({
          title: 'Generation failed',
          description: data.message || 'Failed to start animation generation',
          variant: 'destructive',
        });
        setGenerating(false);
      }
    } catch (error) {
      console.error('Animation generation error:', error);
      toast({
        title: 'Error',
        description: 'Failed to connect to the animation generation service',
        variant: 'destructive',
      });
      setGenerating(false);
    }
  };
  
  const checkJobStatus = async (id: string) => {
    if (checkingStatus) return; // Prevent multiple simultaneous checks
    
    setCheckingStatus(true);
    try {
      const response = await fetch(`/api/ai-tools/animation-status/${id}`);
      const data = await response.json();
      
      if (data.status === 'completed' && data.url) {
        handleAnimationComplete(data);
      } else if (data.status === 'failed') {
        toast({
          title: 'Generation failed',
          description: data.error || 'Animation generation failed',
          variant: 'destructive',
        });
        setGenerating(false);
        setJobId(null);
      } else if (data.progress) {
        // Update progress
        setProgress(data.progress);
      }
    } catch (error) {
      console.error('Error checking job status:', error);
    } finally {
      setCheckingStatus(false);
    }
  };
  
  const handleAnimationComplete = (data: any) => {
    setGeneratedAnimation({
      url: data.url,
      width: data.width || width,
      height: data.height || height,
      frames: data.frames || frames,
      fps: data.fps || fps
    });
    
    if (onAnimationGenerated) {
      onAnimationGenerated(data.url);
    }
    
    toast({
      title: 'Animation complete',
      description: 'Your animation has been generated successfully',
    });
    
    setGenerating(false);
    setJobId(null);
    setProgress(100);
  };
  
  const handleDownload = () => {
    if (!generatedAnimation) return;
    
    // Create a temporary link element
    const link = document.createElement('a');
    link.href = generatedAnimation.url;
    link.download = `generated-animation-${Date.now()}.mp4`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };
  
  const dimensionOptions = [
    { value: 256, label: '256px' },
    { value: 384, label: '384px' },
    { value: 512, label: '512px' },
    { value: 768, label: '768px' },
  ];
  
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>{heading}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <Label htmlFor="prompt">Prompt</Label>
          <Textarea
            id="prompt"
            placeholder="Describe the animation you want to generate..."
            className="min-h-[100px]"
            value={prompt}
            onChange={(e) => setPrompt(e.target.value)}
            disabled={generating}
          />
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="frames">Frames: {frames}</Label>
            <Slider
              id="frames"
              defaultValue={[frames]}
              min={8}
              max={48}
              step={4}
              onValueChange={(value) => setFrames(value[0])}
              disabled={generating}
            />
            <p className="text-xs text-muted-foreground">
              Number of frames in the animation
            </p>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="fps">FPS: {fps}</Label>
            <Slider
              id="fps"
              defaultValue={[fps]}
              min={4}
              max={30}
              step={1}
              onValueChange={(value) => setFps(value[0])}
              disabled={generating}
            />
            <p className="text-xs text-muted-foreground">
              Frames per second (playback speed)
            </p>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="width">Width</Label>
            <Select 
              value={width.toString()} 
              onValueChange={(value) => setWidth(parseInt(value))}
              disabled={generating}
            >
              <SelectTrigger id="width">
                <SelectValue placeholder="Select width" />
              </SelectTrigger>
              <SelectContent>
                {dimensionOptions.map((option) => (
                  <SelectItem key={`width-${option.value}`} value={option.value.toString()}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="height">Height</Label>
            <Select 
              value={height.toString()} 
              onValueChange={(value) => setHeight(parseInt(value))}
              disabled={generating}
            >
              <SelectTrigger id="height">
                <SelectValue placeholder="Select height" />
              </SelectTrigger>
              <SelectContent>
                {dimensionOptions.map((option) => (
                  <SelectItem key={`height-${option.value}`} value={option.value.toString()}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
        
        <Button
          className="w-full"
          disabled={generating || !prompt.trim()}
          onClick={handleGenerate}
        >
          {generating ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Generating...
            </>
          ) : (
            'Generate Animation'
          )}
        </Button>
        
        {generating && (
          <div className="pt-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium">Generation in progress</span>
              <span className="text-sm text-muted-foreground flex items-center">
                <Clock className="h-3 w-3 mr-1" />
                {estimatedTimeMin} min
              </span>
            </div>
            <Progress value={progress} className="h-2" />
            <p className="text-xs text-muted-foreground mt-1">
              Animation generation may take several minutes to complete.
            </p>
          </div>
        )}
        
        {generatedAnimation && (
          <div className="pt-4">
            <div className="text-sm font-medium mb-2">Generated Animation</div>
            <div className="bg-secondary rounded-md p-4 flex justify-center">
              <video 
                src={generatedAnimation.url} 
                controls 
                autoPlay 
                loop 
                muted 
                className="max-w-full rounded shadow-md" 
                style={{ maxHeight: '400px' }}
              />
            </div>
          </div>
        )}
      </CardContent>
      <CardFooter className="flex justify-between">
        {generatedAnimation && (
          <>
            <div className="text-xs text-muted-foreground">
              Size: {generatedAnimation.width}x{generatedAnimation.height}, 
              Frames: {generatedAnimation.frames}, 
              FPS: {generatedAnimation.fps}
            </div>
            <Button variant="outline" size="sm" onClick={handleDownload}>
              <Download className="h-4 w-4 mr-2" />
              Download
            </Button>
          </>
        )}
      </CardFooter>
    </Card>
  );
}

export default AnimationGenerationTool;