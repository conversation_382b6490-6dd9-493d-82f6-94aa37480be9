import { useState, useEffect } from 'react';
import { toast } from '@/hooks/use-toast';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { apiRequest } from '@/lib/queryClient';

interface CollaborationNotificationProps {
  courseId?: number;
  teamId?: number;
}

interface NotificationData {
  id: string;
  type: string;
  title: string;
  message: string;
  from: {
    id: number;
    name: string;
    avatarUrl?: string;
  };
  courseId?: number;
  teamId?: number;
  timestamp: Date;
}

export function CollaborationNotification({ courseId, teamId }: CollaborationNotificationProps) {
  const [notification, setNotification] = useState<NotificationData | null>(null);
  const [isOpen, setIsOpen] = useState(false);
  
  // Poll for new notifications
  useEffect(() => {
    if (!courseId && !teamId) return;
    
    // Poll for new notifications every 15 seconds
    const interval = setInterval(async () => {
      try {
        // Construct the query based on available IDs
        let endpoint = `/api/notifications?`;
        if (courseId) endpoint += `courseId=${courseId}`;
        if (teamId) endpoint += courseId ? `&teamId=${teamId}` : `teamId=${teamId}`;
        
        const response = await apiRequest('GET', endpoint);
        if (!response.ok) return;
        
        const data = await response.json();
        
        // If we have new notifications
        if (data && data.length > 0) {
          const latestNotification = data[0]; // Get latest notification
          
          // Set the notification and open the dialog
          setNotification(latestNotification);
          setIsOpen(true);
          
          // Mark as read in the background
          await apiRequest('POST', `/api/notifications/${latestNotification.id}/read`);
        }
      } catch (error) {
        console.error('Error fetching notifications:', error);
      }
    }, 15000);
    
    return () => clearInterval(interval);
  }, [courseId, teamId]);
  
  const handleAccept = async () => {
    if (!notification) return;
    
    try {
      // Depending on notification type, make API request to accept
      if (notification.type === 'invitation') {
        await apiRequest('POST', `/api/invitations/${notification.id}/accept`);
      } else if (notification.type === 'collaboration_request') {
        await apiRequest('POST', `/api/collaboration-requests/${notification.id}/accept`);
      }
      
      toast({
        title: 'Accepted',
        description: 'You have accepted the request successfully.'
      });
    } catch (error) {
      console.error('Error accepting request:', error);
      toast({
        title: 'Error',
        description: 'Failed to accept the request. Please try again.',
        variant: 'destructive'
      });
    }
    
    setIsOpen(false);
  };
  
  const handleDecline = async () => {
    if (!notification) return;
    
    try {
      // Depending on notification type, make API request to decline
      if (notification.type === 'invitation') {
        await apiRequest('POST', `/api/invitations/${notification.id}/decline`);
      } else if (notification.type === 'collaboration_request') {
        await apiRequest('POST', `/api/collaboration-requests/${notification.id}/decline`);
      }
      
      toast({
        title: 'Declined',
        description: 'You have declined the request.'
      });
    } catch (error) {
      console.error('Error declining request:', error);
      toast({
        title: 'Error',
        description: 'Failed to decline the request. Please try again.',
        variant: 'destructive'
      });
    }
    
    setIsOpen(false);
  };
  
  // If no notification, render nothing
  if (!notification) return null;
  
  return (
    <AlertDialog open={isOpen} onOpenChange={setIsOpen}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>{notification.title}</AlertDialogTitle>
          <AlertDialogDescription>
            {notification.message}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel onClick={handleDecline}>Decline</AlertDialogCancel>
          <AlertDialogAction onClick={handleAccept}>Accept</AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}