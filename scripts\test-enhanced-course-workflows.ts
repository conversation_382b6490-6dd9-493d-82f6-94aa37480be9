#!/usr/bin/env tsx
/**
 * Enhanced Course Workflow Testing Suite
 * Comprehensive testing for both traditional and avatar course generation workflows
 */

import dotenv from 'dotenv';
import { unifiedCourseGenerationService, UnifiedCourseOptions } from '../server/services/unified-course-generation-service';
import { courseGenerationMonitoringService } from '../server/services/course-generation-monitoring';
import { dbManager, safeDbOperation } from '../server/db-enhanced';
import { users, courses } from '../shared/schema';
import { eq } from 'drizzle-orm';

// Load environment variables
dotenv.config();

interface TestResult {
  testName: string;
  passed: boolean;
  duration: number;
  error?: string;
  details?: any;
}

interface WorkflowTestResult {
  workflowType: 'traditional' | 'avatar';
  jobId?: string;
  courseId?: number;
  generationTime: number;
  qualityScore: number;
  success: boolean;
  error?: string;
  metrics?: any;
}

class EnhancedCourseWorkflowTester {
  private results: TestResult[] = [];
  private testUserId: number | null = null;

  /**
   * Run all enhanced course workflow tests
   */
  async runAllTests(): Promise<void> {
    console.log('🧪 Starting Enhanced Course Workflow Tests\n');
    console.log('=' .repeat(70));

    try {
      // Setup test environment
      await this.setupTestEnvironment();

      // Test unified course generation service
      await this.testUnifiedCourseGenerationService();

      // Test traditional course workflow
      await this.testTraditionalCourseWorkflow();

      // Test avatar course workflow
      await this.testAvatarCourseWorkflow();

      // Test monitoring and quality validation
      await this.testMonitoringAndQuality();

      // Test API endpoints
      await this.testAPIEndpoints();

      // Test error handling and edge cases
      await this.testErrorHandling();

      // Cleanup test data
      await this.cleanupTestData();

      // Generate comprehensive report
      this.generateTestReport();

    } catch (error) {
      console.error('❌ Test suite failed:', error);
      throw error;
    }
  }

  /**
   * Setup test environment
   */
  private async setupTestEnvironment(): Promise<void> {
    await this.runTest('Setup Test Environment', async () => {
      // Create test user
      const testUser = await safeDbOperation(async () => {
        const db = dbManager.getDatabase();
        const [user] = await db.insert(users).values({
          username: `test_user_${Date.now()}`,
          email: `test_${Date.now()}@example.com`,
          password: 'hashed_password_test',
          name: 'Test User for Course Workflows',
          plan: 'premium',
          role: 'user'
        }).returning();
        return user;
      });

      if (!testUser) {
        throw new Error('Failed to create test user');
      }

      this.testUserId = testUser.id;

      return {
        testUserId: this.testUserId,
        userCreated: true
      };
    });
  }

  /**
   * Test unified course generation service
   */
  private async testUnifiedCourseGenerationService(): Promise<void> {
    await this.runTest('Unified Course Generation Service', async () => {
      // Test service initialization
      const stats = await unifiedCourseGenerationService.getGenerationStatistics();
      
      // Test active jobs tracking
      const activeJobs = unifiedCourseGenerationService.getActiveJobs();
      
      // Test options validation
      try {
        await unifiedCourseGenerationService.generateCourse({
          type: 'traditional',
          title: '', // Invalid title
          userId: this.testUserId!,
          category: 'technology'
        });
        throw new Error('Should have failed validation');
      } catch (error) {
        // Expected validation error
      }

      return {
        statsRetrieved: !!stats,
        activeJobsTracked: Array.isArray(activeJobs),
        validationWorking: true
      };
    });
  }

  /**
   * Test traditional course workflow
   */
  private async testTraditionalCourseWorkflow(): Promise<void> {
    await this.runTest('Traditional Course Workflow', async () => {
      if (!this.testUserId) {
        throw new Error('Test user not available');
      }

      const options: UnifiedCourseOptions = {
        type: 'traditional',
        title: 'Test Traditional Course: AI and Machine Learning',
        userId: this.testUserId,
        targetAudience: 'Developers and tech enthusiasts',
        category: 'technology',
        difficulty: 'intermediate',
        duration: 'short',
        traditionalOptions: {
          voiceSettings: {
            voiceId: 'tts_models/en/ljspeech/tacotron2-DDC',
            speed: 1.0,
            pitch: 1.0
          },
          visualStyle: 'educational',
          mediaPreferences: {
            videoQuality: 'high',
            sceneChangeFrequency: 'medium'
          }
        }
      };

      // Start generation
      const result = await unifiedCourseGenerationService.generateCourse(options);
      
      if (!result.jobId) {
        throw new Error('No job ID returned');
      }

      // Monitor progress for a short time
      const workflowResult = await this.monitorWorkflowProgress(result.jobId, 'traditional', 60000); // 1 minute timeout

      return {
        jobStarted: true,
        jobId: result.jobId,
        estimatedTime: result.estimatedTime,
        workflowResult
      };
    });
  }

  /**
   * Test avatar course workflow
   */
  private async testAvatarCourseWorkflow(): Promise<void> {
    await this.runTest('Avatar Course Workflow', async () => {
      if (!this.testUserId) {
        throw new Error('Test user not available');
      }

      const options: UnifiedCourseOptions = {
        type: 'avatar',
        title: 'Test Avatar Course: Digital Marketing Fundamentals',
        userId: this.testUserId,
        targetAudience: 'Marketing professionals',
        category: 'business',
        difficulty: 'beginner',
        duration: 'short',
        avatarOptions: {
          avatarConfig: {
            type: 'image',
            sourceUrl: 'https://example.com/test-avatar.jpg', // Test image
            style: 'professional',
            background: 'office'
          },
          voiceSettings: {
            voiceId: 'tts_models/en/ljspeech/tacotron2-DDC',
            speed: 1.0,
            pitch: 1.0,
            emotion: 'enthusiastic'
          }
        }
      };

      // Start generation
      const result = await unifiedCourseGenerationService.generateCourse(options);
      
      if (!result.jobId) {
        throw new Error('No job ID returned');
      }

      // Monitor progress for a short time
      const workflowResult = await this.monitorWorkflowProgress(result.jobId, 'avatar', 90000); // 1.5 minute timeout

      return {
        jobStarted: true,
        jobId: result.jobId,
        estimatedTime: result.estimatedTime,
        workflowResult
      };
    });
  }

  /**
   * Test monitoring and quality validation
   */
  private async testMonitoringAndQuality(): Promise<void> {
    await this.runTest('Monitoring and Quality Validation', async () => {
      // Test monitoring service
      const systemHealth = courseGenerationMonitoringService.getSystemHealth();
      const stats = courseGenerationMonitoringService.getGenerationStatistics();
      
      // Test quality validation (if we have a test course)
      let qualityValidation = null;
      if (this.testUserId) {
        const testCourses = await safeDbOperation(async () => {
          const db = dbManager.getDatabase();
          return await db.select().from(courses).where(eq(courses.userId, this.testUserId!)).limit(1);
        });

        if (testCourses && testCourses.length > 0) {
          qualityValidation = await unifiedCourseGenerationService.validateCourseQuality(testCourses[0].id);
        }
      }

      return {
        systemHealthAvailable: !!systemHealth,
        statisticsAvailable: !!stats,
        qualityValidationWorking: !!qualityValidation,
        qualityScore: qualityValidation?.qualityScore || 0
      };
    });
  }

  /**
   * Test API endpoints
   */
  private async testAPIEndpoints(): Promise<void> {
    await this.runTest('API Endpoints', async () => {
      // Test course generation options endpoint
      const optionsResponse = await fetch('http://localhost:3001/api/course-generation/options');
      const optionsData = optionsResponse.ok ? await optionsResponse.json() : null;

      // Test statistics endpoint (would need authentication in real scenario)
      const statsResponse = await fetch('http://localhost:3001/api/course-generation/statistics');
      const statsAvailable = statsResponse.status !== 404; // May fail due to auth

      return {
        optionsEndpointWorking: optionsResponse.ok,
        optionsData: optionsData?.courseTypes?.length || 0,
        statsEndpointAccessible: statsAvailable
      };
    });
  }

  /**
   * Test error handling and edge cases
   */
  private async testErrorHandling(): Promise<void> {
    await this.runTest('Error Handling and Edge Cases', async () => {
      const errorTests = [];

      // Test invalid course type
      try {
        await unifiedCourseGenerationService.generateCourse({
          type: 'invalid' as any,
          title: 'Test Course',
          userId: this.testUserId!,
          category: 'technology'
        });
        errorTests.push({ test: 'invalid_type', passed: false });
      } catch (error) {
        errorTests.push({ test: 'invalid_type', passed: true });
      }

      // Test missing avatar config for avatar course
      try {
        await unifiedCourseGenerationService.generateCourse({
          type: 'avatar',
          title: 'Test Avatar Course',
          userId: this.testUserId!,
          category: 'technology'
          // Missing avatarOptions
        });
        errorTests.push({ test: 'missing_avatar_config', passed: false });
      } catch (error) {
        errorTests.push({ test: 'missing_avatar_config', passed: true });
      }

      // Test invalid user ID
      try {
        await unifiedCourseGenerationService.generateCourse({
          type: 'traditional',
          title: 'Test Course',
          userId: -1,
          category: 'technology'
        });
        errorTests.push({ test: 'invalid_user_id', passed: false });
      } catch (error) {
        errorTests.push({ test: 'invalid_user_id', passed: true });
      }

      const passedTests = errorTests.filter(t => t.passed).length;
      const totalTests = errorTests.length;

      return {
        errorTestsPassed: passedTests,
        totalErrorTests: totalTests,
        errorHandlingScore: (passedTests / totalTests) * 100,
        details: errorTests
      };
    });
  }

  /**
   * Monitor workflow progress
   */
  private async monitorWorkflowProgress(
    jobId: string,
    workflowType: 'traditional' | 'avatar',
    timeoutMs: number
  ): Promise<WorkflowTestResult> {
    const startTime = Date.now();
    const timeout = startTime + timeoutMs;

    while (Date.now() < timeout) {
      const progress = unifiedCourseGenerationService.getProgress(jobId);
      
      if (!progress) {
        return {
          workflowType,
          jobId,
          generationTime: Date.now() - startTime,
          qualityScore: 0,
          success: false,
          error: 'Progress tracking lost'
        };
      }

      if (progress.status === 'completed') {
        // Validate quality if course was created
        let qualityScore = 0;
        if (progress.courseId) {
          const validation = await unifiedCourseGenerationService.validateCourseQuality(progress.courseId);
          qualityScore = validation.qualityScore;
        }

        return {
          workflowType,
          jobId,
          courseId: progress.courseId,
          generationTime: Date.now() - startTime,
          qualityScore,
          success: true,
          metrics: {
            progress: progress.progress,
            estimatedTime: progress.estimatedTimeRemaining,
            qualityMetrics: progress.qualityMetrics
          }
        };
      }

      if (progress.status === 'error') {
        return {
          workflowType,
          jobId,
          generationTime: Date.now() - startTime,
          qualityScore: 0,
          success: false,
          error: progress.error || 'Unknown error'
        };
      }

      // Wait before checking again
      await new Promise(resolve => setTimeout(resolve, 5000)); // 5 seconds
    }

    // Timeout reached
    return {
      workflowType,
      jobId,
      generationTime: timeoutMs,
      qualityScore: 0,
      success: false,
      error: 'Workflow timeout - generation taking too long'
    };
  }

  /**
   * Clean up test data
   */
  private async cleanupTestData(): Promise<void> {
    await this.runTest('Cleanup Test Data', async () => {
      if (!this.testUserId) {
        return { cleaned: false, reason: 'No test user to clean' };
      }

      let cleanedItems = 0;

      // Delete test courses
      const deletedCourses = await safeDbOperation(async () => {
        const db = dbManager.getDatabase();
        return await db.delete(courses).where(eq(courses.userId, this.testUserId!));
      });
      cleanedItems += deletedCourses?.length || 0;

      // Delete test user
      await safeDbOperation(async () => {
        const db = dbManager.getDatabase();
        await db.delete(users).where(eq(users.id, this.testUserId!));
      });
      cleanedItems += 1;

      return { cleaned: true, itemsRemoved: cleanedItems };
    });
  }

  /**
   * Run individual test with timing and error handling
   */
  private async runTest(name: string, testFn: () => Promise<any>): Promise<void> {
    const startTime = Date.now();
    
    try {
      console.log(`🔄 Running: ${name}...`);
      const result = await testFn();
      const duration = Date.now() - startTime;
      
      this.results.push({
        testName: name,
        passed: true,
        duration,
        details: result
      });
      
      console.log(`✅ ${name} - ${duration}ms`);
      
    } catch (error) {
      const duration = Date.now() - startTime;
      
      this.results.push({
        testName: name,
        passed: false,
        duration,
        error: error instanceof Error ? error.message : String(error)
      });
      
      console.log(`❌ ${name} - ${duration}ms - ${error}`);
    }
  }

  /**
   * Generate comprehensive test report
   */
  private generateTestReport(): void {
    console.log('\n' + '='.repeat(70));
    console.log('📊 ENHANCED COURSE WORKFLOW TEST REPORT');
    console.log('='.repeat(70));

    const totalTests = this.results.length;
    const passedTests = this.results.filter(r => r.passed).length;
    const failedTests = totalTests - passedTests;
    const totalDuration = this.results.reduce((sum, r) => sum + r.duration, 0);

    console.log(`\n📈 SUMMARY:`);
    console.log(`   Total Tests: ${totalTests}`);
    console.log(`   Passed: ${passedTests} ✅`);
    console.log(`   Failed: ${failedTests} ❌`);
    console.log(`   Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
    console.log(`   Total Duration: ${(totalDuration / 1000).toFixed(1)}s`);

    if (failedTests > 0) {
      console.log(`\n❌ FAILED TESTS:`);
      this.results.filter(r => !r.passed).forEach(result => {
        console.log(`   - ${result.testName}: ${result.error}`);
      });
    }

    console.log(`\n✅ PASSED TESTS:`);
    this.results.filter(r => r.passed).forEach(result => {
      console.log(`   - ${result.testName} (${result.duration}ms)`);
    });

    // Detailed results for workflow tests
    const workflowResults = this.results.filter(r => 
      r.testName.includes('Workflow') && r.passed && r.details?.workflowResult
    );

    if (workflowResults.length > 0) {
      console.log(`\n🎬 WORKFLOW RESULTS:`);
      workflowResults.forEach(result => {
        const workflow = result.details.workflowResult;
        console.log(`   ${workflow.workflowType.toUpperCase()} Course:`);
        console.log(`     Success: ${workflow.success ? '✅' : '❌'}`);
        console.log(`     Generation Time: ${(workflow.generationTime / 1000).toFixed(1)}s`);
        console.log(`     Quality Score: ${workflow.qualityScore}%`);
        if (workflow.error) {
          console.log(`     Error: ${workflow.error}`);
        }
      });
    }

    console.log('\n' + '='.repeat(70));
    
    if (passedTests === totalTests) {
      console.log('🎉 ALL TESTS PASSED! Enhanced course workflows are working correctly.');
    } else {
      console.log('⚠️  Some tests failed. Please review the errors above.');
    }

    console.log('\n🚀 Enhanced Course Generation Features:');
    console.log('   ✅ Traditional Course Workflow (Mistral + Coqui TTS + FFmpeg)');
    console.log('   ✅ Avatar Course Workflow (EchoMimic V2 + Enhanced Pipeline)');
    console.log('   ✅ Unified Generation Service with Progress Tracking');
    console.log('   ✅ Quality Validation and Monitoring');
    console.log('   ✅ Enhanced Video Assembly Pipeline');
    console.log('   ✅ Real-time Progress Tracking and Error Handling');
  }
}

// CLI interface
async function main() {
  const tester = new EnhancedCourseWorkflowTester();
  await tester.runAllTests();
}

// Run if called directly
if (require.main === module) {
  main().catch(error => {
    console.error('Enhanced workflow test suite failed:', error);
    process.exit(1);
  });
}

export { EnhancedCourseWorkflowTester };
