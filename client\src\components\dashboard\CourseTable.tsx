import { Course } from "@/types";
import { <PERSON> } from "wouter";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { 
  Edit, Eye, Trash2, 
  CheckCircle, FileEdit, Clock,
  Album, ArrowUpRight
} from "lucide-react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { useState } from "react";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

interface CourseTableProps {
  courses: Course[];
  onEdit: (course: Course) => void;
  onPreview: (course: Course) => void;
  onDelete: (course: Course) => void;
  viewAllUrl?: string;
}

export function CourseTable({ 
  courses, 
  onEdit, 
  onPreview, 
  onDelete,
  viewAllUrl = "/my-courses" 
}: CourseTableProps) {
  const [courseToDelete, setCourseToDelete] = useState<Course | null>(null);

  const getStatusBadgeClasses = (status: string, completion: number = 0) => {
    // For published courses, always use green styling regardless of completion percentage
    if (status === "published") {
      return "bg-green-100 text-green-800";
    }
    
    // For courses marked as in_progress
    if (status === "in_progress") {
      // If fully completed, use a success style
      if (completion === 100) {
        return "bg-blue-100 text-blue-800";
      }
      // Otherwise show as in progress with blue
      return "bg-blue-100 text-blue-800";
    }
    
    // For draft status
    if (status === "draft") {
      return "bg-yellow-100 text-yellow-800";
    }
    
    // Default styling
    return "bg-slate-100 text-slate-800";
  };

  const getStatusLabel = (status: string, completion: number) => {
    // For published courses, always show as "Published" regardless of completion
    // The completion percentage is shown separately in the progress bar
    if (status === "published") {
      return "Published";
    }
    
    // For courses in progress but not completed
    if (status === "in_progress" && completion < 100) {
      return "In Progress";
    }
    
    // For other statuses
    switch (status) {
      case "draft":
        return "Draft";
      case "in_progress":
        return completion === 100 ? "Completed" : "In Progress";
      default:
        return status;
    }
  };

  const getStatusIcon = (status: string, completion: number) => {
    // For published courses, show the checkmark regardless of completion
    // This represents that the course is available to be taken, not that it's completed by the creator
    if (status === "published") {
      return <CheckCircle className="mr-1 h-3 w-3" />;
    }
    
    // For other statuses, use completion to determine if in progress
    if (completion < 100 && status === "in_progress") {
      return <Clock className="mr-1 h-3 w-3" />;
    }
    
    // Default handling for other statuses
    switch (status) {
      case "draft":
        return <FileEdit className="mr-1 h-3 w-3" />;
      case "in_progress":
        return <Clock className="mr-1 h-3 w-3" />;
      default:
        return <Album className="mr-1 h-3 w-3" />;
    }
  };

  const handleDeleteConfirm = () => {
    if (courseToDelete) {
      onDelete(courseToDelete);
      setCourseToDelete(null);
    }
  };

  const formatDate = (dateString: string | Date) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) {
      return 'Today';
    } else if (diffDays === 1) {
      return 'Yesterday';
    } else if (diffDays < 7) {
      return `${diffDays} days ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  return (
    <div className="bg-white rounded-xl shadow-sm border border-slate-200 overflow-hidden">
      <div className="border-b border-slate-200 px-6 py-4 flex justify-between items-center">
        <h2 className="text-lg font-semibold text-slate-900">Recent Courses</h2>
        <Link href={viewAllUrl} className="text-sm text-primary hover:text-primary-700 font-medium flex items-center gap-1">
          View all
          <ArrowUpRight className="h-3 w-3" />
        </Link>
      </div>
      
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-slate-200">
          <thead className="bg-slate-50">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">Course</th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">Status</th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">Completion</th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">Last Updated</th>
              <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-slate-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-slate-200">
            {courses.length === 0 ? (
              <tr>
                <td colSpan={5} className="px-6 py-10 text-center text-sm text-slate-500">
                  No courses found. Start creating your first course!
                </td>
              </tr>
            ) : (
              courses.map((course) => (
                <tr key={course.id} className="hover:bg-slate-50 transition-colors">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="h-10 w-10 flex-shrink-0 rounded bg-slate-200 flex items-center justify-center">
                        {course.thumbnailUrl ? (
                          <img 
                            src={course.thumbnailUrl} 
                            alt={course.title} 
                            className="h-10 w-10 rounded object-cover"
                          />
                        ) : (
                          <i className="ri-slideshow-line text-slate-600"></i>
                        )}
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-slate-900">{course.title}</div>
                        <div className="text-sm text-slate-500">{course.lessonsCount || 0} lessons</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2.5 py-1 text-xs font-medium rounded-full flex items-center w-fit ${getStatusBadgeClasses(course.status, course.completion || 0)}`}>
                      {getStatusIcon(course.status, course.completion || 0)}
                      {getStatusLabel(course.status, course.completion || 0)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="w-full bg-slate-200 rounded-full h-2.5">
                      <div 
                        className="bg-primary h-2.5 rounded-full" 
                        style={{ width: `${course.completion || 0}%` }}
                      ></div>
                    </div>
                    <span className="text-xs text-slate-500 mt-1 block">{course.completion || 0}%</span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-500">
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <span>{formatDate(course.updatedAt)}</span>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>{new Date(course.updatedAt).toLocaleString()}</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex space-x-2 justify-end">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button 
                              variant="ghost" 
                              size="icon"
                              onClick={() => onEdit(course)}
                              className="h-8 w-8 text-slate-500 hover:text-primary"
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Edit course</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                      
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button 
                              variant="ghost"
                              size="icon"
                              onClick={() => onPreview(course)}
                              className="h-8 w-8 text-slate-500 hover:text-primary"
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Preview course</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                      
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button 
                              variant="ghost"
                              size="icon"
                              onClick={() => setCourseToDelete(course)}
                              className="h-8 w-8 text-slate-500 hover:text-destructive"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Delete course</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
      
      {/* Delete Confirmation Dialog */}
      <AlertDialog open={courseToDelete !== null} onOpenChange={(open) => !open && setCourseToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Course</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this course? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setCourseToDelete(null)}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction 
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              onClick={handleDeleteConfirm}
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
