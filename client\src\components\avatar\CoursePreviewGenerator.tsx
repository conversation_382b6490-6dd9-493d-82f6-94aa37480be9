import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import {
  Play,
  Download,
  FileText,
  Video,
  Volume2,
  User,
  CheckCircle,
  Loader2,
  AlertCircle,
  ArrowLeft,
  ArrowRight,
  Eye,
  Settings
} from 'lucide-react';
import { useMutation } from '@tanstack/react-query';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';

interface CoursePreviewGeneratorProps {
  onSubmit: (data: any) => void;
  onBack: () => void;
  courseData: any;
  avatarData: any;
  scriptData: any;
  voiceData: any;
}

interface GenerationStep {
  id: string;
  title: string;
  description: string;
  status: 'pending' | 'processing' | 'completed' | 'error';
  progress: number;
  result?: any;
}

export function CoursePreviewGenerator({
  onSubmit,
  onBack,
  courseData,
  avatarData,
  scriptData,
  voiceData
}: CoursePreviewGeneratorProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [isGenerating, setIsGenerating] = useState(false);
  const [previewData, setPreviewData] = useState<any>(null);
  const { toast } = useToast();

  const [steps, setSteps] = useState<GenerationStep[]>([
    {
      id: 'slides',
      title: 'Generate Slides',
      description: 'Creating presentation slides with Marp',
      status: 'pending',
      progress: 0
    },
    {
      id: 'speech',
      title: 'Generate Speech',
      description: 'Converting script to audio using TTS',
      status: 'pending',
      progress: 0
    },
    {
      id: 'avatar',
      title: 'Create Avatar Video',
      description: 'Generating talking avatar with SadTalker',
      status: 'pending',
      progress: 0
    },
    {
      id: 'finalize',
      title: 'Finalize Course',
      description: 'Combining all elements into final course',
      status: 'pending',
      progress: 0
    }
  ]);

  // Enhanced avatar course generation mutation with individual lessons
  const generateCourseMutation = useMutation({
    mutationFn: async () => {
      // Transform data for the enhanced avatar course service
      const enhancedCourseData = {
        id: Date.now().toString(),
        title: courseData.title,
        description: courseData.description,
        modules: scriptData.segments?.map((segment: any, index: number) => ({
          id: `module-${index}`,
          title: segment.title || `Module ${index + 1}`,
          lessons: [{
            id: `lesson-${index}-1`,
            title: segment.title || `Lesson ${index + 1}`,
            content: segment.content,
            script: segment.script || segment.content,
            slides: segment.slides || [segment.content]
          }]
        })) || []
      };

      const enhancedAvatarSettings = {
        avatarImage: avatarData.selectedAvatarImage || '',
        voiceSettings: {
          service: voiceData.service || 'openai',
          voiceId: voiceData.voiceId || 'alloy',
          speed: voiceData.speed || 1.0,
          pitch: voiceData.pitch || 1.0,
          temperature: voiceData.temperature || 0.7,
          stability: voiceData.stability || 0.75
        }
      };

      const response = await fetch('/api/avatar-course/generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          courseData: enhancedCourseData,
          avatarSettings: enhancedAvatarSettings
        })
      });
      
      if (!response.ok) {
        throw new Error('Failed to generate enhanced avatar course');
      }
      
      return response.json();
    },
    onSuccess: (data) => {
      setPreviewData(data);
      setIsGenerating(false);
      
      const completedLessons = data.lessons?.filter((l: any) => l.status === 'completed').length || 0;
      const totalLessons = data.lessons?.length || 0;
      
      toast({
        title: "Avatar Course Generated Successfully",
        description: `Generated ${completedLessons}/${totalLessons} individual lesson videos with Marp slides and assembled final course video`
      });
    },
    onError: (error: any) => {
      setIsGenerating(false);
      updateStepStatus(currentStep, 'error', 0);
      toast({
        title: "Generation Failed",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  const updateStepStatus = (stepIndex: number, status: GenerationStep['status'], progress: number, result?: any) => {
    setSteps(prev => prev.map((step, index) => 
      index === stepIndex 
        ? { ...step, status, progress, result }
        : step
    ));
  };

  const simulateGeneration = async () => {
    setIsGenerating(true);
    
    // Step 1: Generate Slides
    setCurrentStep(0);
    updateStepStatus(0, 'processing', 10);
    
    try {
      // Simulate slide generation with Marp
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      for (let i = 30; i <= 100; i += 20) {
        updateStepStatus(0, 'processing', i);
        await new Promise(resolve => setTimeout(resolve, 500));
      }
      
      updateStepStatus(0, 'completed', 100, { 
        slides: scriptData.segments?.length || 1,
        format: 'HTML/PDF'
      });

      // Step 2: Generate Speech
      setCurrentStep(1);
      updateStepStatus(1, 'processing', 10);
      
      for (let i = 30; i <= 100; i += 20) {
        updateStepStatus(1, 'processing', i);
        await new Promise(resolve => setTimeout(resolve, 800));
      }
      
      updateStepStatus(1, 'completed', 100, {
        duration: '5:30',
        voice: voiceData.voiceId || 'Female Voice 1'
      });

      // Step 3: Create Avatar Video
      setCurrentStep(2);
      updateStepStatus(2, 'processing', 10);
      
      for (let i = 30; i <= 100; i += 15) {
        updateStepStatus(2, 'processing', i);
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
      
      updateStepStatus(2, 'completed', 100, {
        resolution: '1080p',
        avatar: avatarData.selectedAvatar || 'Default Avatar'
      });

      // Step 4: Finalize
      setCurrentStep(3);
      updateStepStatus(3, 'processing', 10);
      
      for (let i = 40; i <= 100; i += 30) {
        updateStepStatus(3, 'processing', i);
        await new Promise(resolve => setTimeout(resolve, 600));
      }
      
      updateStepStatus(3, 'completed', 100, {
        courseId: Date.now().toString(),
        videoUrl: '/demo-avatar-course.mp4'
      });

      // Set preview data
      setPreviewData({
        courseId: Date.now().toString(),
        title: courseData.title,
        videoUrl: '/demo-avatar-course.mp4',
        slides: scriptData.segments?.length || 1,
        duration: '5:30',
        avatar: avatarData.selectedAvatar || 'Default Avatar',
        voice: voiceData.voiceId || 'Female Voice 1'
      });

      setIsGenerating(false);
      
      toast({
        title: "Course Generated Successfully",
        description: "Your avatar course is ready for preview"
      });

    } catch (error) {
      setIsGenerating(false);
      updateStepStatus(currentStep, 'error', 0);
      toast({
        title: "Generation Failed",
        description: "An error occurred during course generation",
        variant: "destructive"
      });
    }
  };

  const getStepIcon = (step: GenerationStep) => {
    switch (step.status) {
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'processing':
        return <Loader2 className="h-5 w-5 animate-spin text-blue-500" />;
      case 'error':
        return <AlertCircle className="h-5 w-5 text-red-500" />;
      default:
        return <div className="h-5 w-5 rounded-full border-2 border-gray-300" />;
    }
  };

  const handleSubmit = () => {
    onSubmit({
      ...previewData,
      courseData,
      avatarData,
      scriptData,
      voiceData
    });
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-semibold">Course Preview & Generation</h2>
          <p className="text-muted-foreground">Generate and preview your avatar course</p>
        </div>
        <Badge variant="outline" className="flex items-center gap-1">
          <Video className="h-3 w-3" />
          Avatar Course
        </Badge>
      </div>

      {!previewData && !isGenerating && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Course Configuration
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <h4 className="font-medium">Course Details</h4>
                <p className="text-sm text-muted-foreground">
                  <strong>Title:</strong> {courseData.title}
                </p>
                <p className="text-sm text-muted-foreground">
                  <strong>Category:</strong> {courseData.category}
                </p>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium">Avatar & Voice</h4>
                <p className="text-sm text-muted-foreground">
                  <strong>Avatar:</strong> {avatarData.selectedAvatar || 'Default Avatar'}
                </p>
                <p className="text-sm text-muted-foreground">
                  <strong>Voice:</strong> {voiceData.voiceId || 'Female Voice 1'}
                </p>
              </div>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium">Script Content</h4>
              <p className="text-sm text-muted-foreground">
                {scriptData.segments?.length || 0} segments prepared for generation
              </p>
            </div>
            
            <Button 
              onClick={() => generateCourseMutation.mutate()}
              className="w-full"
              size="lg"
              disabled={generateCourseMutation.isPending}
            >
              <Play className="h-4 w-4 mr-2" />
              Generate Avatar Course
            </Button>
          </CardContent>
        </Card>
      )}

      {isGenerating && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Loader2 className="h-5 w-5 animate-spin" />
              Generating Your Avatar Course
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {steps.map((step, index) => (
              <div key={step.id} className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    {getStepIcon(step)}
                    <div>
                      <h4 className="font-medium">{step.title}</h4>
                      <p className="text-sm text-muted-foreground">{step.description}</p>
                    </div>
                  </div>
                  <Badge variant={step.status === 'completed' ? 'default' : 'secondary'}>
                    {step.status === 'processing' ? `${step.progress}%` : step.status}
                  </Badge>
                </div>
                <Progress value={step.progress} className="w-full" />
                {step.result && (
                  <div className="ml-8 text-sm text-muted-foreground">
                    {step.id === 'slides' && `Generated ${step.result.slides} slides in ${step.result.format}`}
                    {step.id === 'speech' && `Audio duration: ${step.result.duration} with ${step.result.voice}`}
                    {step.id === 'avatar' && `${step.result.resolution} video with ${step.result.avatar}`}
                    {step.id === 'finalize' && `Course ready with ID: ${step.result.courseId}`}
                  </div>
                )}
              </div>
            ))}
          </CardContent>
        </Card>
      )}

      {previewData && (
        <Tabs defaultValue="preview" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="preview">Course Preview</TabsTrigger>
            <TabsTrigger value="details">Generation Details</TabsTrigger>
            <TabsTrigger value="download">Download Options</TabsTrigger>
          </TabsList>

          <TabsContent value="preview" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Eye className="h-5 w-5" />
                  Course Preview
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="aspect-video bg-gray-100 rounded-lg flex items-center justify-center">
                  <div className="text-center">
                    <Video className="h-12 w-12 mx-auto mb-2 text-gray-400" />
                    <p className="text-gray-600">Avatar Course Video</p>
                    <p className="text-sm text-gray-500">{previewData.title}</p>
                    <Button className="mt-4" variant="outline">
                      <Play className="h-4 w-4 mr-2" />
                      Play Preview
                    </Button>
                  </div>
                </div>
                
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center">
                    <FileText className="h-6 w-6 mx-auto mb-1 text-blue-500" />
                    <p className="text-sm font-medium">{previewData.slides} Slides</p>
                  </div>
                  <div className="text-center">
                    <Volume2 className="h-6 w-6 mx-auto mb-1 text-green-500" />
                    <p className="text-sm font-medium">{previewData.duration}</p>
                  </div>
                  <div className="text-center">
                    <User className="h-6 w-6 mx-auto mb-1 text-purple-500" />
                    <p className="text-sm font-medium">{previewData.avatar}</p>
                  </div>
                  <div className="text-center">
                    <Video className="h-6 w-6 mx-auto mb-1 text-orange-500" />
                    <p className="text-sm font-medium">1080p HD</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="details" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Generation Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {steps.map((step) => (
                  <div key={step.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center gap-3">
                      {getStepIcon(step)}
                      <div>
                        <h4 className="font-medium">{step.title}</h4>
                        <p className="text-sm text-muted-foreground">{step.description}</p>
                      </div>
                    </div>
                    <Badge variant="default">Completed</Badge>
                  </div>
                ))}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="download" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Download className="h-5 w-5" />
                  Download Options
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Button variant="outline" className="h-16 flex flex-col">
                    <Video className="h-5 w-5 mb-1" />
                    Download Video (MP4)
                  </Button>
                  <Button variant="outline" className="h-16 flex flex-col">
                    <FileText className="h-5 w-5 mb-1" />
                    Download Slides (PDF)
                  </Button>
                  <Button variant="outline" className="h-16 flex flex-col">
                    <Volume2 className="h-5 w-5 mb-1" />
                    Download Audio (MP3)
                  </Button>
                  <Button variant="outline" className="h-16 flex flex-col">
                    <Download className="h-5 w-5 mb-1" />
                    Download All Files
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      )}

      <div className="flex justify-between">
        <Button onClick={onBack} variant="outline">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </Button>
        
        {previewData && (
          <Button onClick={handleSubmit} className="flex items-center gap-2">
            Continue to Publish
            <ArrowRight className="h-4 w-4" />
          </Button>
        )}
      </div>
    </div>
  );
}