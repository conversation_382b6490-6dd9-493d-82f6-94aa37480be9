import { Notification } from '../../types/notifications';
import { Badge } from '../../components/ui/badge';
import { formatDistanceToNow } from 'date-fns';
import { Bell, Calendar, FileText, MessageSquare, Users, Info, AlertCircle, CheckCircle, HelpCircle, GitPullRequest, BookOpen, Star } from 'lucide-react';
import { cn } from '../../lib/utils';

interface NotificationItemProps {
  notification: Notification;
  onMarkAsRead: (id: number) => void;
  onDelete: (id: number) => void;
}

// Map notification types to appropriate icons and colors
const notificationTypeConfig = {
  'system': { icon: Bell, color: 'text-blue-500' },
  'update': { icon: Info, color: 'text-indigo-500' },
  'course': { icon: BookOpen, color: 'text-emerald-500' },
  'message': { icon: MessageSquare, color: 'text-violet-500' },
  'meeting': { icon: Calendar, color: 'text-orange-500' },
  'collaboration': { icon: Users, color: 'text-pink-500' },
  'review': { icon: Star, color: 'text-amber-500' },
  'alert': { icon: AlertCircle, color: 'text-red-500' },
  'success': { icon: CheckCircle, color: 'text-green-500' },
  'team': { icon: Users, color: 'text-cyan-500' },
  'documentation': { icon: FileText, color: 'text-yellow-500' },
  'approval': { icon: GitPullRequest, color: 'text-purple-500' },
  'default': { icon: HelpCircle, color: 'text-gray-500' }
};

export default function NotificationItem({ notification, onMarkAsRead, onDelete }: NotificationItemProps) {
  // Extract notification type and find appropriate icon config
  const typeName = notification.type || 'default';
  const typeConfig = notificationTypeConfig[typeName as keyof typeof notificationTypeConfig] || notificationTypeConfig.default;
  const Icon = notification.iconOverride ? Bell : typeConfig.icon;
  
  // Format date for display
  const formattedDate = notification.createdAt 
    ? formatDistanceToNow(new Date(notification.createdAt), { addSuffix: true })
    : 'Just now';
  
  return (
    <div 
      className={cn(
        "relative flex p-4 border-b transition-colors hover:bg-muted/40",
        !notification.isRead && "bg-muted/20"
      )}
    >
      <div className="mr-4 mt-1">
        <Icon className={cn("h-5 w-5", typeConfig.color)} />
      </div>
      
      <div className="flex-1">
        <div className="flex items-start justify-between mb-1">
          <div className="font-medium">{notification.title}</div>
          <div className="flex gap-2 items-center">
            {!notification.isRead && (
              <Badge variant="secondary" className="text-xs">
                New
              </Badge>
            )}
            <span className="text-xs text-muted-foreground">{formattedDate}</span>
          </div>
        </div>
        
        <p className="text-sm text-muted-foreground mb-2">
          {notification.message}
        </p>
        
        {notification.sourceName && (
          <div className="text-xs text-muted-foreground mb-2">
            From: {notification.sourceName}
          </div>
        )}
        
        <div className="flex gap-2 mt-2">
          {!notification.isRead && (
            <button
              onClick={() => onMarkAsRead(notification.id)}
              className="text-xs text-blue-500 hover:text-blue-700"
            >
              Mark as read
            </button>
          )}
          
          {notification.linkUrl && (
            <a
              href={notification.linkUrl}
              className="text-xs text-blue-500 hover:text-blue-700"
              onClick={() => !notification.isRead && onMarkAsRead(notification.id)}
            >
              View details
            </a>
          )}
          
          <button
            onClick={() => onDelete(notification.id)}
            className="text-xs text-red-500 hover:text-red-700 ml-auto"
          >
            Delete
          </button>
        </div>
      </div>
    </div>
  );
}
