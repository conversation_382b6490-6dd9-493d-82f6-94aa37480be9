import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Trophy, Star, Zap, Award } from 'lucide-react';
import { cn } from '@/lib/utils';

interface Achievement {
  id: string;
  type: 'xp' | 'badge' | 'level' | 'milestone';
  title: string;
  description: string;
  value?: number;
  icon?: string;
  rarity?: string;
}

interface AchievementNotificationProps {
  achievements: Achievement[];
  onClose?: (achievementId: string) => void;
  duration?: number;
}

export function AchievementNotification({ 
  achievements, 
  onClose, 
  duration = 5000 
}: AchievementNotificationProps) {
  const [visibleAchievements, setVisibleAchievements] = useState<Achievement[]>([]);

  useEffect(() => {
    if (achievements.length > 0) {
      // Show achievements one by one with slight delay
      achievements.forEach((achievement, index) => {
        setTimeout(() => {
          setVisibleAchievements(prev => [...prev, achievement]);
          
          // Auto-hide after duration
          setTimeout(() => {
            setVisibleAchievements(prev => prev.filter(a => a.id !== achievement.id));
            onClose?.(achievement.id);
          }, duration);
        }, index * 500);
      });
    }
  }, [achievements, duration, onClose]);

  const getAchievementIcon = (achievement: Achievement) => {
    switch (achievement.type) {
      case 'xp':
        return <Zap className="h-5 w-5 text-yellow-500" />;
      case 'badge':
        return <Award className="h-5 w-5 text-purple-500" />;
      case 'level':
        return <Star className="h-5 w-5 text-blue-500" />;
      case 'milestone':
        return <Trophy className="h-5 w-5 text-gold-500" />;
      default:
        return achievement.icon ? (
          <span className="text-lg">{achievement.icon}</span>
        ) : (
          <Star className="h-5 w-5 text-gray-500" />
        );
    }
  };

  const getAchievementColor = (achievement: Achievement) => {
    switch (achievement.type) {
      case 'xp':
        return 'border-yellow-200 bg-yellow-50';
      case 'badge':
        return 'border-purple-200 bg-purple-50';
      case 'level':
        return 'border-blue-200 bg-blue-50';
      case 'milestone':
        return 'border-orange-200 bg-orange-50';
      default:
        return 'border-gray-200 bg-gray-50';
    }
  };

  const getRarityColor = (rarity?: string) => {
    switch (rarity) {
      case 'legendary':
        return 'bg-orange-500 text-white';
      case 'epic':
        return 'bg-purple-500 text-white';
      case 'rare':
        return 'bg-blue-500 text-white';
      case 'common':
        return 'bg-gray-500 text-white';
      default:
        return 'bg-green-500 text-white';
    }
  };

  return (
    <div className="fixed top-4 right-4 z-50 space-y-2 max-w-sm">
      <AnimatePresence>
        {visibleAchievements.map((achievement) => (
          <motion.div
            key={achievement.id}
            initial={{ opacity: 0, x: 300, scale: 0.8 }}
            animate={{ opacity: 1, x: 0, scale: 1 }}
            exit={{ opacity: 0, x: 300, scale: 0.8 }}
            transition={{ duration: 0.3, ease: "easeOut" }}
          >
            <Card className={cn(
              'border-2 shadow-lg backdrop-blur-sm',
              getAchievementColor(achievement)
            )}>
              <CardContent className="p-4">
                <div className="flex items-start gap-3">
                  <motion.div
                    initial={{ rotate: -180, scale: 0 }}
                    animate={{ rotate: 0, scale: 1 }}
                    transition={{ delay: 0.2, duration: 0.4 }}
                    className="flex-shrink-0"
                  >
                    {getAchievementIcon(achievement)}
                  </motion.div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-1">
                      <h4 className="font-semibold text-sm text-gray-900 truncate">
                        {achievement.title}
                      </h4>
                      {achievement.rarity && (
                        <Badge
                          variant="secondary"
                          className={cn('text-xs ml-2', getRarityColor(achievement.rarity))}
                        >
                          {achievement.rarity}
                        </Badge>
                      )}
                    </div>
                    
                    <p className="text-xs text-gray-600 leading-relaxed">
                      {achievement.description}
                    </p>
                    
                    {achievement.value && (
                      <div className="mt-2 flex items-center gap-1">
                        <Zap className="h-3 w-3 text-yellow-500" />
                        <span className="text-xs font-medium text-yellow-700">
                          +{achievement.value} XP
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </AnimatePresence>
    </div>
  );
}