/**
 * This module handles subscription plan-based feature restrictions
 */
import { User } from "@/types";

interface PlanLimits {
  maxCourses: number;
  maxVideos: number;
  maxStorage: number; // in GB
  aiCreditsPerMonth: number;
  canDownloadScripts: boolean;
  canUseMicroLearning: boolean;
  canCustomizeBranding: boolean;
  supportedLanguages: number;
  hasTeamCollaboration: boolean;
  hasAdvancedAnalytics: boolean;
}

// Define plan tiers and their limits
const PLAN_LIMITS = {
  free: {
    maxCourses: 1,
    maxVideos: 5,
    maxStorage: 1,
    aiCreditsPerMonth: 100,
    canDownloadScripts: false,
    canUseMicroLearning: false,
    canCustomizeBranding: false,
    supportedLanguages: 1,
    hasTeamCollaboration: false,
    hasAdvancedAnalytics: false
  } as PlanLimits,
  
  starter: {
    maxCourses: 5,
    maxVideos: 25,
    maxStorage: 5,
    aiCreditsPerMonth: 500,
    canDownloadScripts: true,
    canUseMicroLearning: true,
    canCustomizeBranding: false,
    supportedLanguages: 5,
    hasTeamCollaboration: false,
    hasAdvancedAnalytics: false
  } as PlanLimits,
  
  pro: {
    maxCourses: 15,
    maxVideos: 100,
    maxStorage: 15,
    aiCreditsPerMonth: 1500,
    canDownloadScripts: true,
    canUseMicroLearning: true,
    canCustomizeBranding: true,
    supportedLanguages: 10,
    hasTeamCollaboration: true,
    hasAdvancedAnalytics: false
  } as PlanLimits,
  
  business: {
    maxCourses: 50,
    maxVideos: 500,
    maxStorage: 100,
    aiCreditsPerMonth: 5000,
    canDownloadScripts: true,
    canUseMicroLearning: true,
    canCustomizeBranding: true,
    supportedLanguages: 25,
    hasTeamCollaboration: true,
    hasAdvancedAnalytics: true
  } as PlanLimits,
  
  enterprise: {
    maxCourses: 1000,
    maxVideos: 10000,
    maxStorage: 1000,
    aiCreditsPerMonth: 50000,
    canDownloadScripts: true,
    canUseMicroLearning: true,
    canCustomizeBranding: true,
    supportedLanguages: 25,
    hasTeamCollaboration: true,
    hasAdvancedAnalytics: true
  } as PlanLimits
};

/**
 * Gets the limits for a specific user's plan
 */
export function getPlanLimits(user: User | null): PlanLimits {
  if (!user || !user.plan || !(user.plan in PLAN_LIMITS)) {
    return PLAN_LIMITS.free;
  }
  
  return PLAN_LIMITS[user.plan as keyof typeof PLAN_LIMITS];
}

/**
 * Check if user has reached their course limit
 */
export function hasReachedCourseLimit(user: User | null, currentCourseCount: number): boolean {
  const limits = getPlanLimits(user);
  return currentCourseCount >= limits.maxCourses;
}

/**
 * Check if user can download scripts
 */
export function canDownloadScripts(user: User | null): boolean {
  const limits = getPlanLimits(user);
  return limits.canDownloadScripts;
}

/**
 * Get max number of courses for the user's plan
 */
export function getMaxCourses(user: User | null): number | null {
  const limits = getPlanLimits(user);
  return limits.maxCourses;
}

/**
 * Check if the user has access to micro-learning mode
 */
export function canUseMicroLearning(user: User | null): boolean {
  const limits = getPlanLimits(user);
  return limits.canUseMicroLearning;
}

/**
 * Check if user has access to team collaboration features
 */
export function hasTeamCollaboration(user: User | null): boolean {
  const limits = getPlanLimits(user);
  return limits.hasTeamCollaboration;
}

/**
 * Get the number of supported languages for TTS
 */
export function getSupportedLanguageCount(user: User | null): number | null {
  const limits = getPlanLimits(user);
  return limits.supportedLanguages;
}

/**
 * Get descriptive text for plan course limits
 */
export function getCourseLimitText(user: User | null): string {
  const limits = getPlanLimits(user);
  if (limits.maxCourses === 1) {
    return "Limited to 1 course";
  } else if (limits.maxCourses < 1000) {
    return `Up to ${limits.maxCourses} courses`;
  } else {
    return "Unlimited courses";
  }
}

/**
 * Returns a list of premium features unavailable to free users
 */
export function getPremiumFeatures(): string[] {
  return [
    "Create unlimited courses",
    "Download course scripts as PDF",
    "Use micro-learning mode",
    "Access to 25+ TTS languages",
    "Team collaboration features",
    "Advanced course analytics",
    "Customizable branding",
    "Priority support"
  ];
}