#!/usr/bin/env python3
"""
AI Course Builder - Main Application Server
Coordinates between React frontend and Modal A100 GPU backend
"""

import os
import sys
from pathlib import Path
from fastapi import FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse, JSONResponse
import httpx
import asyncio
from contextlib import asynccontextmanager
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Configuration
MODAL_API_URL = os.getenv('MODAL_API_URL', 'http://localhost:8001')
FRONTEND_BUILD_PATH = Path(__file__).parent.parent / "frontend" / "build"
API_TIMEOUT = 300  # 5 minutes for GPU operations

class ModalClient:
    def __init__(self, base_url: str):
        self.base_url = base_url.rstrip('/')
        self.client = httpx.AsyncClient(timeout=API_TIMEOUT)
    
    async def health_check(self):
        """Check Modal GPU backend health"""
        try:
            response = await self.client.get(f"{self.base_url}/health")
            return response.json()
        except Exception as e:
            logger.error(f"Modal health check failed: {e}")
            return {"status": "error", "error": str(e)}
    
    async def generate_image(self, data):
        """Forward image generation to Modal backend"""
        try:
            response = await self.client.post(f"{self.base_url}/gen_image", json=data)
            return response.json()
        except Exception as e:
            logger.error(f"Image generation failed: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    async def generate_video(self, data):
        """Forward video generation to Modal backend"""
        try:
            response = await self.client.post(f"{self.base_url}/gen_video", json=data)
            return response.json()
        except Exception as e:
            logger.error(f"Video generation failed: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    async def generate_tts_chatter(self, data):
        """Forward Chatterbox TTS to Modal backend"""
        try:
            response = await self.client.post(f"{self.base_url}/tts_chatter", json=data)
            return response.json()
        except Exception as e:
            logger.error(f"Chatterbox TTS failed: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    async def generate_tts_coqui(self, data):
        """Forward Coqui TTS to Modal backend"""
        try:
            response = await self.client.post(f"{self.base_url}/tts_coqui", json=data)
            return response.json()
        except Exception as e:
            logger.error(f"Coqui TTS failed: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    async def generate_slides(self, data):
        """Forward slide generation to Modal backend"""
        try:
            response = await self.client.post(f"{self.base_url}/slides", json=data)
            return response.json()
        except Exception as e:
            logger.error(f"Slide generation failed: {e}")
            raise HTTPException(status_code=500, detail=str(e))

# Initialize Modal client
modal_client = ModalClient(MODAL_API_URL)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan management"""
    logger.info("Starting AI Course Builder server...")
    
    # Check Modal backend on startup
    health = await modal_client.health_check()
    if health.get("status") == "online":
        logger.info("Modal A100 GPU backend is online")
    else:
        logger.warning("Modal A100 GPU backend is not available - using fallback mode")
    
    yield
    
    # Cleanup
    await modal_client.client.aclose()
    logger.info("Server shutdown complete")

# Create FastAPI app
app = FastAPI(
    title="AI Course Builder API",
    description="GPU-accelerated content creation platform",
    version="1.0.0",
    lifespan=lifespan
)

# CORS middleware with restricted origins
allowed_origins = os.getenv("ALLOWED_ORIGINS", "http://localhost:3000,http://localhost:5000").split(",")
app.add_middleware(
    CORSMiddleware,
    allow_origins=allowed_origins,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["Origin", "X-Requested-With", "Content-Type", "Accept", "Authorization", "X-CSRF-Token"],
)

# API Routes
@app.get("/api/health")
async def health():
    """API health check"""
    return {"status": "online", "service": "AI Course Builder API"}

@app.get("/api/modal/health")
async def modal_health():
    """Check Modal GPU backend health"""
    return await modal_client.health_check()

@app.post("/api/gen_image")
async def api_generate_image(request: Request):
    """Generate images using Stable Diffusion XL"""
    data = await request.json()
    return await modal_client.generate_image(data)

@app.post("/api/gen_video")
async def api_generate_video(request: Request):
    """Generate videos using SadTalker"""
    data = await request.json()
    return await modal_client.generate_video(data)

@app.post("/api/tts_chatter")
async def api_tts_chatter(request: Request):
    """Generate speech using Chatterbox TTS"""
    data = await request.json()
    return await modal_client.generate_tts_chatter(data)

@app.post("/api/tts_coqui")
async def api_tts_coqui(request: Request):
    """Generate speech using Coqui TTS"""
    data = await request.json()
    return await modal_client.generate_tts_coqui(data)

@app.post("/api/slides")
async def api_generate_slides(request: Request):
    """Generate slides using Marp"""
    data = await request.json()
    return await modal_client.generate_slides(data)

# Error handlers
@app.exception_handler(404)
async def not_found_handler(request: Request, exc):
    """Handle 404 errors by serving React app"""
    if request.url.path.startswith("/api/"):
        return JSONResponse(
            status_code=404,
            content={"detail": "API endpoint not found"}
        )
    return FileResponse(FRONTEND_BUILD_PATH / "index.html")

@app.exception_handler(500)
async def internal_error_handler(request: Request, exc):
    """Handle internal server errors"""
    logger.error(f"Internal server error: {exc}")
    return JSONResponse(
        status_code=500,
        content={"detail": "Internal server error"}
    )

# Serve React frontend (if built)
if FRONTEND_BUILD_PATH.exists():
    app.mount("/static", StaticFiles(directory=FRONTEND_BUILD_PATH / "static"), name="static")
    
    @app.get("/")
    async def serve_frontend():
        """Serve React frontend"""
        return FileResponse(FRONTEND_BUILD_PATH / "index.html")
    
    @app.get("/{path:path}")
    async def serve_frontend_routes(path: str):
        """Serve React frontend for all non-API routes"""
        if path.startswith("api/"):
            raise HTTPException(status_code=404, detail="API endpoint not found")
        return FileResponse(FRONTEND_BUILD_PATH / "index.html")
else:
    @app.get("/")
    async def development_mode():
        """Development mode message"""
        return {
            "message": "AI Course Builder API is running in development mode",
            "frontend": "Please run 'npm start' in the frontend directory",
            "api_docs": "/docs",
            "health": "/api/health"
        }

if __name__ == "__main__":
    import uvicorn
    
    port = int(os.getenv("PORT", 8000))
    host = os.getenv("HOST", "0.0.0.0")
    
    logger.info(f"Starting server on {host}:{port}")
    
    uvicorn.run(
        "server:app",
        host=host,
        port=port,
        reload=True if os.getenv("NODE_ENV") != "production" else False,
        log_level="info"
    )