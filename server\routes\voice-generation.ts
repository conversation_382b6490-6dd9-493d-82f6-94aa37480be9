import express, { Request, Response } from 'express';
import { voiceServiceManager } from '../services/voice-service-manager';
import path from 'path';
import fs from 'fs';

const router = express.Router();

// Get all available voices from all providers
router.get('/voices', async (req: Request, res: Response) => {
  try {
    const voices = await voiceServiceManager.getAllVoices();
    const serviceStatus = await voiceServiceManager.getServiceStatus();
    
    res.json({
      voices,
      serviceStatus,
      total: voices.length
    });
  } catch (error: any) {
    console.error('Error fetching voices:', error);
    res.status(500).json({
      message: 'Failed to fetch available voices',
      error: error.message
    });
  }
});

// Generate voice preview for voice selection
router.post('/preview', async (req: Request, res: Response) => {
  try {
    const { voiceId } = req.body;
    
    if (!voiceId) {
      return res.status(400).json({ message: 'Voice ID is required' });
    }

    const previewBuffer = await voiceServiceManager.generatePreview(voiceId);
    
    if (!previewBuffer) {
      return res.status(404).json({ message: 'Preview not available for this voice' });
    }

    res.set({
      'Content-Type': 'audio/wav',
      'Content-Length': previewBuffer.length,
      'Cache-Control': 'public, max-age=3600'
    });
    
    res.send(previewBuffer);
  } catch (error: any) {
    console.error('Error generating preview:', error);
    res.status(500).json({
      message: 'Failed to generate voice preview',
      error: error.message
    });
  }
});

// Generate speech from text (single generation)
router.post('/generate', async (req: any, res: Response) => {
  try {
    if (!req.user || !req.user.id) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const { text, voiceId, options = {} } = req.body;
    
    if (!text || !voiceId) {
      return res.status(400).json({ 
        message: 'Text and voice ID are required' 
      });
    }

    // Create output directory for user's audio files
    const userAudioDir = path.join(process.cwd(), 'uploads', 'audio', req.user.id.toString());
    if (!fs.existsSync(userAudioDir)) {
      fs.mkdirSync(userAudioDir, { recursive: true });
    }

    // Generate unique filename
    const timestamp = Date.now();
    const filename = `voice_${timestamp}.wav`;
    const outputPath = path.join(userAudioDir, filename);

    // Generate speech using voice service manager
    const result = await voiceServiceManager.generateSpeech(
      text,
      voiceId,
      outputPath,
      options
    );

    if (!result.success) {
      return res.status(500).json({
        message: 'Voice generation failed',
        error: result.error,
        provider: result.provider
      });
    }

    // Create public URL for the generated audio
    const baseUrl = `${req.protocol}://${req.get('host')}`;
    const audioUrl = `${baseUrl}/uploads/audio/${req.user.id}/${filename}`;

    res.json({
      success: true,
      audioUrl,
      provider: result.provider,
      filename,
      message: `Audio generated successfully using ${result.provider}`
    });

  } catch (error: any) {
    console.error('Error in voice generation:', error);
    res.status(500).json({
      message: 'Voice generation failed',
      error: error.message
    });
  }
});

// Batch generate voices for course scripts
router.post('/generate-batch', async (req: any, res: Response) => {
  try {
    if (!req.user || !req.user.id) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const { scripts, voiceId, options = {} } = req.body;
    
    if (!scripts || !Array.isArray(scripts) || scripts.length === 0) {
      return res.status(400).json({ 
        message: 'Scripts array is required' 
      });
    }

    if (!voiceId) {
      return res.status(400).json({ 
        message: 'Voice ID is required' 
      });
    }

    // Create output directory
    const userAudioDir = path.join(process.cwd(), 'uploads', 'audio', req.user.id.toString());
    if (!fs.existsSync(userAudioDir)) {
      fs.mkdirSync(userAudioDir, { recursive: true });
    }

    const results = [];
    const timestamp = Date.now();

    // Process each script
    for (let i = 0; i < scripts.length; i++) {
      const script = scripts[i];
      const filename = `${script.moduleId}_${script.lessonId}_${timestamp}.wav`;
      const outputPath = path.join(userAudioDir, filename);

      try {
        const result = await voiceServiceManager.generateSpeech(
          script.text,
          voiceId,
          outputPath,
          options
        );

        const baseUrl = `${req.protocol}://${req.get('host')}`;
        const audioUrl = `${baseUrl}/uploads/audio/${req.user.id}/${filename}`;

        results.push({
          moduleId: script.moduleId,
          lessonId: script.lessonId,
          success: result.success,
          audioUrl: result.success ? audioUrl : null,
          provider: result.provider,
          error: result.error
        });

      } catch (error: any) {
        results.push({
          moduleId: script.moduleId,
          lessonId: script.lessonId,
          success: false,
          audioUrl: null,
          provider: 'error',
          error: error.message
        });
      }

      // Add small delay between requests to avoid overwhelming services
      if (i < scripts.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    const successCount = results.filter(r => r.success).length;
    const totalCount = results.length;

    res.json({
      success: successCount > 0,
      results,
      summary: {
        total: totalCount,
        successful: successCount,
        failed: totalCount - successCount
      },
      message: `Generated ${successCount}/${totalCount} audio files successfully`
    });

  } catch (error: any) {
    console.error('Error in batch voice generation:', error);
    res.status(500).json({
      message: 'Batch voice generation failed',
      error: error.message
    });
  }
});

// Get service status and configuration
router.get('/status', async (req: Request, res: Response) => {
  try {
    const serviceStatus = await voiceServiceManager.getServiceStatus();
    
    res.json({
      services: serviceStatus,
      availableProviders: Object.keys(serviceStatus).filter(
        key => serviceStatus[key]
      ),
      primaryProvider: 'coqui',
      fallbackProvider: 'kokoro',
      premiumProvider: 'elevenlabs'
    });
  } catch (error: any) {
    console.error('Error getting service status:', error);
    res.status(500).json({
      message: 'Failed to get service status',
      error: error.message
    });
  }
});

export default router;