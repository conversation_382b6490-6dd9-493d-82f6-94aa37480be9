import { useState, useEffect } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { HoverCard, HoverCardContent, HoverCardTrigger } from '@/components/ui/hover-card';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Users, CheckCircle, Clock } from 'lucide-react';

interface Collaborator {
  id: number;
  name: string;
  avatarUrl?: string;
  role: string;
  status: 'active' | 'idle' | 'offline';
  lastActive?: Date;
}

interface CollaborationStatusBadgeProps {
  courseId: number;
  compact?: boolean; // If true, show a more compact version
  showTooltip?: boolean; // If true, show tooltip on hover instead of hover card
}

export function CollaborationStatusBadge({ 
  courseId, 
  compact = false,
  showTooltip = false
}: CollaborationStatusBadgeProps) {
  const [activeCollaborators, setActiveCollaborators] = useState<Collaborator[]>([]);
  
  // Simulate fetching active collaborators
  useEffect(() => {
    // For demo purposes, we'll use a timer to simulate real-time updates
    const fetchActiveCollaborators = () => {
      // In a real app, this would be a WebSocket or API call
      const demoCollaborators: Collaborator[] = [
        {
          id: 1,
          name: 'Sarah Johnson',
          avatarUrl: '/avatars/sarah.jpg',
          role: 'editor',
          status: 'active',
          lastActive: new Date()
        },
        {
          id: 2,
          name: 'Michael Chen',
          role: 'viewer',
          status: Math.random() > 0.5 ? 'active' : 'idle',
          lastActive: new Date(Date.now() - 1000 * 60 * 5) // 5 minutes ago
        },
        {
          id: 3,
          name: 'Emma Wilson',
          avatarUrl: '/avatars/emma.jpg',
          role: 'editor',
          status: Math.random() > 0.7 ? 'active' : 'offline',
          lastActive: new Date(Date.now() - 1000 * 60 * 30) // 30 minutes ago
        }
      ];
      
      // Randomly select 1-3 collaborators
      const count = Math.floor(Math.random() * 3) + 1;
      const selectedCollaborators = demoCollaborators.slice(0, count);
      
      setActiveCollaborators(selectedCollaborators);
    };
    
    fetchActiveCollaborators();
    const interval = setInterval(fetchActiveCollaborators, 60000); // Update every minute
    
    return () => clearInterval(interval);
  }, [courseId]);
  
  // Format the time difference
  const getTimeAgo = (date?: Date) => {
    if (!date) return 'Unknown';
    
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
    
    if (diffInSeconds < 60) return 'Just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
    return `${Math.floor(diffInSeconds / 86400)}d ago`;
  };
  
  // Get status color
  const getStatusColor = (status: string) => {
    switch(status) {
      case 'active': return 'bg-green-500';
      case 'idle': return 'bg-yellow-500';
      case 'offline': return 'bg-gray-400';
      default: return 'bg-gray-400';
    }
  };
  
  if (activeCollaborators.length === 0) {
    return null;
  }
  
  const activeCount = activeCollaborators.filter(c => c.status === 'active').length;
  
  // Compact version just shows the count and avatars
  if (compact) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div className="flex items-center">
              <div className="flex -space-x-1 mr-1">
                {activeCollaborators.slice(0, 3).map(collaborator => (
                  <Avatar key={collaborator.id} className="h-6 w-6 border-2 border-white">
                    {collaborator.avatarUrl ? (
                      <AvatarImage src={collaborator.avatarUrl} alt={collaborator.name} />
                    ) : (
                      <AvatarFallback className="text-xs">
                        {collaborator.name.charAt(0)}
                      </AvatarFallback>
                    )}
                  </Avatar>
                ))}
                {activeCollaborators.length > 3 && (
                  <div className="h-6 w-6 rounded-full bg-primary/10 text-primary flex items-center justify-center text-xs border-2 border-white">
                    +{activeCollaborators.length - 3}
                  </div>
                )}
              </div>
            </div>
          </TooltipTrigger>
          <TooltipContent>
            <p>{activeCount} active collaborator{activeCount !== 1 ? 's' : ''}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }
  
  // Full version with hover card details
  if (showTooltip) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div className="flex items-center border rounded-full py-1 px-2 bg-primary/5 text-primary-foreground">
              <Users className="h-3.5 w-3.5 mr-1" />
              <span className="text-xs font-medium">{activeCollaborators.length}</span>
            </div>
          </TooltipTrigger>
          <TooltipContent>
            <div className="space-y-2 max-w-sm">
              <h4 className="text-sm font-medium">Active Collaborators</h4>
              <ul className="space-y-1">
                {activeCollaborators.map(collaborator => (
                  <li key={collaborator.id} className="text-xs flex items-center">
                    <div className={`h-1.5 w-1.5 rounded-full ${getStatusColor(collaborator.status)} mr-1`} />
                    {collaborator.name} ({collaborator.status})
                  </li>
                ))}
              </ul>
            </div>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }
  
  return (
    <HoverCard>
      <HoverCardTrigger asChild>
        <div className="flex items-center border rounded-full py-1 px-2 bg-primary/5 text-primary-foreground cursor-pointer">
          <Users className="h-3.5 w-3.5 mr-1" />
          <span className="text-xs font-medium">{activeCollaborators.length}</span>
        </div>
      </HoverCardTrigger>
      <HoverCardContent className="w-64 p-2">
        <div className="space-y-2">
          <h3 className="text-sm font-medium">Course Collaborators</h3>
          <div className="space-y-2">
            {activeCollaborators.map(collaborator => (
              <div key={collaborator.id} className="flex items-center justify-between">
                <div className="flex items-center">
                  <Avatar className="h-7 w-7 mr-2">
                    {collaborator.avatarUrl ? (
                      <AvatarImage src={collaborator.avatarUrl} alt={collaborator.name} />
                    ) : (
                      <AvatarFallback>{collaborator.name.charAt(0)}</AvatarFallback>
                    )}
                    <div className={`absolute bottom-0 right-0 h-2 w-2 rounded-full ${getStatusColor(collaborator.status)} border border-white`} />
                  </Avatar>
                  <div>
                    <p className="text-xs font-medium">{collaborator.name}</p>
                    <p className="text-xs text-muted-foreground">{collaborator.role}</p>
                  </div>
                </div>
                <div className="flex items-center text-xs text-muted-foreground">
                  {collaborator.status === 'active' ? (
                    <CheckCircle className="h-3 w-3 text-green-500 mr-1" />
                  ) : (
                    <Clock className="h-3 w-3 mr-1" />
                  )}
                  {collaborator.status === 'active' ? 'Now' : getTimeAgo(collaborator.lastActive)}
                </div>
              </div>
            ))}
          </div>
        </div>
      </HoverCardContent>
    </HoverCard>
  );
}