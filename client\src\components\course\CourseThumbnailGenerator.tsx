import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import { StabilityImageGenerator } from '@/components/ai/StabilityImageGenerator';

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

import {
  ImageIcon,
  Loader2,
  RefreshCw,
  Upload,
  Search,
  Sparkles,
  Plus,
  Palette,
  FileImage,
} from 'lucide-react';

interface CourseThumbnailGeneratorProps {
  courseId: number;
  courseTitle?: string;
  courseDescription?: string;
  initialThumbnailUrl?: string | null;
  onThumbnailUpdate?: (thumbnailUrl: string) => void;
}

export function CourseThumbnailGenerator({
  courseId,
  courseTitle = '',
  courseDescription = '',
  initialThumbnailUrl = null,
  onThumbnailUpdate
}: CourseThumbnailGeneratorProps) {
  const [activeTab, setActiveTab] = useState<string>('current');
  const [thumbnailUrl, setThumbnailUrl] = useState<string | null>(initialThumbnailUrl);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch media library
  const { data: mediaItems, isLoading: isLoadingMedia } = useQuery({
    queryKey: ['/api/media'],
    queryFn: async () => {
      const response = await apiRequest('GET', '/api/media');
      if (!response.ok) throw new Error('Failed to fetch media');
      return response.json();
    }
  });

  // Update course thumbnail mutation
  const updateThumbnailMutation = useMutation({
    mutationFn: async (thumbnailUrl: string) => {
      const response = await apiRequest('PATCH', `/api/courses/${courseId}`, {
        thumbnailUrl
      });
      if (!response.ok) throw new Error('Failed to update thumbnail');
      return response.json();
    },
    onSuccess: (data) => {
      setThumbnailUrl(data.thumbnailUrl);
      if (onThumbnailUpdate) {
        onThumbnailUpdate(data.thumbnailUrl);
      }
      toast({
        title: 'Thumbnail Updated',
        description: 'Course thumbnail has been updated successfully',
      });
      queryClient.invalidateQueries({ queryKey: ['/api/courses', courseId] });
    },
    onError: (error: Error) => {
      toast({
        variant: 'destructive',
        title: 'Update Failed',
        description: error.message || 'Failed to update thumbnail',
      });
    }
  });

  // Handle selecting a media item as thumbnail
  const handleSelectMediaAsThumbnail = (mediaUrl: string) => {
    updateThumbnailMutation.mutate(mediaUrl);
  };

  // Handle generated image from Stability AI
  const handleGeneratedImage = (imageData: any) => {
    if (imageData && imageData.imageUrl) {
      updateThumbnailMutation.mutate(imageData.imageUrl);
    }
  };

  // Generate prompt for the thumbnail
  const generateThumbnailPrompt = () => {
    let prompt = '';
    
    if (courseTitle) {
      prompt += `Course thumbnail for "${courseTitle}"`;
      
      if (courseDescription) {
        // Extract key terms from description (simplified approach)
        const descriptionWords = courseDescription.split(' ');
        const keyTerms = descriptionWords
          .filter(word => word.length > 4) // Only include significant words
          .slice(0, 5) // Limit number of terms
          .join(', ');
          
        if (keyTerms) {
          prompt += `, featuring ${keyTerms}`;
        }
      }
      
      prompt += '. Professional, clean design, suitable for an educational platform.';
    } else {
      prompt = 'Professional course thumbnail with abstract educational design';
    }
    
    return prompt;
  };

  // Filter image media items
  const imageMediaItems = mediaItems?.filter(
    (item: any) => item.type === 'image'
  ) || [];

  return (
    <div className="space-y-4">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid grid-cols-3">
          <TabsTrigger value="current">
            <ImageIcon className="h-4 w-4 mr-2" />
            Current
          </TabsTrigger>
          <TabsTrigger value="ai">
            <Sparkles className="h-4 w-4 mr-2" />
            AI Generate
          </TabsTrigger>
          <TabsTrigger value="library">
            <FileImage className="h-4 w-4 mr-2" />
            Media Library
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="current" className="pt-4">
          <Card>
            <CardHeader>
              <CardTitle>Current Thumbnail</CardTitle>
              <CardDescription>
                The current thumbnail for your course
              </CardDescription>
            </CardHeader>
            <CardContent>
              {thumbnailUrl ? (
                <div className="relative aspect-video rounded-md overflow-hidden border bg-muted/20">
                  <img 
                    src={thumbnailUrl} 
                    alt="Course Thumbnail" 
                    className="w-full h-full object-cover"
                  />
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center h-48 bg-muted/20 rounded-md border border-dashed">
                  <ImageIcon className="h-12 w-12 text-muted-foreground/60 mb-2" />
                  <p className="text-sm text-muted-foreground">No thumbnail set</p>
                </div>
              )}
            </CardContent>
            <CardFooter className="flex justify-between">
              <div className="text-xs text-muted-foreground">
                Recommended size: 1280×720 (16:9 aspect ratio)
              </div>
              <StabilityImageGenerator
                buttonLabel="Generate New"
                variant="outline"
                contextPrompt={generateThumbnailPrompt()}
                courseId={courseId}
                onImageGenerated={handleGeneratedImage}
                placeholder="Describe your ideal course thumbnail..."
              />
            </CardFooter>
          </Card>
        </TabsContent>
        
        <TabsContent value="ai" className="pt-4">
          <Card>
            <CardHeader>
              <CardTitle>AI-Generated Thumbnail</CardTitle>
              <CardDescription>
                Create a professional thumbnail with Stability AI
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className="text-sm">
                  Generate a custom thumbnail using AI that matches your course content and style preferences.
                </p>
                <div className="bg-muted/30 rounded-md p-4">
                  <h4 className="text-sm font-medium mb-2">Suggested Prompt:</h4>
                  <div className="bg-background rounded border p-3 text-sm">
                    <p>{generateThumbnailPrompt()}</p>
                  </div>
                </div>
                <div className="flex justify-center mt-4">
                  <StabilityImageGenerator
                    buttonLabel="Generate Thumbnail"
                    variant="default"
                    size="lg"
                    contextPrompt={generateThumbnailPrompt()}
                    courseId={courseId}
                    onImageGenerated={handleGeneratedImage}
                    placeholder="Describe your ideal course thumbnail..."
                    autoOpen={activeTab === 'ai'}
                  />
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <div className="text-xs text-muted-foreground">
                This will use 5 AI credits from your account
              </div>
            </CardFooter>
          </Card>
        </TabsContent>
        
        <TabsContent value="library" className="pt-4">
          <Card>
            <CardHeader>
              <CardTitle>Media Library</CardTitle>
              <CardDescription>
                Select an existing image from your media library
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoadingMedia ? (
                <div className="flex items-center justify-center h-48">
                  <Loader2 className="h-8 w-8 animate-spin text-primary" />
                </div>
              ) : imageMediaItems.length === 0 ? (
                <div className="flex flex-col items-center justify-center h-48 bg-muted/20 rounded-md border border-dashed">
                  <FileImage className="h-12 w-12 text-muted-foreground/60 mb-2" />
                  <p className="text-sm text-muted-foreground">No images in your media library</p>
                  <Button
                    variant="link"
                    className="mt-2"
                    onClick={() => setActiveTab('ai')}
                  >
                    Generate one with AI
                  </Button>
                </div>
              ) : (
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  {imageMediaItems.map((item: any) => (
                    <div 
                      key={item.id}
                      className="relative aspect-video rounded-md overflow-hidden border cursor-pointer hover:opacity-90 transition-opacity"
                      onClick={() => handleSelectMediaAsThumbnail(item.url)}
                    >
                      <img 
                        src={item.url} 
                        alt={item.name} 
                        className="w-full h-full object-cover"
                      />
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
            <CardFooter>
              <div className="text-xs text-muted-foreground">
                Click on an image to set it as your course thumbnail
              </div>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}