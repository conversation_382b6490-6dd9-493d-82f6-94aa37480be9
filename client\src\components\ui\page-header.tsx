import React from 'react';

interface PageHeaderProps {
  title: string;
  description?: string;
  icon?: React.ReactNode;
  actions?: React.ReactNode;
}

export function PageHeader({ title, description, icon, actions }: PageHeaderProps) {
  return (
    <div className="flex flex-col sm:flex-row justify-between items-start gap-4 mb-8">
      <div className="flex flex-col gap-1">
        {icon && title && (
          <div className="flex items-center gap-2 mb-1">
            <div className="flex items-center justify-center w-10 h-10 rounded-md bg-primary/10 text-primary">
              {icon}
            </div>
            <h1 className="text-2xl font-bold tracking-tight">{title}</h1>
          </div>
        )}
        
        {!icon && title && (
          <h1 className="text-2xl font-bold tracking-tight">{title}</h1>
        )}
        
        {description && (
          <p className="text-sm text-muted-foreground max-w-2xl">
            {description}
          </p>
        )}
      </div>
      
      {actions && (
        <div className="flex mt-4 sm:mt-0 gap-2 ml-auto">
          {actions}
        </div>
      )}
    </div>
  );
}