import React, { useState, useEffect } from "react";
import { useQ<PERSON>y, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { 
  Card, 
  CardContent, 
  CardFooter,
  CardHeader,
  CardTitle,
  CardDescription 
} from "@/components/ui/card";
import { 
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue 
} from "@/components/ui/select";
import { 
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage 
} from "@/components/ui/form";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Loader2,
  UserCircle,
  Mic,
  Video,
  Film,
  Sparkles,
  RefreshCw,
  Save,
  Play,
  Download
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { AvatarSelector } from "../avatar/AvatarSelector";
import { Progress } from "@/components/ui/progress";

// Voice interface definition for avatar voice options
interface Voice {
  voice_id: string;
  name: string;
  gender: 'male' | 'female';
  preview_url?: string;
  source?: string;
}

const sadTalkerFormSchema = z.object({
  script: z.string().min(10, "Script must be at least 10 characters"),
  avatarFile: z.any().optional(),
  avatarUrl: z.string().optional(),
  voice: z.string().min(1, "Please select a voice"),
  enhanceAudio: z.boolean().optional(),
  title: z.string().optional(),
  useLocal: z.boolean().optional()
});

type SadTalkerFormValues = z.infer<typeof sadTalkerFormSchema>;

interface TalkingAvatarGeneratorProps {
  courseId?: number;
  lessonId?: number;
  onVideoGenerated?: (videoUrl: string, videoTitle: string) => void;
  onCancel?: () => void;
}

export function TalkingAvatarGenerator({
  courseId,
  lessonId,
  onVideoGenerated,
  onCancel
}: TalkingAvatarGeneratorProps) {
  const [videoGenerating, setVideoGenerating] = useState(false);
  const [videoProgress, setVideoProgress] = useState(0);
  const [videoJobId, setVideoJobId] = useState<string | null>(null);
  const [videoUrl, setVideoUrl] = useState<string | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const { toast } = useToast();
  const queryClient = useQueryClient();
  
  // Get available voices
  const { data: voices, isLoading: loadingVoices } = useQuery({
    queryKey: ['/api/ai/all-voices'],
    queryFn: async () => {
      const response = await apiRequest('GET', '/api/ai/all-voices');
      if (!response.ok) {
        throw new Error('Failed to fetch voices');
      }
      return response.json();
    }
  });
  
  // Form setup
  const form = useForm<SadTalkerFormValues>({
    resolver: zodResolver(sadTalkerFormSchema),
    defaultValues: {
      script: '',
      avatarUrl: '',
      voice: '',
      enhanceAudio: true,
      title: 'Talking Avatar Video'
    }
  });
  
  // Video generation mutation
  const generateVideoMutation = useMutation({
    mutationFn: async (data: SadTalkerFormValues) => {
      setVideoGenerating(true);
      setVideoProgress(5); // Initial progress
      
      const formData = new FormData();
      formData.append('script', data.script);
      
      if (data.avatarFile) {
        formData.append('image', data.avatarFile);
      } else if (data.avatarUrl) {
        formData.append('imageUrl', data.avatarUrl);
      }
      
      formData.append('voice', data.voice);
      formData.append('enhanceAudio', String(data.enhanceAudio || false));
      
      if (courseId) {
        formData.append('courseId', String(courseId));
      }
      
      if (lessonId) {
        formData.append('lessonId', String(lessonId));
      }
      
      if (data.title) {
        formData.append('title', data.title);
      }
      
      const response = await fetch('/api/ai/sadtalker/generate', {
        method: 'POST',
        body: formData,
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to generate video');
      }
      
      const result = await response.json();
      setVideoJobId(result.jobId);
      
      // Poll for status
      let statusComplete = false;
      const startTime = Date.now();
      const timeout = 15 * 60 * 1000; // 15 minute timeout
      
      while (!statusComplete && Date.now() - startTime < timeout) {
        await new Promise(resolve => setTimeout(resolve, 3000)); // Poll every 3 seconds
        
        const statusResponse = await apiRequest('GET', `/api/ai/sadtalker/status/${result.jobId}`);
        if (!statusResponse.ok) {
          throw new Error('Failed to check video status');
        }
        
        const statusData = await statusResponse.json();
        
        if (statusData.status === 'completed') {
          statusComplete = true;
          setVideoUrl(statusData.videoUrl);
          setVideoProgress(100);
          return {
            videoUrl: statusData.videoUrl,
            videoTitle: data.title || 'Talking Avatar Video'
          };
        } else if (statusData.status === 'error') {
          throw new Error(statusData.error || 'Error generating video');
        } else {
          // Update progress based on estimation
          const progressPercent = Math.min(
            Math.floor((Date.now() - startTime) / (statusData.estimatedTime * 10)),
            95
          );
          setVideoProgress(progressPercent);
        }
      }
      
      if (!statusComplete) {
        throw new Error('Video generation timed out');
      }
      
      return {
        videoUrl: '',
        videoTitle: ''
      };
    },
    onSuccess: (data) => {
      setVideoGenerating(false);
      toast({
        title: "Video generated successfully",
        description: "Your talking avatar video is ready to view and use.",
      });
      
      if (onVideoGenerated) {
        onVideoGenerated(data.videoUrl, data.videoTitle);
      }
      
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ['/api/media'] });
    },
    onError: (error: Error) => {
      setVideoGenerating(false);
      setVideoProgress(0);
      toast({
        title: "Video generation failed",
        description: error.message,
        variant: "destructive"
      });
    }
  });
  
  // Handle avatar selection
  const handleAvatarChange = (file: File | null, url: string | null) => {
    form.setValue('avatarFile', file);
    form.setValue('avatarUrl', url || '');
  };
  
  // Handle form submission
  const onSubmit = (data: SadTalkerFormValues) => {
    if (!data.avatarFile && !data.avatarUrl) {
      toast({
        title: "Avatar required",
        description: "Please select or upload an avatar image",
        variant: "destructive"
      });
      return;
    }
    
    generateVideoMutation.mutate(data);
  };
  
  // Handle video preview
  useEffect(() => {
    if (videoUrl) {
      setPreviewUrl(videoUrl);
    }
  }, [videoUrl]);
  
  return (
    <div className="space-y-6">
      <div className="flex flex-col space-y-2">
        <h2 className="text-2xl font-bold">Generate Talking Avatar Video</h2>
        <p className="text-muted-foreground">
          Create a video with a talking avatar using your script and selected voice
        </p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-6">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              {/* Title */}
              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Video Title</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter video title" {...field} />
                    </FormControl>
                    <FormDescription>
                      This will be used to identify your video in the media library
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              {/* Script */}
              <FormField
                control={form.control}
                name="script"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Script</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="Enter what you want the avatar to say..." 
                        className="min-h-32"
                        {...field} 
                      />
                    </FormControl>
                    <FormDescription>
                      The text that will be spoken by the avatar. Keep it between 10-500 words for best results.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              {/* Voice Selection */}
              <FormField
                control={form.control}
                name="voice"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Voice</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a voice" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {loadingVoices ? (
                          <div className="flex items-center justify-center p-4">
                            <Loader2 className="h-4 w-4 animate-spin mr-2" />
                            <span>Loading voices...</span>
                          </div>
                        ) : (
                          <>
                            <SelectGroup>
                              <SelectLabel>Female Voices</SelectLabel>
                              {voices?.filter((voice: any) => voice.gender === 'female').map((voice: any) => (
                                <SelectItem
                                  key={`${voice.voice_id}-${voice.source || 'default'}`}
                                  value={`${voice.voice_id}:${voice.source || 'elevenlabs'}`}
                                >
                                  {voice.name} ({voice.source || 'ElevenLabs'})
                                </SelectItem>
                              ))}
                            </SelectGroup>
                            <SelectGroup>
                              <SelectLabel>Male Voices</SelectLabel>
                              {voices?.filter((voice: any) => voice.gender === 'male').map((voice: any) => (
                                <SelectItem
                                  key={`${voice.voice_id}-${voice.source || 'default'}`}
                                  value={`${voice.voice_id}:${voice.source || 'elevenlabs'}`}
                                >
                                  {voice.name} ({voice.source || 'ElevenLabs'})
                                </SelectItem>
                              ))}
                            </SelectGroup>
                          </>
                        )}
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Select a voice for your avatar to speak with
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              {/* Avatar Selection */}
              <FormItem>
                <FormLabel>Avatar</FormLabel>
                <AvatarSelector
                  value={form.watch('avatarFile') || form.watch('avatarUrl')}
                  onChange={handleAvatarChange}
                />
                <FormDescription>
                  Upload an image or select one from the library to use as your talking avatar
                </FormDescription>
              </FormItem>
              
              {/* Action Buttons */}
              <div className="flex justify-between pt-4">
                {onCancel && (
                  <Button type="button" variant="outline" onClick={onCancel}>
                    Cancel
                  </Button>
                )}
                
                <Button 
                  type="submit" 
                  disabled={videoGenerating || generateVideoMutation.isPending}
                  className="ml-auto"
                >
                  {videoGenerating || generateVideoMutation.isPending ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Generating Video...
                    </>
                  ) : (
                    <>
                      <Video className="mr-2 h-4 w-4" />
                      Generate Video
                    </>
                  )}
                </Button>
              </div>
            </form>
          </Form>
        </div>
        
        {/* Video Preview Section */}
        <div className="space-y-4">
          {videoGenerating || generateVideoMutation.isPending ? (
            <Card>
              <CardHeader>
                <CardTitle>Generating Video</CardTitle>
                <CardDescription>
                  This can take a few minutes depending on the script length
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Progress value={videoProgress} className="w-full" />
                  <p className="text-sm text-center text-muted-foreground">
                    {videoProgress}% complete
                  </p>
                </div>
                
                <div className="flex items-center justify-center h-48 bg-muted rounded-md">
                  <div className="text-center">
                    <Film className="h-12 w-12 mx-auto mb-2 text-muted-foreground" />
                    <p className="text-sm text-muted-foreground">Animating your avatar...</p>
                    <p className="text-xs text-muted-foreground mt-1">This may take 2-5 minutes</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ) : previewUrl ? (
            <Card>
              <CardHeader>
                <CardTitle>Video Preview</CardTitle>
                <CardDescription>
                  Your generated talking avatar video
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="rounded-md overflow-hidden border">
                  <video
                    src={previewUrl}
                    controls
                    className="w-full aspect-video"
                    poster="/assets/video-poster.png"
                  />
                </div>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" onClick={() => {
                  form.reset({
                    ...form.getValues(),
                    avatarFile: undefined,
                    avatarUrl: '',
                  });
                  setPreviewUrl(null);
                  setVideoUrl(null);
                }}>
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Create New
                </Button>
                
                {videoUrl && (
                  <Button 
                    variant="default" 
                    onClick={() => {
                      // Create and trigger a download link
                      const a = document.createElement('a');
                      a.href = videoUrl;
                      a.download = `${form.getValues('title') || 'talking-avatar'}.mp4`;
                      document.body.appendChild(a);
                      a.click();
                      document.body.removeChild(a);
                    }}
                  >
                    <Download className="mr-2 h-4 w-4" />
                    Download Video
                  </Button>
                )}
              </CardFooter>
            </Card>
          ) : (
            <Card>
              <CardHeader>
                <CardTitle>Video Preview</CardTitle>
                <CardDescription>
                  Your video will appear here after generation
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-center h-[300px] bg-muted rounded-md">
                  <div className="text-center">
                    <Film className="h-12 w-12 mx-auto mb-2 text-muted-foreground" />
                    <p className="text-sm text-muted-foreground">No video generated yet</p>
                    <p className="text-xs text-muted-foreground mt-1">Complete the form to generate a video</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}

export default TalkingAvatarGenerator;