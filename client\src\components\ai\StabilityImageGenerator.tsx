import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils';

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Slider } from '@/components/ui/slider';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

import {
  ImageIcon,
  Loader2,
  RefreshCw,
  Wand2,
  Sparkles,
  ThumbsUp,
  ThumbsDown,
  Download,
  RotateCw,
  Settings,
  Zap,
  ImagePlus,
  PaintBucket,
  X,
  Copy,
  Save,
} from 'lucide-react';

interface StabilityImageGeneratorProps {
  onImageGenerated?: (image: any) => void;
  contextPrompt?: string;
  placeholder?: string;
  courseId?: number;
  lessonId?: number;
  showAdvancedOptions?: boolean;
  buttonLabel?: string;
  size?: 'sm' | 'default' | 'lg' | 'icon';
  variant?: 'default' | 'outline' | 'destructive' | 'secondary' | 'ghost' | 'link';
  className?: string;
  autoOpen?: boolean;
}

type StylePreset = string;
type AspectRatio = '1:1' | '3:2' | '2:3' | '16:9' | '9:16';

interface GenerationParams {
  prompt: string;
  negativePrompt: string;
  stylePreset: StylePreset;
  aspectRatio: AspectRatio;
  steps: number;
  cfgScale: number;
  seed?: number;
  courseId?: number;
  lessonId?: number;
}

const DEFAULT_NEGATIVE_PROMPT = "low quality, distorted, deformed, ugly, bad anatomy, blurry, disfigured";

const ASPECT_RATIO_DIMENSIONS: Record<AspectRatio, { width: number, height: number }> = {
  '1:1': { width: 1024, height: 1024 },
  '3:2': { width: 1024, height: 684 },
  '2:3': { width: 684, height: 1024 },
  '16:9': { width: 1024, height: 576 },
  '9:16': { width: 576, height: 1024 },
};

const PRESETS_EXAMPLES: Record<string, string> = {
  'photographic': 'Realistic photo of a product on a clean white background',
  'digital-art': 'Digital illustration of a fantasy landscape with mountains and castles',
  'cinematic': 'Cinematic shot of a hero standing on a cliff at sunset',
  'anime': 'Anime-style character with blue hair and futuristic outfit',
  '3d-model': '3D render of a futuristic city with flying cars',
  'pixel-art': 'Pixel art of a retro game character in a forest',
  'oil-painting': 'Oil painting of a vase with flowers in impressionist style',
  'comic-book': 'Comic book panel of a superhero fighting a villain',
  'fantasy-art': 'Fantasy artwork of a dragon in a magical forest',
  'line-art': 'Minimalist line art of a face',
  'analog-film': 'Vintage film photo of a 1970s street scene',
  'neon-punk': 'Cyberpunk city street with neon lights and rain',
  'isometric': 'Isometric view of a small town with buildings and trees',
  'low-poly': 'Low-poly 3D landscape with mountains and a lake',
  'origami': 'Origami-style paper art of animals',
  'watercolor': 'Watercolor painting of a coastal landscape'
};

export function StabilityImageGenerator({
  onImageGenerated,
  contextPrompt = '',
  placeholder = 'Describe the image you want to generate...',
  courseId,
  lessonId,
  showAdvancedOptions = true,
  buttonLabel = 'Generate Image',
  size = 'default',
  variant = 'default',
  className,
  autoOpen = false
}: StabilityImageGeneratorProps) {
  const [isOpen, setIsOpen] = useState(autoOpen);
  const [activeTab, setActiveTab] = useState<string>('generate');
  const [prompt, setPrompt] = useState<string>(contextPrompt);
  const [negativePrompt, setNegativePrompt] = useState<string>(DEFAULT_NEGATIVE_PROMPT);
  const [stylePreset, setStylePreset] = useState<StylePreset>('photographic');
  const [aspectRatio, setAspectRatio] = useState<AspectRatio>('1:1');
  const [steps, setSteps] = useState<number>(30);
  const [cfgScale, setCfgScale] = useState<number>(7);
  const [seed, setSeed] = useState<number | undefined>(undefined);
  const [generatedImage, setGeneratedImage] = useState<any>(null);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Set initial prompt from contextPrompt
  useEffect(() => {
    if (contextPrompt) {
      setPrompt(contextPrompt);
    }
  }, [contextPrompt]);

  // Reset state when dialog closes
  const handleOpenChange = (open: boolean) => {
    setIsOpen(open);
    if (!open) {
      setGeneratedImage(null);
      setActiveTab('generate');
    }
  };

  // Fetch style presets
  const { data: stylePresets = [], isLoading: isLoadingPresets } = useQuery({
    queryKey: ['/api/ai/stability/style-presets'],
    queryFn: async () => {
      const response = await apiRequest('GET', '/api/ai/stability/style-presets');
      if (!response.ok) throw new Error('Failed to fetch style presets');
      return response.json();
    }
  });

  // Fetch user stats for AI credits info
  const { data: userStats, isLoading: isLoadingStats } = useQuery({
    queryKey: ['/api/user-stats'],
    queryFn: async () => {
      const response = await apiRequest('GET', '/api/user-stats');
      if (!response.ok) throw new Error('Failed to fetch user stats');
      return response.json();
    },
  });

  // Generate image mutation
  const generateImageMutation = useMutation({
    mutationFn: async (params: GenerationParams) => {
      const { aspectRatio, ...rest } = params;
      const dimensions = ASPECT_RATIO_DIMENSIONS[aspectRatio];
      
      const response = await apiRequest('POST', '/api/ai/stability/generate-image', {
        ...rest,
        ...dimensions,
        courseId,
        lessonId
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to generate image');
      }
      
      return response.json();
    },
    onSuccess: (data) => {
      setGeneratedImage(data);
      setActiveTab('result');
      
      // Invalidate user stats to reflect the credit usage
      queryClient.invalidateQueries({ queryKey: ['/api/user-stats'] });
      
      toast({
        title: 'Image Generated',
        description: 'Your image has been successfully generated',
      });
      
      if (onImageGenerated) {
        onImageGenerated(data);
      }
    },
    onError: (error: Error) => {
      toast({
        variant: 'destructive',
        title: 'Generation Failed',
        description: error.message || 'Failed to generate image. Please try again.',
      });
    }
  });

  // Handle image generation
  const handleGenerateImage = () => {
    if (!prompt || prompt.trim().length < 3) {
      toast({
        variant: 'destructive',
        title: 'Invalid Prompt',
        description: 'Please enter a more detailed description for your image.',
      });
      return;
    }

    if (userStats && userStats.aiCredits < 5) {
      toast({
        variant: 'destructive',
        title: 'Insufficient Credits',
        description: 'You need at least 5 AI credits to generate an image.',
      });
      return;
    }

    generateImageMutation.mutate({
      prompt,
      negativePrompt,
      stylePreset,
      aspectRatio,
      steps,
      cfgScale,
      seed,
      courseId,
      lessonId
    });
  };

  // Generate random seed
  const handleRandomSeed = () => {
    setSeed(Math.floor(Math.random() * 1000000));
  };

  // Use the generated image
  const handleUseImage = () => {
    if (generatedImage && onImageGenerated) {
      onImageGenerated(generatedImage);
      setIsOpen(false);
    }
  };

  // Generate another image with the same settings
  const handleRegenerateImage = () => {
    setActiveTab('generate');
    // Automatically generate a new random seed
    setSeed(Math.floor(Math.random() * 1000000));
  };

  return (
    <>
      <Dialog open={isOpen} onOpenChange={handleOpenChange}>
        <DialogTrigger asChild>
          <Button 
            variant={variant} 
            size={size}
            className={cn("gap-2", className)}
          >
            <Wand2 className="h-4 w-4" />
            {buttonLabel}
          </Button>
        </DialogTrigger>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Sparkles className="h-5 w-5 text-amber-500" />
              AI Image Generation
            </DialogTitle>
            <DialogDescription>
              Create unique images using Stability AI's advanced text-to-image technology
            </DialogDescription>
          </DialogHeader>

          <div className="flex items-center justify-between mb-3">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid grid-cols-2">
                <TabsTrigger value="generate" disabled={generateImageMutation.isPending}>
                  <ImagePlus className="h-4 w-4 mr-2" />
                  Generate
                </TabsTrigger>
                <TabsTrigger value="result" disabled={!generatedImage}>
                  <ImageIcon className="h-4 w-4 mr-2" />
                  Result
                </TabsTrigger>
              </TabsList>
            </Tabs>
            
            {userStats && (
              <Badge variant="outline" className="ml-2 px-2 py-1 gap-1">
                <Zap className="h-3.5 w-3.5 text-amber-500" />
                <span>{userStats.aiCredits || 0} Credits</span>
              </Badge>
            )}
          </div>

          <TabsContent value="generate" className="space-y-4">
            <div>
              <Label htmlFor="prompt">Image Description</Label>
              <Textarea
                id="prompt"
                placeholder={placeholder}
                value={prompt}
                onChange={(e) => setPrompt(e.target.value)}
                className="min-h-[80px]"
              />
              <p className="text-xs text-muted-foreground mt-1">
                Describe the image you want in detail, include style, colors, and elements
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="style-preset">Style</Label>
                <Select value={stylePreset} onValueChange={setStylePreset}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a style" />
                  </SelectTrigger>
                  <SelectContent>
                    {stylePresets.map((preset: string) => (
                      <SelectItem key={preset} value={preset}>
                        {preset.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <p className="text-xs text-muted-foreground mt-1">
                  Choose a visual style for your generated image
                </p>
              </div>

              <div>
                <Label htmlFor="aspect-ratio">Aspect Ratio</Label>
                <Select value={aspectRatio} onValueChange={(value: AspectRatio) => setAspectRatio(value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select aspect ratio" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1:1">Square (1:1)</SelectItem>
                    <SelectItem value="3:2">Landscape (3:2)</SelectItem>
                    <SelectItem value="2:3">Portrait (2:3)</SelectItem>
                    <SelectItem value="16:9">Widescreen (16:9)</SelectItem>
                    <SelectItem value="9:16">Mobile (9:16)</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-xs text-muted-foreground mt-1">
                  Choose the dimensions for your image
                </p>
              </div>
            </div>

            {/* Style Example */}
            {stylePreset && (
              <Card className="bg-secondary/40">
                <CardHeader className="py-3">
                  <CardTitle className="text-sm">Style Example: {stylePreset.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}</CardTitle>
                </CardHeader>
                <CardContent className="py-2">
                  <p className="text-sm italic">"{PRESETS_EXAMPLES[stylePreset] || 'Example not available for this style'}"</p>
                </CardContent>
              </Card>
            )}

            {showAdvancedOptions && (
              <div className="space-y-4">
                <div className="text-sm font-medium">Advanced Options</div>
                <div>
                  <div className="flex justify-between mb-1">
                    <Label htmlFor="negative-prompt">Negative Prompt</Label>
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      className="h-6 text-xs"
                      onClick={() => setNegativePrompt(DEFAULT_NEGATIVE_PROMPT)}
                    >
                      Reset
                    </Button>
                  </div>
                  <Textarea
                    id="negative-prompt"
                    placeholder="Features to exclude from the image..."
                    value={negativePrompt}
                    onChange={(e) => setNegativePrompt(e.target.value)}
                    className="min-h-[60px]"
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Describe what you don't want to see in the image
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <div className="flex justify-between mb-1">
                      <Label htmlFor="steps">Quality Level ({steps})</Label>
                    </div>
                    <Slider
                      id="steps"
                      min={20}
                      max={50}
                      step={1}
                      value={[steps]}
                      onValueChange={(value) => setSteps(value[0])}
                    />
                    <p className="text-xs text-muted-foreground mt-1">
                      Higher values produce better quality but take longer
                    </p>
                  </div>
                  
                  <div>
                    <div className="flex justify-between mb-1">
                      <Label htmlFor="cfg-scale">Prompt Adherence ({cfgScale})</Label>
                    </div>
                    <Slider
                      id="cfg-scale"
                      min={0}
                      max={20}
                      step={1}
                      value={[cfgScale]}
                      onValueChange={(value) => setCfgScale(value[0])}
                    />
                    <p className="text-xs text-muted-foreground mt-1">
                      Higher values make the image follow your prompt more closely
                    </p>
                  </div>
                </div>

                <div>
                  <div className="flex items-center justify-between mb-1">
                    <Label htmlFor="seed">Seed</Label>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 text-xs"
                      onClick={handleRandomSeed}
                    >
                      <RefreshCw className="h-3 w-3 mr-1" />
                      Randomize
                    </Button>
                  </div>
                  <Input
                    id="seed"
                    type="number"
                    value={seed === undefined ? '' : seed}
                    onChange={(e) => setSeed(e.target.value ? parseInt(e.target.value) : undefined)}
                    placeholder="Random seed"
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Use the same seed to get consistent results (leave empty for random)
                  </p>
                </div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="result" className="space-y-3">
            {generateImageMutation.isPending ? (
              <div className="flex flex-col items-center justify-center h-60 gap-3">
                <Loader2 className="h-12 w-12 animate-spin text-primary" />
                <div className="text-sm">Generating your image...</div>
                <div className="text-xs text-muted-foreground">
                  This may take up to 30 seconds
                </div>
              </div>
            ) : generatedImage ? (
              <div className="space-y-4">
                <div className="w-full rounded-md overflow-hidden bg-black/5 flex items-center justify-center">
                  <img 
                    src={generatedImage.imageUrl} 
                    alt={generatedImage.prompt}
                    className="max-h-[400px] max-w-full object-contain"
                  />
                </div>
                <div className="bg-muted p-3 rounded-md text-xs">
                  <div className="font-semibold mb-1">Prompt:</div>
                  <div className="text-muted-foreground mb-2">{generatedImage.prompt}</div>
                  <div className="grid grid-cols-2 gap-4 mt-1">
                    <div className="space-y-1">
                      <div className="font-semibold">Style:</div>
                      <div className="text-muted-foreground">
                        {generatedImage.stylePreset?.split('-').map((word: string) => 
                          word.charAt(0).toUpperCase() + word.slice(1)
                        ).join(' ') || 'Standard'}
                      </div>
                    </div>
                    <div className="space-y-1">
                      <div className="font-semibold">Seed:</div>
                      <div className="text-muted-foreground">{generatedImage.seed || 'Random'}</div>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center h-60">
                <div className="text-sm text-muted-foreground">No image generated yet</div>
              </div>
            )}
          </TabsContent>

          <DialogFooter className="flex justify-between">
            {activeTab === 'generate' ? (
              <>
                <Button variant="outline" onClick={() => setIsOpen(false)}>
                  Cancel
                </Button>
                <Button 
                  onClick={handleGenerateImage} 
                  disabled={generateImageMutation.isPending || !prompt.trim()}
                >
                  {generateImageMutation.isPending ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Generating...
                    </>
                  ) : (
                    <>
                      <Sparkles className="h-4 w-4 mr-2" />
                      Generate Image (5 Credits)
                    </>
                  )}
                </Button>
              </>
            ) : (
              <>
                <div className="flex gap-2">
                  <Button variant="outline" onClick={handleRegenerateImage}>
                    <RotateCw className="h-4 w-4 mr-2" />
                    Try Another
                  </Button>
                </div>
                <Button onClick={handleUseImage}>
                  <Save className="h-4 w-4 mr-2" />
                  Use This Image
                </Button>
              </>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}