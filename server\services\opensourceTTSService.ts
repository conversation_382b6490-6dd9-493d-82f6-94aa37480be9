/**
 * Open-Source TTS Service
 * Unified service for Coqui TTS (primary) and Kokoro TTS (fallback)
 * Removes all proprietary TTS dependencies
 */

import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';

// Modal A100 GPU endpoint for TTS services
const MODAL_TTS_ENDPOINT = process.env.MODAL_GPU_BASE_URL || 'https://trade-digital--courseai-opensource';

interface TTSResponse {
  status: string;
  audio_base64?: string;
  error?: string;
  engine?: string;
  text?: string;
  voice?: string;
  timestamp?: number;
}

interface CoquiVoice {
  id: string;
  name: string;
  language: string;
  gender: string;
  style: string;
  quality: 'high' | 'medium' | 'basic';
  description: string;
}

interface KokoroVoice {
  id: string;
  name: string;
  language: string;
  gender: string;
  description: string;
}

/**
 * Available Coqui TTS voices with metadata
 */
export const COQUI_VOICES: CoquiVoice[] = [
  {
    id: 'tts_models/en/ljspeech/tacotron2-DDC',
    name: 'LJSpeech Female',
    language: 'en',
    gender: 'female',
    style: 'neutral',
    quality: 'high',
    description: 'Clear, professional female voice with neutral accent'
  },
  {
    id: 'tts_models/en/ljspeech/glow-tts',
    name: 'LJSpeech Glow',
    language: 'en',
    gender: 'female',
    style: 'expressive',
    quality: 'high',
    description: 'Expressive female voice with natural intonation'
  },
  {
    id: 'tts_models/en/vctk/vits',
    name: 'VCTK Multi-Speaker',
    language: 'en',
    gender: 'mixed',
    style: 'varied',
    quality: 'high',
    description: 'Multi-speaker model with various accents and styles'
  },
  {
    id: 'tts_models/en/sam/tacotron-DDC',
    name: 'SAM Male',
    language: 'en',
    gender: 'male',
    style: 'neutral',
    quality: 'medium',
    description: 'Clear male voice suitable for educational content'
  },
  {
    id: 'tts_models/multilingual/multi-dataset/your_tts',
    name: 'YourTTS Multilingual',
    language: 'multilingual',
    gender: 'mixed',
    style: 'adaptable',
    quality: 'high',
    description: 'Advanced multilingual model with voice cloning capabilities'
  }
];

/**
 * Available Kokoro TTS voices
 */
export const KOKORO_VOICES: KokoroVoice[] = [
  {
    id: 'af_sarah',
    name: 'Sarah (African)',
    language: 'en',
    gender: 'female',
    description: 'Warm female voice with African accent'
  },
  {
    id: 'af_sky',
    name: 'Sky (African)',
    language: 'en',
    gender: 'male',
    description: 'Clear male voice with African accent'
  },
  {
    id: 'am_adam',
    name: 'Adam (American)',
    language: 'en',
    gender: 'male',
    description: 'Professional American male voice'
  },
  {
    id: 'am_michael',
    name: 'Michael (American)',
    language: 'en',
    gender: 'male',
    description: 'Friendly American male voice'
  },
  {
    id: 'bf_emma',
    name: 'Emma (British)',
    language: 'en',
    gender: 'female',
    description: 'Elegant British female voice'
  },
  {
    id: 'bf_isabella',
    name: 'Isabella (British)',
    language: 'en',
    gender: 'female',
    description: 'Sophisticated British female voice'
  }
];

/**
 * Generate speech using Coqui TTS (primary)
 */
export async function generateSpeechWithCoqui(
  text: string,
  voiceName: string = 'tts_models/en/ljspeech/tacotron2-DDC',
  language: string = 'en'
): Promise<TTSResponse> {
  try {
    const response = await fetch(`${MODAL_TTS_ENDPOINT}-generate-speech.modal.run`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        text,
        voice_name: voiceName,
        language
      })
    });

    if (!response.ok) {
      throw new Error(`Coqui TTS API error: ${response.status} ${response.statusText}`);
    }

    const result: TTSResponse = await response.json();
    
    if (result.status === 'error') {
      throw new Error(`Coqui TTS failed: ${result.error}`);
    }

    return {
      ...result,
      engine: 'coqui_tts'
    };
  } catch (error) {
    console.error('Coqui TTS generation failed:', error);
    throw error;
  }
}

/**
 * Generate speech using Kokoro TTS (fallback)
 */
export async function generateSpeechWithKokoro(
  text: string,
  voice: string = 'af_sarah',
  speed: number = 1.0
): Promise<TTSResponse> {
  try {
    const response = await fetch(`${MODAL_TTS_ENDPOINT}-generate-speech-fallback.modal.run`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        text,
        voice,
        speed
      })
    });

    if (!response.ok) {
      throw new Error(`Kokoro TTS API error: ${response.status} ${response.statusText}`);
    }

    const result: TTSResponse = await response.json();
    
    if (result.status === 'error') {
      throw new Error(`Kokoro TTS failed: ${result.error}`);
    }

    return {
      ...result,
      engine: result.engine || 'kokoro_tts'
    };
  } catch (error) {
    console.error('Kokoro TTS generation failed:', error);
    throw error;
  }
}

/**
 * Unified TTS generation with automatic fallback
 */
export async function generateSpeech(
  text: string,
  options: {
    voice?: string;
    language?: string;
    speed?: number;
    preferredEngine?: 'coqui' | 'kokoro';
    skipFallback?: boolean;
  } = {}
): Promise<TTSResponse & { finalEngine: string }> {
  
  const {
    voice = 'tts_models/en/ljspeech/tacotron2-DDC',
    language = 'en',
    speed = 1.0,
    preferredEngine = 'coqui',
    skipFallback = false
  } = options;

  // Force specific engine if requested
  if (preferredEngine === 'kokoro') {
    const kokoroVoice = voice.startsWith('tts_models/') ? 'af_sarah' : voice;
    return {
      ...(await generateSpeechWithKokoro(text, kokoroVoice, speed)),
      finalEngine: 'kokoro'
    };
  }

  // Default: Try Coqui first, fallback to Kokoro
  try {
    console.log('🗣️ Attempting TTS generation with Coqui (primary)...');
    const result = await generateSpeechWithCoqui(text, voice, language);
    return {
      ...result,
      finalEngine: 'coqui'
    };
  } catch (coquiError) {
    console.warn('⚠️ Coqui TTS failed, trying Kokoro (fallback):', coquiError);
    
    if (skipFallback) {
      throw coquiError;
    }
    
    try {
      // Convert Coqui voice to Kokoro voice if needed
      const kokoroVoice = voice.startsWith('tts_models/') ? 'af_sarah' : voice;
      const result = await generateSpeechWithKokoro(text, kokoroVoice, speed);
      return {
        ...result,
        finalEngine: 'kokoro_fallback'
      };
    } catch (kokoroError) {
      console.error('❌ All TTS services failed');
      throw new Error('All TTS services failed. Please check your configuration and try again.');
    }
  }
}

/**
 * Get all available voices from both services
 */
export function getAvailableVoices(): {
  coqui: CoquiVoice[];
  kokoro: KokoroVoice[];
  total: number;
} {
  return {
    coqui: COQUI_VOICES,
    kokoro: KOKORO_VOICES,
    total: COQUI_VOICES.length + KOKORO_VOICES.length
  };
}

/**
 * Find voice by ID across both services
 */
export function findVoiceById(voiceId: string): (CoquiVoice | KokoroVoice) | null {
  // Check Coqui voices
  const coquiVoice = COQUI_VOICES.find(v => v.id === voiceId);
  if (coquiVoice) return coquiVoice;
  
  // Check Kokoro voices
  const kokoroVoice = KOKORO_VOICES.find(v => v.id === voiceId);
  if (kokoroVoice) return kokoroVoice;
  
  return null;
}

/**
 * Get recommended voices for different use cases
 */
export function getRecommendedVoices(): {
  educational: CoquiVoice[];
  professional: CoquiVoice[];
  conversational: KokoroVoice[];
  multilingual: CoquiVoice[];
} {
  return {
    educational: COQUI_VOICES.filter(v => v.style === 'neutral' && v.quality === 'high'),
    professional: COQUI_VOICES.filter(v => v.quality === 'high'),
    conversational: KOKORO_VOICES,
    multilingual: COQUI_VOICES.filter(v => v.language === 'multilingual')
  };
}

/**
 * Test TTS service availability
 */
export async function testTTSServices(): Promise<{
  coqui: boolean;
  kokoro: boolean;
  modalEndpoint: string;
}> {
  const status = {
    coqui: false,
    kokoro: false,
    modalEndpoint: MODAL_TTS_ENDPOINT
  };

  // Test Coqui TTS
  try {
    const result = await generateSpeechWithCoqui('Test', 'tts_models/en/ljspeech/tacotron2-DDC');
    status.coqui = result.status === 'success';
  } catch {
    status.coqui = false;
  }

  // Test Kokoro TTS
  try {
    const result = await generateSpeechWithKokoro('Test', 'af_sarah');
    status.kokoro = result.status === 'success';
  } catch {
    status.kokoro = false;
  }

  return status;
}

/**
 * Save audio to file system
 */
export async function saveAudioToFile(
  audioBase64: string,
  filename?: string
): Promise<string> {
  const uploadsDir = path.join(process.cwd(), 'uploads', 'audio');
  
  // Ensure directory exists
  if (!fs.existsSync(uploadsDir)) {
    fs.mkdirSync(uploadsDir, { recursive: true });
  }
  
  // Generate filename if not provided
  const audioFilename = filename || `tts_${uuidv4()}.wav`;
  const filePath = path.join(uploadsDir, audioFilename);
  
  // Decode and save audio
  const audioBuffer = Buffer.from(audioBase64, 'base64');
  fs.writeFileSync(filePath, audioBuffer);
  
  return `/uploads/audio/${audioFilename}`;
}
