#!/usr/bin/env python3
"""
Final A100 80G GPU Deployment Script
Resolves Modal credential issues and deploys production-ready Avatar Course Creation
"""

import os
import sys
import subprocess
import json
import time
import requests
from pathlib import Path

def check_python_version():
    """Ensure Python 3.10+ is available"""
    if sys.version_info < (3, 10):
        print("❌ Python 3.10+ required. Current version:", sys.version)
        return False
    print("✅ Python version:", sys.version.split()[0])
    return True

def install_modal():
    """Install Modal CLI with proper dependencies"""
    print("📦 Installing Modal CLI...")
    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", 
            "modal>=0.56.0", "--upgrade", "--force-reinstall"
        ], check=True, capture_output=True)
        print("✅ Modal CLI installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print("❌ Failed to install Modal CLI:", e.stderr.decode())
        return False

def check_modal_credentials():
    """Check if Modal credentials are properly configured"""
    token_id = os.getenv('MODAL_TOKEN_ID')
    token_secret = os.getenv('MODAL_TOKEN_SECRET')
    
    if not token_id or not token_secret:
        print("❌ Modal credentials not found in environment variables")
        print("Required environment variables:")
        print("  - MODAL_TOKEN_ID")
        print("  - MODAL_TOKEN_SECRET")
        return False
    
    # Validate token format
    if len(token_id) < 20 or len(token_secret) < 40:
        print("❌ Modal credentials appear to be malformed")
        print(f"Token ID length: {len(token_id)} (expected 20+)")
        print(f"Token Secret length: {len(token_secret)} (expected 40+)")
        return False
    
    print("✅ Modal credentials found and properly formatted")
    return True

def authenticate_modal():
    """Authenticate Modal using environment credentials"""
    print("🔐 Authenticating with Modal...")
    
    try:
        # Set Modal credentials in environment
        env = os.environ.copy()
        env['MODAL_TOKEN_ID'] = os.getenv('MODAL_TOKEN_ID')
        env['MODAL_TOKEN_SECRET'] = os.getenv('MODAL_TOKEN_SECRET')
        
        # Test authentication
        result = subprocess.run([
            sys.executable, "-c", 
            "import modal; print('Modal authentication successful')"
        ], env=env, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ Modal authentication successful")
            return True
        else:
            print("❌ Modal authentication failed:", result.stderr)
            return False
            
    except Exception as e:
        print("❌ Modal authentication error:", str(e))
        return False

def deploy_a100_app():
    """Deploy the A100 GPU application to Modal"""
    print("🚀 Deploying A100 GPU application...")
    
    try:
        env = os.environ.copy()
        env['MODAL_TOKEN_ID'] = os.getenv('MODAL_TOKEN_ID')
        env['MODAL_TOKEN_SECRET'] = os.getenv('MODAL_TOKEN_SECRET')
        
        # Deploy the production app
        result = subprocess.run([
            sys.executable, "-m", "modal", "deploy", "modal_a100_production_final.py"
        ], env=env, capture_output=True, text=True, timeout=600)
        
        if result.returncode == 0:
            print("✅ A100 GPU application deployed successfully")
            print("Deployment output:")
            print(result.stdout)
            
            # Extract deployment URL
            lines = result.stdout.split('\n')
            for line in lines:
                if 'https://' in line and 'modal.run' in line:
                    print(f"🌐 Deployment URL: {line.strip()}")
                    return line.strip()
            
            return True
        else:
            print("❌ A100 GPU deployment failed:")
            print("STDOUT:", result.stdout)
            print("STDERR:", result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Deployment timed out after 10 minutes")
        return False
    except Exception as e:
        print("❌ Deployment error:", str(e))
        return False

def test_deployed_endpoints(base_url):
    """Test the deployed A100 GPU endpoints"""
    print("🧪 Testing deployed endpoints...")
    
    endpoints = {
        "health": "/health",
        "tts": "/tts",
        "sadtalker": "/sadtalker", 
        "slides": "/slides",
        "avatar_course": "/avatar-course"
    }
    
    results = {}
    
    for name, endpoint in endpoints.items():
        try:
            if name == "health":
                response = requests.get(f"{base_url}{endpoint}", timeout=30)
            else:
                # Test POST endpoints with minimal data
                test_data = {
                    "tts": {"text": "Hello, this is a test"},
                    "sadtalker": {"image_base64": "test", "audio_base64": "test"},
                    "slides": {"markdown_content": "# Test Slide"},
                    "avatar_course": {
                        "course_title": "Test Course",
                        "lesson_scripts": [{"title": "Test", "script": "Test"}],
                        "avatar_image_base64": "test"
                    }
                }
                response = requests.post(
                    f"{base_url}{endpoint}", 
                    json=test_data.get(name, {}),
                    timeout=60
                )
            
            results[name] = {
                "status": "✅ Available" if response.status_code < 500 else "❌ Error",
                "status_code": response.status_code,
                "response_time": response.elapsed.total_seconds()
            }
            
        except Exception as e:
            results[name] = {
                "status": "❌ Failed",
                "error": str(e)
            }
    
    # Print results
    print("\n📊 Endpoint Test Results:")
    for name, result in results.items():
        print(f"  {name}: {result['status']}")
        if 'response_time' in result:
            print(f"    Response time: {result['response_time']:.2f}s")
        if 'error' in result:
            print(f"    Error: {result['error']}")
    
    return results

def update_environment_config(deployment_url):
    """Update environment configuration with deployment URL"""
    print("⚙️ Updating environment configuration...")
    
    try:
        # Update .env file
        env_file = Path('.env')
        env_content = ""
        
        if env_file.exists():
            env_content = env_file.read_text()
        
        # Update or add MODAL_GPU_BASE_URL
        lines = env_content.split('\n')
        updated = False
        
        for i, line in enumerate(lines):
            if line.startswith('MODAL_GPU_BASE_URL='):
                lines[i] = f'MODAL_GPU_BASE_URL={deployment_url}'
                updated = True
                break
        
        if not updated:
            lines.append(f'MODAL_GPU_BASE_URL={deployment_url}')
        
        env_file.write_text('\n'.join(lines))
        print(f"✅ Environment updated with deployment URL: {deployment_url}")
        
    except Exception as e:
        print(f"❌ Failed to update environment: {e}")

def main():
    """Main deployment process"""
    print("🎯 Starting A100 80G GPU Final Deployment")
    print("=" * 50)
    
    # Step 1: Check Python version
    if not check_python_version():
        return False
    
    # Step 2: Install Modal CLI
    if not install_modal():
        return False
    
    # Step 3: Check credentials
    if not check_modal_credentials():
        print("\n🔑 Modal API credentials are required:")
        print("1. Go to https://modal.com/tokens")
        print("2. Create a new token")
        print("3. Set environment variables:")
        print("   export MODAL_TOKEN_ID='your-token-id'")
        print("   export MODAL_TOKEN_SECRET='your-token-secret'")
        return False
    
    # Step 4: Authenticate
    if not authenticate_modal():
        return False
    
    # Step 5: Deploy application
    deployment_result = deploy_a100_app()
    if not deployment_result:
        return False
    
    # Extract deployment URL if it's a string
    deployment_url = deployment_result if isinstance(deployment_result, str) else None
    
    # Step 6: Test endpoints (if we have URL)
    if deployment_url:
        test_results = test_deployed_endpoints(deployment_url)
        update_environment_config(deployment_url)
    
    print("\n🎉 A100 80G GPU Deployment Complete!")
    print("=" * 50)
    print("✅ SadTalker avatar generation: Ready")
    print("✅ Chatterbox TTS voice synthesis: Ready") 
    print("✅ Marp slide generation: Ready")
    print("✅ Complete Avatar Course workflow: Ready")
    print("\n🔧 Next steps:")
    print("1. Restart your application server")
    print("2. Test Avatar Course Creation in the UI")
    print("3. Monitor GPU usage in Modal dashboard")
    
    if deployment_url:
        print(f"\n🌐 A100 GPU Backend: {deployment_url}")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n❌ Deployment cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)