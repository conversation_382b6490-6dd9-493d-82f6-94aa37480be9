<?xml version="1.0" encoding="UTF-8"?>
<svg width="400" height="300" viewBox="0 0 400 300" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Collaboration board -->
  <rect x="80" y="60" width="240" height="160" rx="8" fill="white" stroke="#fbbf24" stroke-width="3"/>
  
  <!-- Header section -->
  <rect x="80" y="60" width="240" height="30" rx="8" fill="#fbbf24"/>
  <text x="105" y="80" font-family="Arial" font-size="14" font-weight="bold" fill="white">Course Collaboration</text>
  
  <!-- User avatars in a row -->
  <g transform="translate(120, 110)">
    <!-- Avatar 1 - Active -->
    <circle cx="0" cy="0" r="20" fill="#f3f4f6" stroke="#fbbf24" stroke-width="2"/>
    <circle cx="0" cy="-5" r="5" fill="#fbbf24"/>
    <path d="M-8,5 Q0,15 8,5" stroke="#fbbf24" stroke-width="2" fill="none"/>
    <circle cx="0" cy="0" r="24" stroke="#fbbf24" stroke-width="2" stroke-dasharray="4 2">
      <animateTransform attributeName="transform" type="rotate" from="0 0 0" to="360 0 0" dur="8s" repeatCount="indefinite"/>
    </circle>
    
    <!-- Avatar 2 -->
    <circle cx="60" cy="0" r="20" fill="#f3f4f6" stroke="#fbbf24" stroke-width="2"/>
    <circle cx="60" cy="-5" r="5" fill="#fbbf24"/>
    <path d="M52,5 Q60,15 68,5" stroke="#fbbf24" stroke-width="2" fill="none"/>
    
    <!-- Avatar 3 -->
    <circle cx="120" cy="0" r="20" fill="#f3f4f6" stroke="#fbbf24" stroke-width="2"/>
    <circle cx="120" cy="-5" r="5" fill="#fbbf24"/>
    <path d="M112,5 Q120,15 128,5" stroke="#fbbf24" stroke-width="2" fill="none"/>
  </g>
  
  <!-- Connecting lines -->
  <path d="M140,110 L180,110" stroke="#fbbf24" stroke-width="2" stroke-dasharray="4 2"/>
  <path d="M200,110 L240,110" stroke="#fbbf24" stroke-width="2" stroke-dasharray="4 2"/>
  
  <!-- Chat bubbles -->
  <g transform="translate(120, 160)">
    <!-- Chat bubble 1 -->
    <path d="M-10,-10 L-10,10 L10,10 L10,-5 L15,0 L10,-10 Z" fill="#fbbf24" opacity="0.2" stroke="#fbbf24"/>
    <text x="0" y="5" font-family="Arial" font-size="5" text-anchor="middle" fill="#666">Let's add...</text>
    
    <!-- Chat bubble 2 -->
    <path d="M50,-15 L50,5 L70,5 L70,-10 L75,-5 L70,-15 Z" fill="#fbbf24" opacity="0.2" stroke="#fbbf24"/>
    <text x="60" y="0" font-family="Arial" font-size="5" text-anchor="middle" fill="#666">Great idea!</text>
    
    <!-- Chat bubble 3 -->
    <path d="M110,-10 L110,10 L130,10 L130,-5 L135,0 L130,-10 Z" fill="#fbbf24" opacity="0.2" stroke="#fbbf24"/>
    <text x="120" y="5" font-family="Arial" font-size="5" text-anchor="middle" fill="#666">I'll work on...</text>
  </g>
  
  <!-- Shared course outline with animations -->
  <g transform="translate(200, 200)">
    <rect x="-60" y="-10" width="120" height="20" rx="3" fill="#fbbf24" opacity="0.1" stroke="#fbbf24"/>
    <rect x="-60" y="15" width="90" height="10" rx="3" fill="#fbbf24" opacity="0.1" stroke="#fbbf24"/>
    
    <!-- Cursor 1 - avatar 1 editing -->
    <circle cx="-30" cy="0" r="3" fill="#fbbf24">
      <animate attributeName="opacity" values="1;0.5;1" dur="2s" repeatCount="indefinite"/>
    </circle>
    
    <!-- Cursor 2 - avatar 3 editing -->
    <circle cx="10" cy="20" r="3" fill="#fbbf24">
      <animate attributeName="opacity" values="0.5;1;0.5" dur="2s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- Publishing indicator -->
  <g transform="translate(330, 120)">
    <rect x="-20" y="-20" width="40" height="70" rx="5" fill="white" stroke="#fbbf24" stroke-width="2"/>
    <rect x="-10" y="-10" width="20" height="10" rx="2" fill="#f3f4f6"/>
    <rect x="-10" y="10" width="20" height="10" rx="2" fill="#f3f4f6"/>
    <rect x="-10" y="30" width="20" height="10" rx="2" fill="#f3f4f6"/>
    <text x="0" y="55" font-family="Arial" font-size="8" text-anchor="middle" fill="#fbbf24">Publish</text>
  </g>
  
  <!-- Floating elements -->
  <circle cx="60" cy="90" r="12" fill="#fbbf24" opacity="0.3"/>
  <circle cx="340" cy="220" r="15" fill="#fbbf24" opacity="0.2"/>
  <circle cx="90" cy="230" r="10" fill="#fbbf24" opacity="0.3"/>
</svg>