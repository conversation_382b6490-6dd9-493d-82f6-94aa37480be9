#!/usr/bin/env python3
"""
AILearnMaster Open-Source AI Platform
Modal A100 GPU deployment with EchoMimic V2, Mistral/Mixtral, Coqui TTS, and Kokoro TTS
"""

import modal
import os
import time
import base64
import tempfile
import subprocess
import json
from typing import Dict, Any, List, Optional
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create Modal app
app = modal.App("courseai-opensource")

# Create shared volume for model storage and caching
shared_volume = modal.Volume.from_name("courseai-models", create_if_missing=True)

# Base image with Python and system dependencies
base_image = (
    modal.Image.debian_slim(python_version="3.11")
    .apt_install([
        # System dependencies
        "git", "wget", "curl", "unzip", "ffmpeg", "espeak", "espeak-data",
        # Build tools
        "build-essential", "cmake", "pkg-config",
        # Media processing
        "libsndfile1-dev", "libportaudio2", "libportaudiocpp0",
        # Graphics and CV
        "libgl1-mesa-glx", "libglib2.0-0", "libsm6", "libxext6", 
        "libxrender-dev", "libgomp1", "libgoogle-perftools4",
        # Node.js for Marp
        "nodejs", "npm"
    ])
    .run_commands([
        # Install Marp CLI
        "npm install -g @marp-team/marp-cli",
        # Create directories
        "mkdir -p /app/models /app/cache /app/temp"
    ])
)

# GPU image with AI/ML packages optimized for open-source models
gpu_image = (
    base_image
    .pip_install([
        # Core ML packages
        "torch>=2.1.0", "torchvision>=0.16.0", "torchaudio>=2.1.0",
        "transformers>=4.35.0", "accelerate>=0.24.1", "diffusers>=0.21.4",
        
        # Quantization and optimization
        "bitsandbytes>=0.41.0", "optimum>=1.14.0", "auto-gptq>=0.4.2",
        
        # Computer Vision & Image Processing
        "opencv-python>=4.8.0", "pillow>=10.0.0", "imageio>=2.31.0",
        "imageio-ffmpeg>=0.4.9", "scikit-image>=0.21.0",
        
        # Audio Processing
        "librosa>=0.10.1", "soundfile>=0.12.1", "pydub>=0.25.1",
        "resampy>=0.4.2", "pyaudio>=0.2.11",
        
        # TTS Models (Coqui TTS)
        "TTS>=0.22.0", "phonemizer>=3.2.1",
        
        # Scientific Computing
        "numpy>=1.24.0", "scipy>=1.11.0", "matplotlib>=3.7.0",
        
        # Web Framework
        "fastapi>=0.104.0", "uvicorn>=0.24.0", "requests>=2.31.0",
        
        # Utilities
        "huggingface-hub", "safetensors", "einops", "omegaconf", "hydra-core"
    ])
    .run_commands([
        # Clone EchoMimic V2 repository
        "cd /app && git clone https://github.com/BadToBest/EchoMimic.git echomimic",
        "cd /app/echomimic && pip install -r requirements.txt",
        
        # Clone Kokoro TTS repository
        "cd /app && git clone https://github.com/hexgrad/kokoro.git kokoro",
        "cd /app/kokoro && pip install -r requirements.txt",
        
        # Download Mistral 7B model (quantized)
        "mkdir -p /app/models/mistral",
        
        # Set up model directories
        "mkdir -p /app/models/echomimic /app/models/coqui /app/models/kokoro"
    ])
)

@app.function(
    image=gpu_image,
    gpu="A100-80GB",
    volumes={"/app/storage": shared_volume},
    timeout=300,
    memory=16384,
    min_containers=0
)
def health_check() -> Dict[str, Any]:
    """Comprehensive health check for open-source AI services"""
    import torch
    import psutil
    
    try:
        # GPU Information
        gpu_available = torch.cuda.is_available()
        gpu_info = {}
        if gpu_available:
            gpu_info = {
                "name": torch.cuda.get_device_name(0),
                "memory_total_gb": round(torch.cuda.get_device_properties(0).total_memory / 1024**3, 2),
                "memory_allocated_gb": round(torch.cuda.memory_allocated(0) / 1024**3, 2),
                "memory_free_gb": round((torch.cuda.get_device_properties(0).total_memory - torch.cuda.memory_allocated(0)) / 1024**3, 2)
            }
        
        # System Information
        system_info = {
            "cpu_count": psutil.cpu_count(),
            "memory_total_gb": round(psutil.virtual_memory().total / 1024**3, 2),
            "memory_available_gb": round(psutil.virtual_memory().available / 1024**3, 2)
        }
        
        # Check service availability
        services = {}
        
        # Check EchoMimic V2
        echomimic_available = os.path.exists("/app/echomimic")
        services["echomimic_v2"] = echomimic_available
        
        # Check Mistral model
        try:
            from transformers import AutoTokenizer
            services["mistral_ready"] = True
        except Exception:
            services["mistral_ready"] = False
        
        # Check Coqui TTS
        try:
            import TTS
            services["coqui_tts"] = True
        except Exception:
            services["coqui_tts"] = False
        
        # Check Kokoro TTS
        kokoro_available = os.path.exists("/app/kokoro")
        services["kokoro_tts"] = kokoro_available
        
        # Check Marp CLI
        try:
            result = subprocess.run(['marp', '--version'], capture_output=True, text=True)
            services["marp_cli"] = result.returncode == 0
        except Exception:
            services["marp_cli"] = False
        
        return {
            "status": "healthy",
            "timestamp": int(time.time() * 1000),
            "platform": "Open-Source AI Platform",
            "gpu_available": gpu_available,
            "gpu_info": gpu_info,
            "system_info": system_info,
            "services": services,
            "capabilities": {
                "avatar_generation": "EchoMimic V2",
                "language_model": "Mistral/Mixtral",
                "text_to_speech": "Coqui TTS + Kokoro",
                "slide_generation": "Marp CLI",
                "quantization": "4-bit/8-bit support"
            }
        }
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return {
            "status": "error",
            "error": str(e),
            "timestamp": int(time.time() * 1000)
        }

@app.function(
    image=gpu_image,
    gpu="A100-80GB",
    volumes={"/app/storage": shared_volume},
    timeout=600,
    memory=32768
)
def mistral_generate_text(
    prompt: str,
    max_length: int = 512,
    temperature: float = 0.7,
    top_p: float = 0.9,
    use_quantization: bool = True
) -> Dict[str, Any]:
    """Generate text using quantized Mistral 7B model"""
    try:
        import torch
        from transformers import AutoTokenizer, AutoModelForCausalLM, BitsAndBytesConfig
        
        model_name = "mistralai/Mistral-7B-Instruct-v0.1"
        
        # Configure quantization for A100 80GB optimization
        if use_quantization:
            quantization_config = BitsAndBytesConfig(
                load_in_4bit=True,
                bnb_4bit_compute_dtype=torch.float16,
                bnb_4bit_use_double_quant=True,
                bnb_4bit_quant_type="nf4"
            )
        else:
            quantization_config = None
        
        # Load tokenizer and model
        tokenizer = AutoTokenizer.from_pretrained(model_name)
        model = AutoModelForCausalLM.from_pretrained(
            model_name,
            quantization_config=quantization_config,
            device_map="auto",
            torch_dtype=torch.float16 if not use_quantization else None,
            trust_remote_code=True
        )
        
        # Format prompt for Mistral
        formatted_prompt = f"<s>[INST] {prompt} [/INST]"
        
        # Tokenize input
        inputs = tokenizer(formatted_prompt, return_tensors="pt").to(model.device)
        
        # Generate text
        with torch.no_grad():
            outputs = model.generate(
                **inputs,
                max_length=max_length,
                temperature=temperature,
                top_p=top_p,
                do_sample=True,
                pad_token_id=tokenizer.eos_token_id
            )
        
        # Decode output
        generated_text = tokenizer.decode(outputs[0], skip_special_tokens=True)
        
        # Extract only the generated part (remove the prompt)
        response = generated_text[len(formatted_prompt):].strip()
        
        return {
            "status": "success",
            "generated_text": response,
            "model": model_name,
            "quantized": use_quantization,
            "prompt_length": len(prompt),
            "response_length": len(response),
            "gpu_memory_used_gb": round(torch.cuda.memory_allocated(0) / 1024**3, 2),
            "timestamp": int(time.time() * 1000)
        }
        
    except Exception as e:
        logger.error(f"Mistral text generation failed: {e}")
        return {
            "status": "error",
            "error": str(e),
            "model": "mistralai/Mistral-7B-Instruct-v0.1",
            "timestamp": int(time.time() * 1000)
        }

@app.function(
    image=gpu_image,
    gpu="A100-80GB",
    volumes={"/app/storage": shared_volume},
    timeout=300,
    memory=16384
)
def coqui_tts_generate(
    text: str,
    voice_name: str = "tts_models/en/ljspeech/tacotron2-DDC",
    language: str = "en"
) -> Dict[str, Any]:
    """Generate speech using Coqui TTS"""
    try:
        import torch
        from TTS.api import TTS
        
        device = "cuda" if torch.cuda.is_available() else "cpu"
        
        # Initialize TTS model
        tts = TTS(voice_name).to(device)
        
        # Create temp file for output
        temp_dir = tempfile.mkdtemp()
        output_path = os.path.join(temp_dir, "output.wav")
        
        # Generate speech
        tts.tts_to_file(text=text, file_path=output_path)
        
        # Read and encode audio
        with open(output_path, "rb") as f:
            audio_data = f.read()
            audio_base64 = base64.b64encode(audio_data).decode()
        
        # Cleanup
        import shutil
        shutil.rmtree(temp_dir)
        
        return {
            "status": "success",
            "audio_base64": audio_base64,
            "text": text,
            "voice": voice_name,
            "language": language,
            "engine": "coqui_tts",
            "gpu_used": torch.cuda.is_available(),
            "timestamp": int(time.time() * 1000)
        }
        
    except Exception as e:
        logger.error(f"Coqui TTS generation failed: {e}")
        return {
            "status": "error",
            "error": str(e),
            "text": text,
            "voice": voice_name,
            "timestamp": int(time.time() * 1000)
        }

@app.function(
    image=gpu_image,
    gpu="A100-80GB",
    volumes={"/app/storage": shared_volume},
    timeout=900,  # 15 minutes for video generation
    memory=40960  # 40GB RAM for video processing
)
def echomimic_generate_avatar(
    image_base64: str,
    audio_base64: str,
    pose_style: int = 0,
    expression_scale: float = 1.0,
    output_format: str = "mp4"
) -> Dict[str, Any]:
    """Generate avatar video using EchoMimic V2"""
    try:
        import torch
        import cv2
        import numpy as np
        from PIL import Image
        import io

        # Create temp directory
        temp_dir = tempfile.mkdtemp()

        # Decode input image
        image_data = base64.b64decode(image_base64)
        image_path = os.path.join(temp_dir, "input_image.jpg")
        with open(image_path, "wb") as f:
            f.write(image_data)

        # Decode input audio
        audio_data = base64.b64decode(audio_base64)
        audio_path = os.path.join(temp_dir, "input_audio.wav")
        with open(audio_path, "wb") as f:
            f.write(audio_data)

        # Output video path
        output_path = os.path.join(temp_dir, f"output.{output_format}")

        # Run EchoMimic V2 inference
        cmd = [
            "python", "/app/echomimic/inference.py",
            "--source_image", image_path,
            "--driving_audio", audio_path,
            "--output", output_path,
            "--pose_style", str(pose_style),
            "--expression_scale", str(expression_scale)
        ]

        result = subprocess.run(cmd, capture_output=True, text=True, timeout=800)

        if result.returncode != 0:
            return {
                "status": "error",
                "error": f"EchoMimic V2 failed: {result.stderr}",
                "timestamp": int(time.time() * 1000)
            }

        # Read and encode output video
        if os.path.exists(output_path):
            with open(output_path, "rb") as f:
                video_data = f.read()
                video_base64 = base64.b64encode(video_data).decode()
        else:
            return {
                "status": "error",
                "error": "Output video not generated",
                "timestamp": int(time.time() * 1000)
            }

        # Get video info
        cap = cv2.VideoCapture(output_path)
        fps = cap.get(cv2.CAP_PROP_FPS)
        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        duration = frame_count / fps if fps > 0 else 0
        cap.release()

        # Cleanup
        import shutil
        shutil.rmtree(temp_dir)

        return {
            "status": "success",
            "video_base64": video_base64,
            "duration_seconds": duration,
            "fps": fps,
            "frame_count": frame_count,
            "pose_style": pose_style,
            "expression_scale": expression_scale,
            "output_format": output_format,
            "engine": "echomimic_v2",
            "gpu_used": torch.cuda.is_available(),
            "timestamp": int(time.time() * 1000)
        }

    except Exception as e:
        logger.error(f"EchoMimic V2 avatar generation failed: {e}")
        return {
            "status": "error",
            "error": str(e),
            "timestamp": int(time.time() * 1000)
        }

@app.function(
    image=gpu_image,
    gpu="A100-80GB",
    volumes={"/app/storage": shared_volume},
    timeout=300,
    memory=16384
)
def kokoro_tts_generate(
    text: str,
    voice: str = "af_sarah",
    speed: float = 1.0
) -> Dict[str, Any]:
    """Generate speech using Kokoro TTS (fallback)"""
    try:
        # Create temp directory
        temp_dir = tempfile.mkdtemp()
        output_path = os.path.join(temp_dir, "output.wav")

        # Check if Kokoro is available
        kokoro_available = os.path.exists("/app/kokoro/kokoro.py")

        if kokoro_available:
            cmd = [
                "python", "/app/kokoro/kokoro.py",
                "--text", text,
                "--voice", voice,
                "--speed", str(speed),
                "--output", output_path
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=180)
            kokoro_success = result.returncode == 0
        else:
            kokoro_success = False

        # Fallback to espeak if Kokoro fails
        if not kokoro_success:
            espeak_cmd = ["espeak", "-w", output_path, "-s", str(int(speed * 175)), text]
            espeak_result = subprocess.run(espeak_cmd, capture_output=True, text=True)

            if espeak_result.returncode != 0:
                return {
                    "status": "error",
                    "error": "Both Kokoro and espeak failed",
                    "timestamp": int(time.time() * 1000)
                }

        # Read and encode audio
        with open(output_path, "rb") as f:
            audio_data = f.read()
            audio_base64 = base64.b64encode(audio_data).decode()

        # Cleanup
        import shutil
        shutil.rmtree(temp_dir)

        return {
            "status": "success",
            "audio_base64": audio_base64,
            "text": text,
            "voice": voice,
            "speed": speed,
            "engine": "kokoro" if kokoro_success else "espeak",
            "gpu_used": False,
            "timestamp": int(time.time() * 1000)
        }

    except Exception as e:
        logger.error(f"Kokoro TTS generation failed: {e}")
        return {
            "status": "error",
            "error": str(e),
            "text": text,
            "voice": voice,
            "timestamp": int(time.time() * 1000)
        }

@app.function(
    image=gpu_image,
    volumes={"/app/storage": shared_volume},
    timeout=300,
    memory=8192
)
def marp_generate_slides(
    markdown_content: str,
    theme: str = "default",
    output_format: str = "html"
) -> Dict[str, Any]:
    """Generate slides using Marp CLI"""
    try:
        # Create temp directory
        temp_dir = tempfile.mkdtemp()

        # Write markdown content
        md_path = os.path.join(temp_dir, "slides.md")
        with open(md_path, "w", encoding="utf-8") as f:
            f.write(markdown_content)

        # Output path
        output_ext = "html" if output_format == "html" else "pdf"
        output_path = os.path.join(temp_dir, f"slides.{output_ext}")

        # Marp command
        cmd = [
            "marp",
            md_path,
            "--theme", theme,
            "--output", output_path
        ]

        if output_format == "pdf":
            cmd.extend(["--pdf"])

        result = subprocess.run(cmd, capture_output=True, text=True, timeout=180)

        if result.returncode != 0:
            return {
                "status": "error",
                "error": f"Marp generation failed: {result.stderr}",
                "timestamp": int(time.time() * 1000)
            }

        # Read and encode output
        with open(output_path, "rb") as f:
            output_data = f.read()
            output_base64 = base64.b64encode(output_data).decode()

        # Cleanup
        import shutil
        shutil.rmtree(temp_dir)

        return {
            "status": "success",
            "slides_base64": output_base64,
            "format": output_format,
            "theme": theme,
            "content_length": len(markdown_content),
            "engine": "marp_cli",
            "timestamp": int(time.time() * 1000)
        }

    except Exception as e:
        logger.error(f"Marp slide generation failed: {e}")
        return {
            "status": "error",
            "error": str(e),
            "timestamp": int(time.time() * 1000)
        }

# Web endpoints for API access
@modal.fastapi_endpoint(method="GET")
def health():
    """Health check endpoint"""
    return health_check.remote()

@modal.fastapi_endpoint(method="POST")
def generate_text(request_data: Dict[str, Any]):
    """Mistral text generation endpoint"""
    prompt = request_data.get("prompt", "")
    max_length = request_data.get("max_length", 512)
    temperature = request_data.get("temperature", 0.7)
    top_p = request_data.get("top_p", 0.9)
    use_quantization = request_data.get("use_quantization", True)

    if not prompt:
        return {"status": "error", "error": "Prompt is required"}

    return mistral_generate_text.remote(prompt, max_length, temperature, top_p, use_quantization)

@modal.fastapi_endpoint(method="POST")
def generate_speech(request_data: Dict[str, Any]):
    """Coqui TTS speech generation endpoint"""
    text = request_data.get("text", "")
    voice_name = request_data.get("voice_name", "tts_models/en/ljspeech/tacotron2-DDC")
    language = request_data.get("language", "en")

    if not text:
        return {"status": "error", "error": "Text is required"}

    return coqui_tts_generate.remote(text, voice_name, language)

@modal.fastapi_endpoint(method="POST")
def generate_speech_fallback(request_data: Dict[str, Any]):
    """Kokoro TTS speech generation endpoint (fallback)"""
    text = request_data.get("text", "")
    voice = request_data.get("voice", "af_sarah")
    speed = request_data.get("speed", 1.0)

    if not text:
        return {"status": "error", "error": "Text is required"}

    return kokoro_tts_generate.remote(text, voice, speed)

@modal.fastapi_endpoint(method="POST")
def generate_avatar(request_data: Dict[str, Any]):
    """EchoMimic V2 avatar generation endpoint"""
    image_base64 = request_data.get("image_base64", "")
    audio_base64 = request_data.get("audio_base64", "")
    pose_style = request_data.get("pose_style", 0)
    expression_scale = request_data.get("expression_scale", 1.0)
    output_format = request_data.get("output_format", "mp4")

    if not image_base64 or not audio_base64:
        return {"status": "error", "error": "Both image and audio are required"}

    return echomimic_generate_avatar.remote(image_base64, audio_base64, pose_style, expression_scale, output_format)

@modal.fastapi_endpoint(method="POST")
def generate_slides(request_data: Dict[str, Any]):
    """Marp slide generation endpoint"""
    markdown_content = request_data.get("markdown_content", "")
    theme = request_data.get("theme", "default")
    output_format = request_data.get("output_format", "html")

    if not markdown_content:
        return {"status": "error", "error": "Markdown content is required"}

    return marp_generate_slides.remote(markdown_content, theme, output_format)

if __name__ == "__main__":
    print("AILearnMaster Open-Source AI Platform")
    print("Ready for deployment to Modal A100 GPU")
