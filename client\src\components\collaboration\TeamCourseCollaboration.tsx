import { useState, useEffect } from 'react';
import { useMutation, useQuery } from '@tanstack/react-query';
import { apiRequest, queryClient } from '@/lib/queryClient';
import { useAuth } from '../../hooks/use-auth';
import { useToast } from '../../hooks/use-toast';
import { Loader2, UserPlus, Mail, Check, X, ChevronDown, Calendar, MessageSquare, ClipboardList, FileEdit } from 'lucide-react';
import { Button } from '../../components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '../../components/ui/dialog';
import { Input } from '../../components/ui/input';
import { Label } from '../../components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../components/ui/select';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '../../components/ui/tabs';
import { Avatar, AvatarFallback, AvatarImage } from '../../components/ui/avatar';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '../../components/ui/card';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '../../components/ui/dropdown-menu';
import { Badge } from '../../components/ui/badge';
import { Separator } from '../../components/ui/separator';
import { CollaborationChat } from './CollaborationChat';
import { cn } from '../../lib/utils';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '../../components/ui/tooltip';
import { ScrollArea } from '../../components/ui/scroll-area';
import SharedCalendar from './SharedCalendar';
import TasksBoard from './TasksBoard';
import SharedDocuments from './SharedDocuments';

interface Team {
  id: number;
  name: string;
  description: string | null;
  ownerId: number;
  avatarUrl: string | null;
  createdAt: string;
  updatedAt: string;
}

interface TeamMember {
  userId: number;
  teamId: number;
  role: string;
  status: string;
  username: string;
  name: string | null;
  email: string;
  avatarUrl: string | null;
}

interface CourseCollaborator {
  courseId: number;
  userId: number;
  role: string;
  canEdit: boolean;
  addedAt: string;
  addedById: number;
  username: string;
  name: string | null;
  email: string;
  avatarUrl: string | null;
}

interface TeamCourseCollaborationProps {
  courseId: number;
  teamId?: number;
  onTeamSelected?: (teamId: number) => void;
}

export default function TeamCourseCollaboration({ courseId, teamId: initialTeamId, onTeamSelected }: TeamCourseCollaborationProps) {
  const { user } = useAuth();
  const { toast } = useToast();
  const [isInviteDialogOpen, setIsInviteDialogOpen] = useState(false);
  const [inviteEmail, setInviteEmail] = useState('');
  const [inviteRole, setInviteRole] = useState('editor');
  const [selectedTeamId, setSelectedTeamId] = useState<number | undefined>(initialTeamId);
  const [activeTab, setActiveTab] = useState('collaborators');
  
  // Fetch user's teams
  const { data: teams, isLoading: isTeamsLoading } = useQuery<Team[]>({
    queryKey: ["/api/teams"],
    enabled: !!user,
  });
  
  // Fetch team members if a team is selected
  const { data: teamMembers, isLoading: isTeamMembersLoading } = useQuery<TeamMember[]>({
    queryKey: [`/api/teams/${selectedTeamId}/members`],
    enabled: !!selectedTeamId,
  });
  
  // Fetch course collaborators
  const { data: collaborators, isLoading: isCollaboratorsLoading } = useQuery<CourseCollaborator[]>({
    queryKey: [`/api/courses/${courseId}/collaborators`],
    enabled: !!courseId,
  });
  
  // Add course to team mutation
  const addCourseToTeamMutation = useMutation({
    mutationFn: async (teamId: number) => {
      const response = await apiRequest("POST", `/api/teams/${teamId}/courses`, { courseId });
      return response.json();
    },
    onSuccess: () => {
      toast({
        title: "Course added to team",
        description: "Team members can now access and collaborate on this course",
      });
      
      queryClient.invalidateQueries({ queryKey: [`/api/courses/${courseId}/collaborators`] });
    },
    onError: (error) => {
      toast({
        title: "Failed to add course to team",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive",
      });
    },
  });
  
  // Add collaborator mutation
  const addCollaboratorMutation = useMutation({
    mutationFn: async (data: { email: string; role: string; canEdit: boolean }) => {
      const response = await apiRequest("POST", `/api/courses/${courseId}/collaborators`, data);
      return response.json();
    },
    onSuccess: () => {
      setInviteDialogOpen(false);
      setInviteEmail('');
      setInviteRole('editor');
      
      toast({
        title: "Collaborator invited",
        description: "An invitation has been sent to the collaborator",
      });
      
      queryClient.invalidateQueries({ queryKey: [`/api/courses/${courseId}/collaborators`] });
    },
    onError: (error) => {
      toast({
        title: "Failed to invite collaborator",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive",
      });
    },
  });
  
  // Update collaborator role mutation
  const updateCollaboratorRoleMutation = useMutation({
    mutationFn: async ({ userId, role, canEdit }: { userId: number; role: string; canEdit: boolean }) => {
      const response = await apiRequest("PATCH", `/api/courses/${courseId}/collaborators/${userId}`, { role, canEdit });
      return response.json();
    },
    onSuccess: () => {
      toast({
        title: "Collaborator updated",
        description: "The collaborator's permissions have been updated",
      });
      
      queryClient.invalidateQueries({ queryKey: [`/api/courses/${courseId}/collaborators`] });
    },
    onError: (error) => {
      toast({
        title: "Failed to update collaborator",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive",
      });
    },
  });
  
  // Remove collaborator mutation
  const removeCollaboratorMutation = useMutation({
    mutationFn: async (userId: number) => {
      const response = await apiRequest("DELETE", `/api/courses/${courseId}/collaborators/${userId}`);
      return response.json();
    },
    onSuccess: () => {
      toast({
        title: "Collaborator removed",
        description: "The collaborator has been removed from this course",
      });
      
      queryClient.invalidateQueries({ queryKey: [`/api/courses/${courseId}/collaborators`] });
    },
    onError: (error) => {
      toast({
        title: "Failed to remove collaborator",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive",
      });
    },
  });
  
  // Handle team selection
  const handleTeamSelect = (teamId: string) => {
    const id = parseInt(teamId);
    setSelectedTeamId(id);
    if (onTeamSelected) {
      onTeamSelected(id);
    }
    
    // Add course to team if not already added
    addCourseToTeamMutation.mutate(id);
  };
  
  // Handle inviting a collaborator
  const handleInviteCollaborator = () => {
    if (!inviteEmail.trim()) {
      toast({
        title: "Email required",
        description: "Please enter an email address",
        variant: "destructive",
      });
      return;
    }
    
    addCollaboratorMutation.mutate({
      email: inviteEmail,
      role: inviteRole,
      canEdit: inviteRole === 'editor' || inviteRole === 'owner',
    });
  };
  
  // Get initials for avatar
  const getInitials = (name: string | null, username: string): string => {
    if (name) {
      return name.split(' ')
        .map(part => part[0])
        .join('')
        .toUpperCase()
        .substring(0, 2);
    }
    return username.substring(0, 2).toUpperCase();
  };

  // Get role badge styling
  const getRoleBadgeStyles = (role: string) => {
    switch (role.toLowerCase()) {
      case 'owner':
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300";
      case 'admin':
        return "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300";
      case 'editor':
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300";
      case 'viewer':
        return "bg-slate-100 text-slate-800 dark:bg-slate-800 dark:text-slate-300";
      default:
        return "bg-slate-100 text-slate-800 dark:bg-slate-800 dark:text-slate-300";
    }
  };
  
  return (
    <div className="space-y-6">
      {/* Team Selection */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium">Team Collaboration</h3>
          <p className="text-sm text-muted-foreground">Share and collaborate on this course with your team</p>
        </div>
        
        <div className="flex items-center gap-3">
          <Select value={selectedTeamId?.toString()} onValueChange={handleTeamSelect}>
            <SelectTrigger className="w-[200px]">
              <SelectValue placeholder="Select a team" />
            </SelectTrigger>
            <SelectContent>
              {isTeamsLoading ? (
                <div className="flex items-center justify-center py-4">
                  <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
                </div>
              ) : teams?.length ? (
                teams.map(team => (
                  <SelectItem key={team.id} value={team.id.toString()}>
                    {team.name}
                  </SelectItem>
                ))
              ) : (
                <div className="px-2 py-4 text-center text-sm text-muted-foreground">
                  No teams available
                </div>
              )}
            </SelectContent>
          </Select>
          
          <Button variant="outline" size="sm" asChild>
            <a href="/teams">Manage Teams</a>
          </Button>
        </div>
      </div>
      
      {/* Collaboration Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid grid-cols-4 mb-4">
          <TabsTrigger value="collaborators" className="flex items-center">
            <UserPlus className="h-4 w-4 mr-2" />
            Collaborators
          </TabsTrigger>
          <TabsTrigger value="calendar" className="flex items-center">
            <Calendar className="h-4 w-4 mr-2" />
            Calendar
          </TabsTrigger>
          <TabsTrigger value="tasks" className="flex items-center">
            <ClipboardList className="h-4 w-4 mr-2" />
            Tasks
          </TabsTrigger>
          <TabsTrigger value="documents" className="flex items-center">
            <FileEdit className="h-4 w-4 mr-2" />
            Documents
          </TabsTrigger>
        </TabsList>
        
        {/* Collaborators Tab */}
        <TabsContent value="collaborators" className="space-y-6">
          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-base">Course Collaborators</CardTitle>
                <Dialog open={isInviteDialogOpen} onOpenChange={setIsInviteDialogOpen}>
                  <DialogTrigger asChild>
                    <Button size="sm">
                      <UserPlus className="h-4 w-4 mr-2" />
                      Invite
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Invite Collaborator</DialogTitle>
                      <DialogDescription>
                        Add a collaborator to work on this course together.
                      </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4 py-4">
                      <div className="space-y-2">
                        <Label htmlFor="email">Email address</Label>
                        <Input
                          id="email"
                          type="email"
                          placeholder="<EMAIL>"
                          value={inviteEmail}
                          onChange={(e) => setInviteEmail(e.target.value)}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="role">Role</Label>
                        <Select value={inviteRole} onValueChange={setInviteRole}>
                          <SelectTrigger id="role">
                            <SelectValue placeholder="Select a role" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="editor">Editor</SelectItem>
                            <SelectItem value="viewer">Viewer</SelectItem>
                          </SelectContent>
                        </Select>
                        <p className="text-xs text-muted-foreground mt-1">
                          {inviteRole === 'editor' ? (
                            'Can edit and make changes to the course'
                          ) : (
                            'Can only view the course content'
                          )}
                        </p>
                      </div>
                    </div>
                    <DialogFooter>
                      <Button 
                        variant="outline" 
                        onClick={() => setIsInviteDialogOpen(false)}
                      >
                        Cancel
                      </Button>
                      <Button 
                        onClick={handleInviteCollaborator}
                        disabled={!inviteEmail.trim() || addCollaboratorMutation.isPending}
                      >
                        {addCollaboratorMutation.isPending ? (
                          <>
                            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                            Inviting...
                          </>
                        ) : (
                          <>Invite</>  
                        )}
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </div>
            </CardHeader>
            <CardContent>
              {isCollaboratorsLoading ? (
                <div className="flex items-center justify-center py-4">
                  <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
                </div>
              ) : !collaborators?.length ? (
                <div className="text-center py-6">
                  <UserPlus className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
                  <h3 className="text-sm font-medium">No collaborators yet</h3>
                  <p className="text-xs text-muted-foreground mt-1 mb-4">Invite people to collaborate on this course</p>
                  <Button 
                    size="sm" 
                    variant="outline" 
                    onClick={() => setIsInviteDialogOpen(true)}
                  >
                    <UserPlus className="h-4 w-4 mr-2" />
                    Invite Collaborator
                  </Button>
                </div>
              ) : (
                <ScrollArea className="max-h-[300px]">
                  <div className="space-y-3">
                    {collaborators.map((collaborator) => (
                      <div key={collaborator.userId} className="flex items-center justify-between p-2 rounded-md hover:bg-muted">
                        <div className="flex items-center gap-3">
                          <Avatar className="h-8 w-8">
                            {collaborator.avatarUrl ? (
                              <AvatarImage src={collaborator.avatarUrl} alt={collaborator.name || collaborator.username} />
                            ) : (
                              <AvatarFallback>
                                {getInitials(collaborator.name, collaborator.username)}
                              </AvatarFallback>
                            )}
                          </Avatar>
                          <div>
                            <div className="font-medium">{collaborator.name || collaborator.username}</div>
                            <div className="text-xs text-muted-foreground">{collaborator.email}</div>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge className={cn("text-xs", getRoleBadgeStyles(collaborator.role))}>
                            {collaborator.role.charAt(0).toUpperCase() + collaborator.role.slice(1)}
                          </Badge>
                          
                          {user?.id !== collaborator.userId && (
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="icon" className="h-8 w-8">
                                  <ChevronDown className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem
                                  onClick={() => updateCollaboratorRoleMutation.mutate({userId: collaborator.userId, role: 'editor', canEdit: true})}
                                >
                                  Make Editor
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  onClick={() => updateCollaboratorRoleMutation.mutate({userId: collaborator.userId, role: 'viewer', canEdit: false})}
                                >
                                  Make Viewer
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  onClick={() => removeCollaboratorMutation.mutate(collaborator.userId)}
                                  className="text-red-500"
                                >
                                  Remove
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              )}
            </CardContent>
          </Card>
          
          {/* Team Members Section */}
          {selectedTeamId && (
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-base">Team Members</CardTitle>
                <CardDescription>
                  Team members can be invited to collaborate on this course
                </CardDescription>
              </CardHeader>
              <CardContent>
                {isTeamMembersLoading ? (
                  <div className="flex items-center justify-center py-4">
                    <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
                  </div>
                ) : !teamMembers?.length ? (
                  <div className="text-center py-6">
                    <UserPlus className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
                    <h3 className="text-sm font-medium">No team members</h3>
                    <p className="text-xs text-muted-foreground mt-1">This team has no members yet</p>
                  </div>
                ) : (
                  <ScrollArea className="max-h-[300px]">
                    <div className="space-y-3">
                      {teamMembers.map((member) => {
                        // Check if this team member is already a collaborator
                        const isCollaborator = collaborators?.some(c => c.userId === member.userId);
                        
                        return (
                          <div key={member.userId} className="flex items-center justify-between p-2 rounded-md hover:bg-muted">
                            <div className="flex items-center gap-3">
                              <Avatar className="h-8 w-8">
                                {member.avatarUrl ? (
                                  <AvatarImage src={member.avatarUrl} alt={member.name || member.username} />
                                ) : (
                                  <AvatarFallback>
                                    {getInitials(member.name, member.username)}
                                  </AvatarFallback>
                                )}
                              </Avatar>
                              <div>
                                <div className="font-medium">{member.name || member.username}</div>
                                <div className="text-xs text-muted-foreground">{member.email}</div>
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              <Badge className={cn("text-xs", getRoleBadgeStyles(member.role))}>
                                {member.role.charAt(0).toUpperCase() + member.role.slice(1)}
                              </Badge>
                              
                              {!isCollaborator && user?.id !== member.userId && (
                                <Button 
                                  size="sm" 
                                  variant="outline" 
                                  onClick={() => addCollaboratorMutation.mutate({email: member.email, role: 'editor', canEdit: true})}
                                  disabled={addCollaboratorMutation.isPending}
                                >
                                  {addCollaboratorMutation.isPending ? (
                                    <Loader2 className="h-3 w-3 animate-spin" />
                                  ) : (
                                    <>Invite</>
                                  )}
                                </Button>
                              )}
                              
                              {isCollaborator && (
                                <TooltipProvider>
                                  <Tooltip>
                                    <TooltipTrigger asChild>
                                      <Check className="h-4 w-4 text-green-500" />
                                    </TooltipTrigger>
                                    <TooltipContent>
                                      <p>Already a collaborator</p>
                                    </TooltipContent>
                                  </Tooltip>
                                </TooltipProvider>
                              )}
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </ScrollArea>
                )}
              </CardContent>
            </Card>
          )}
        </TabsContent>
        
        {/* Calendar Tab */}
        <TabsContent value="calendar" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Team Calendar</CardTitle>
              <CardDescription>
                Schedule and coordinate course creation activities with your team
              </CardDescription>
            </CardHeader>
            <CardContent>
              <SharedCalendar courseId={courseId} teamId={selectedTeamId} />
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Tasks Tab */}
        <TabsContent value="tasks" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Course Tasks</CardTitle>
              <CardDescription>
                Track and assign course development tasks to team members
              </CardDescription>
            </CardHeader>
            <CardContent>
              <TasksBoard courseId={courseId} teamId={selectedTeamId} />
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Documents Tab */}
        <TabsContent value="documents" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Shared Documents</CardTitle>
              <CardDescription>
                Access and manage course-related documents and resources
              </CardDescription>
            </CardHeader>
            <CardContent>
              <SharedDocuments courseId={courseId} teamId={selectedTeamId} />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
      
      {/* Team Chat Section */}
      {selectedTeamId && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base">
              <div className="flex items-center gap-2">
                <MessageSquare className="h-4 w-4 text-primary" />
                Team Chat
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <CollaborationChat courseId={courseId} teamId={selectedTeamId} />
          </CardContent>
        </Card>
      )}
    </div>
  );
}
