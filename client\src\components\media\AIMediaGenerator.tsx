import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import { StabilityImageGenerator } from '@/components/ai/StabilityImageGenerator';

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';

import {
  Sparkles,
  Zap,
  ImageIcon,
  PenLine,
  PaintBucket,
  Palette,
  Lightbulb,
  BookOpen,
} from 'lucide-react';

interface AIMediaGeneratorProps {
  onMediaGenerated?: (media: any) => void;
  courseId?: number;
  lessonId?: number;
}

export function AIMediaGenerator({
  onMediaGenerated,
  courseId,
  lessonId
}: AIMediaGeneratorProps) {
  const [activeTab, setActiveTab] = useState<string>('illustrations');
  const { toast } = useToast();
  
  // Fetch user stats for AI credits info
  const { data: userStats } = useQuery({
    queryKey: ['/api/user-stats'],
    queryFn: async () => {
      const response = await apiRequest('GET', '/api/user-stats');
      if (!response.ok) throw new Error('Failed to fetch user stats');
      return response.json();
    },
  });

  // Handle generated image from StabilityImageGenerator
  const handleGeneratedImage = (imageData: any) => {
    if (imageData && imageData.imageUrl) {
      if (onMediaGenerated) {
        onMediaGenerated(imageData);
      }
      
      toast({
        title: 'Media Created',
        description: 'Your AI-generated media has been added to your library',
      });
    }
  };

  // Image generation suggestion cards for different contexts
  const illustrationSuggestions = [
    {
      title: "Course Cover",
      description: "Professional thumbnail for course",
      prompt: "Professional, clean educational course cover with abstract shapes and modern design, suitable for online learning platform",
      style: "digital-art",
      icon: <BookOpen className="h-8 w-8 text-primary/80" />
    },
    {
      title: "Concept Diagram",
      description: "Visual explanation of a concept",
      prompt: "Clean, minimal diagram explaining a complex concept with clear visual hierarchy, educational style",
      style: "digital-art",
      icon: <Lightbulb className="h-8 w-8 text-primary/80" />
    },
    {
      title: "Infographic Background",
      description: "Background for text overlay",
      prompt: "Abstract gradient background with subtle patterns, perfect for infographic text overlay, soft colors, educational style",
      style: "digital-art",
      icon: <PenLine className="h-8 w-8 text-primary/80" />
    },
    {
      title: "Stylized Icon Set",
      description: "Themed educational icons",
      prompt: "Set of cohesive educational icons with consistent style, arranged in a grid, clean minimal design",
      style: "digital-art",
      icon: <Palette className="h-8 w-8 text-primary/80" />
    }
  ];

  const styleExamples = [
    {
      title: "Photographic",
      description: "Realistic photo style images",
      prompt: "Professional photograph of a student studying in a modern library with natural lighting",
      style: "photographic"
    },
    {
      title: "Digital Art",
      description: "Clean digital illustrations",
      prompt: "Digital art illustration of educational concepts with modern colors and clean lines",
      style: "digital-art"
    },
    {
      title: "Watercolor",
      description: "Artistic watercolor style",
      prompt: "Watercolor painting of an abstract concept related to learning and education",
      style: "watercolor"
    },
    {
      title: "3D Rendering",
      description: "Three-dimensional visuals",
      prompt: "3D rendered educational objects and symbols with soft lighting and clean background",
      style: "3d-model"
    }
  ];

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-bold">AI Media Generation</h2>
          <p className="text-sm text-muted-foreground">Create custom visuals for your courses with Stability AI</p>
        </div>
        {userStats && (
          <Badge variant="outline" className="px-3 py-1">
            <Zap className="h-4 w-4 mr-1.5 text-amber-500" />
            <span>{userStats.aiCredits || 0} AI Credits</span>
          </Badge>
        )}
      </div>
      
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid grid-cols-2 w-full">
          <TabsTrigger value="illustrations">
            <ImageIcon className="h-4 w-4 mr-2" />
            Course Illustrations
          </TabsTrigger>
          <TabsTrigger value="styles">
            <PaintBucket className="h-4 w-4 mr-2" />
            Style Gallery
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="illustrations" className="pt-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {illustrationSuggestions.map((suggestion, index) => (
              <Card key={index}>
                <CardHeader className="pb-2">
                  <div className="flex items-start gap-3">
                    <div className="bg-primary/10 p-2 rounded-md">
                      {suggestion.icon}
                    </div>
                    <div>
                      <CardTitle className="text-lg">{suggestion.title}</CardTitle>
                      <CardDescription>{suggestion.description}</CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="text-xs text-muted-foreground p-2 bg-muted/30 rounded-md mb-4">
                    <span className="font-medium text-xs">Suggested prompt: </span> 
                    {suggestion.prompt}
                  </div>
                </CardContent>
                <CardFooter className="pt-0">
                  <StabilityImageGenerator
                    buttonLabel="Generate"
                    variant="outline"
                    size="sm"
                    className="w-full"
                    contextPrompt={suggestion.prompt}
                    courseId={courseId}
                    lessonId={lessonId}
                    onImageGenerated={handleGeneratedImage}
                    placeholder="Customize this prompt or use as suggested..."
                  />
                </CardFooter>
              </Card>
            ))}
          </div>
          
          <div className="mt-8">
            <div className="flex items-center gap-2 mb-4">
              <Sparkles className="h-5 w-5 text-primary" />
              <h3 className="text-lg font-semibold">Custom Generation</h3>
            </div>
            
            <Card>
              <CardHeader>
                <CardTitle>Create Custom Visual</CardTitle>
                <CardDescription>
                  Generate a completely custom image for your course
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm mb-4">
                  Describe exactly what you want to create for your course. Be specific about style, content, and context.
                </p>
                <div className="bg-muted p-3 rounded-md text-xs">
                  <p className="font-medium mb-1">Tips for better results:</p>
                  <ul className="space-y-1 list-disc pl-4">
                    <li>Include details about style, colors, and mood</li>
                    <li>Specify if you need space for text overlay</li>
                    <li>Mention if it's for a specific course topic</li>
                    <li>Request specific composition (centered, landscape, etc.)</li>
                  </ul>
                </div>
              </CardContent>
              <CardFooter>
                <StabilityImageGenerator
                  buttonLabel="Create Custom Image"
                  variant="default"
                  className="w-full"
                  courseId={courseId}
                  lessonId={lessonId}
                  onImageGenerated={handleGeneratedImage}
                  placeholder="Describe the image you want to create..."
                />
              </CardFooter>
            </Card>
          </div>
        </TabsContent>
        
        <TabsContent value="styles" className="pt-4">
          <div className="mb-4">
            <p className="text-sm">
              Explore different visual styles for your course content. Each style gives a unique look to your educational materials.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {styleExamples.map((style, index) => (
              <Card key={index}>
                <CardHeader>
                  <CardTitle>{style.title}</CardTitle>
                  <CardDescription>{style.description}</CardDescription>
                </CardHeader>
                <CardContent className="pb-2">
                  <div className="aspect-video bg-muted/40 rounded-md mb-3 flex items-center justify-center text-sm text-muted-foreground">
                    <Palette className="h-10 w-10 opacity-40 mb-2" />
                  </div>
                  <div className="text-xs text-muted-foreground">
                    <span className="font-medium">Example prompt: </span> 
                    {style.prompt}
                  </div>
                </CardContent>
                <CardFooter>
                  <StabilityImageGenerator
                    buttonLabel="Try This Style"
                    variant="outline"
                    size="sm"
                    className="w-full"
                    contextPrompt={style.prompt}
                    courseId={courseId}
                    lessonId={lessonId}
                    onImageGenerated={handleGeneratedImage}
                    showAdvancedOptions={true}
                  />
                </CardFooter>
              </Card>
            ))}
          </div>
          
          <div className="mt-8">
            <Separator className="my-6" />
            <div className="text-center">
              <h3 className="text-lg font-semibold mb-2">Want to explore more styles?</h3>
              <p className="text-sm text-muted-foreground mb-4">
                Use the custom generator to access all 16 style presets including anime, comic book, pixel art, and more.
              </p>
              <StabilityImageGenerator
                buttonLabel="Open Full Generator"
                variant="default"
                courseId={courseId}
                lessonId={lessonId}
                onImageGenerated={handleGeneratedImage}
              />
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}