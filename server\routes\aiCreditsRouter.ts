import express, { Request, Response } from 'express';
import { db } from '../db';
import { aiCredits, aiUsageHistory } from '@shared/schema';
import { eq, desc, and, gte, lte } from 'drizzle-orm';
import { z } from 'zod';
import { sql } from 'drizzle-orm';

const router = express.Router();

// Middleware to check if user is authenticated
const isAuthenticated = (req: Request, res: Response, next: Function) => {
  if (req.session?.userId) {
    // Add user to request object for consistent access pattern
    req.user = { id: req.session.userId };
    return next();
  }
  res.status(401).json({ message: 'Unauthorized' });
};

// Get user's AI credits
router.get('/', isAuthenticated, async (req: Request, res: Response) => {
  try {
    const userId = req.user.id;
    const userCredits = await db.select().from(aiCredits).where(eq(aiCredits.userId, userId));
    
    if (userCredits.length === 0) {
      // Create initial credits record if it doesn't exist
      const now = new Date();
      const nextMonth = new Date(now.getFullYear(), now.getMonth() + 1, 1);
      
      const [newCredits] = await db.insert(aiCredits).values({
        userId,
        totalCredits: 100, // Free tier starting credits
        usedCredits: 0,
        nextRefill: nextMonth,
        plan: 'free'
      }).returning();
      
      return res.json(newCredits);
    }
    
    return res.json(userCredits[0]);
  } catch (error) {
    console.error('Error fetching AI credits:', error);
    res.status(500).json({ message: 'Failed to fetch AI credits' });
  }
});

// Get usage history
router.get('/history', isAuthenticated, async (req: Request, res: Response) => {
  try {
    const userId = req.user.id;
    const { period, feature, limit = 20, offset = 0 } = req.query;
    
    // Build query conditions
    let conditions = [eq(aiUsageHistory.userId, userId)];
    
    // Filter by period
    if (period) {
      const now = new Date();
      let startDate: Date;
      
      switch (period) {
        case 'day':
          startDate = new Date(now.setHours(0, 0, 0, 0));
          break;
        case 'week':
          const day = now.getDay();
          startDate = new Date(now.setDate(now.getDate() - day));
          startDate.setHours(0, 0, 0, 0);
          break;
        case 'month':
          startDate = new Date(now.getFullYear(), now.getMonth(), 1);
          break;
        case 'year':
          startDate = new Date(now.getFullYear(), 0, 1);
          break;
        default:
          startDate = new Date(0); // Default to all time
      }
      
      conditions.push(gte(aiUsageHistory.createdAt, startDate));
    }
    
    // Filter by feature
    if (feature) {
      conditions.push(eq(aiUsageHistory.feature, feature as string));
    }
    
    // Get history with pagination
    const history = await db.select()
      .from(aiUsageHistory)
      .where(and(...conditions))
      .orderBy(desc(aiUsageHistory.createdAt))
      .limit(Number(limit))
      .offset(Number(offset));
    
    // Get total count for pagination
    const [{ count }] = await db.select({
      count: sql`count(*)`.mapWith(Number)
    })
    .from(aiUsageHistory)
    .where(and(...conditions));
    
    res.json({
      data: history,
      count,
      limit: Number(limit),
      offset: Number(offset)
    });
  } catch (error) {
    console.error('Error fetching AI usage history:', error);
    res.status(500).json({ message: 'Failed to fetch AI usage history' });
  }
});

// Purchase more credits
router.post('/purchase', isAuthenticated, async (req: Request, res: Response) => {
  try {
    const userId = req.user.id;
    const { amount } = req.body;
    
    // Validate input
    const schema = z.object({
      amount: z.number().int().positive()
    });
    
    const result = schema.safeParse({ amount });
    if (!result.success) {
      return res.status(400).json({ message: 'Invalid input', errors: result.error.format() });
    }
    
    // Update credits
    const [updatedCredits] = await db
      .update(aiCredits)
      .set({
        totalCredits: sql`${aiCredits.totalCredits} + ${amount}`,
        updatedAt: new Date()
      })
      .where(eq(aiCredits.userId, userId))
      .returning();
    
    res.json(updatedCredits);
  } catch (error) {
    console.error('Error purchasing AI credits:', error);
    res.status(500).json({ message: 'Failed to purchase AI credits' });
  }
});

// Upgrade plan
router.post('/upgrade-plan', isAuthenticated, async (req: Request, res: Response) => {
  try {
    const userId = req.user.id;
    const { plan } = req.body;
    
    // Validate input
    const schema = z.object({
      plan: z.enum(['free', 'starter', 'pro', 'business', 'enterprise'])
    });
    
    const result = schema.safeParse({ plan });
    if (!result.success) {
      return res.status(400).json({ message: 'Invalid plan type', errors: result.error.format() });
    }
    
    // Define credits based on plan
    let additionalCredits = 0;
    switch (plan) {
      case 'starter':
        additionalCredits = 500;
        break;
      case 'pro':
        additionalCredits = 2000;
        break;
      case 'business':
        additionalCredits = 5000;
        break;
      case 'enterprise':
        additionalCredits = 10000;
        break;
    }
    
    // Update plan and add credits
    const [updatedCredits] = await db
      .update(aiCredits)
      .set({
        plan,
        totalCredits: sql`${aiCredits.totalCredits} + ${additionalCredits}`,
        updatedAt: new Date()
      })
      .where(eq(aiCredits.userId, userId))
      .returning();
    
    res.json(updatedCredits);
  } catch (error) {
    console.error('Error upgrading plan:', error);
    res.status(500).json({ message: 'Failed to upgrade plan' });
  }
});

// Consume credits
router.post('/consume', isAuthenticated, async (req: Request, res: Response) => {
  try {
    const userId = req.user.id;
    const { credits, feature, model, courseId, courseName, tokenCount, characterCount, imageCount, prompt } = req.body;
    
    // Validate input
    const schema = z.object({
      credits: z.number().int().positive(),
      feature: z.enum(['text', 'speech', 'image', 'video']),
      model: z.string(),
      courseId: z.number().optional(),
      courseName: z.string().optional(),
      tokenCount: z.number().int().optional(),
      characterCount: z.number().int().optional(),
      imageCount: z.number().int().optional(),
      prompt: z.string().optional()
    });
    
    const result = schema.safeParse({ 
      credits, feature, model, courseId, courseName, 
      tokenCount, characterCount, imageCount, prompt 
    });
    
    if (!result.success) {
      return res.status(400).json({ message: 'Invalid input', errors: result.error.format() });
    }
    
    // Get current credits
    const [userCredits] = await db.select().from(aiCredits).where(eq(aiCredits.userId, userId));
    
    if (!userCredits) {
      return res.status(404).json({ message: 'AI credits record not found' });
    }
    
    // Check if user has enough credits
    if (userCredits.totalCredits - userCredits.usedCredits < credits) {
      return res.status(400).json({ message: 'Insufficient credits' });
    }
    
    // Record usage
    await db.insert(aiUsageHistory).values({
      userId,
      feature,
      credits,
      model,
      courseId,
      courseName,
      tokenCount,
      characterCount,
      imageCount,
      prompt
    });
    
    // Update credits
    const [updatedCredits] = await db
      .update(aiCredits)
      .set({
        usedCredits: sql`${aiCredits.usedCredits} + ${credits}`,
        updatedAt: new Date()
      })
      .where(eq(aiCredits.userId, userId))
      .returning();
    
    res.json(updatedCredits);
  } catch (error) {
    console.error('Error consuming AI credits:', error);
    res.status(500).json({ message: 'Failed to consume AI credits' });
  }
});

export const aiCreditsRouter = router;