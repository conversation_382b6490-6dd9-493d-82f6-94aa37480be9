import { Integration } from "@/types";

interface IntegrationCardProps {
  integration: Integration;
  onConnect: (integration: Integration) => void;
}

export function IntegrationCard({ integration, onConnect }: IntegrationCardProps) {
  return (
    <div className="flex items-center justify-between p-4 border border-slate-200 rounded-lg">
      <div className="flex items-center">
        <div className="h-10 w-10 flex-shrink-0 rounded-md bg-slate-100 flex items-center justify-center">
          <i className="ri-store-2-line text-slate-600"></i>
        </div>
        <div className="ml-4">
          <h3 className="font-medium text-slate-900">{integration.platform}</h3>
          <p className="text-sm text-slate-500">Connect your account to publish directly</p>
        </div>
      </div>
      <div>
        {integration.isConnected ? (
          <span className="px-2.5 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">Connected</span>
        ) : (
          <button 
            className="px-2.5 py-1 text-xs font-medium rounded-full bg-slate-100 text-slate-800 hover:bg-primary-100 hover:text-primary"
            onClick={() => onConnect(integration)}
          >
            Not Connected
          </button>
        )}
      </div>
    </div>
  );
}
