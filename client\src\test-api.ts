import { apiRequest } from "./lib/queryClient";
import { generateCourseStructure, CourseStructureGenerationParams } from "./lib/ai";

/**
 * Function to test the course structure generation API
 */
export async function testCourseStructureAPI() {
  try {
    // Test parameters
    const params: CourseStructureGenerationParams = {
      title: "AI-Powered Marketing",
      description: "Learn how to use AI tools for marketing automation",
      category: "Marketing",
      targetAudience: "Marketing professionals and business owners"
    };
    
    // Direct test using debug endpoint
    console.log("Testing debug endpoint directly...");
    const response = await apiRequest("POST", "/api/debug/generate-course-structure", params);
    const result = await response.json();
    console.log("Debug endpoint result:", result);
    
    // Test using the library function (which uses the authenticated endpoint)
    console.log("Testing through library function...");
    try {
      const libraryResult = await generateCourseStructure(params);
      console.log("Library function result:", libraryResult);
    } catch (error) {
      console.error("Library function error (expected if not authenticated):", error);
    }
    
    return { success: true, result };
  } catch (error) {
    console.error("Test failed:", error);
    return { success: false, error };
  }
}