import * as unifiedAI from './unifiedAIService';

interface ScriptGenerationParams {
  moduleTitle: string;
  moduleDescription?: string;
  lessonTitle?: string;
  lessonDescription?: string;
  courseTitle?: string;
  targetAudience?: string;
  tone: string;
  wordCount: number;
  keyPoints?: string[];
  lessonTitles?: string[];
}

/**
 * Generate a clean, voice-ready script using open-source AI (Mistral primary) with fallbacks
 */
export async function generateScript(params: ScriptGenerationParams): Promise<string> {
  try {
    console.log('🧠 Generating script with unified AI service (Mistral → Gemini → OpenAI)...');

    // Use unified AI service for lesson script generation
    const script = await unifiedAI.generateLessonScript(
      params.courseTitle || 'Course',
      params.moduleDescription || 'Educational content',
      params.moduleTitle,
      params.moduleDescription || '',
      params.lessonTitle || params.moduleTitle,
      params.lessonDescription || params.moduleDescription || '',
      params.targetAudience || 'general audience'
    );

    return cleanScriptForVoice(script);
  } catch (error) {
    console.error('Unified AI script generation failed:', error);
    throw new Error('Script generation failed with all available AI services');
  }
}

// Old OpenAI and Gemini functions removed - now using unified AI service

/**
 * Clean and optimize script for voice generation
 */
function cleanScriptForVoice(script: string): string {
  return script
    // Remove any remaining markdown or formatting
    .replace(/[#*_`]/g, '')
    // Remove bullet points and list markers
    .replace(/^[\s]*[-•]\s*/gm, '')
    // Remove numbered lists
    .replace(/^[\s]*\d+\.\s*/gm, '')
    // Remove any remaining brackets or parenthetical stage directions
    .replace(/\[.*?\]/g, '')
    .replace(/\(.*?\)/g, '')
    // Normalize whitespace
    .replace(/\s+/g, ' ')
    // Remove extra line breaks but keep paragraph structure
    .replace(/\n\s*\n\s*\n/g, '\n\n')
    // Trim whitespace
    .trim();
}

/**
 * Generate lesson-specific script
 */
export async function generateLessonScript(params: Omit<ScriptGenerationParams, 'lessonTitles'>): Promise<string> {
  if (!params.lessonTitle) {
    throw new Error("Lesson title is required for lesson script generation");
  }
  
  return generateScript(params);
}

/**
 * Generate module overview script
 */
export async function generateModuleScript(params: Omit<ScriptGenerationParams, 'lessonTitle' | 'lessonDescription'>): Promise<string> {
  return generateScript(params);
}