import OpenAI from 'openai';
import { GoogleGenerativeAI } from '@google/generative-ai';

// Initialize OpenAI (primary) and Gemini (fallback)
const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });
let gemini: GoogleGenerativeAI | null = null;

// Initialize Gemini if API key is available
if (process.env.GOOGLE_API_KEY) {
  try {
    gemini = new GoogleGenerativeAI(process.env.GOOGLE_API_KEY);
    console.log('Gemini API initialized as fallback for script generation');
  } catch (error) {
    console.warn('Failed to initialize Gemini API:', error);
  }
}

interface ScriptGenerationParams {
  moduleTitle: string;
  moduleDescription?: string;
  lessonTitle?: string;
  lessonDescription?: string;
  courseTitle?: string;
  targetAudience?: string;
  tone: string;
  wordCount: number;
  keyPoints?: string[];
  lessonTitles?: string[];
}

/**
 * Generate a clean, voice-ready script using OpenAI (primary) with Gemini fallback
 */
export async function generateScript(params: ScriptGenerationParams): Promise<string> {
  // First try OpenAI
  try {
    console.log('Attempting script generation with OpenAI...');
    const script = await generateWithOpenAI(params);
    return cleanScriptForVoice(script);
  } catch (openaiError) {
    console.warn('OpenAI script generation failed, trying Gemini fallback:', openaiError);
    
    // Fallback to Gemini if available
    if (gemini) {
      try {
        console.log('Attempting script generation with Gemini...');
        const script = await generateWithGemini(params);
        return cleanScriptForVoice(script);
      } catch (geminiError) {
        console.error('Gemini script generation also failed:', geminiError);
        throw new Error('Both OpenAI and Gemini script generation failed');
      }
    } else {
      throw new Error('OpenAI failed and Gemini not available');
    }
  }
}

/**
 * Generate script using OpenAI GPT-4o
 */
async function generateWithOpenAI(params: ScriptGenerationParams): Promise<string> {
  const systemPrompt = `You are an expert educational script writer. Create clean, natural scripts optimized for text-to-speech conversion and voice generation.

CRITICAL REQUIREMENTS:
- Output ONLY the spoken content, nothing else
- No markdown, headers, bullets, or any formatting
- No stage directions, visual cues, or meta commentary
- No phrases like "in this video" or "welcome to this lesson"
- Write in natural, flowing sentences with appropriate pauses
- Use conversational language that sounds natural when spoken
- Include smooth transitions between topics
- End with a clear, motivating conclusion`;

  const lessonContext = params.lessonTitle 
    ? `lesson "${params.lessonTitle}"${params.moduleTitle ? ` from module "${params.moduleTitle}"` : ''}`
    : `module "${params.moduleTitle}"`;

  const keyPointsText = params.keyPoints && params.keyPoints.length > 0 
    ? `\nKey points to cover: ${params.keyPoints.join(', ')}`
    : '';

  const lessonsText = params.lessonTitles && params.lessonTitles.length > 0
    ? `\nThis module covers these lessons: ${params.lessonTitles.join(', ')}`
    : '';

  const userPrompt = `Create a ${params.wordCount}-word educational script for the ${lessonContext}${params.courseTitle ? ` in course "${params.courseTitle}"` : ''}.

${params.moduleDescription ? `Module Description: ${params.moduleDescription}` : ''}
${params.lessonDescription ? `Lesson Description: ${params.lessonDescription}` : ''}${keyPointsText}${lessonsText}

Target Audience: ${params.targetAudience || 'General learners'}
Tone: ${params.tone}

Requirements:
- Start directly with engaging content about the topic
- Present information in a logical, easy-to-follow sequence
- Use natural speech patterns with appropriate emphasis
- Include practical insights and clear explanations
- End with a summary and encouraging next steps
- Ensure content flows smoothly for voice narration
- Make it exactly ${params.wordCount} words (approximately)

Generate ONLY the clean script content that will be spoken:`;

  const response = await openai.chat.completions.create({
    model: "gpt-4o", // the newest OpenAI model is "gpt-4o" which was released May 13, 2024. do not change this unless explicitly requested by the user
    messages: [
      { role: "system", content: systemPrompt },
      { role: "user", content: userPrompt }
    ],
    temperature: 0.7,
    max_tokens: Math.min(4000, params.wordCount * 2),
  });

  const script = response.choices[0].message.content;
  if (!script) {
    throw new Error("Empty response from OpenAI");
  }

  return script;
}

/**
 * Generate script using Google Gemini
 */
async function generateWithGemini(params: ScriptGenerationParams): Promise<string> {
  if (!gemini) {
    throw new Error("Gemini not initialized");
  }

  const model = gemini.getGenerativeModel({ model: "gemini-1.5-pro" });

  const lessonContext = params.lessonTitle 
    ? `lesson "${params.lessonTitle}"${params.moduleTitle ? ` from module "${params.moduleTitle}"` : ''}`
    : `module "${params.moduleTitle}"`;

  const keyPointsText = params.keyPoints && params.keyPoints.length > 0 
    ? `\nKey points to cover: ${params.keyPoints.join(', ')}`
    : '';

  const lessonsText = params.lessonTitles && params.lessonTitles.length > 0
    ? `\nThis module covers these lessons: ${params.lessonTitles.join(', ')}`
    : '';

  const prompt = `Write a comprehensive educational script for the ${lessonContext}${params.courseTitle ? ` in course "${params.courseTitle}"` : ''}.

${params.moduleDescription ? `Module Description: ${params.moduleDescription}` : ''}
${params.lessonDescription ? `Lesson Description: ${params.lessonDescription}` : ''}${keyPointsText}${lessonsText}

Target Audience: ${params.targetAudience || 'General learners'}
Tone: ${params.tone}
Word Count: Approximately ${params.wordCount} words

Script Requirements:
- This script will be directly processed by a text-to-speech system
- Include only the educational content that should be spoken
- Include a clear introduction that hooks the learner
- Cover all key concepts in a logical order
- Use ${params.tone} language appropriate for ${params.targetAudience || 'beginners'}
- End with a conclusion that summarizes key points and next steps
- Ensure the script directly relates to the ${lessonContext}

Format the script for direct text-to-speech processing:
- No markdown formatting, headers, bullets, or other special notation
- No camera directions, visual cues, or meta commentary
- No phrases like "in this video" or references to visual elements
- Use natural language patterns with appropriate pauses
- Keep content clean and focused solely on the educational material`;

  const result = await model.generateContent(prompt);
  const response = result.response;
  const text = response.text();

  if (!text) {
    throw new Error("Empty response from Gemini");
  }

  return text;
}

/**
 * Clean and optimize script for voice generation
 */
function cleanScriptForVoice(script: string): string {
  return script
    // Remove any remaining markdown or formatting
    .replace(/[#*_`]/g, '')
    // Remove bullet points and list markers
    .replace(/^[\s]*[-•]\s*/gm, '')
    // Remove numbered lists
    .replace(/^[\s]*\d+\.\s*/gm, '')
    // Remove any remaining brackets or parenthetical stage directions
    .replace(/\[.*?\]/g, '')
    .replace(/\(.*?\)/g, '')
    // Normalize whitespace
    .replace(/\s+/g, ' ')
    // Remove extra line breaks but keep paragraph structure
    .replace(/\n\s*\n\s*\n/g, '\n\n')
    // Trim whitespace
    .trim();
}

/**
 * Generate lesson-specific script
 */
export async function generateLessonScript(params: Omit<ScriptGenerationParams, 'lessonTitles'>): Promise<string> {
  if (!params.lessonTitle) {
    throw new Error("Lesson title is required for lesson script generation");
  }
  
  return generateScript(params);
}

/**
 * Generate module overview script
 */
export async function generateModuleScript(params: Omit<ScriptGenerationParams, 'lessonTitle' | 'lessonDescription'>): Promise<string> {
  return generateScript(params);
}