import express from 'express';
import { z } from 'zod';
import { db } from '../db';
import { emailSettings } from '@shared/schema';
import { eq } from 'drizzle-orm';
import { Resend } from 'resend';

// Initialize Resend API with environment variable
const resend = new Resend(process.env.RESEND_API_KEY);

const router = express.Router();

// Schema for updating email settings
const updateEmailSettingsSchema = z.object({
  fromName: z.string().min(1, { message: 'From name is required' }),
  fromEmail: z.string().email({ message: 'Please enter a valid email address' }),
  replyToEmail: z.string().email({ message: 'Please enter a valid email address' }).optional().nullable(),
  emailProvider: z.enum(['resend', 'sendgrid']).default('resend'),
  footerText: z.string().optional().nullable(),
  enableUnsubscribeLink: z.boolean().default(true),
  enableOpenTracking: z.boolean().default(true),
  enableClickTracking: z.boolean().default(true),
  sendgridApiKey: z.string().optional(),
  resendApiKey: z.string().optional()
});

// Get email settings
router.get('/', async (req, res) => {
  try {
    // Get the authenticated user
    const user = req.user;
    if (!user) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    // Get settings for the user
    const settings = await db.select().from(emailSettings).where(eq(emailSettings.userId, user.id));
    
    if (settings.length === 0) {
      // Return default settings if none exist
      return res.status(200).json({
        fromName: 'Course Creator',
        fromEmail: '<EMAIL>',
        replyToEmail: null,
        emailProvider: 'resend',
        footerText: '© 2025 Course Creator. All rights reserved.',
        enableUnsubscribeLink: true,
        enableOpenTracking: true,
        enableClickTracking: true,
        hasResendApiKey: !!process.env.RESEND_API_KEY,
        hasSendgridApiKey: false
      });
    }

    // Don't return sensitive API keys in the response
    const sanitizedSettings = {
      ...settings[0],
      hasResendApiKey: !!settings[0].resendApiKey || !!process.env.RESEND_API_KEY,
      hasSendgridApiKey: !!settings[0].sendgridApiKey,
      // Remove actual API keys
      resendApiKey: undefined,
      sendgridApiKey: undefined
    };

    return res.status(200).json(sanitizedSettings);
  } catch (error) {
    console.error('Error fetching email settings:', error);
    return res.status(500).json({ 
      message: 'Server error', 
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Update or create email settings
router.patch('/', async (req, res) => {
  try {
    // Get the authenticated user
    const user = req.user;
    if (!user) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    // Validate request data
    const validatedData = updateEmailSettingsSchema.parse(req.body);

    // Handle API keys - check if they're valid before saving
    let resendApiKeyToSave = null;
    let sendgridApiKeyToSave = null;

    // If Resend API key is provided, verify it
    if (validatedData.resendApiKey) {
      try {
        const testResend = new Resend(validatedData.resendApiKey);
        const domains = await testResend.domains.list();
        if (domains) {
          resendApiKeyToSave = validatedData.resendApiKey;
        }
      } catch (apiError) {
        return res.status(400).json({ 
          error: 'Invalid Resend API key', 
          message: 'The provided Resend API key could not be verified.' 
        });
      }
    }

    // If SendGrid API key is provided, save it (verification would be added here)
    if (validatedData.sendgridApiKey) {
      sendgridApiKeyToSave = validatedData.sendgridApiKey;
    }

    // Check if settings exist for the user
    const existingSettings = await db.select().from(emailSettings).where(eq(emailSettings.userId, user.id));

    if (existingSettings.length === 0) {
      // Create new settings
      const valuesToInsert: any = {
        userId: user.id,
        fromName: validatedData.fromName,
        fromEmail: validatedData.fromEmail,
        emailProvider: validatedData.emailProvider,
        replyToEmail: validatedData.replyToEmail || null,
        footerText: validatedData.footerText || null,
        enableUnsubscribeLink: validatedData.enableUnsubscribeLink,
        enableOpenTracking: validatedData.enableOpenTracking,
        enableClickTracking: validatedData.enableClickTracking
      };

      // Only include API keys if they were provided and validated
      if (resendApiKeyToSave) {
        valuesToInsert.resendApiKey = resendApiKeyToSave;
      }
      
      if (sendgridApiKeyToSave) {
        valuesToInsert.sendgridApiKey = sendgridApiKeyToSave;
      }

      const [newSettings] = await db.insert(emailSettings).values(valuesToInsert).returning();

      // Don't return sensitive API keys in the response
      const sanitizedSettings = {
        ...newSettings,
        hasResendApiKey: !!newSettings.resendApiKey || !!process.env.RESEND_API_KEY,
        hasSendgridApiKey: !!newSettings.sendgridApiKey,
        // Remove actual API keys
        resendApiKey: undefined,
        sendgridApiKey: undefined
      };

      return res.status(201).json(sanitizedSettings);
    } else {
      // Update existing settings
      const valuesToUpdate: any = {
        fromName: validatedData.fromName,
        fromEmail: validatedData.fromEmail,
        emailProvider: validatedData.emailProvider,
        replyToEmail: validatedData.replyToEmail || null,
        footerText: validatedData.footerText || null,
        enableUnsubscribeLink: validatedData.enableUnsubscribeLink,
        enableOpenTracking: validatedData.enableOpenTracking,
        enableClickTracking: validatedData.enableClickTracking
      };

      // Only include API keys if they were provided and validated
      if (resendApiKeyToSave) {
        valuesToUpdate.resendApiKey = resendApiKeyToSave;
      }
      
      if (sendgridApiKeyToSave) {
        valuesToUpdate.sendgridApiKey = sendgridApiKeyToSave;
      }

      const [updatedSettings] = await db.update(emailSettings)
        .set(valuesToUpdate)
        .where(eq(emailSettings.userId, user.id))
        .returning();

      // Don't return sensitive API keys in the response
      const sanitizedSettings = {
        ...updatedSettings,
        hasResendApiKey: !!updatedSettings.resendApiKey || !!process.env.RESEND_API_KEY,
        hasSendgridApiKey: !!updatedSettings.sendgridApiKey,
        // Remove actual API keys
        resendApiKey: undefined,
        sendgridApiKey: undefined
      };

      return res.status(200).json(sanitizedSettings);
    }
  } catch (error) {
    console.error('Error updating email settings:', error);

    // Handle validation errors
    if (error instanceof z.ZodError) {
      return res.status(400).json({ message: 'Validation error', errors: error.errors });
    }

    return res.status(500).json({ 
      message: 'Server error', 
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Verify sending domain with Resend
router.post('/verify-domain', async (req, res) => {
  try {
    // Get the authenticated user
    const user = req.user;
    if (!user) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const { domain } = req.body;
    if (!domain) {
      return res.status(400).json({ error: 'Domain name is required' });
    }

    // Get user's email settings to see if they have a custom Resend API key
    const settings = await db.select().from(emailSettings).where(eq(emailSettings.userId, user.id));
    
    let apiKey = process.env.RESEND_API_KEY;
    if (settings.length > 0 && settings[0].resendApiKey) {
      apiKey = settings[0].resendApiKey;
    }

    if (!apiKey) {
      return res.status(400).json({ 
        error: 'No Resend API key found', 
        message: 'Please configure your email settings with a valid Resend API key.'
      });
    }

    // Use Resend API to verify domain
    try {
      const resendClient = new Resend(apiKey);
      const domainResponse = await resendClient.domains.create({ name: domain });
      return res.status(200).json({ 
        success: true, 
        message: 'Domain verification initiated',
        domain: domainResponse.data
      });
    } catch (apiError) {
      console.error('Error verifying domain with Resend:', apiError);
      return res.status(500).json({ 
        error: 'Failed to verify domain', 
        message: apiError instanceof Error ? apiError.message : 'Unknown error'
      });
    }
  } catch (error) {
    console.error('Error in domain verification endpoint:', error);
    return res.status(500).json({ 
      error: 'Server error', 
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Get all domains from Resend
router.get('/domains', async (req, res) => {
  try {
    // Get the authenticated user
    const user = req.user;
    if (!user) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    // Get user's email settings to see if they have a custom Resend API key
    const settings = await db.select().from(emailSettings).where(eq(emailSettings.userId, user.id));
    
    let apiKey = process.env.RESEND_API_KEY;
    if (settings.length > 0 && settings[0].resendApiKey) {
      apiKey = settings[0].resendApiKey;
    }

    if (!apiKey) {
      return res.status(400).json({ 
        error: 'No Resend API key found', 
        message: 'Please configure your email settings with a valid Resend API key.'
      });
    }

    const resendClient = new Resend(apiKey);
    const domainsResponse = await resendClient.domains.list();
    
    return res.status(200).json({
      success: true,
      domains: domainsResponse.data || []
    });
  } catch (error) {
    console.error('Error getting domains from Resend:', error);
    return res.status(500).json({ 
      error: 'Server error', 
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export default router;