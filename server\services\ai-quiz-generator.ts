import OpenAI from "openai";

// The newest OpenAI model is "gpt-4o" which was released May 13, 2024. do not change this unless explicitly requested by the user
const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });

interface QuizGenerationRequest {
  content: string;
  questionCount: number;
  difficulty: 'easy' | 'medium' | 'hard' | 'mixed';
  questionTypes: string[];
  moduleTitle?: string;
  lessonTitle?: string;
}

interface GeneratedQuestion {
  type: 'multiple-choice' | 'true-false' | 'short-answer' | 'essay';
  question: string;
  options?: string[];
  correctAnswer: string | number;
  explanation: string;
  difficulty: 'easy' | 'medium' | 'hard';
  bloomsLevel: string;
  learningObjective: string;
}

export class AIQuizGenerator {
  
  async generateQuestions(request: QuizGenerationRequest): Promise<{ questions: GeneratedQuestion[] }> {
    try {
      // Try OpenAI first
      return await this.generateWithOpenAI(request);
    } catch (openaiError) {
      console.warn('OpenA<PERSON> failed, trying Gemini fallback:', openaiError);
      try {
        return await this.generateWithGemini(request);
      } catch (geminiError) {
        console.error('Both OpenAI and Gemini failed:', { openaiError, geminiError });
        throw new Error('Unable to generate questions. Please check your AI service configuration.');
      }
    }
  }

  private async generateWithOpenAI(request: QuizGenerationRequest): Promise<{ questions: GeneratedQuestion[] }> {
    const systemPrompt = this.createSystemPrompt();
    const userPrompt = this.createUserPrompt(request);

    const response = await openai.chat.completions.create({
      model: "gpt-4o",
      messages: [
        { role: "system", content: systemPrompt },
        { role: "user", content: userPrompt }
      ],
      response_format: { type: "json_object" },
      temperature: 0.7,
      max_tokens: 4000,
    });

    const result = JSON.parse(response.choices[0].message.content || '{}');
    
    if (!result.questions || !Array.isArray(result.questions)) {
      throw new Error('Invalid response format from OpenAI');
    }

    return this.validateAndFormatQuestions(result);
  }

  private async generateWithGemini(request: QuizGenerationRequest): Promise<{ questions: GeneratedQuestion[] }> {
    // Import Gemini service dynamically to avoid circular dependencies
    const { generateContent } = await import('./gemini');
    
    const systemPrompt = this.createSystemPrompt();
    const userPrompt = this.createUserPrompt(request);
    
    const fullPrompt = `${systemPrompt}\n\n${userPrompt}`;
    
    const response = await generateContent(fullPrompt);
    
    // Parse JSON response from Gemini
    let result;
    try {
      // Extract JSON from response if it's wrapped in text
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      const jsonString = jsonMatch ? jsonMatch[0] : response;
      result = JSON.parse(jsonString);
    } catch (parseError) {
      throw new Error('Failed to parse Gemini response as JSON');
    }

    if (!result.questions || !Array.isArray(result.questions)) {
      throw new Error('Invalid response format from Gemini');
    }

    return this.validateAndFormatQuestions(result);
  }

  private createSystemPrompt(): string {
    return `You are an expert educational content creator and assessment designer. Your task is to generate high-quality quiz questions based on educational content.

IMPORTANT: Respond ONLY with valid JSON in the exact format specified below.

Guidelines:
1. Create engaging, pedagogically sound questions that test understanding, not just memorization
2. Ensure questions are clear, unambiguous, and appropriate for the difficulty level
3. Include plausible distractors for multiple-choice questions
4. Provide comprehensive explanations that teach and reinforce learning
5. Align questions with appropriate Bloom's taxonomy levels
6. Ensure cultural sensitivity and inclusivity

Question Types:
- multiple-choice: 4 options, one correct answer
- true-false: Simple true/false with clear reasoning
- short-answer: Requires brief written response (1-3 sentences)
- essay: Requires detailed written response (paragraph+)

Difficulty Levels:
- easy: Basic recall and comprehension (Bloom's: Remember, Understand)
- medium: Application and analysis (Bloom's: Apply, Analyze)
- hard: Synthesis and evaluation (Bloom's: Evaluate, Create)`;
  }

  private createUserPrompt(request: QuizGenerationRequest): string {
    const difficultyInstructions = this.getDifficultyInstructions(request.difficulty);
    const typeInstructions = this.getTypeInstructions(request.questionTypes);

    return `Generate ${request.questionCount} quiz questions based on the following educational content:

CONTENT:
${request.content}

REQUIREMENTS:
- Question count: ${request.questionCount}
- Difficulty: ${request.difficulty}
- Question types: ${request.questionTypes.join(', ')}
${request.moduleTitle ? `- Module: ${request.moduleTitle}` : ''}
${request.lessonTitle ? `- Lesson: ${request.lessonTitle}` : ''}

${difficultyInstructions}

${typeInstructions}

RESPONSE FORMAT (JSON only):
{
  "questions": [
    {
      "type": "multiple-choice",
      "question": "Question text here?",
      "options": ["Option A", "Option B", "Option C", "Option D"],
      "correctAnswer": 0,
      "explanation": "Detailed explanation of why this answer is correct and others are wrong",
      "difficulty": "medium",
      "bloomsLevel": "Apply",
      "learningObjective": "Students will be able to..."
    }
  ]
}

For true-false questions:
- correctAnswer should be "true" or "false" (string)
- omit options array

For short-answer/essay questions:
- correctAnswer should be a sample answer or key points (string)
- omit options array

Generate the questions now:`;
  }

  private getDifficultyInstructions(difficulty: string): string {
    switch (difficulty) {
      case 'easy':
        return `DIFFICULTY FOCUS - EASY:
- Test basic recall and comprehension
- Use straightforward language
- Focus on key facts and definitions
- Bloom's levels: Remember, Understand`;

      case 'medium':
        return `DIFFICULTY FOCUS - MEDIUM:
- Test application and analysis
- Require students to connect concepts
- Include scenario-based questions
- Bloom's levels: Apply, Analyze`;

      case 'hard':
        return `DIFFICULTY FOCUS - HARD:
- Test synthesis and evaluation
- Require critical thinking and judgment
- Include complex scenarios and problems
- Bloom's levels: Evaluate, Create`;

      case 'mixed':
        return `DIFFICULTY FOCUS - MIXED:
- Include a balanced mix of easy, medium, and hard questions
- Distribute across all Bloom's taxonomy levels
- Ensure progression from basic to advanced concepts`;

      default:
        return '';
    }
  }

  private getTypeInstructions(types: string[]): string {
    let instructions = 'QUESTION TYPE REQUIREMENTS:\n';

    if (types.includes('multiple-choice')) {
      instructions += `- Multiple Choice: Create 4 plausible options with one clearly correct answer
- Avoid "all of the above" or "none of the above" options
- Make distractors believable but clearly incorrect\n`;
    }

    if (types.includes('true-false')) {
      instructions += `- True/False: Create statements that are definitively true or false
- Avoid absolute terms unless they are accurate
- Focus on key concepts, not trivial details\n`;
    }

    if (types.includes('short-answer')) {
      instructions += `- Short Answer: Require 1-3 sentence responses
- Test understanding and application
- Provide sample answer with key points\n`;
    }

    if (types.includes('essay')) {
      instructions += `- Essay: Require paragraph-length responses
- Test analysis, synthesis, and evaluation
- Provide comprehensive sample answer outline\n`;
    }

    return instructions;
  }

  private validateAndFormatQuestions(result: any): { questions: GeneratedQuestion[] } {
    const validatedQuestions: GeneratedQuestion[] = [];

    for (const question of result.questions) {
      try {
        const validatedQuestion = this.validateSingleQuestion(question);
        validatedQuestions.push(validatedQuestion);
      } catch (error) {
        console.warn('Skipping invalid question:', error);
        // Continue with other questions
      }
    }

    if (validatedQuestions.length === 0) {
      throw new Error('No valid questions were generated');
    }

    return { questions: validatedQuestions };
  }

  private validateSingleQuestion(question: any): GeneratedQuestion {
    // Validate required fields
    if (!question.type || !question.question || !question.explanation || !question.difficulty) {
      throw new Error('Missing required question fields');
    }

    // Validate question type
    const validTypes = ['multiple-choice', 'true-false', 'short-answer', 'essay'];
    if (!validTypes.includes(question.type)) {
      throw new Error(`Invalid question type: ${question.type}`);
    }

    // Validate difficulty
    const validDifficulties = ['easy', 'medium', 'hard'];
    if (!validDifficulties.includes(question.difficulty)) {
      throw new Error(`Invalid difficulty: ${question.difficulty}`);
    }

    // Type-specific validation
    if (question.type === 'multiple-choice') {
      if (!Array.isArray(question.options) || question.options.length !== 4) {
        throw new Error('Multiple choice questions must have exactly 4 options');
      }
      if (typeof question.correctAnswer !== 'number' || question.correctAnswer < 0 || question.correctAnswer > 3) {
        throw new Error('Multiple choice correct answer must be a number between 0 and 3');
      }
    }

    if (question.type === 'true-false') {
      if (question.correctAnswer !== 'true' && question.correctAnswer !== 'false') {
        throw new Error('True/false correct answer must be "true" or "false"');
      }
    }

    if (['short-answer', 'essay'].includes(question.type)) {
      if (typeof question.correctAnswer !== 'string' || question.correctAnswer.trim().length === 0) {
        throw new Error('Short answer and essay questions must have a string correct answer');
      }
    }

    return {
      type: question.type,
      question: question.question.trim(),
      options: question.options,
      correctAnswer: question.correctAnswer,
      explanation: question.explanation.trim(),
      difficulty: question.difficulty,
      bloomsLevel: question.bloomsLevel || this.getDefaultBloomsLevel(question.difficulty),
      learningObjective: question.learningObjective || this.generateLearningObjective(question.question, question.difficulty),
    };
  }

  private getDefaultBloomsLevel(difficulty: string): string {
    switch (difficulty) {
      case 'easy': return 'Remember';
      case 'medium': return 'Apply';
      case 'hard': return 'Evaluate';
      default: return 'Understand';
    }
  }

  private generateLearningObjective(question: string, difficulty: string): string {
    const verbs = {
      easy: ['identify', 'recall', 'recognize', 'define'],
      medium: ['apply', 'demonstrate', 'solve', 'analyze'],
      hard: ['evaluate', 'create', 'synthesize', 'critique']
    };

    const verbList = verbs[difficulty as keyof typeof verbs] || verbs.medium;
    const verb = verbList[Math.floor(Math.random() * verbList.length)];
    
    return `Students will be able to ${verb} key concepts related to the topic.`;
  }

  // Content analysis for intelligent question generation
  async analyzeContent(content: string): Promise<{
    keyTopics: string[];
    difficulty: string;
    suggestedQuestionCount: number;
    contentType: string;
  }> {
    try {
      const systemPrompt = `You are an educational content analyzer. Analyze the provided content and return a JSON response with insights for question generation.`;
      
      const userPrompt = `Analyze this educational content and provide insights:

CONTENT:
${content.substring(0, 2000)}...

Return JSON with:
{
  "keyTopics": ["topic1", "topic2", "topic3"],
  "difficulty": "easy|medium|hard",
  "suggestedQuestionCount": number,
  "contentType": "theoretical|practical|mixed"
}`;

      const response = await openai.chat.completions.create({
        model: "gpt-4o",
        messages: [
          { role: "system", content: systemPrompt },
          { role: "user", content: userPrompt }
        ],
        response_format: { type: "json_object" },
        temperature: 0.3,
        max_tokens: 500,
      });

      return JSON.parse(response.choices[0].message.content || '{}');
    } catch (error) {
      console.error('Content analysis failed:', error);
      // Return reasonable defaults
      return {
        keyTopics: ['General concepts'],
        difficulty: 'medium',
        suggestedQuestionCount: 5,
        contentType: 'mixed'
      };
    }
  }
}

export const aiQuizGenerator = new AIQuizGenerator();