import React from 'react';
import { Box, Chip, Typography, LinearProgress } from '@mui/material';
import { CheckCircle, Error, Warning, Memory, Speed } from '@mui/icons-material';

const StatusBar = ({ status }) => {
  const getStatusColor = () => {
    switch (status.status) {
      case 'online': return 'success';
      case 'checking': return 'warning';
      case 'error': return 'error';
      default: return 'default';
    }
  };

  const getStatusIcon = () => {
    switch (status.status) {
      case 'online': return <CheckCircle />;
      case 'checking': return <Warning />;
      case 'error': return <Error />;
      default: return null;
    }
  };

  const formatMemory = (gb) => {
    return gb ? `${gb.toFixed(1)}GB` : 'N/A';
  };

  return (
    <Box sx={{ 
      display: 'flex', 
      alignItems: 'center', 
      gap: 2, 
      p: 2, 
      backgroundColor: '#f5f5f5',
      borderRadius: 1,
      mb: 2 
    }}>
      <Chip
        icon={getStatusIcon()}
        label={`GPU: ${status.status === 'online' ? 'Online' : status.status === 'checking' ? 'Checking' : 'Offline'}`}
        color={getStatusColor()}
        variant="filled"
      />
      
      {status.gpu_name && (
        <Typography variant="body2" sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
          <Memory fontSize="small" />
          {status.gpu_name}
        </Typography>
      )}
      
      {status.gpu_memory_total_gb && (
        <Typography variant="body2" sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
          <Speed fontSize="small" />
          {formatMemory(status.gpu_memory_free_gb)} / {formatMemory(status.gpu_memory_total_gb)} free
        </Typography>
      )}

      {status.services && (
        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
          {Object.entries(status.services).map(([service, available]) => (
            <Chip
              key={service}
              label={service.replace(/_/g, ' ')}
              color={available ? 'success' : 'default'}
              size="small"
              variant="outlined"
            />
          ))}
        </Box>
      )}

      {status.status === 'checking' && (
        <LinearProgress sx={{ flexGrow: 1, height: 4 }} />
      )}
    </Box>
  );
};

export default StatusBar;