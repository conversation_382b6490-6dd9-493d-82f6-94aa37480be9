import { Request, Response } from 'express';
import OpenAI from 'openai';
import { z } from 'zod';

// Initialize OpenAI client
const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });

// Schema for script generation requests
const scriptGenerationSchema = z.object({
  moduleId: z.string().optional(),
  moduleTitle: z.string(),
  moduleDescription: z.string().optional(),
  lessonTitles: z.array(z.string()).optional(),
  lessonTitle: z.string().optional(),
  lessonDescription: z.string().optional(),
  courseTitle: z.string().optional(),
  targetAudience: z.string().optional(),
  tone: z.enum(['professional', 'conversational', 'friendly', 'academic']).default('conversational'),
  wordCount: z.number().min(100).max(2000).default(500),
});

export async function generateModuleScript(req: any, res: Response) {
  try {
    if (!req.session?.userId) {
      return res.status(401).json({ message: "Not authenticated" });
    }

    const result = scriptGenerationSchema.safeParse(req.body);
    
    if (!result.success) {
      return res.status(400).json({ 
        message: "Invalid data", 
        errors: result.error.format() 
      });
    }

    const { 
      moduleTitle, 
      moduleDescription, 
      lessonTitles, 
      courseTitle,
      targetAudience,
      tone,
      wordCount 
    } = result.data;

    // Check if user has sufficient AI credits
    const { storage } = await import('../../storage');
    const stats = await storage.getUserStats(req.session.userId);
    
    if (!stats || (stats.aiCredits !== null && stats.aiCredits <= 0)) {
      return res.status(403).json({ message: "Insufficient AI credits" });
    }

    try {
      // Generate clean script using OpenAI
      const script = await generateCleanScript({
        title: moduleTitle,
        description: moduleDescription,
        lessons: lessonTitles,
        courseTitle,
        targetAudience,
        tone,
        wordCount
      });

      // Deduct AI credits (10 credits per script generation)
      if (stats.aiCredits !== null) {
        await storage.updateUserStats(req.session.userId, {
          aiCredits: Math.max(0, stats.aiCredits - 10)
        });
      }

      return res.status(200).json({ 
        script,
        creditsUsed: 10,
        remainingCredits: stats.aiCredits !== null ? Math.max(0, stats.aiCredits - 10) : null
      });

    } catch (aiError: any) {
      console.error("AI script generation error:", aiError);
      return res.status(500).json({ 
        message: "Failed to generate script. Please try again.",
        error: "AI_GENERATION_FAILED"
      });
    }

  } catch (error: any) {
    console.error("Script generation error:", error);
    return res.status(500).json({ 
      message: "Server error during script generation",
      error: "SERVER_ERROR"
    });
  }
}

export async function generateLessonScript(req: any, res: Response) {
  try {
    if (!req.session?.userId) {
      return res.status(401).json({ message: "Not authenticated" });
    }

    const lessonSchema = z.object({
      lessonTitle: z.string(),
      lessonDescription: z.string().optional(),
      moduleTitle: z.string().optional(),
      courseTitle: z.string().optional(),
      targetAudience: z.string().optional(),
      tone: z.enum(['professional', 'conversational', 'friendly', 'academic']).default('conversational'),
      wordCount: z.number().min(100).max(2000).default(500),
      keyPoints: z.array(z.string()).optional(),
    });

    const result = lessonSchema.safeParse(req.body);
    
    if (!result.success) {
      return res.status(400).json({ 
        message: "Invalid data", 
        errors: result.error.format() 
      });
    }

    const { 
      lessonTitle,
      lessonDescription,
      moduleTitle,
      courseTitle,
      targetAudience,
      tone,
      wordCount,
      keyPoints
    } = result.data;

    // Check AI credits
    const { storage } = await import('../../storage');
    const stats = await storage.getUserStats(req.session.userId);
    
    if (!stats || (stats.aiCredits !== null && stats.aiCredits <= 0)) {
      return res.status(403).json({ message: "Insufficient AI credits" });
    }

    try {
      // Generate clean lesson script
      const script = await generateCleanLessonScript({
        lessonTitle,
        lessonDescription,
        moduleTitle,
        courseTitle,
        targetAudience,
        tone,
        wordCount,
        keyPoints
      });

      // Deduct AI credits
      if (stats.aiCredits !== null) {
        await storage.updateUserStats(req.session.userId, {
          aiCredits: Math.max(0, stats.aiCredits - 15)
        });
      }

      return res.status(200).json({ 
        script,
        creditsUsed: 15,
        remainingCredits: stats.aiCredits !== null ? Math.max(0, stats.aiCredits - 15) : null
      });

    } catch (aiError: any) {
      console.error("AI lesson script generation error:", aiError);
      return res.status(500).json({ 
        message: "Failed to generate lesson script. Please try again.",
        error: "AI_GENERATION_FAILED"
      });
    }

  } catch (error: any) {
    console.error("Lesson script generation error:", error);
    return res.status(500).json({ 
      message: "Server error during lesson script generation",
      error: "SERVER_ERROR"
    });
  }
}

// Helper function to generate clean, voice-ready scripts
async function generateCleanScript(params: {
  title: string;
  description?: string;
  lessons?: string[];
  courseTitle?: string;
  targetAudience?: string;
  tone: string;
  wordCount: number;
}): Promise<string> {
  
  const systemPrompt = `You are an expert educational script writer. Create clean, natural scripts that are optimized for text-to-speech conversion and voice generation.

CRITICAL REQUIREMENTS:
- Output ONLY the spoken content, nothing else
- No markdown, headers, bullets, or formatting
- No stage directions, visual cues, or meta commentary
- No phrases like "in this video" or "welcome to this lesson"
- Write in natural, flowing sentences with appropriate pauses
- Use conversational language that sounds natural when spoken
- Include smooth transitions between topics
- End with a clear, motivating conclusion`;

  const userPrompt = `Create a ${params.wordCount}-word educational script for the module "${params.title}" ${params.courseTitle ? `in the course "${params.courseTitle}"` : ''}.

${params.description ? `Module Description: ${params.description}` : ''}
${params.lessons && params.lessons.length > 0 ? `This module covers: ${params.lessons.join(', ')}` : ''}

Target Audience: ${params.targetAudience || 'General learners'}
Tone: ${params.tone}

Requirements:
- Start directly with engaging content (no introductory phrases)
- Present information in a logical, easy-to-follow sequence
- Use natural speech patterns with appropriate emphasis
- Include practical insights and clear explanations
- End with a summary and next steps
- Ensure content flows smoothly for voice narration
- Make it exactly ${params.wordCount} words (approximately)

Generate ONLY the clean script content that will be spoken:`;

  try {
    const response = await openai.chat.completions.create({
      model: "gpt-4o", // the newest OpenAI model is "gpt-4o" which was released May 13, 2024. do not change this unless explicitly requested by the user
      messages: [
        { role: "system", content: systemPrompt },
        { role: "user", content: userPrompt }
      ],
      temperature: 0.7,
      max_tokens: Math.min(4000, params.wordCount * 2), // Ensure we have enough tokens
    });

    const script = response.choices[0].message.content;
    
    if (!script) {
      throw new Error("Empty response from AI service");
    }

    // Clean the script to ensure it's voice-ready
    return cleanScriptForVoice(script);

  } catch (error) {
    console.error("OpenAI script generation failed:", error);
    throw new Error("Failed to generate script with AI service");
  }
}

// Helper function for lesson-specific scripts
async function generateCleanLessonScript(params: {
  lessonTitle: string;
  lessonDescription?: string;
  moduleTitle?: string;
  courseTitle?: string;
  targetAudience?: string;
  tone: string;
  wordCount: number;
  keyPoints?: string[];
}): Promise<string> {

  const systemPrompt = `You are an expert educational script writer specializing in lesson content. Create clean, natural scripts optimized for text-to-speech and voice generation.

CRITICAL REQUIREMENTS:
- Output ONLY the spoken educational content
- No formatting, headers, bullets, or visual elements
- No stage directions or presenter instructions
- Write in natural, conversational language
- Include smooth transitions and logical flow
- Make content engaging and easy to understand when heard
- No references to visual elements or "this lesson"`;

  const keyPointsText = params.keyPoints && params.keyPoints.length > 0 
    ? `\nKey points to cover: ${params.keyPoints.join(', ')}`
    : '';

  const userPrompt = `Create a ${params.wordCount}-word lesson script for "${params.lessonTitle}"${params.moduleTitle ? ` from the module "${params.moduleTitle}"` : ''}${params.courseTitle ? ` in "${params.courseTitle}"` : ''}.

${params.lessonDescription ? `Lesson focus: ${params.lessonDescription}` : ''}${keyPointsText}

Target Audience: ${params.targetAudience || 'General learners'}
Tone: ${params.tone}

Create a complete lesson script that:
- Begins with an engaging hook about the topic
- Explains concepts clearly and logically
- Uses natural speech patterns and emphasis
- Includes practical examples or applications
- Concludes with key takeaways and next steps
- Flows smoothly for voice narration
- Is approximately ${params.wordCount} words

Generate ONLY the clean script content:`;

  try {
    const response = await openai.chat.completions.create({
      model: "gpt-4o", // the newest OpenAI model is "gpt-4o" which was released May 13, 2024. do not change this unless explicitly requested by the user
      messages: [
        { role: "system", content: systemPrompt },
        { role: "user", content: userPrompt }
      ],
      temperature: 0.7,
      max_tokens: Math.min(4000, params.wordCount * 2),
    });

    const script = response.choices[0].message.content;
    
    if (!script) {
      throw new Error("Empty response from AI service");
    }

    return cleanScriptForVoice(script);

  } catch (error) {
    console.error("OpenAI lesson script generation failed:", error);
    throw new Error("Failed to generate lesson script with AI service");
  }
}

// Function to clean and optimize script for voice generation
function cleanScriptForVoice(script: string): string {
  return script
    // Remove any remaining markdown or formatting
    .replace(/[#*_`]/g, '')
    // Remove bullet points and list markers
    .replace(/^[\s]*[-•]\s*/gm, '')
    // Remove numbered lists
    .replace(/^[\s]*\d+\.\s*/gm, '')
    // Remove any remaining brackets or parenthetical stage directions
    .replace(/\[.*?\]/g, '')
    .replace(/\(.*?\)/g, '')
    // Normalize whitespace
    .replace(/\s+/g, ' ')
    // Remove extra line breaks but keep paragraph structure
    .replace(/\n\s*\n\s*\n/g, '\n\n')
    // Trim whitespace
    .trim();
}