import { Request, Response } from 'express';

// Default Coqui TTS voices available in the system
const COQUI_VOICES = [
  { id: 'ljspeech', name: 'LJSpeech Female', language: 'en-US', gender: 'female', description: 'Clear, professional English voice' },
  { id: 'sam_male', name: 'SAM Male', language: 'en-US', gender: 'male', description: 'Deep, authoritative voice' },
  { id: 'sam_female', name: 'SAM Female', language: 'en-US', gender: 'female', description: 'Warm, engaging voice' },
  { id: 'yourtts_default', name: 'YourTTS Neutral', language: 'en-US', gender: 'neutral', description: 'Balanced, conversational voice' }
];

/**
 * Get available TTS services status
 */
export async function getTTSServices(req: Request, res: Response) {
  try {
    const services = [
      {
        id: 'local',
        name: 'Local TTS',
        status: 'available',
        type: 'local',
        features: ['Free', 'Open Source', 'Multiple Languages', 'Fast Generation']
      },
      {
        id: 'premium',
        name: 'Premium TTS',
        status: 'requires_api_key',
        type: 'cloud',
        features: ['Premium Quality', 'Voice Cloning', 'Multiple Languages', 'Realistic Voices']
      }
    ];

    return res.status(200).json({ services });
  } catch (error: any) {
    console.error('Error getting TTS services:', error);
    return res.status(500).json({ 
      error: 'Failed to fetch TTS services',
      details: error.message 
    });
  }
}

/**
 * Get available local TTS voices
 */
export async function getLocalVoices(req: Request, res: Response) {
  try {
    const localVoices = [
      { id: 'ljspeech', name: 'Female Voice 1', language: 'English', description: 'Clear female voice' },
      { id: 'jenny', name: 'Female Voice 2', language: 'English', description: 'Natural female voice' },
      { id: 'male1', name: 'Male Voice 1', language: 'English', description: 'Professional male voice' },
      { id: 'male2', name: 'Male Voice 2', language: 'English', description: 'Warm male voice' },
      { id: 'neutral', name: 'Neutral Voice', language: 'English', description: 'Gender-neutral voice' }
    ];

    return res.status(200).json({
      voices: localVoices,
      service: 'local',
      total: localVoices.length
    });
  } catch (error: any) {
    console.error('Error getting local voices:', error);
    return res.status(500).json({ 
      error: 'Failed to fetch local voices',
      details: error.message 
    });
  }
}

/**
 * Get available premium TTS voices
 */
export async function getPremiumVoices(req: Request, res: Response) {
  try {
    const apiKey = req.query.apiKey || req.headers['x-api-key'] || process.env.ELEVENLABS_API_KEY;
    
    if (!apiKey) {
      return res.status(400).json({ 
        error: 'Premium TTS API key required',
        message: 'Please provide your premium TTS API key'
      });
    }

    // Use fetch to get voices from premium TTS API
    const response = await fetch('https://api.elevenlabs.io/v1/voices', {
      method: 'GET',
      headers: {
        'xi-api-key': apiKey as string,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Premium TTS API error: ${response.statusText}`);
    }

    const data = await response.json();
    const voices = data.voices || [];
    
    return res.status(200).json({
      voices,
      service: 'premium',
      total: voices.length
    });
  } catch (error: any) {
    console.error('Error getting premium voices:', error);
    return res.status(500).json({ 
      error: 'Failed to fetch premium voices',
      details: error.message 
    });
  }
}

/**
 * Test premium TTS API key
 */
export async function testPremiumKey(req: Request, res: Response) {
  try {
    const { apiKey } = req.body;
    
    if (!apiKey) {
      return res.status(400).json({ 
        error: 'API key required',
        message: 'Please provide a premium TTS API key to test'
      });
    }

    // Test by fetching voices from premium TTS API
    const response = await fetch('https://api.elevenlabs.io/v1/voices', {
      method: 'GET',
      headers: {
        'xi-api-key': apiKey,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Premium TTS API error: ${response.statusText}`);
    }

    const data = await response.json();
    const voices = data.voices || [];
    
    return res.status(200).json({
      valid: true,
      message: 'API key is valid',
      voiceCount: voices.length
    });
  } catch (error: any) {
    console.error('Error testing premium key:', error);
    return res.status(400).json({ 
      valid: false,
      error: 'Invalid API key',
      message: error.message || 'The provided API key is not valid'
    });
  }
}

/**
 * Generate TTS preview with local service
 */
export async function generateLocalPreview(req: Request, res: Response) {
  try {
    const { text, voiceId } = req.body;
    
    if (!text) {
      return res.status(400).json({ error: 'Text is required' });
    }

    const previewText = text.substring(0, 200);
    
    // Use existing TTS endpoint for preview
    const baseURL = `${req.protocol}://${req.get('host')}`;
    const ttsResponse = await fetch(`${baseURL}/api/ai/text-to-speech`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ 
        text: previewText, 
        voiceId: voiceId || 'ljspeech'
      })
    });

    if (!ttsResponse.ok) {
      throw new Error('Failed to generate TTS preview');
    }

    // Forward the audio response
    const audioBuffer = await ttsResponse.arrayBuffer();
    res.setHeader('Content-Type', 'audio/wav');
    res.setHeader('Content-Disposition', 'attachment; filename="preview.wav"');
    res.send(Buffer.from(audioBuffer));
  } catch (error: any) {
    console.error('Error generating local preview:', error);
    return res.status(500).json({ 
      error: 'Failed to generate preview',
      details: error.message 
    });
  }
}

/**
 * Generate TTS preview with premium service
 */
export async function generatePremiumPreview(req: Request, res: Response) {
  try {
    const { text, voiceId, apiKey } = req.body;
    
    if (!text || !voiceId || !apiKey) {
      return res.status(400).json({ 
        error: 'Missing required fields',
        message: 'Text, voice ID, and API key are required' 
      });
    }

    const previewText = text.substring(0, 200);
    
    // Generate speech with premium TTS API
    const response = await fetch(`https://api.elevenlabs.io/v1/text-to-speech/${voiceId}`, {
      method: 'POST',
      headers: {
        'xi-api-key': apiKey,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        text: previewText,
        model_id: 'eleven_monolingual_v1',
        voice_settings: {
          stability: 0.5,
          similarity_boost: 0.5,
        },
      }),
    });

    if (!response.ok) {
      throw new Error(`Premium TTS API error: ${response.statusText}`);
    }

    // Forward the audio response
    const audioBuffer = await response.arrayBuffer();
    res.setHeader('Content-Type', 'audio/mpeg');
    res.setHeader('Content-Disposition', 'attachment; filename="preview.mp3"');
    res.send(Buffer.from(audioBuffer));
  } catch (error: any) {
    console.error('Error generating premium preview:', error);
    return res.status(500).json({ 
      error: 'Failed to generate preview',
      details: error.message 
    });
  }
}