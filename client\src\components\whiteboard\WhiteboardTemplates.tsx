import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Grid3X3 } from 'lucide-react';
import type { Action } from './SimpleWhiteboard';

// Export Action type from SimpleWhiteboard for use here
export interface TemplateProps {
  onSelectTemplate: (actions: Action[]) => void;
}

// Template definitions
const templates: Record<string, { name: string; description: string; actions: Action[] }> = {
  blank: {
    name: 'Blank Canvas',
    description: 'Start with a clean slate',
    actions: []
  },
  kanban: {
    name: 'Kanban Board',
    description: 'Task management board with To Do, In Progress, and Done columns',
    actions: [
      // Header
      {
        tool: 'text',
        x: 400,
        y: 50,
        size: 30,
        color: '#000000',
        text: 'Kanban Board'
      },
      // To Do Column
      {
        tool: 'rectangle',
        x: 50,
        y: 100,
        width: 250,
        height: 400,
        color: '#000000',
        size: 2
      },
      {
        tool: 'text',
        x: 140,
        y: 130,
        size: 20,
        color: '#000000',
        text: 'To Do'
      },
      // In Progress Column
      {
        tool: 'rectangle',
        x: 350,
        y: 100,
        width: 250,
        height: 400,
        color: '#000000',
        size: 2
      },
      {
        tool: 'text',
        x: 410,
        y: 130,
        size: 20,
        color: '#000000',
        text: 'In Progress'
      },
      // Done Column
      {
        tool: 'rectangle',
        x: 650,
        y: 100,
        width: 250,
        height: 400,
        color: '#000000',
        size: 2
      },
      {
        tool: 'text',
        x: 750,
        y: 130,
        size: 20,
        color: '#000000',
        text: 'Done'
      }
    ]
  },
  mindmap: {
    name: 'Mind Map',
    description: 'Organize ideas in a mind map structure',
    actions: [
      // Central Topic
      {
        tool: 'circle',
        x: 400,
        y: 250,
        width: 150,
        height: 150,
        color: '#000000',
        size: 2
      },
      {
        tool: 'text',
        x: 425,
        y: 300,
        size: 20,
        color: '#000000',
        text: 'Main Topic'
      },
      // Branch 1
      {
        tool: 'pen',
        points: [
          { x: 475, y: 250 },
          { x: 600, y: 150 }
        ],
        color: '#000000',
        size: 2
      },
      {
        tool: 'circle',
        x: 600,
        y: 100,
        width: 100,
        height: 100,
        color: '#000000',
        size: 2
      },
      {
        tool: 'text',
        x: 615,
        y: 150,
        size: 16,
        color: '#000000',
        text: 'Subtopic 1'
      },
      // Branch 2
      {
        tool: 'pen',
        points: [
          { x: 550, y: 325 },
          { x: 650, y: 350 }
        ],
        color: '#000000',
        size: 2
      },
      {
        tool: 'circle',
        x: 650,
        y: 300,
        width: 100,
        height: 100,
        color: '#000000',
        size: 2
      },
      {
        tool: 'text',
        x: 665,
        y: 350,
        size: 16,
        color: '#000000',
        text: 'Subtopic 2'
      },
      // Branch 3
      {
        tool: 'pen',
        points: [
          { x: 425, y: 400 },
          { x: 425, y: 500 }
        ],
        color: '#000000',
        size: 2
      },
      {
        tool: 'circle',
        x: 375,
        y: 500,
        width: 100,
        height: 100,
        color: '#000000',
        size: 2
      },
      {
        tool: 'text',
        x: 390,
        y: 550,
        size: 16,
        color: '#000000',
        text: 'Subtopic 3'
      },
      // Branch 4
      {
        tool: 'pen',
        points: [
          { x: 325, y: 325 },
          { x: 225, y: 350 }
        ],
        color: '#000000',
        size: 2
      },
      {
        tool: 'circle',
        x: 175,
        y: 300,
        width: 100,
        height: 100,
        color: '#000000',
        size: 2
      },
      {
        tool: 'text',
        x: 190,
        y: 350,
        size: 16,
        color: '#000000',
        text: 'Subtopic 4'
      }
    ]
  },
  courseOutline: {
    name: 'Course Outline',
    description: 'Plan your course modules and lessons',
    actions: [
      // Title
      {
        tool: 'text',
        x: 400,
        y: 50,
        size: 30,
        color: '#000000',
        text: 'Course Outline'
      },
      // Module 1
      {
        tool: 'rectangle',
        x: 100,
        y: 100,
        width: 700,
        height: 100,
        color: '#000000',
        size: 2
      },
      {
        tool: 'text',
        x: 120,
        y: 130,
        size: 20,
        color: '#000000',
        text: 'Module 1: Introduction'
      },
      {
        tool: 'text',
        x: 120,
        y: 160,
        size: 16,
        color: '#000000',
        text: 'Lessons: 1.1 Welcome, 1.2 Course Overview, 1.3 Tools & Setup'
      },
      // Module 2
      {
        tool: 'rectangle',
        x: 100,
        y: 220,
        width: 700,
        height: 100,
        color: '#000000',
        size: 2
      },
      {
        tool: 'text',
        x: 120,
        y: 250,
        size: 20,
        color: '#000000',
        text: 'Module 2: Fundamentals'
      },
      {
        tool: 'text',
        x: 120,
        y: 280,
        size: 16,
        color: '#000000',
        text: 'Lessons: 2.1 Core Concepts, 2.2 Practical Examples, 2.3 Quiz'
      },
      // Module 3
      {
        tool: 'rectangle',
        x: 100,
        y: 340,
        width: 700,
        height: 100,
        color: '#000000',
        size: 2
      },
      {
        tool: 'text',
        x: 120,
        y: 370,
        size: 20,
        color: '#000000',
        text: 'Module 3: Advanced Topics'
      },
      {
        tool: 'text',
        x: 120,
        y: 400,
        size: 16,
        color: '#000000',
        text: 'Lessons: 3.1 Advanced Techniques, 3.2 Case Studies, 3.3 Project'
      }
    ]
  },
  weeklyPlanner: {
    name: 'Weekly Planner',
    description: 'Plan your week with a day-by-day breakdown',
    actions: [
      // Title
      {
        tool: 'text',
        x: 400,
        y: 50,
        size: 30,
        color: '#000000',
        text: 'Weekly Planner'
      },
      // Monday
      {
        tool: 'rectangle',
        x: 50,
        y: 100,
        width: 200,
        height: 100,
        color: '#000000',
        size: 2
      },
      {
        tool: 'text',
        x: 120,
        y: 130,
        size: 20,
        color: '#000000',
        text: 'Monday'
      },
      // Tuesday
      {
        tool: 'rectangle',
        x: 300,
        y: 100,
        width: 200,
        height: 100,
        color: '#000000',
        size: 2
      },
      {
        tool: 'text',
        x: 370,
        y: 130,
        size: 20,
        color: '#000000',
        text: 'Tuesday'
      },
      // Wednesday
      {
        tool: 'rectangle',
        x: 550,
        y: 100,
        width: 200,
        height: 100,
        color: '#000000',
        size: 2
      },
      {
        tool: 'text',
        x: 600,
        y: 130,
        size: 20,
        color: '#000000',
        text: 'Wednesday'
      },
      // Thursday
      {
        tool: 'rectangle',
        x: 50,
        y: 250,
        width: 200,
        height: 100,
        color: '#000000',
        size: 2
      },
      {
        tool: 'text',
        x: 120,
        y: 280,
        size: 20,
        color: '#000000',
        text: 'Thursday'
      },
      // Friday
      {
        tool: 'rectangle',
        x: 300,
        y: 250,
        width: 200,
        height: 100,
        color: '#000000',
        size: 2
      },
      {
        tool: 'text',
        x: 370,
        y: 280,
        size: 20,
        color: '#000000',
        text: 'Friday'
      },
      // Weekend
      {
        tool: 'rectangle',
        x: 550,
        y: 250,
        width: 200,
        height: 100,
        color: '#000000',
        size: 2
      },
      {
        tool: 'text',
        x: 610,
        y: 280,
        size: 20,
        color: '#000000',
        text: 'Weekend'
      }
    ]
  }
};

const WhiteboardTemplates: React.FC<TemplateProps> = ({ onSelectTemplate }) => {
  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="outline" size="sm" title="Templates">
          <Grid3X3 size={16} className="mr-2" />
          Templates
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80">
        <div className="grid gap-4">
          <h3 className="text-lg font-medium">Whiteboard Templates</h3>
          <p className="text-sm text-muted-foreground">
            Select a template to get started quickly.
          </p>
          <div className="grid gap-2">
            {Object.entries(templates).map(([id, template]) => (
              <Button
                key={id}
                variant="outline"
                className="justify-start"
                onClick={() => onSelectTemplate(template.actions)}
              >
                <div className="text-left">
                  <div className="font-medium">{template.name}</div>
                  <div className="text-xs text-muted-foreground">{template.description}</div>
                </div>
              </Button>
            ))}
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
};

export default WhiteboardTemplates;