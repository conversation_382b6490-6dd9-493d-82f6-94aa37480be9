/**
 * Mistral Primary Service - Open-Source AI Language Model Service
 * Prioritizes Mistral/Mixtral models over proprietary services
 */

import { CourseStructure, GeneratedQuiz } from '@shared/schema';

// Modal A100 GPU endpoint for Mistral models
const MODAL_MISTRAL_ENDPOINT = process.env.MODAL_GPU_BASE_URL || 'https://trade-digital--courseai-opensource';

interface MistralResponse {
  status: string;
  generated_text?: string;
  error?: string;
  model?: string;
  quantized?: boolean;
}

/**
 * Call Mistral model on Modal A100 GPU
 */
async function callMistralAPI(prompt: string, options: {
  max_length?: number;
  temperature?: number;
  top_p?: number;
  use_quantization?: boolean;
} = {}): Promise<string> {
  try {
    const response = await fetch(`${MODAL_MISTRAL_ENDPOINT}-generate-text.modal.run`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        prompt,
        max_length: options.max_length || 512,
        temperature: options.temperature || 0.7,
        top_p: options.top_p || 0.9,
        use_quantization: options.use_quantization !== false
      })
    });

    if (!response.ok) {
      throw new Error(`Mistral API error: ${response.status} ${response.statusText}`);
    }

    const result: MistralResponse = await response.json();
    
    if (result.status === 'error') {
      throw new Error(`Mistral generation failed: ${result.error}`);
    }

    return result.generated_text || '';
  } catch (error) {
    console.error('Mistral API call failed:', error);
    throw error;
  }
}

/**
 * Generate course structure using Mistral (primary choice)
 */
export async function generateCourseStructureWithMistral(
  courseTitle: string,
  courseDescription: string,
  category: string,
  moduleCount?: number
): Promise<CourseStructure> {
  const prompt = `Create a detailed course structure for an online course titled "${courseTitle}" in the ${category} category.

Course Description: ${courseDescription}

Requirements:
- Generate ${moduleCount || 4} modules
- Each module should have 3-5 lessons
- Include learning objectives for each module
- Provide clear, educational content structure
- Format as JSON with this structure:
{
  "title": "Course Title",
  "description": "Course Description", 
  "modules": [
    {
      "title": "Module Title",
      "description": "Module Description",
      "learningObjectives": ["Objective 1", "Objective 2"],
      "lessons": [
        {
          "title": "Lesson Title",
          "description": "Lesson Description",
          "duration": "15 minutes"
        }
      ]
    }
  ]
}

Generate only the JSON structure, no additional text:`;

  try {
    const response = await callMistralAPI(prompt, {
      max_length: 1024,
      temperature: 0.7
    });

    // Parse JSON response
    const courseStructure = JSON.parse(response);
    
    // Validate structure
    if (!courseStructure.modules || !Array.isArray(courseStructure.modules)) {
      throw new Error('Invalid course structure format');
    }

    return courseStructure;
  } catch (error) {
    console.error('Mistral course structure generation failed:', error);
    throw error;
  }
}

/**
 * Generate lesson script using Mistral
 */
export async function generateLessonScriptWithMistral(
  courseTitle: string,
  courseDescription: string,
  moduleTitle: string,
  moduleDescription: string,
  lessonTitle: string,
  lessonDescription: string,
  targetAudience: string
): Promise<string> {
  const prompt = `Write a comprehensive educational script for a lesson titled "${lessonTitle}".

Context:
- Course: ${courseTitle}
- Course Description: ${courseDescription}
- Module: ${moduleTitle} - ${moduleDescription}
- Lesson: ${lessonTitle} - ${lessonDescription}
- Target Audience: ${targetAudience}

Requirements:
- Write in a conversational, engaging tone suitable for ${targetAudience}
- Include clear explanations and examples
- Structure with introduction, main content, and conclusion
- Optimize for text-to-speech conversion (avoid complex punctuation)
- Length: 800-1200 words
- Use natural speech patterns

Generate the script content only, no additional formatting:`;

  try {
    const response = await callMistralAPI(prompt, {
      max_length: 1500,
      temperature: 0.8
    });

    return response.trim();
  } catch (error) {
    console.error('Mistral lesson script generation failed:', error);
    throw error;
  }
}

/**
 * Generate quiz using Mistral
 */
export async function generateQuizWithMistral(
  courseTitle: string,
  courseDescription: string,
  lessonScript?: string,
  numQuestions: number = 5,
  includeFlashcards: boolean = true,
  includeSummary: boolean = true,
  difficulty: string = "medium"
): Promise<GeneratedQuiz> {
  const prompt = `Create a ${difficulty} difficulty quiz for the course "${courseTitle}".

Course Description: ${courseDescription}
${lessonScript ? `Lesson Content: ${lessonScript.substring(0, 1000)}...` : ''}

Requirements:
- Generate ${numQuestions} multiple choice questions
- Each question should have 4 options (A, B, C, D)
- Include correct answers and explanations
${includeFlashcards ? '- Include 5 flashcards for key concepts' : ''}
${includeSummary ? '- Include a course summary' : ''}

Format as JSON:
{
  "questions": [
    {
      "question": "Question text",
      "options": ["A) Option 1", "B) Option 2", "C) Option 3", "D) Option 4"],
      "correctAnswer": "A",
      "explanation": "Why this answer is correct"
    }
  ],
  ${includeFlashcards ? '"flashcards": [{"front": "Term", "back": "Definition"}],' : ''}
  ${includeSummary ? '"summary": "Course summary text"' : ''}
}

Generate only the JSON, no additional text:`;

  try {
    const response = await callMistralAPI(prompt, {
      max_length: 1200,
      temperature: 0.7
    });

    const quiz = JSON.parse(response);
    
    // Validate quiz structure
    if (!quiz.questions || !Array.isArray(quiz.questions)) {
      throw new Error('Invalid quiz format');
    }

    return quiz;
  } catch (error) {
    console.error('Mistral quiz generation failed:', error);
    throw error;
  }
}

/**
 * Generate module structure using Mistral
 */
export async function generateModuleStructureWithMistral(
  courseTitle: string,
  courseDescription: string,
  existingModules: string[] = [],
  moduleTitle?: string,
  moduleIndex?: number,
  totalModules?: number
): Promise<{ title: string; description: string }> {
  const prompt = `Create a module for the course "${courseTitle}".

Course Description: ${courseDescription}
${existingModules.length > 0 ? `Existing Modules: ${existingModules.join(', ')}` : ''}
${moduleIndex ? `This is module ${moduleIndex} of ${totalModules}` : ''}
${moduleTitle ? `Suggested title: ${moduleTitle}` : ''}

Requirements:
- Create a unique module that complements existing modules
- Provide clear title and description
- Focus on specific learning outcomes
- Ensure logical progression in the course

Format as JSON:
{
  "title": "Module Title",
  "description": "Detailed module description"
}

Generate only the JSON, no additional text:`;

  try {
    const response = await callMistralAPI(prompt, {
      max_length: 300,
      temperature: 0.8
    });

    const module = JSON.parse(response);
    
    if (!module.title || !module.description) {
      throw new Error('Invalid module format');
    }

    return module;
  } catch (error) {
    console.error('Mistral module generation failed:', error);
    // Return fallback module
    return {
      title: moduleTitle || `Module ${moduleIndex || ''} for ${courseTitle}`,
      description: `A comprehensive module covering important topics related to ${courseTitle}.`
    };
  }
}

/**
 * Generate complete course using Mistral
 */
export async function generateFullCourseWithMistral(
  courseTitle: string,
  courseDescription: string,
  category: string
) {
  // Step 1: Generate course structure
  const courseStructure = await generateCourseStructureWithMistral(courseTitle, courseDescription, category);
  
  // Step 2: Generate scripts for each lesson
  const courseScripts: Record<string, Record<string, string>> = {};
  
  for (const module of courseStructure.modules) {
    courseScripts[module.title] = {};
    
    for (const lesson of module.lessons) {
      try {
        const script = await generateLessonScriptWithMistral(
          courseTitle,
          courseDescription,
          module.title,
          module.description,
          lesson.title,
          lesson.description,
          "general audience"
        );
        courseScripts[module.title][lesson.title] = script;
      } catch (error) {
        console.error(`Failed to generate script for ${lesson.title}:`, error);
        courseScripts[module.title][lesson.title] = `Script for ${lesson.title} - ${lesson.description}`;
      }
    }
  }
  
  // Step 3: Generate quiz
  const combinedScripts = Object.values(courseScripts)
    .map(moduleScripts => Object.values(moduleScripts).join('\n\n'))
    .join('\n\n');
    
  const quiz = await generateQuizWithMistral(
    courseTitle,
    courseDescription,
    combinedScripts,
    courseStructure.modules.length + 3,
    true,
    true,
    "medium"
  );
  
  return {
    courseStructure,
    courseScripts,
    quiz
  };
}
