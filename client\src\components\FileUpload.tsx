import React, { useState, ChangeEvent, FormEvent } from 'react';
import { apiRequest } from '@/lib/queryClient';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';
import { Loader2, Upload } from 'lucide-react';

interface FileUploadProps {
  courseId?: number;
  lessonId?: number;
  onUploadComplete?: (media: any) => void;
  allowedTypes?: string[];
  maxSizeInMB?: number;
}

const FileUpload: React.FC<FileUploadProps> = ({
  courseId,
  lessonId,
  onUploadComplete,
  allowedTypes = ['image/*', 'audio/*', 'application/pdf', 'application/msword', 'application/vnd.ms-excel', 'text/plain'],
  maxSizeInMB = 10
}) => {
  const [file, setFile] = useState<File | null>(null);
  const [fileName, setFileName] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const { toast } = useToast();

  const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const selectedFile = e.target.files[0];
      
      // Validate file size
      if (selectedFile.size > maxSizeInMB * 1024 * 1024) {
        setError(`File size must be less than ${maxSizeInMB}MB`);
        setFile(null);
        return;
      }
      
      // Validate file type if allowedTypes is provided
      if (allowedTypes.length > 0) {
        const fileTypeIsValid = allowedTypes.some(type => {
          if (type.includes('*')) {
            // Handle patterns like "image/*"
            const prefix = type.split('/')[0];
            return selectedFile.type.startsWith(`${prefix}/`);
          }
          return selectedFile.type === type;
        });

        if (!fileTypeIsValid) {
          setError(`File type not allowed. Allowed types: ${allowedTypes.join(', ')}`);
          setFile(null);
          return;
        }
      }
      
      setFile(selectedFile);
      setFileName(selectedFile.name);
      setError('');
    }
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    
    if (!file) {
      setError('Please select a file');
      return;
    }
    
    setLoading(true);
    
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('name', fileName);
      
      if (courseId) formData.append('courseId', courseId.toString());
      if (lessonId) formData.append('lessonId', lessonId.toString());
      
      const response = await apiRequest('POST', '/api/media/upload', formData);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to upload file');
      }
      
      const media = await response.json();
      
      toast({
        title: "File uploaded successfully",
        description: "Your file has been uploaded and is ready to use.",
      });
      
      setFile(null);
      setFileName('');
      
      if (onUploadComplete) {
        onUploadComplete(media);
      }
    } catch (error: any) {
      toast({
        title: "Upload failed",
        description: error.message || "Something went wrong",
        variant: "destructive",
      });
      setError(error.message || "Something went wrong");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Upload File</CardTitle>
        <CardDescription>
          Upload images, documents, spreadsheets, audio files, and more. 
          Maximum file size: {maxSizeInMB}MB
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit}>
          <div className="grid w-full items-center gap-4">
            <div className="flex flex-col space-y-1.5">
              <Label htmlFor="file">Select File</Label>
              <Input
                id="file"
                type="file"
                onChange={handleFileChange}
                disabled={loading}
                required
              />
              {error && (
                <p className="text-sm text-red-500">{error}</p>
              )}
            </div>
            <div className="flex flex-col space-y-1.5">
              <Label htmlFor="name">File Name</Label>
              <Input
                id="name"
                placeholder="Custom name (optional)"
                value={fileName}
                onChange={(e) => setFileName(e.target.value)}
                disabled={loading}
              />
            </div>
          </div>
          <CardFooter className="flex justify-end px-0 pt-4">
            <Button type="submit" disabled={loading || !file}>
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Uploading...
                </>
              ) : (
                <>
                  <Upload className="mr-2 h-4 w-4" />
                  Upload
                </>
              )}
            </Button>
          </CardFooter>
        </form>
      </CardContent>
    </Card>
  );
};

export default FileUpload;