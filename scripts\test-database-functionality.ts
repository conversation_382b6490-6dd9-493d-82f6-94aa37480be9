#!/usr/bin/env tsx
/**
 * Comprehensive Database Functionality Test Suite
 * Tests all database operations, connections, and AILearnMaster features
 */

import dotenv from 'dotenv';
import { dbManager, getDatabaseStatus, safeDbOperation } from '../server/db-enhanced';
import { users, courses, modules, lessons, mediaLibrary } from '../shared/schema';
import { eq } from 'drizzle-orm';
import { sql } from 'drizzle-orm';

// Load environment variables
dotenv.config();

interface TestResult {
  name: string;
  passed: boolean;
  duration: number;
  error?: string;
  details?: any;
}

class DatabaseFunctionalityTester {
  private results: TestResult[] = [];
  private testUserId: number | null = null;
  private testCourseId: number | null = null;

  /**
   * Run all database functionality tests
   */
  async runAllTests(): Promise<void> {
    console.log('🧪 Starting Comprehensive Database Functionality Tests\n');
    console.log('=' .repeat(60));

    try {
      // Core connectivity tests
      await this.testDatabaseConnection();
      await this.testDatabaseHealth();
      await this.testTransactionSupport();

      // CRUD operation tests
      await this.testUserOperations();
      await this.testCourseOperations();
      await this.testModuleOperations();
      await this.testLessonOperations();
      await this.testMediaOperations();

      // Advanced feature tests
      await this.testSessionManagement();
      await this.testQueryPerformance();
      await this.testConcurrentOperations();
      await this.testErrorHandling();

      // Cleanup test data
      await this.cleanupTestData();

      // Generate report
      this.generateReport();

    } catch (error) {
      console.error('❌ Test suite failed:', error);
      throw error;
    }
  }

  /**
   * Test basic database connection
   */
  private async testDatabaseConnection(): Promise<void> {
    await this.runTest('Database Connection', async () => {
      const status = getDatabaseStatus();
      
      if (!status.isConnected) {
        throw new Error('Database not connected');
      }

      const result = await safeDbOperation(async () => {
        const db = dbManager.getDatabase();
        const testResult = await db.execute(sql`SELECT 1 as test, NOW() as timestamp`);
        return testResult[0];
      });

      if (!result) {
        throw new Error('Database query failed');
      }

      return { connected: true, timestamp: result.timestamp };
    });
  }

  /**
   * Test database health monitoring
   */
  private async testDatabaseHealth(): Promise<void> {
    await this.runTest('Database Health Monitoring', async () => {
      const status = getDatabaseStatus();
      
      const healthData = {
        isConnected: status.isConnected,
        lastHealthCheck: status.lastHealthCheck,
        maxConnections: status.config.maxConnections,
        environment: status.config.environment
      };

      if (!status.isConnected) {
        throw new Error('Health check indicates database is not connected');
      }

      return healthData;
    });
  }

  /**
   * Test transaction support
   */
  private async testTransactionSupport(): Promise<void> {
    await this.runTest('Transaction Support', async () => {
      const result = await safeDbOperation(async () => {
        const db = dbManager.getDatabase();
        
        return await db.transaction(async (tx) => {
          // Test transaction with multiple operations
          await tx.execute(sql`SELECT 1`);
          await tx.execute(sql`SELECT 2`);
          return { transactionTest: 'success' };
        });
      });

      if (!result) {
        throw new Error('Transaction test failed');
      }

      return result;
    });
  }

  /**
   * Test user CRUD operations
   */
  private async testUserOperations(): Promise<void> {
    await this.runTest('User CRUD Operations', async () => {
      const db = dbManager.getDatabase();
      
      // Create test user
      const [newUser] = await db.insert(users).values({
        username: `test_user_${Date.now()}`,
        email: `test_${Date.now()}@example.com`,
        password: 'hashed_password_test',
        name: 'Test User',
        plan: 'free',
        role: 'user'
      }).returning();

      this.testUserId = newUser.id;

      // Read user
      const [readUser] = await db.select().from(users).where(eq(users.id, newUser.id));
      if (!readUser) {
        throw new Error('Failed to read created user');
      }

      // Update user
      const [updatedUser] = await db.update(users)
        .set({ name: 'Updated Test User' })
        .where(eq(users.id, newUser.id))
        .returning();

      if (updatedUser.name !== 'Updated Test User') {
        throw new Error('Failed to update user');
      }

      return {
        created: newUser.id,
        read: readUser.id,
        updated: updatedUser.name
      };
    });
  }

  /**
   * Test course CRUD operations
   */
  private async testCourseOperations(): Promise<void> {
    await this.runTest('Course CRUD Operations', async () => {
      if (!this.testUserId) {
        throw new Error('Test user not available');
      }

      const db = dbManager.getDatabase();
      
      // Create test course
      const [newCourse] = await db.insert(courses).values({
        userId: this.testUserId,
        title: `Test Course ${Date.now()}`,
        description: 'Test course description',
        category: 'technology',
        status: 'draft'
      }).returning();

      this.testCourseId = newCourse.id;

      // Read course
      const [readCourse] = await db.select().from(courses).where(eq(courses.id, newCourse.id));
      if (!readCourse) {
        throw new Error('Failed to read created course');
      }

      // Update course
      const [updatedCourse] = await db.update(courses)
        .set({ status: 'published' })
        .where(eq(courses.id, newCourse.id))
        .returning();

      if (updatedCourse.status !== 'published') {
        throw new Error('Failed to update course');
      }

      return {
        created: newCourse.id,
        read: readCourse.id,
        updated: updatedCourse.status
      };
    });
  }

  /**
   * Test module operations
   */
  private async testModuleOperations(): Promise<void> {
    await this.runTest('Module Operations', async () => {
      if (!this.testCourseId) {
        throw new Error('Test course not available');
      }

      const db = dbManager.getDatabase();
      
      // Create test module
      const [newModule] = await db.insert(modules).values({
        courseId: this.testCourseId,
        title: `Test Module ${Date.now()}`,
        description: 'Test module description',
        order: 1
      }).returning();

      // Verify module creation
      const [readModule] = await db.select().from(modules).where(eq(modules.id, newModule.id));
      if (!readModule) {
        throw new Error('Failed to read created module');
      }

      return {
        created: newModule.id,
        courseId: newModule.courseId,
        title: readModule.title
      };
    });
  }

  /**
   * Test lesson operations
   */
  private async testLessonOperations(): Promise<void> {
    await this.runTest('Lesson Operations', async () => {
      if (!this.testCourseId) {
        throw new Error('Test course not available');
      }

      const db = dbManager.getDatabase();
      
      // Get a module for the lesson
      const [module] = await db.select().from(modules).where(eq(modules.courseId, this.testCourseId)).limit(1);
      if (!module) {
        throw new Error('No module available for lesson test');
      }

      // Create test lesson
      const [newLesson] = await db.insert(lessons).values({
        courseId: this.testCourseId,
        moduleId: module.id,
        title: `Test Lesson ${Date.now()}`,
        description: 'Test lesson description',
        script: 'Test lesson script content',
        order: 1,
        status: 'draft'
      }).returning();

      // Verify lesson creation
      const [readLesson] = await db.select().from(lessons).where(eq(lessons.id, newLesson.id));
      if (!readLesson) {
        throw new Error('Failed to read created lesson');
      }

      return {
        created: newLesson.id,
        moduleId: newLesson.moduleId,
        title: readLesson.title
      };
    });
  }

  /**
   * Test media operations
   */
  private async testMediaOperations(): Promise<void> {
    await this.runTest('Media Operations', async () => {
      if (!this.testUserId) {
        throw new Error('Test user not available');
      }

      const db = dbManager.getDatabase();
      
      // Create test media
      const [newMedia] = await db.insert(mediaLibrary).values({
        type: 'image',
        name: `test_image_${Date.now()}.jpg`,
        url: 'https://example.com/test-image.jpg',
        userId: this.testUserId,
        mimeType: 'image/jpeg',
        fileSize: 1024,
        source: 'upload'
      }).returning();

      // Verify media creation
      const [readMedia] = await db.select().from(mediaLibrary).where(eq(mediaLibrary.id, newMedia.id));
      if (!readMedia) {
        throw new Error('Failed to read created media');
      }

      return {
        created: newMedia.id,
        type: readMedia.type,
        name: readMedia.name
      };
    });
  }

  /**
   * Test session management
   */
  private async testSessionManagement(): Promise<void> {
    await this.runTest('Session Management', async () => {
      // Test session store functionality
      const sessionTest = await safeDbOperation(async () => {
        const db = dbManager.getDatabase();
        
        // Check if session table exists
        const sessionTableCheck = await db.execute(sql`
          SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name = 'session'
          ) as exists
        `);

        return sessionTableCheck[0];
      });

      return {
        sessionTableExists: sessionTest?.exists || false,
        sessionStoreReady: true
      };
    });
  }

  /**
   * Test query performance
   */
  private async testQueryPerformance(): Promise<void> {
    await this.runTest('Query Performance', async () => {
      const startTime = Date.now();
      
      const result = await safeDbOperation(async () => {
        const db = dbManager.getDatabase();
        
        // Run multiple queries to test performance
        const [userCount] = await db.execute(sql`SELECT COUNT(*) as count FROM users`);
        const [courseCount] = await db.execute(sql`SELECT COUNT(*) as count FROM courses`);
        const [moduleCount] = await db.execute(sql`SELECT COUNT(*) as count FROM modules`);
        
        return {
          users: parseInt(userCount.count as string),
          courses: parseInt(courseCount.count as string),
          modules: parseInt(moduleCount.count as string)
        };
      });

      const duration = Date.now() - startTime;

      return {
        queryDuration: duration,
        counts: result,
        performanceAcceptable: duration < 1000 // Should complete within 1 second
      };
    });
  }

  /**
   * Test concurrent operations
   */
  private async testConcurrentOperations(): Promise<void> {
    await this.runTest('Concurrent Operations', async () => {
      const promises = Array.from({ length: 5 }, async (_, index) => {
        return await safeDbOperation(async () => {
          const db = dbManager.getDatabase();
          await db.execute(sql`SELECT ${index} as test_number, pg_sleep(0.1)`);
          return index;
        });
      });

      const results = await Promise.all(promises);
      const successfulResults = results.filter(r => r !== null);

      return {
        totalOperations: promises.length,
        successfulOperations: successfulResults.length,
        allSuccessful: successfulResults.length === promises.length
      };
    });
  }

  /**
   * Test error handling
   */
  private async testErrorHandling(): Promise<void> {
    await this.runTest('Error Handling', async () => {
      // Test invalid query handling
      const invalidQueryResult = await safeDbOperation(async () => {
        const db = dbManager.getDatabase();
        // This should fail gracefully
        await db.execute(sql`SELECT * FROM non_existent_table`);
        return true;
      });

      // Should return null due to error handling
      const errorHandledCorrectly = invalidQueryResult === null;

      return {
        invalidQueryHandled: errorHandledCorrectly,
        gracefulDegradation: true
      };
    });
  }

  /**
   * Clean up test data
   */
  private async cleanupTestData(): Promise<void> {
    await this.runTest('Cleanup Test Data', async () => {
      const db = dbManager.getDatabase();
      let cleanedItems = 0;

      // Clean up in reverse order of dependencies
      if (this.testCourseId) {
        // Delete lessons
        const deletedLessons = await db.delete(lessons).where(eq(lessons.courseId, this.testCourseId));
        cleanedItems += deletedLessons.length;

        // Delete modules
        const deletedModules = await db.delete(modules).where(eq(modules.courseId, this.testCourseId));
        cleanedItems += deletedModules.length;

        // Delete course
        await db.delete(courses).where(eq(courses.id, this.testCourseId));
        cleanedItems += 1;
      }

      if (this.testUserId) {
        // Delete media
        const deletedMedia = await db.delete(mediaLibrary).where(eq(mediaLibrary.userId, this.testUserId));
        cleanedItems += deletedMedia.length;

        // Delete user
        await db.delete(users).where(eq(users.id, this.testUserId));
        cleanedItems += 1;
      }

      return { cleanedItems };
    });
  }

  /**
   * Run individual test with timing and error handling
   */
  private async runTest(name: string, testFn: () => Promise<any>): Promise<void> {
    const startTime = Date.now();
    
    try {
      console.log(`🔄 Running: ${name}...`);
      const result = await testFn();
      const duration = Date.now() - startTime;
      
      this.results.push({
        name,
        passed: true,
        duration,
        details: result
      });
      
      console.log(`✅ ${name} - ${duration}ms`);
      
    } catch (error) {
      const duration = Date.now() - startTime;
      
      this.results.push({
        name,
        passed: false,
        duration,
        error: error instanceof Error ? error.message : String(error)
      });
      
      console.log(`❌ ${name} - ${duration}ms - ${error}`);
    }
  }

  /**
   * Generate comprehensive test report
   */
  private generateReport(): void {
    console.log('\n' + '='.repeat(60));
    console.log('📊 DATABASE FUNCTIONALITY TEST REPORT');
    console.log('='.repeat(60));

    const totalTests = this.results.length;
    const passedTests = this.results.filter(r => r.passed).length;
    const failedTests = totalTests - passedTests;
    const totalDuration = this.results.reduce((sum, r) => sum + r.duration, 0);

    console.log(`\n📈 SUMMARY:`);
    console.log(`   Total Tests: ${totalTests}`);
    console.log(`   Passed: ${passedTests} ✅`);
    console.log(`   Failed: ${failedTests} ❌`);
    console.log(`   Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
    console.log(`   Total Duration: ${totalDuration}ms`);

    if (failedTests > 0) {
      console.log(`\n❌ FAILED TESTS:`);
      this.results.filter(r => !r.passed).forEach(result => {
        console.log(`   - ${result.name}: ${result.error}`);
      });
    }

    console.log(`\n✅ PASSED TESTS:`);
    this.results.filter(r => r.passed).forEach(result => {
      console.log(`   - ${result.name} (${result.duration}ms)`);
    });

    console.log('\n' + '='.repeat(60));
    
    if (passedTests === totalTests) {
      console.log('🎉 ALL TESTS PASSED! Database functionality is working correctly.');
    } else {
      console.log('⚠️  Some tests failed. Please review the errors above.');
    }
  }
}

// CLI interface
async function main() {
  const tester = new DatabaseFunctionalityTester();
  await tester.runAllTests();
}

// Run if called directly
if (require.main === module) {
  main().catch(error => {
    console.error('Test suite failed:', error);
    process.exit(1);
  });
}

export { DatabaseFunctionalityTester };
