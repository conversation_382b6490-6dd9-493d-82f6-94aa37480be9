
#!/bin/bash

echo "Setting up Sad<PERSON>alker on Replit..."

# Install Python dependencies
echo "Installing Python dependencies..."
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu
pip install opencv-python face-alignment resampy numpy scipy scikit-image pillow imageio imageio-ffmpeg librosa==0.8.0 numba

# Clone SadTalker repository
echo "Cloning SadTalker repository..."
if [ ! -d "SadTalker" ]; then
    git clone https://github.com/OpenTalker/SadTalker.git
fi

cd SadTalker

# Install additional requirements
echo "Installing SadTalker requirements..."
pip install -r requirements.txt

# Download models (this will take some time)
echo "Downloading models... This may take several minutes."
bash scripts/download_models.sh

echo "SadTalker setup complete!"
echo "You can now use local SadTalker in your application."
