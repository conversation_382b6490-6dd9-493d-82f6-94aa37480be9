import { spawn } from 'child_process';
import path from 'path';
import fs from 'fs';
import fetch from 'cross-fetch';

// Voice options for Kokoro TTS
const KOKORO_VOICES = [
  { id: 'kokoro_american_female', name: 'American Female - Professional', language: 'en-US', gender: 'female', style: 'professional', service: 'kokoro' },
  { id: 'kokoro_american_male', name: 'American Male - Professional', language: 'en-US', gender: 'male', style: 'professional', service: 'kokoro' },
  { id: 'kokoro_british_female', name: 'British Female - Narrative', language: 'en-GB', gender: 'female', style: 'narrative', service: 'kokoro' },
  { id: 'kokoro_british_male', name: 'British Male - Conversational', language: 'en-GB', gender: 'male', style: 'conversational', service: 'kokoro' },
  { id: 'kokoro_australian_female', name: 'Australian Female - Friendly', language: 'en-AU', gender: 'female', style: 'friendly', service: 'kokoro' },
  { id: 'kokoro_canadian_male', name: 'Canadian Male - Educational', language: 'en-CA', gender: 'male', style: 'educational', service: 'kokoro' }
];

/**
 * Get available voices for Kokoro TTS
 */
export async function getAvailableVoices(): Promise<Array<{
  id: string;
  name: string;
  language: string;
  gender: string;
  style: string;
  service: string;
}>> {
  try {
    // Check if Kokoro TTS service is available
    const endpoint = process.env.KOKORO_TTS_ENDPOINT || 'http://localhost:8000';
    
    try {
      const response = await fetch(`${endpoint}/voices`, { 
        method: 'GET',
        timeout: 3000 
      });
      
      if (response.ok) {
        const data = await response.json();
        return data.voices || KOKORO_VOICES;
      }
    } catch (error) {
      console.log('Kokoro TTS service not available, using default voices');
    }
    
    return KOKORO_VOICES;
  } catch (error) {
    console.error('Error getting Kokoro TTS voices:', error);
    return KOKORO_VOICES;
  }
}

/**
 * Generate speech using Kokoro TTS
 */
export async function textToSpeech(
  text: string,
  outputPath: string,
  voiceId: string = 'kokoro_american_female',
  options: {
    speed?: number;
    emotion?: string;
    style?: string;
  } = {}
): Promise<string> {
  try {
    const endpoint = process.env.KOKORO_TTS_ENDPOINT || 'http://localhost:8000';
    
    console.log(`Generating speech with Kokoro TTS: ${voiceId}`);
    
    const requestBody = {
      text: text,
      voice_id: voiceId,
      speed: options.speed || 1.0,
      emotion: options.emotion || 'neutral',
      style: options.style || 'professional',
      output_format: 'wav'
    };

    const response = await fetch(`${endpoint}/synthesize`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Kokoro TTS API error (${response.status}): ${errorText}`);
    }

    // Get audio data as arrayBuffer
    const audioBuffer = await response.arrayBuffer();
    
    // Ensure the directory exists
    const dir = path.dirname(outputPath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    
    // Write audio to file
    fs.writeFileSync(outputPath, Buffer.from(audioBuffer));
    
    console.log(`Audio saved to: ${outputPath}`);
    return outputPath;
    
  } catch (error) {
    console.error('Error generating speech with Kokoro TTS:', error);
    throw error;
  }
}

/**
 * Generate preview audio sample
 */
export async function generatePreview(
  voiceId: string,
  sampleText: string = "Hello! This is a preview of how this voice sounds for your course content."
): Promise<Buffer> {
  try {
    const endpoint = process.env.KOKORO_TTS_ENDPOINT || 'http://localhost:8000';
    
    const response = await fetch(`${endpoint}/preview`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        text: sampleText,
        voice_id: voiceId,
        speed: 1.0,
        output_format: 'mp3'
      }),
    });

    if (!response.ok) {
      throw new Error(`Preview generation failed: ${response.statusText}`);
    }

    const audioBuffer = await response.arrayBuffer();
    return Buffer.from(audioBuffer);
    
  } catch (error) {
    console.error('Error generating preview with Kokoro TTS:', error);
    throw error;
  }
}

/**
 * Check if Kokoro TTS service is available
 */
export async function isServiceAvailable(): Promise<boolean> {
  try {
    const endpoint = process.env.KOKORO_TTS_ENDPOINT || 'http://localhost:8000';
    const response = await fetch(`${endpoint}/health`, { 
      method: 'GET',
      timeout: 2000 
    });
    return response.ok;
  } catch (error) {
    return false;
  }
}

export { KOKORO_VOICES };