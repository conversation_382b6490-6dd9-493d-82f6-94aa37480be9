import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { useToast } from '@/hooks/use-toast';
import { useMediaAssignments } from '@/hooks/useMediaAssignments';
import MediaSelector from './MediaSelector';
import { 
  Play, 
  Pause, 
  Video, 
  Volume2, 
  VolumeX,
  Download, 
  Upload,
  Settings,
  Eye,
  Edit,
  Trash2,
  Plus,
  ArrowLeft,
  CheckCircle2,
  AlertCircle,
  Loader2,
  Wand2,
  Clock,
  BookOpen,
  Film,
  Music,
  Image as ImageIcon,
  RefreshCw
} from 'lucide-react';

interface MediaItem {
  id: string;
  name: string;
  title?: string;
  type: 'image' | 'video' | 'document';
  url: string;
  thumbnailUrl?: string;
  size: number;
  uploadedAt: string;
  source: 'upload' | 'pexels' | 'pixabay';
  metadata?: {
    width?: number;
    height?: number;
    duration?: number;
    author?: string;
  };
}

interface VideoProject {
  id: string;
  moduleId: string;
  lessonId: string;
  title: string;
  status: 'pending' | 'processing' | 'completed' | 'error';
  videoUrl?: string;
  audioUrl?: string;
  progress: number;
  assignedMedia?: MediaItem;
}

interface VideoProductionStudioProps {
  modules: any[];
  scripts: any;
  voices: any[];
  quizzes?: any;
  onBack: () => void;
  onNext: () => void;
}

export default function VideoProductionStudio({ 
  modules, 
  scripts, 
  voices, 
  quizzes, 
  onBack, 
  onNext 
}: VideoProductionStudioProps) {
  const [activeTab, setActiveTab] = useState('overview');
  const [videoProjects, setVideoProjects] = useState<VideoProject[]>([]);
  const [isGeneratingAll, setIsGeneratingAll] = useState(false);
  const [overallProgress, setOverallProgress] = useState(0);
  const [selectedVoice, setSelectedVoice] = useState('openai-alloy');
  const [videoStyle, setVideoStyle] = useState('professional');
  const [selectedMediaTarget, setSelectedMediaTarget] = useState<{moduleId: string, lessonId: string, title: string} | null>(null);
  const [showMediaSelector, setShowMediaSelector] = useState(false);
  const [completedVideos, setCompletedVideos] = useState<VideoProject[]>([]);
  
  const { toast } = useToast();
  const { getModuleMedia, getLessonMedia, assignMediaToLesson, assignMediaToModule } = useMediaAssignments();

  // Initialize video projects from modules and scripts
  useEffect(() => {
    const projects: VideoProject[] = [];
    
    modules.forEach(module => {
      module.lessons?.forEach((lesson: any) => {
        const script = scripts[module.id]?.[lesson.id];
        if (script) {
          projects.push({
            id: `${module.id}-${lesson.id}`,
            moduleId: module.id,
            lessonId: lesson.id,
            title: lesson.title,
            status: 'pending',
            progress: 0,
            assignedMedia: getLessonMedia(module.id, lesson.id) || getModuleMedia(module.id)
          });
        }
      });
    });
    
    setVideoProjects(projects);
  }, [modules, scripts, getLessonMedia, getModuleMedia]);

  // Generate voice audio from script
  const generateVoiceForLesson = async (moduleId: string, lessonId: string, script: string) => {
    try {
      const response = await fetch('/api/ai/text-to-speech', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          text: script,
          voice: selectedVoice,
          speed: 1.0
        })
      });

      if (!response.ok) {
        throw new Error('Failed to generate voice');
      }

      const data = await response.json();
      return data.audioUrl;
    } catch (error) {
      console.error('Voice generation error:', error);
      throw error;
    }
  };

  // Auto-select relevant media based on script content
  const autoSelectMedia = async (script: string, moduleId: string, lessonId: string) => {
    try {
      // Extract keywords from script for media search
      const keywords = extractKeywords(script);
      
      // Search for relevant stock media
      const response = await fetch(`/api/stock/photos?q=${encodeURIComponent(keywords.join(' '))}&per_page=5`);
      if (!response.ok) throw new Error('Failed to search media');
      
      const mediaResults = await response.json();
      
      if (mediaResults.results && mediaResults.results.length > 0) {
        const selectedMedia = mediaResults.results[0];
        
        // Import the selected media
        const importResponse = await fetch('/api/stock/import', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            mediaId: selectedMedia.id,
            source: 'auto-select',
            type: 'image'
          })
        });
        
        if (importResponse.ok) {
          const importedMedia = await importResponse.json();
          assignMediaToLesson(moduleId, lessonId, importedMedia.media);
          return importedMedia.media;
        }
      }
      
      return null;
    } catch (error) {
      console.error('Auto media selection error:', error);
      return null;
    }
  };

  // Extract keywords from script for media search
  const extractKeywords = (script: string): string[] => {
    const words = script.toLowerCase().split(/\s+/);
    const stopWords = ['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'];
    const keywords = words
      .filter(word => word.length > 3 && !stopWords.includes(word))
      .slice(0, 5);
    return keywords;
  };

  // Generate single video
  const generateSingleVideo = async (projectId: string) => {
    const project = videoProjects.find(p => p.id === projectId);
    if (!project) return;

    const script = scripts[project.moduleId]?.[project.lessonId];
    if (!script) {
      toast({
        title: "Script Missing",
        description: "Please generate a script for this lesson first.",
        variant: "destructive"
      });
      return;
    }

    // Update project status
    setVideoProjects(prev => prev.map(p => 
      p.id === projectId ? { ...p, status: 'processing', progress: 0 } : p
    ));

    try {
      // Step 1: Generate voice (20%)
      setVideoProjects(prev => prev.map(p => 
        p.id === projectId ? { ...p, progress: 10 } : p
      ));
      
      const audioUrl = await generateVoiceForLesson(project.moduleId, project.lessonId, script);
      
      setVideoProjects(prev => prev.map(p => 
        p.id === projectId ? { ...p, progress: 30, audioUrl } : p
      ));

      // Step 2: Auto-select media if not assigned (40%)
      let mediaItem = project.assignedMedia;
      if (!mediaItem) {
        mediaItem = await autoSelectMedia(script, project.moduleId, project.lessonId);
      }
      
      setVideoProjects(prev => prev.map(p => 
        p.id === projectId ? { ...p, progress: 50, assignedMedia: mediaItem } : p
      ));

      // Step 3: Generate video (60% - 100%)
      const videoResponse = await fetch('/api/ai/generate-video', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          lessonId: project.lessonId,
          moduleId: project.moduleId,
          timeline: [
            {
              id: 'audio',
              type: 'audio',
              startTime: 0,
              duration: 60, // Estimated duration
              content: { audioUrl }
            },
            {
              id: 'media',
              type: 'media',
              startTime: 0,
              duration: 60,
              content: mediaItem
            }
          ],
          settings: {
            resolution: '1080p',
            fps: 30,
            format: 'mp4'
          }
        })
      });

      if (!videoResponse.ok) {
        throw new Error('Failed to start video generation');
      }

      const { jobId } = await videoResponse.json();

      // Poll for completion
      const pollProgress = async () => {
        const statusResponse = await fetch(`/api/ai/video-status/${jobId}`);
        const status = await statusResponse.json();
        
        const progress = 50 + (status.progress || 0) * 0.5;
        setVideoProjects(prev => prev.map(p => 
          p.id === projectId ? { ...p, progress } : p
        ));
        
        if (status.status === 'completed') {
          setVideoProjects(prev => prev.map(p => 
            p.id === projectId ? { 
              ...p, 
              status: 'completed', 
              progress: 100, 
              videoUrl: status.videoUrl 
            } : p
          ));
          
          toast({
            title: "Video Generated",
            description: `Video for "${project.title}" has been generated successfully.`
          });
        } else if (status.status === 'error') {
          throw new Error('Video generation failed');
        } else {
          setTimeout(pollProgress, 2000);
        }
      };
      
      setTimeout(pollProgress, 1000);

    } catch (error: any) {
      console.error('Video generation error:', error);
      setVideoProjects(prev => prev.map(p => 
        p.id === projectId ? { ...p, status: 'error', progress: 0 } : p
      ));
      
      toast({
        title: "Generation Failed",
        description: error.message || "Failed to generate video",
        variant: "destructive"
      });
    }
  };

  // Generate all videos
  const generateAllVideos = async () => {
    setIsGeneratingAll(true);
    setOverallProgress(0);

    const pendingProjects = videoProjects.filter(p => p.status !== 'completed');
    
    for (let i = 0; i < pendingProjects.length; i++) {
      const project = pendingProjects[i];
      await generateSingleVideo(project.id);
      setOverallProgress(((i + 1) / pendingProjects.length) * 100);
    }

    setIsGeneratingAll(false);
  };

  // Handle media selection
  const handleMediaSelect = (moduleId: string, lessonId: string, title: string) => {
    setSelectedMediaTarget({ moduleId, lessonId, title });
    setShowMediaSelector(true);
  };

  const handleMediaAssigned = (moduleId: string, lessonId: string | null, media: MediaItem) => {
    if (lessonId) {
      setVideoProjects(prev => prev.map(p => 
        p.moduleId === moduleId && p.lessonId === lessonId 
          ? { ...p, assignedMedia: media }
          : p
      ));
    }
    setShowMediaSelector(false);
    setSelectedMediaTarget(null);
  };

  // Get completed videos for preview tab
  useEffect(() => {
    setCompletedVideos(videoProjects.filter(p => p.status === 'completed' && p.videoUrl));
  }, [videoProjects]);

  if (showMediaSelector && selectedMediaTarget) {
    return (
      <MediaSelector
        modules={modules}
        onMediaAssigned={handleMediaAssigned}
        onMediaRemoved={() => {}}
        onBack={() => {
          setShowMediaSelector(false);
          setSelectedMediaTarget(null);
        }}
      />
    );
  }

  return (
    <div className="h-full flex flex-col space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Button variant="outline" size="sm" onClick={onBack}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h2 className="text-xl font-semibold">Video Production</h2>
            <p className="text-muted-foreground">
              Generate professional video lessons with automatic voice and media
            </p>
          </div>
        </div>
        <Button onClick={onNext}>
          Continue to Next Step
        </Button>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="overview">Production Overview</TabsTrigger>
          <TabsTrigger value="preview">Video Preview</TabsTrigger>
        </TabsList>

        {/* Production Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          {/* Production Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Video Production Settings
              </CardTitle>
              <CardDescription>
                Configure settings for automatic video generation with voice and media
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Voice Selection</label>
                  <Select value={selectedVoice} onValueChange={setSelectedVoice}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select voice" />
                    </SelectTrigger>
                    <SelectContent>
                      {voices?.map((voice: any) => (
                        <SelectItem key={voice.id} value={voice.id}>
                          {voice.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <label className="text-sm font-medium">Video Style</label>
                  <Select value={videoStyle} onValueChange={setVideoStyle}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select style" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="professional">Professional</SelectItem>
                      <SelectItem value="modern">Modern</SelectItem>
                      <SelectItem value="creative">Creative</SelectItem>
                      <SelectItem value="educational">Educational</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <label className="text-sm font-medium">Quality</label>
                  <Select defaultValue="hd">
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="sd">Standard (720p)</SelectItem>
                      <SelectItem value="hd">High Definition (1080p)</SelectItem>
                      <SelectItem value="4k">Ultra HD (4K)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <Separator />
              
              <div className="flex flex-wrap gap-3">
                <Button 
                  onClick={generateAllVideos}
                  disabled={isGeneratingAll || videoProjects.every(p => p.status === 'completed')}
                  className="flex items-center gap-2"
                >
                  {isGeneratingAll ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin" />
                      Generating All Videos...
                    </>
                  ) : (
                    <>
                      <Video className="h-4 w-4" />
                      Generate All Videos
                    </>
                  )}
                </Button>
                
                <Button variant="outline">
                  <Download className="h-4 w-4 mr-2" />
                  Export All Videos
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Progress Overview */}
          {isGeneratingAll && (
            <Card>
              <CardHeader>
                <CardTitle>Video Generation Progress</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between text-sm">
                    <span>Overall Progress</span>
                    <span>{Math.round(overallProgress)}%</span>
                  </div>
                  <Progress value={overallProgress} className="h-2" />
                </div>
              </CardContent>
            </Card>
          )}

          {/* Lesson Video Production */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Lesson Videos</h3>
            
            {modules.map((module) => (
              <Card key={module.id} className="overflow-hidden">
                <CardHeader className="bg-muted/30">
                  <CardTitle className="text-base flex items-center gap-2">
                    <BookOpen className="h-5 w-5" />
                    {module.title}
                  </CardTitle>
                  <CardDescription>
                    {module.lessons?.length || 0} lessons • Generate videos with automatic voice and media
                  </CardDescription>
                </CardHeader>
                
                <CardContent className="p-0">
                  <div className="space-y-0">
                    {module.lessons?.map((lesson: any) => {
                      const script = scripts[module.id]?.[lesson.id];
                      const project = videoProjects.find(p => p.lessonId === lesson.id);
                      const assignedMedia = getLessonMedia(module.id, lesson.id) || getModuleMedia(module.id);
                      
                      if (!script) return null;
                      
                      return (
                        <div key={lesson.id} className="border-b last:border-b-0 p-4 hover:bg-muted/20 transition-colors">
                          <div className="flex items-center justify-between">
                            <div className="flex-1 min-w-0">
                              <h4 className="font-medium truncate">{lesson.title}</h4>
                              <div className="flex items-center gap-4 mt-2 text-sm text-muted-foreground">
                                <div className="flex items-center gap-1">
                                  <div className="w-2 h-2 rounded-full bg-green-500" />
                                  Script Ready
                                </div>
                                <div className="flex items-center gap-1">
                                  <div className={`w-2 h-2 rounded-full ${project?.audioUrl ? 'bg-green-500' : 'bg-yellow-500'}`} />
                                  Voice {project?.audioUrl ? 'Generated' : 'Auto-Generate'}
                                </div>
                                <div className="flex items-center gap-1">
                                  <div className={`w-2 h-2 rounded-full ${assignedMedia ? 'bg-green-500' : 'bg-blue-500'}`} />
                                  Media {assignedMedia ? 'Assigned' : 'Auto-Select'}
                                </div>
                              </div>
                              {project && project.progress > 0 && (
                                <div className="mt-2">
                                  <Progress value={project.progress} className="h-1" />
                                </div>
                              )}
                            </div>
                            
                            <div className="flex items-center gap-2 ml-4">
                              {project?.status === 'completed' && project.videoUrl ? (
                                <>
                                  <Button size="sm" variant="outline" asChild>
                                    <a href={project.videoUrl} target="_blank" rel="noopener noreferrer">
                                      <Eye className="h-4 w-4 mr-1" />
                                      Preview
                                    </a>
                                  </Button>
                                  <Button size="sm" variant="outline" asChild>
                                    <a href={project.videoUrl} download>
                                      <Download className="h-4 w-4 mr-1" />
                                      Download
                                    </a>
                                  </Button>
                                </>
                              ) : project?.status === 'processing' ? (
                                <Button size="sm" disabled>
                                  <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                                  Generating...
                                </Button>
                              ) : (
                                <Button 
                                  size="sm" 
                                  onClick={() => generateSingleVideo(`${module.id}-${lesson.id}`)}
                                  disabled={isGeneratingAll}
                                >
                                  <Video className="h-4 w-4 mr-1" />
                                  Generate Video
                                </Button>
                              )}
                              
                              <Button 
                                size="sm" 
                                variant="outline" 
                                onClick={() => handleMediaSelect(module.id, lesson.id, lesson.title)}
                              >
                                <Upload className="h-4 w-4 mr-1" />
                                Media
                              </Button>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Video Preview Tab */}
        <TabsContent value="preview" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Film className="h-5 w-5" />
                Generated Videos
              </CardTitle>
              <CardDescription>
                Preview and download your completed video lessons
              </CardDescription>
            </CardHeader>
            <CardContent>
              {completedVideos.length === 0 ? (
                <div className="text-center py-12">
                  <Video className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="font-medium mb-2">No Videos Generated Yet</h3>
                  <p className="text-muted-foreground mb-4">
                    Generate videos from the Production Overview tab to see them here
                  </p>
                  <Button onClick={() => setActiveTab('overview')}>
                    Go to Production
                  </Button>
                </div>
              ) : (
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                  {completedVideos.map((video) => {
                    const module = modules.find(m => m.id === video.moduleId);
                    const lesson = module?.lessons?.find((l: any) => l.id === video.lessonId);
                    
                    return (
                      <Card key={video.id} className="overflow-hidden">
                        <div className="aspect-video bg-muted/50 flex items-center justify-center">
                          <Video className="h-8 w-8 text-muted-foreground" />
                        </div>
                        <CardContent className="p-4">
                          <h4 className="font-medium truncate">{video.title}</h4>
                          <p className="text-sm text-muted-foreground truncate">
                            {module?.title}
                          </p>
                          <div className="flex gap-2 mt-3">
                            <Button size="sm" className="flex-1" asChild>
                              <a href={video.videoUrl} target="_blank" rel="noopener noreferrer">
                                <Play className="h-4 w-4 mr-1" />
                                Play
                              </a>
                            </Button>
                            <Button size="sm" variant="outline" asChild>
                              <a href={video.videoUrl} download>
                                <Download className="h-4 w-4" />
                              </a>
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    );
                  })}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}