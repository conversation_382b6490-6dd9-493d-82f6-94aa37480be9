/**
 * Enhanced Traditional Course Generation Service
 * Leverages Mistral AI + Coqui TTS + Enhanced Media Pipeline for automated course creation
 */

import { dbManager, safeDbOperation } from '../db-enhanced';
import { courses, modules, lessons, mediaLibrary } from '@shared/schema';
import { eq } from 'drizzle-orm';
import * as mistralService from './mistralPrimaryService';
import * as coquiTTS from './coqui-tts';
import * as kokoroTTS from './kokoro-tts';
import * as pexelsService from './pexels-service';
import * as pixabayService from './pixabay-service';
import { marpSlideService } from './marpSlideService';
import { awsS3Service } from './awsS3Service';
import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';

interface CourseGenerationOptions {
  title: string;
  userId: number;
  targetAudience?: string;
  category: string;
  difficulty?: 'beginner' | 'intermediate' | 'advanced';
  duration?: 'short' | 'medium' | 'long'; // 30min, 1-2hr, 3-5hr
  voiceSettings?: {
    voiceId?: string;
    speed?: number;
    pitch?: number;
  };
}

interface GenerationProgress {
  jobId: string;
  status: 'initializing' | 'generating_structure' | 'creating_content' | 'generating_media' | 'assembling_videos' | 'finalizing' | 'completed' | 'error';
  progress: number; // 0-100
  currentStep: string;
  estimatedTimeRemaining?: number;
  courseId?: number;
  error?: string;
  startTime: Date;
  lastUpdate: Date;
}

interface CourseStructure {
  title: string;
  description: string;
  targetAudience: string;
  learningObjectives: string[];
  modules: ModuleStructure[];
  estimatedDuration: number;
}

interface ModuleStructure {
  title: string;
  description: string;
  learningObjectives: string[];
  lessons: LessonStructure[];
  order: number;
}

interface LessonStructure {
  title: string;
  description: string;
  script: string;
  searchTerms: string[];
  duration: number;
  order: number;
  keyPoints: string[];
}

interface MediaAsset {
  type: 'video' | 'image' | 'slide';
  url: string;
  duration?: number;
  searchTerm?: string;
  startTime?: number;
  endTime?: number;
}

export class EnhancedTraditionalCourseGenerator {
  private progressMap = new Map<string, GenerationProgress>();
  private readonly MAX_RETRIES = 3;
  private readonly LESSON_TARGET_DURATION = 300; // 5 minutes in seconds
  private readonly SCENE_CHANGE_INTERVAL = 45; // seconds

  /**
   * Start traditional course generation
   */
  async generateCourse(options: CourseGenerationOptions): Promise<{ jobId: string; estimatedTime: number }> {
    const jobId = uuidv4();
    const estimatedTime = this.calculateEstimatedTime(options);

    // Initialize progress tracking
    const progress: GenerationProgress = {
      jobId,
      status: 'initializing',
      progress: 0,
      currentStep: 'Initializing course generation...',
      estimatedTimeRemaining: estimatedTime,
      startTime: new Date(),
      lastUpdate: new Date()
    };

    this.progressMap.set(jobId, progress);

    // Start generation process asynchronously
    this.executeGeneration(jobId, options).catch(error => {
      console.error(`Course generation failed for job ${jobId}:`, error);
      this.updateProgress(jobId, {
        status: 'error',
        progress: 0,
        currentStep: 'Generation failed',
        error: error.message
      });
    });

    return { jobId, estimatedTime };
  }

  /**
   * Get generation progress
   */
  getProgress(jobId: string): GenerationProgress | null {
    return this.progressMap.get(jobId) || null;
  }

  /**
   * Execute the complete course generation pipeline
   */
  private async executeGeneration(jobId: string, options: CourseGenerationOptions): Promise<void> {
    try {
      // Step 1: Generate course structure (15%)
      this.updateProgress(jobId, {
        status: 'generating_structure',
        progress: 5,
        currentStep: 'Generating course structure with Mistral AI...'
      });

      const courseStructure = await this.generateCourseStructure(options);
      
      this.updateProgress(jobId, {
        progress: 15,
        currentStep: 'Course structure generated successfully'
      });

      // Step 2: Create database entries (20%)
      this.updateProgress(jobId, {
        status: 'creating_content',
        progress: 20,
        currentStep: 'Creating course in database...'
      });

      const courseId = await this.createCourseInDatabase(courseStructure, options);
      
      this.updateProgress(jobId, {
        progress: 25,
        currentStep: 'Course created in database',
        courseId
      });

      // Step 3: Generate media assets (50%)
      this.updateProgress(jobId, {
        status: 'generating_media',
        progress: 30,
        currentStep: 'Generating voice narration and visual media...'
      });

      await this.generateMediaAssets(jobId, courseId, courseStructure);

      // Step 4: Assemble videos (85%)
      this.updateProgress(jobId, {
        status: 'assembling_videos',
        progress: 60,
        currentStep: 'Assembling video lessons with FFmpeg...'
      });

      await this.assembleVideoLessons(jobId, courseId, courseStructure);

      // Step 5: Finalize course (100%)
      this.updateProgress(jobId, {
        status: 'finalizing',
        progress: 90,
        currentStep: 'Finalizing course and updating database...'
      });

      await this.finalizeCourse(courseId);

      this.updateProgress(jobId, {
        status: 'completed',
        progress: 100,
        currentStep: 'Course generation completed successfully!',
        estimatedTimeRemaining: 0
      });

    } catch (error) {
      console.error(`Course generation error for job ${jobId}:`, error);
      throw error;
    }
  }

  /**
   * Generate course structure using Mistral AI
   */
  private async generateCourseStructure(options: CourseGenerationOptions): Promise<CourseStructure> {
    const moduleCount = this.getModuleCount(options.duration);
    const lessonCount = this.getLessonCount(options.duration);

    const prompt = `Create a comprehensive course structure for: "${options.title}"

Requirements:
- Target Audience: ${options.targetAudience || 'General learners'}
- Category: ${options.category}
- Difficulty: ${options.difficulty || 'intermediate'}
- Duration: ${options.duration || 'medium'} course
- Modules: ${moduleCount} modules
- Lessons per module: ${lessonCount} lessons

Generate a detailed course structure with:
1. Course description and learning objectives
2. ${moduleCount} modules with clear titles, descriptions, and learning objectives
3. ${lessonCount} lessons per module with detailed scripts (500-800 words each)
4. 5-10 one-word search terms per lesson for background media
5. Key learning points for each lesson

Format as JSON with this structure:
{
  "title": "Course Title",
  "description": "Comprehensive course description",
  "targetAudience": "Target audience description",
  "learningObjectives": ["objective1", "objective2", ...],
  "estimatedDuration": minutes,
  "modules": [
    {
      "title": "Module Title",
      "description": "Module description",
      "learningObjectives": ["objective1", "objective2"],
      "order": 1,
      "lessons": [
        {
          "title": "Lesson Title",
          "description": "Lesson description",
          "script": "Detailed 500-800 word script",
          "searchTerms": ["term1", "term2", "term3", "term4", "term5"],
          "duration": 300,
          "order": 1,
          "keyPoints": ["point1", "point2", "point3"]
        }
      ]
    }
  ]
}`;

    try {
      // Try Mistral first
      const response = await mistralService.generateText({
        prompt,
        maxTokens: 8000,
        temperature: 0.7
      });

      if (response.success && response.text) {
        const courseStructure = JSON.parse(response.text);
        return this.validateCourseStructure(courseStructure);
      }
      
      throw new Error('Mistral generation failed');
      
    } catch (error) {
      console.error('Mistral course structure generation failed:', error);
      
      // Fallback to Gemini/OpenAI
      return await this.generateCourseStructureFallback(options);
    }
  }

  /**
   * Fallback course structure generation
   */
  private async generateCourseStructureFallback(options: CourseGenerationOptions): Promise<CourseStructure> {
    // Implement fallback using Gemini or OpenAI
    // For now, return a basic structure
    const moduleCount = this.getModuleCount(options.duration);
    const lessonCount = this.getLessonCount(options.duration);

    return {
      title: options.title,
      description: `A comprehensive course on ${options.title} designed for ${options.targetAudience || 'learners'}.`,
      targetAudience: options.targetAudience || 'General learners',
      learningObjectives: [
        `Understand the fundamentals of ${options.title}`,
        `Apply key concepts in practical scenarios`,
        `Develop proficiency in related skills`
      ],
      estimatedDuration: moduleCount * lessonCount * 5, // 5 minutes per lesson
      modules: Array.from({ length: moduleCount }, (_, i) => ({
        title: `Module ${i + 1}: Introduction to ${options.title}`,
        description: `Learn the essential concepts and principles.`,
        learningObjectives: [`Master key concepts`, `Apply practical skills`],
        order: i + 1,
        lessons: Array.from({ length: lessonCount }, (_, j) => ({
          title: `Lesson ${j + 1}: Core Concepts`,
          description: `Explore fundamental principles and applications.`,
          script: this.generateFallbackScript(options.title, i + 1, j + 1),
          searchTerms: ['education', 'learning', 'technology', 'business', 'innovation'],
          duration: 300,
          order: j + 1,
          keyPoints: ['Key concept 1', 'Key concept 2', 'Key concept 3']
        }))
      }))
    };
  }

  /**
   * Generate fallback script content
   */
  private generateFallbackScript(title: string, moduleNum: number, lessonNum: number): string {
    return `Welcome to Module ${moduleNum}, Lesson ${lessonNum} of our comprehensive course on ${title}.

In this lesson, we'll explore the fundamental concepts that form the foundation of ${title}. Understanding these principles is crucial for your success in this field.

Let's begin by examining the key components that make ${title} such an important topic in today's world. We'll cover the essential elements, practical applications, and real-world examples that demonstrate the value of this knowledge.

Throughout this lesson, you'll discover how these concepts connect to broader themes and how you can apply them in your own context. We'll also discuss common challenges and proven strategies for overcoming them.

By the end of this lesson, you'll have a solid understanding of the core principles and be ready to move forward with confidence. Remember to take notes and practice the concepts we discuss.

Let's dive in and explore the fascinating world of ${title} together. Your learning journey starts now, and each step will build upon the previous one to create a comprehensive understanding.

This foundation will serve you well as we progress through more advanced topics in the upcoming lessons. Stay engaged and don't hesitate to review this content as needed.`;
  }

  /**
   * Validate and sanitize course structure
   */
  private validateCourseStructure(structure: any): CourseStructure {
    // Validate required fields and sanitize data
    if (!structure.title || !structure.modules || !Array.isArray(structure.modules)) {
      throw new Error('Invalid course structure format');
    }

    // Ensure all modules have lessons
    structure.modules.forEach((module: any, moduleIndex: number) => {
      if (!module.lessons || !Array.isArray(module.lessons)) {
        throw new Error(`Module ${moduleIndex + 1} missing lessons`);
      }
      
      module.lessons.forEach((lesson: any, lessonIndex: number) => {
        if (!lesson.script || lesson.script.length < 100) {
          throw new Error(`Lesson ${lessonIndex + 1} in module ${moduleIndex + 1} has insufficient script content`);
        }
        
        // Ensure search terms exist
        if (!lesson.searchTerms || !Array.isArray(lesson.searchTerms)) {
          lesson.searchTerms = ['education', 'learning', 'technology', 'business', 'innovation'];
        }
      });
    });

    return structure as CourseStructure;
  }

  /**
   * Create course entries in database
   */
  private async createCourseInDatabase(structure: CourseStructure, options: CourseGenerationOptions): Promise<number> {
    return await safeDbOperation(async () => {
      const db = dbManager.getDatabase();
      
      // Create course
      const [course] = await db.insert(courses).values({
        userId: options.userId,
        title: structure.title,
        description: structure.description,
        targetAudience: structure.targetAudience,
        category: options.category,
        status: 'generating',
        structure: structure as any,
        lessonsCount: structure.modules.reduce((total, module) => total + module.lessons.length, 0)
      }).returning();

      // Create modules and lessons
      for (const moduleData of structure.modules) {
        const [module] = await db.insert(modules).values({
          courseId: course.id,
          title: moduleData.title,
          description: moduleData.description,
          order: moduleData.order,
          lessonsCount: moduleData.lessons.length
        }).returning();

        for (const lessonData of moduleData.lessons) {
          await db.insert(lessons).values({
            courseId: course.id,
            moduleId: module.id,
            title: lessonData.title,
            description: lessonData.description,
            script: lessonData.script,
            duration: lessonData.duration,
            order: lessonData.order,
            status: 'generating'
          });
        }
      }

      return course.id;
    }) || 0;
  }

  /**
   * Calculate module count based on duration
   */
  private getModuleCount(duration?: string): number {
    switch (duration) {
      case 'short': return 3;
      case 'medium': return 5;
      case 'long': return 8;
      default: return 5;
    }
  }

  /**
   * Calculate lesson count per module based on duration
   */
  private getLessonCount(duration?: string): number {
    switch (duration) {
      case 'short': return 5;
      case 'medium': return 6;
      case 'long': return 8;
      default: return 6;
    }
  }

  /**
   * Calculate estimated generation time
   */
  private calculateEstimatedTime(options: CourseGenerationOptions): number {
    const moduleCount = this.getModuleCount(options.duration);
    const lessonCount = this.getLessonCount(options.duration);
    const totalLessons = moduleCount * lessonCount;
    
    // Base time: 2 minutes per lesson + overhead
    return Math.max(300, totalLessons * 120 + 180); // minimum 5 minutes
  }

  /**
   * Update progress tracking
   */
  private updateProgress(jobId: string, updates: Partial<GenerationProgress>): void {
    const current = this.progressMap.get(jobId);
    if (current) {
      const updated = {
        ...current,
        ...updates,
        lastUpdate: new Date()
      };
      
      // Update estimated time remaining
      if (updated.progress > 0) {
        const elapsed = Date.now() - updated.startTime.getTime();
        const totalEstimated = elapsed / (updated.progress / 100);
        updated.estimatedTimeRemaining = Math.max(0, totalEstimated - elapsed);
      }
      
      this.progressMap.set(jobId, updated);
    }
  }

  /**
   * Generate media assets (voice + visuals)
   */
  private async generateMediaAssets(jobId: string, courseId: number, structure: CourseStructure): Promise<void> {
    const totalLessons = structure.modules.reduce((total, module) => total + module.lessons.length, 0);
    let processedLessons = 0;

    for (const module of structure.modules) {
      for (const lesson of module.lessons) {
        try {
          this.updateProgress(jobId, {
            progress: 30 + (processedLessons / totalLessons) * 30,
            currentStep: `Generating media for: ${lesson.title}`
          });

          // Generate voice narration
          const audioUrl = await this.generateVoiceNarration(lesson.script, lesson.title);

          // Generate visual media
          const visualMedia = await this.generateVisualMedia(lesson.searchTerms, lesson.duration);

          // Generate slides if needed
          const slides = await this.generateLessonSlides(lesson);

          // Store media references in database
          await this.storeMediaReferences(courseId, lesson, audioUrl, visualMedia, slides);

          processedLessons++;

        } catch (error) {
          console.error(`Failed to generate media for lesson: ${lesson.title}`, error);
          // Continue with next lesson
        }
      }
    }

    this.updateProgress(jobId, {
      progress: 60,
      currentStep: 'All media assets generated successfully'
    });
  }

  /**
   * Assemble video lessons using FFmpeg
   */
  private async assembleVideoLessons(jobId: string, courseId: number, structure: CourseStructure): Promise<void> {
    const totalLessons = structure.modules.reduce((total, module) => total + module.lessons.length, 0);
    let processedLessons = 0;

    for (const module of structure.modules) {
      for (const lesson of module.lessons) {
        try {
          this.updateProgress(jobId, {
            progress: 60 + (processedLessons / totalLessons) * 25,
            currentStep: `Assembling video for: ${lesson.title}`
          });

          const videoUrl = await this.assembleVideoLesson(courseId, lesson);

          // Update lesson with video URL
          await this.updateLessonVideo(courseId, lesson, videoUrl);

          processedLessons++;

        } catch (error) {
          console.error(`Failed to assemble video for lesson: ${lesson.title}`, error);
          // Continue with next lesson
        }
      }
    }

    this.updateProgress(jobId, {
      progress: 85,
      currentStep: 'All video lessons assembled successfully'
    });
  }

  /**
   * Generate voice narration using Coqui TTS with Kokoro fallback
   */
  private async generateVoiceNarration(script: string, title: string): Promise<string> {
    try {
      // Try Coqui TTS first
      const coquiResult = await coquiTTS.generateSpeech({
        text: script,
        voiceId: 'tts_models/en/ljspeech/tacotron2-DDC',
        language: 'en'
      });

      if (coquiResult.success && coquiResult.audioUrl) {
        return coquiResult.audioUrl;
      }

      throw new Error('Coqui TTS failed');

    } catch (error) {
      console.error('Coqui TTS failed, trying Kokoro TTS:', error);

      // Fallback to Kokoro TTS
      const kokoroResult = await kokoroTTS.generateSpeech({
        text: script,
        voiceId: 'kokoro-v0_19',
        language: 'en'
      });

      if (kokoroResult.success && kokoroResult.audioUrl) {
        return kokoroResult.audioUrl;
      }

      throw new Error('Both Coqui and Kokoro TTS failed');
    }
  }

  /**
   * Generate visual media using Pexels with Pixabay fallback
   */
  private async generateVisualMedia(searchTerms: string[], duration: number): Promise<MediaAsset[]> {
    const mediaAssets: MediaAsset[] = [];
    const scenesNeeded = Math.ceil(duration / this.SCENE_CHANGE_INTERVAL);

    for (let i = 0; i < Math.min(scenesNeeded, searchTerms.length); i++) {
      const searchTerm = searchTerms[i];

      try {
        // Try Pexels first
        const pexelsResult = await pexelsService.searchVideos({
          query: searchTerm,
          per_page: 3,
          orientation: 'landscape'
        });

        if (pexelsResult.videos && pexelsResult.videos.length > 0) {
          const video = pexelsResult.videos[0];
          mediaAssets.push({
            type: 'video',
            url: video.video_files[0].link,
            duration: this.SCENE_CHANGE_INTERVAL,
            searchTerm,
            startTime: i * this.SCENE_CHANGE_INTERVAL,
            endTime: (i + 1) * this.SCENE_CHANGE_INTERVAL
          });
          continue;
        }

        throw new Error('Pexels search failed');

      } catch (error) {
        console.error(`Pexels failed for term "${searchTerm}", trying Pixabay:`, error);

        // Fallback to Pixabay
        try {
          const pixabayResult = await pixabayService.searchVideos({
            q: searchTerm,
            per_page: 3,
            category: 'education'
          });

          if (pixabayResult.hits && pixabayResult.hits.length > 0) {
            const video = pixabayResult.hits[0];
            mediaAssets.push({
              type: 'video',
              url: video.videos.medium.url,
              duration: this.SCENE_CHANGE_INTERVAL,
              searchTerm,
              startTime: i * this.SCENE_CHANGE_INTERVAL,
              endTime: (i + 1) * this.SCENE_CHANGE_INTERVAL
            });
          }
        } catch (pixabayError) {
          console.error(`Both Pexels and Pixabay failed for term "${searchTerm}"`);
          // Use a default placeholder video or image
        }
      }
    }

    return mediaAssets;
  }

  /**
   * Generate lesson slides using Marp CLI
   */
  private async generateLessonSlides(lesson: LessonStructure): Promise<MediaAsset[]> {
    try {
      const slideContent = this.createSlideMarkdown(lesson);

      const slideResult = await marpSlideService.generateSlides({
        markdown: slideContent,
        theme: 'default',
        format: 'html'
      });

      if (slideResult.success && slideResult.outputPath) {
        return [{
          type: 'slide',
          url: slideResult.outputPath,
          duration: 10, // 10 seconds per slide
          searchTerm: 'educational-slide'
        }];
      }
    } catch (error) {
      console.error('Slide generation failed:', error);
    }

    return [];
  }

  /**
   * Create slide markdown content
   */
  private createSlideMarkdown(lesson: LessonStructure): string {
    return `---
marp: true
theme: default
class: lead
paginate: true
---

# ${lesson.title}

${lesson.description}

---

## Key Learning Points

${lesson.keyPoints.map(point => `- ${point}`).join('\n')}

---

## Summary

${lesson.script.substring(0, 200)}...

---

## Next Steps

Continue to the next lesson to build upon these concepts.
`;
  }

  /**
   * Store media references in database
   */
  private async storeMediaReferences(
    courseId: number,
    lesson: LessonStructure,
    audioUrl: string,
    visualMedia: MediaAsset[],
    slides: MediaAsset[]
  ): Promise<void> {
    await safeDbOperation(async () => {
      const db = dbManager.getDatabase();

      // Store audio
      await db.insert(mediaLibrary).values({
        type: 'audio',
        name: `${lesson.title}-narration.mp3`,
        url: audioUrl,
        userId: 1, // System generated
        mimeType: 'audio/mpeg',
        fileSize: 0,
        source: 'coqui_tts',
        lessonId: lesson.order,
        courseId
      });

      // Store visual media
      for (const media of visualMedia) {
        await db.insert(mediaLibrary).values({
          type: media.type,
          name: `${lesson.title}-${media.searchTerm}.mp4`,
          url: media.url,
          userId: 1,
          mimeType: 'video/mp4',
          fileSize: 0,
          source: 'pexels',
          lessonId: lesson.order,
          courseId
        });
      }

      // Store slides
      for (const slide of slides) {
        await db.insert(mediaLibrary).values({
          type: 'slide',
          name: `${lesson.title}-slides.html`,
          url: slide.url,
          userId: 1,
          mimeType: 'text/html',
          fileSize: 0,
          source: 'marp',
          lessonId: lesson.order,
          courseId
        });
      }
    });
  }

  /**
   * Assemble individual video lesson using FFmpeg
   */
  private async assembleVideoLesson(courseId: number, lesson: LessonStructure): Promise<string> {
    const tempDir = path.join(process.cwd(), 'temp', `course-${courseId}`, `lesson-${lesson.order}`);

    // Create temp directory
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    try {
      // Get media assets from database
      const mediaAssets = await this.getLessonMediaAssets(courseId, lesson.order);

      // Download and prepare media files
      const preparedMedia = await this.prepareMediaFiles(tempDir, mediaAssets);

      // Create FFmpeg command for video assembly
      const outputPath = path.join(tempDir, `${lesson.title.replace(/[^a-zA-Z0-9]/g, '_')}.mp4`);
      const ffmpegCommand = this.buildFFmpegCommand(preparedMedia, outputPath, lesson.duration);

      // Execute FFmpeg
      await this.executeFFmpeg(ffmpegCommand);

      // Upload to S3 and get URL
      const s3Url = await this.uploadVideoToS3(outputPath, courseId, lesson);

      // Cleanup temp files
      this.cleanupTempFiles(tempDir);

      return s3Url;

    } catch (error) {
      console.error(`Video assembly failed for lesson ${lesson.title}:`, error);
      this.cleanupTempFiles(tempDir);
      throw error;
    }
  }

  /**
   * Get lesson media assets from database
   */
  private async getLessonMediaAssets(courseId: number, lessonOrder: number): Promise<any[]> {
    return await safeDbOperation(async () => {
      const db = dbManager.getDatabase();
      return await db.select()
        .from(mediaLibrary)
        .where(eq(mediaLibrary.courseId, courseId) && eq(mediaLibrary.lessonId, lessonOrder));
    }) || [];
  }

  /**
   * Prepare media files for FFmpeg processing
   */
  private async prepareMediaFiles(tempDir: string, mediaAssets: any[]): Promise<any[]> {
    const preparedMedia = [];

    for (const asset of mediaAssets) {
      try {
        const localPath = path.join(tempDir, `${asset.type}_${asset.id}.${this.getFileExtension(asset.mimeType)}`);

        // Download file locally
        await this.downloadFile(asset.url, localPath);

        preparedMedia.push({
          ...asset,
          localPath,
          duration: asset.type === 'audio' ? null : this.SCENE_CHANGE_INTERVAL
        });

      } catch (error) {
        console.error(`Failed to prepare media asset ${asset.id}:`, error);
      }
    }

    return preparedMedia;
  }

  /**
   * Build FFmpeg command for video assembly
   */
  private buildFFmpegCommand(mediaAssets: any[], outputPath: string, duration: number): string {
    const audioFile = mediaAssets.find(asset => asset.type === 'audio');
    const videoFiles = mediaAssets.filter(asset => asset.type === 'video');
    const slideFiles = mediaAssets.filter(asset => asset.type === 'slide');

    if (!audioFile) {
      throw new Error('No audio file found for video assembly');
    }

    // Basic FFmpeg command structure
    let command = 'ffmpeg -y '; // -y to overwrite output file

    // Add video inputs
    videoFiles.forEach(video => {
      command += `-i "${video.localPath}" `;
    });

    // Add audio input
    command += `-i "${audioFile.localPath}" `;

    // Create video filter for scene transitions
    const videoFilter = this.createVideoFilter(videoFiles, duration);

    // Add filters and output settings
    command += `-filter_complex "${videoFilter}" `;
    command += `-c:v libx264 -c:a aac -b:a 128k `;
    command += `-r 30 -s 1920x1080 `;
    command += `-t ${duration} `;
    command += `"${outputPath}"`;

    return command;
  }

  /**
   * Create video filter for scene transitions
   */
  private createVideoFilter(videoFiles: any[], duration: number): string {
    if (videoFiles.length === 0) {
      return '[0:v]scale=1920:1080[v]';
    }

    if (videoFiles.length === 1) {
      return '[0:v]scale=1920:1080,loop=loop=-1:size=1:start=0[v]';
    }

    // Create complex filter for multiple videos with transitions
    let filter = '';
    const sceneDuration = this.SCENE_CHANGE_INTERVAL;

    videoFiles.forEach((video, index) => {
      filter += `[${index}:v]scale=1920:1080,setpts=PTS-STARTPTS[v${index}];`;
    });

    // Concatenate videos with crossfade transitions
    filter += videoFiles.map((_, index) => `[v${index}]`).join('') +
              `concat=n=${videoFiles.length}:v=1:a=0[v]`;

    return filter;
  }

  /**
   * Execute FFmpeg command
   */
  private async executeFFmpeg(command: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const { exec } = require('child_process');

      exec(command, { timeout: 300000 }, (error: any, stdout: any, stderr: any) => {
        if (error) {
          console.error('FFmpeg error:', error);
          console.error('FFmpeg stderr:', stderr);
          reject(error);
        } else {
          console.log('FFmpeg completed successfully');
          resolve();
        }
      });
    });
  }

  /**
   * Upload video to S3 and return URL
   */
  private async uploadVideoToS3(localPath: string, courseId: number, lesson: LessonStructure): Promise<string> {
    try {
      const fileName = `courses/${courseId}/lessons/${lesson.order}/${lesson.title.replace(/[^a-zA-Z0-9]/g, '_')}.mp4`;
      const s3Url = await awsS3Service.uploadFile(localPath, fileName);
      return s3Url;
    } catch (error) {
      console.error('S3 upload failed:', error);
      throw error;
    }
  }

  /**
   * Update lesson with video URL
   */
  private async updateLessonVideo(courseId: number, lesson: LessonStructure, videoUrl: string): Promise<void> {
    await safeDbOperation(async () => {
      const db = dbManager.getDatabase();
      await db.update(lessons)
        .set({
          videoUrl,
          status: 'completed',
          updatedAt: new Date()
        })
        .where(eq(lessons.courseId, courseId) && eq(lessons.order, lesson.order));
    });
  }

  /**
   * Download file from URL to local path
   */
  private async downloadFile(url: string, localPath: string): Promise<void> {
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`Failed to download file: ${response.statusText}`);
    }

    const buffer = await response.arrayBuffer();
    fs.writeFileSync(localPath, Buffer.from(buffer));
  }

  /**
   * Get file extension from MIME type
   */
  private getFileExtension(mimeType: string): string {
    const extensions: { [key: string]: string } = {
      'audio/mpeg': 'mp3',
      'audio/wav': 'wav',
      'video/mp4': 'mp4',
      'video/webm': 'webm',
      'text/html': 'html',
      'image/jpeg': 'jpg',
      'image/png': 'png'
    };

    return extensions[mimeType] || 'bin';
  }

  /**
   * Cleanup temporary files
   */
  private cleanupTempFiles(tempDir: string): void {
    try {
      if (fs.existsSync(tempDir)) {
        fs.rmSync(tempDir, { recursive: true, force: true });
      }
    } catch (error) {
      console.error('Failed to cleanup temp files:', error);
    }
  }

  /**
   * Finalize course and update status
   */
  private async finalizeCourse(courseId: number): Promise<void> {
    await safeDbOperation(async () => {
      const db = dbManager.getDatabase();
      await db.update(courses)
        .set({
          status: 'published',
          completion: 100,
          updatedAt: new Date()
        })
        .where(eq(courses.id, courseId));
    });
  }
}

export const enhancedTraditionalCourseGenerator = new EnhancedTraditionalCourseGenerator();
