import React, { useEffect, useRef, useState } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';
import { Download, Save, FileUp, FileDown, Trash2 } from 'lucide-react';
// Import Excalidraw components
import { Excalidraw, exportToSvg } from '@excalidraw/excalidraw';
import type { ExcalidrawElement } from '@excalidraw/excalidraw/types/element/types';

interface ExcalidrawWhiteboardProps {
  onSaved?: (url: string) => void;
  courseId?: number;
  lessonId?: number;
  heading?: string;
  description?: string;
}

export function ExcalidrawWhiteboard({
  onSaved,
  courseId,
  lessonId,
  heading = 'Interactive Whiteboard',
  description = 'Create diagrams, drawings, and sketches with Excalidraw.'
}: ExcalidrawWhiteboardProps) {
  const { toast } = useToast();
  const [elements, setElements] = useState<readonly ExcalidrawElement[]>([]);
  const [saving, setSaving] = useState(false);
  const [loading, setLoading] = useState(false);
  const [savedUrl, setSavedUrl] = useState<string | null>(null);
  const excalidrawRef = useRef<any>(null);
  
  // Load saved whiteboard if course and lesson IDs are provided
  useEffect(() => {
    if (courseId && lessonId) {
      loadSavedWhiteboard();
    }
  }, [courseId, lessonId]);
  
  const loadSavedWhiteboard = async () => {
    if (!courseId || !lessonId) return;
    
    setLoading(true);
    
    try {
      const response = await apiRequest(
        'GET',
        `/api/courses/${courseId}/lessons/${lessonId}/whiteboard`,
        undefined
      );
      
      const data = await response.json();
      
      if (data.elements) {
        setElements(data.elements);
        setSavedUrl(data.url || null);
      }
    } catch (error) {
      console.error('Error loading whiteboard:', error);
      // Don't show an error toast here as it's expected that a new lesson won't have a saved whiteboard
    } finally {
      setLoading(false);
    }
  };
  
  const saveWhiteboard = async () => {
    if (!excalidrawRef.current) {
      toast({
        title: 'Error',
        description: 'Whiteboard not initialized',
        variant: 'destructive'
      });
      return;
    }
    
    setSaving(true);
    
    try {
      const currentElements = excalidrawRef.current.getSceneElements();
      
      if (currentElements.length === 0) {
        toast({
          title: 'Empty whiteboard',
          description: 'Please add some content to the whiteboard before saving',
          variant: 'destructive'
        });
        setSaving(false);
        return;
      }
      
      const response = await apiRequest(
        'POST',
        '/api/ai-tools/excalidraw',
        {
          elements: currentElements,
          courseId,
          lessonId
        }
      );
      
      const data = await response.json();
      
      if (data.url) {
        setSavedUrl(data.url);
        
        if (onSaved) {
          onSaved(data.url);
        }
        
        toast({
          title: 'Whiteboard saved',
          description: 'Your whiteboard has been saved successfully'
        });
      } else {
        toast({
          title: 'Error',
          description: data.message || 'Failed to save whiteboard',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('Error saving whiteboard:', error);
      toast({
        title: 'Error',
        description: 'Failed to save whiteboard',
        variant: 'destructive'
      });
    } finally {
      setSaving(false);
    }
  };
  
  const exportAsImage = async () => {
    if (!excalidrawRef.current) return;
    
    try {
      const elements = excalidrawRef.current.getSceneElements();
      
      if (elements.length === 0) {
        toast({
          title: 'Empty whiteboard',
          description: 'Please add some content to the whiteboard before exporting',
          variant: 'destructive'
        });
        return;
      }
      
      const svg = await exportToSvg({
        elements,
        appState: excalidrawRef.current.getAppState(),
        files: excalidrawRef.current.getFiles(),
      });
      
      // Convert SVG to a data URL
      const svgString = new XMLSerializer().serializeToString(svg);
      const blob = new Blob([svgString], { type: 'image/svg+xml' });
      const url = URL.createObjectURL(blob);
      
      // Create a link and trigger download
      const link = document.createElement('a');
      link.href = url;
      link.download = `whiteboard-${Date.now()}.svg`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      // Clean up the URL
      setTimeout(() => URL.revokeObjectURL(url), 5000);
    } catch (error) {
      console.error('Error exporting whiteboard:', error);
      toast({
        title: 'Export failed',
        description: 'Failed to export whiteboard as image',
        variant: 'destructive'
      });
    }
  };
  
  const exportToMedia = async () => {
    if (!excalidrawRef.current) return;
    
    try {
      const elements = excalidrawRef.current.getSceneElements();
      
      if (elements.length === 0) {
        toast({
          title: 'Empty whiteboard',
          description: 'Please add some content to the whiteboard before exporting',
          variant: 'destructive'
        });
        return;
      }
      
      // Convert to SVG
      const svg = await exportToSvg({
        elements,
        appState: excalidrawRef.current.getAppState(),
        files: excalidrawRef.current.getFiles(),
      });
      
      // Convert SVG to a Blob
      const svgString = new XMLSerializer().serializeToString(svg);
      const blob = new Blob([svgString], { type: 'image/svg+xml' });
      
      // Create form data for the upload
      const formData = new FormData();
      formData.append('file', blob, `whiteboard-${Date.now()}.svg`);
      
      if (courseId) {
        formData.append('courseId', courseId.toString());
      }
      
      if (lessonId) {
        formData.append('lessonId', lessonId.toString());
      }
      
      formData.append('type', 'image');
      formData.append('name', 'Whiteboard Drawing');
      
      // Upload to media library
      const response = await fetch('/api/media/upload', {
        method: 'POST',
        body: formData
      });
      
      const data = await response.json();
      
      if (data.url) {
        toast({
          title: 'Export successful',
          description: 'Whiteboard has been added to your media library'
        });
      } else {
        throw new Error(data.message || 'Failed to export to media library');
      }
    } catch (error) {
      console.error('Error exporting to media library:', error);
      toast({
        title: 'Export failed',
        description: 'Failed to export whiteboard to media library',
        variant: 'destructive'
      });
    }
  };
  
  const resetWhiteboard = () => {
    if (!excalidrawRef.current) return;
    
    excalidrawRef.current.resetScene();
    setElements([]);
  };
  
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>{heading}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="h-[600px] border rounded-lg overflow-hidden">
          <Excalidraw
            ref={excalidrawRef}
            initialData={{
              elements: elements as ExcalidrawElement[],
              appState: { viewBackgroundColor: '#ffffff' }
            }}
            onChange={(elements) => setElements(elements)}
          />
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <div>
          <Button 
            variant="destructive" 
            size="sm" 
            onClick={resetWhiteboard}
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Clear
          </Button>
        </div>
        <div className="space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={exportAsImage}
          >
            <FileDown className="h-4 w-4 mr-2" />
            Download SVG
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={exportToMedia}
          >
            <FileUp className="h-4 w-4 mr-2" />
            Add to Media
          </Button>
          
          <Button
            size="sm"
            disabled={saving}
            onClick={saveWhiteboard}
          >
            <Save className="h-4 w-4 mr-2" />
            {saving ? 'Saving...' : 'Save Whiteboard'}
          </Button>
        </div>
      </CardFooter>
    </Card>
  );
}

export default ExcalidrawWhiteboard;