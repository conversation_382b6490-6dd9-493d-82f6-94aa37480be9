#!/usr/bin/env python3
"""
Production Modal A100 GPU Application for Course AI Platform
High-performance GPU computing with web endpoints for Replit integration

This app provides:
- A100 80G GPU acceleration for AI workloads
- Web endpoints for HTTP API integration
- GPU health monitoring and status checks
- Sample functions for testing and development
- Production-ready error handling and logging
"""

import modal
import os
import json
import base64
import io
import time
import torch
from typing import Dict, Any, List, Optional
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Modal app configuration
app = modal.App("courseai-a100-gpu")

# A100 80G GPU image with essential dependencies
gpu_image = (
    modal.Image.debian_slim(python_version="3.11")
    .pip_install([
        "torch",
        "torchvision", 
        "torchaudio",
        "transformers",
        "diffusers",
        "accelerate",
        "pillow",
        "numpy",
        "requests",
        "fastapi",
        "pydantic"
    ])
    .run_commands([
        "pip install --upgrade pip",
        "pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118"
    ])
)

@app.function(
    image=gpu_image,
    gpu=modal.gpu.A100(count=1, size="80GB"),
    timeout=600,
    container_idle_timeout=300,
    allow_concurrent_inputs=10,
    memory=32768
)
def gpu_health_check() -> Dict[str, Any]:
    """
    Comprehensive A100 GPU health check and system information
    Returns detailed GPU status and capabilities
    """
    try:
        import torch
        
        # GPU availability check
        gpu_available = torch.cuda.is_available()
        gpu_count = torch.cuda.device_count()
        
        if gpu_available and gpu_count > 0:
            gpu_name = torch.cuda.get_device_name(0)
            gpu_memory_total = torch.cuda.get_device_properties(0).total_memory
            gpu_memory_allocated = torch.cuda.memory_allocated(0)
            gpu_memory_free = gpu_memory_total - gpu_memory_allocated
            
            # Test tensor operations
            test_tensor = torch.randn(1000, 1000, device='cuda')
            computation_test = torch.matmul(test_tensor, test_tensor.T)
            
            return {
                "status": "healthy",
                "gpu_available": True,
                "gpu_count": gpu_count,
                "gpu_name": gpu_name,
                "gpu_memory_total_gb": round(gpu_memory_total / (1024**3), 2),
                "gpu_memory_free_gb": round(gpu_memory_free / (1024**3), 2),
                "gpu_memory_allocated_gb": round(gpu_memory_allocated / (1024**3), 2),
                "torch_version": torch.__version__,
                "cuda_version": torch.version.cuda,
                "computation_test_passed": computation_test.shape == (1000, 1000),
                "timestamp": time.time()
            }
        else:
            return {
                "status": "error",
                "gpu_available": False,
                "error": "No GPU detected",
                "timestamp": time.time()
            }
            
    except Exception as e:
        logger.error(f"GPU health check failed: {str(e)}")
        return {
            "status": "error",
            "error": str(e),
            "timestamp": time.time()
        }

@app.function(
    image=gpu_image,
    gpu=modal.gpu.A100(count=1, size="80GB"),
    timeout=300,
    allow_concurrent_inputs=5
)
def gpu_echo_test(message: str = "Hello from A100 GPU!") -> Dict[str, Any]:
    """
    Simple echo function to test GPU connectivity and response
    """
    try:
        import torch
        
        # Create a small tensor operation on GPU
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        test_tensor = torch.randn(100, 100, device=device)
        result = torch.sum(test_tensor).item()
        
        return {
            "echo_message": message,
            "gpu_device": str(device),
            "tensor_sum": result,
            "gpu_name": torch.cuda.get_device_name(0) if torch.cuda.is_available() else "No GPU",
            "timestamp": time.time(),
            "status": "success"
        }
        
    except Exception as e:
        logger.error(f"Echo test failed: {str(e)}")
        return {
            "status": "error",
            "error": str(e),
            "timestamp": time.time()
        }

@app.function(
    image=gpu_image,
    gpu=modal.gpu.A100(count=1, size="80GB"),
    timeout=600,
    allow_concurrent_inputs=3
)
def gpu_image_generation_demo(
    prompt: str = "A beautiful landscape with mountains and a lake",
    width: int = 512,
    height: int = 512,
    num_inference_steps: int = 20
) -> Dict[str, Any]:
    """
    Demo image generation function using Stable Diffusion on A100 GPU
    """
    try:
        from diffusers import StableDiffusionPipeline
        import torch
        from PIL import Image
        import io
        import base64
        
        # Load model on GPU
        device = torch.device('cuda')
        pipe = StableDiffusionPipeline.from_pretrained(
            "runwayml/stable-diffusion-v1-5",
            torch_dtype=torch.float16
        ).to(device)
        
        # Generate image
        image = pipe(
            prompt,
            width=width,
            height=height,
            num_inference_steps=num_inference_steps
        ).images[0]
        
        # Convert to base64
        buffer = io.BytesIO()
        image.save(buffer, format='PNG')
        image_base64 = base64.b64encode(buffer.getvalue()).decode()
        
        return {
            "status": "success",
            "prompt": prompt,
            "image_base64": image_base64,
            "dimensions": f"{width}x{height}",
            "inference_steps": num_inference_steps,
            "timestamp": time.time()
        }
        
    except Exception as e:
        logger.error(f"Image generation failed: {str(e)}")
        return {
            "status": "error",
            "error": str(e),
            "timestamp": time.time()
        }

@app.function(
    image=gpu_image,
    gpu=modal.gpu.A100(count=1, size="80GB"),
    timeout=300,
    allow_concurrent_inputs=5
)
def gpu_text_analysis(text: str) -> Dict[str, Any]:
    """
    Demo text analysis using transformers on A100 GPU
    """
    try:
        from transformers import pipeline
        import torch
        
        device = 0 if torch.cuda.is_available() else -1
        
        # Load sentiment analysis pipeline
        sentiment_analyzer = pipeline(
            "sentiment-analysis",
            model="cardiffnlp/twitter-roberta-base-sentiment-latest",
            device=device
        )
        
        # Analyze text
        result = sentiment_analyzer(text)
        
        return {
            "status": "success",
            "text": text,
            "sentiment": result[0],
            "gpu_used": device == 0,
            "timestamp": time.time()
        }
        
    except Exception as e:
        logger.error(f"Text analysis failed: {str(e)}")
        return {
            "status": "error",
            "error": str(e),
            "timestamp": time.time()
        }

# Web endpoints for HTTP API integration
@app.function(
    image=gpu_image,
    allow_concurrent_inputs=10
)
@modal.web_endpoint(method="GET")
def health():
    """Health check endpoint"""
    try:
        # Call GPU health check
        gpu_status = gpu_health_check.remote()
        return {
            "status": "online",
            "service": "CourseAI A100 GPU Backend",
            "gpu_status": gpu_status,
            "timestamp": time.time()
        }
    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "timestamp": time.time()
        }

@app.function(
    image=gpu_image,
    allow_concurrent_inputs=5
)
@modal.web_endpoint(method="POST")
def api_echo(item: Dict[str, Any]):
    """Echo API endpoint for testing"""
    try:
        message = item.get("message", "Hello from Modal A100!")
        result = gpu_echo_test.remote(message)
        return result
    except Exception as e:
        logger.error(f"Echo API failed: {str(e)}")
        return {
            "status": "error",
            "error": str(e),
            "timestamp": time.time()
        }

@app.function(
    image=gpu_image,
    allow_concurrent_inputs=3
)
@modal.web_endpoint(method="POST")
def api_generate_image(item: Dict[str, Any]):
    """Image generation API endpoint"""
    try:
        prompt = item.get("prompt", "A beautiful landscape")
        width = item.get("width", 512)
        height = item.get("height", 512)
        steps = item.get("num_inference_steps", 20)
        
        result = gpu_image_generation_demo.remote(prompt, width, height, steps)
        return result
    except Exception as e:
        logger.error(f"Image generation API failed: {str(e)}")
        return {
            "status": "error",
            "error": str(e),
            "timestamp": time.time()
        }

@app.function(
    image=gpu_image,
    allow_concurrent_inputs=5
)
@modal.web_endpoint(method="POST")
def api_analyze_text(item: Dict[str, Any]):
    """Text analysis API endpoint"""
    try:
        text = item.get("text", "This is a sample text for analysis.")
        result = gpu_text_analysis.remote(text)
        return result
    except Exception as e:
        logger.error(f"Text analysis API failed: {str(e)}")
        return {
            "status": "error",
            "error": str(e),
            "timestamp": time.time()
        }

# Local testing
if __name__ == "__main__":
    # Test functions locally (will use CPU)
    print("Testing Modal A100 GPU Application...")
    
    # Test echo
    echo_result = gpu_echo_test("Local test message")
    print(f"Echo test: {echo_result}")
    
    # Test health check
    health_result = gpu_health_check()
    print(f"Health check: {health_result}")