import React, { useState, useEffect, useRef } from 'react';
import { Link, useLocation } from 'wouter';
import { motion, useInView, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { useAuth } from '@/hooks/use-auth';
import { ProfileDropdown } from '@/components/header/ProfileDropdown';
import { SubscriptionBadge } from '@/components/header/SubscriptionBadge';
import { 
  Play, 
  Sparkles, 
  Video, 
  Mic, 
  Image, 
  BookOpen, 
  Users, 
  Zap, 
  ArrowRight, 
  Check, 
  Star,
  Brain,
  Camera,
  Presentation,
  FileVideo,
  Headphones,
  Globe,
  Trophy,
  Rocket,
  Shield,
  Clock,
  TrendingUp,
  Menu,
  X
} from 'lucide-react';

// Animation variants
const fadeInUp = {
  initial: { opacity: 0, y: 60 },
  animate: { opacity: 1, y: 0 },
  transition: { duration: 0.6 }
};

const fadeInLeft = {
  initial: { opacity: 0, x: -60 },
  animate: { opacity: 1, x: 0 },
  transition: { duration: 0.6 }
};

const fadeInRight = {
  initial: { opacity: 0, x: 60 },
  animate: { opacity: 1, x: 0 },
  transition: { duration: 0.6 }
};

const staggerChildren = {
  animate: {
    transition: {
      staggerChildren: 0.1
    }
  }
};

// Animated counter component
function AnimatedCounter({ value, suffix = '' }: { value: number; suffix?: string }) {
  const [count, setCount] = useState(0);
  const ref = useRef(null);
  const isInView = useInView(ref);

  useEffect(() => {
    if (isInView) {
      const timer = setInterval(() => {
        setCount((prev) => {
          if (prev >= value) {
            clearInterval(timer);
            return value;
          }
          return prev + Math.ceil(value / 50);
        });
      }, 30);
      return () => clearInterval(timer);
    }
  }, [isInView, value]);

  return <span ref={ref}>{count.toLocaleString()}{suffix}</span>;
}

// Feature highlight component
function FeatureHighlight({ icon: Icon, title, description, gradient }: {
  icon: any;
  title: string;
  description: string;
  gradient: string;
}) {
  return (
    <motion.div
      variants={fadeInUp}
      className="group relative overflow-hidden rounded-2xl bg-white border border-gray-200 p-6 hover:shadow-xl transition-all duration-300"
    >
      <div className={`absolute inset-0 bg-gradient-to-br ${gradient} opacity-0 group-hover:opacity-5 transition-opacity duration-300`} />
      <div className={`inline-flex p-3 rounded-xl bg-gradient-to-br ${gradient} mb-4`}>
        <Icon className="h-6 w-6 text-white" />
      </div>
      <h3 className="text-xl font-semibold mb-2 group-hover:text-gray-900 transition-colors">
        {title}
      </h3>
      <p className="text-gray-600 leading-relaxed">
        {description}
      </p>
    </motion.div>
  );
}

// Header Navigation Component
interface NavItemProps {
  to: string;
  children: React.ReactNode;
  isActive?: boolean;
  onClick?: () => void;
}

function NavItem({ to, children, isActive, onClick }: NavItemProps) {
  return (
    <Link to={to}>
      <motion.div
        className={`relative px-4 py-2 rounded-full transition-all duration-300 cursor-pointer ${
          isActive
            ? "bg-gradient-to-r from-blue-50 to-purple-50 text-blue-600 border border-blue-200/50"
            : "text-gray-700 hover:text-blue-600 hover:bg-blue-50/50"
        }`}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        onClick={onClick}
      >
        <span className="relative z-10 font-medium">{children}</span>
      </motion.div>
    </Link>
  );
}

function LandingPage() {
  const [, setLocation] = useLocation();
  const [location] = useLocation();
  const { user, isLoading } = useAuth();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const heroRef = useRef(null);
  const isHeroInView = useInView(heroRef);

  const navigationItems = [
    { to: '/product/features', label: 'Features' },
    { to: '/product/how-it-works', label: 'How It Works' },
    { to: '/pricing', label: 'Pricing' },
    { to: '/product/testimonials', label: 'Testimonials' }
  ];

  const isActiveRoute = (route: string) => {
    return location === route;
  };

  const features = [
    {
      icon: Brain,
      title: "AI Course Generation",
      description: "Generate complete course structures with AI-powered content creation and intelligent learning paths.",
      gradient: "from-blue-500 to-indigo-600"
    },
    {
      icon: Camera,
      title: "Avatar Video Creation",
      description: "Create talking head videos with SadTalker technology and GPU-accelerated rendering.",
      gradient: "from-purple-500 to-pink-600"
    },
    {
      icon: Headphones,
      title: "Premium Voice Synthesis",
      description: "Choose from 24+ professional voices across Chatterbox TTS, OpenAI, and ElevenLabs.",
      gradient: "from-green-500 to-emerald-600"
    },
    {
      icon: Presentation,
      title: "Interactive Presentations",
      description: "Generate beautiful slides with Marp and combine with video for engaging lessons.",
      gradient: "from-orange-500 to-red-600"
    },
    {
      icon: FileVideo,
      title: "Video Assembly",
      description: "Automated video production with FFmpeg integration and professional editing tools.",
      gradient: "from-cyan-500 to-blue-600"
    },
    {
      icon: Globe,
      title: "Multi-Platform Publishing",
      description: "Publish to LMS platforms, social media, and hosting services with one click.",
      gradient: "from-violet-500 to-purple-600"
    }
  ];

  const stats = [
    { value: 50000, suffix: '+', label: 'Courses Created' },
    { value: 1000000, suffix: '+', label: 'Students Reached' },
    { value: 98, suffix: '%', label: 'Satisfaction Rate' },
    { value: 24, suffix: '', label: 'AI Voice Options' }
  ];

  const testimonials = [
    {
      name: "Sarah Johnson",
      role: "Educational Content Creator",
      company: "TechEd Solutions",
      content: "The AI course generation saved me weeks of work. The avatar videos are incredibly professional and engaging.",
      rating: 5
    },
    {
      name: "Michael Chen",
      role: "Training Manager",
      company: "Corporate Learning Inc.",
      content: "We've scaled our training programs 10x with this platform. The voice synthesis quality is outstanding.",
      rating: 5
    },
    {
      name: "Emily Rodriguez",
      role: "Online Instructor",
      company: "Creative Academy",
      content: "The multi-platform publishing feature is a game-changer. I can reach students everywhere effortlessly.",
      rating: 5
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-white">
      {/* Modern Glass-Morphism Header */}
      <motion.header
        className="fixed top-0 left-0 right-0 z-50 bg-white/80 backdrop-blur-xl border-b border-gray-200/50 shadow-lg"
        initial={{ y: -100, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.6, ease: "easeOut" }}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo Section */}
            <motion.div
              className="flex items-center space-x-3"
              whileHover={{ scale: 1.02 }}
              transition={{ duration: 0.2 }}
            >
              <Link to="/" className="flex items-center space-x-3">
                <div className="relative">
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600 blur-lg opacity-20"></div>
                  <Sparkles className="relative h-8 w-8 text-blue-600" />
                </div>
                <span className="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  CourseAI
                </span>
              </Link>
            </motion.div>

            {/* Desktop Navigation */}
            <nav className="hidden md:flex items-center space-x-2">
              {navigationItems.map((item) => (
                <NavItem
                  key={item.to}
                  to={item.to}
                  isActive={isActiveRoute(item.to)}
                >
                  {item.label}
                </NavItem>
              ))}
            </nav>

            {/* Desktop Auth Section */}
            <div className="hidden md:flex items-center space-x-4">
              {user ? (
                <motion.div 
                  className="flex items-center space-x-4"
                  initial={{ scale: 0.8, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  transition={{ duration: 0.3 }}
                >
                  <Link to="/dashboard">
                    <Button 
                      variant="ghost" 
                      className="text-gray-700 hover:text-blue-600 hover:bg-blue-50 rounded-full px-4 py-2 transition-all duration-300"
                    >
                      Dashboard
                    </Button>
                  </Link>
                  <SubscriptionBadge user={user} />
                  <ProfileDropdown />
                </motion.div>
              ) : (
                <motion.div 
                  className="flex items-center space-x-3"
                  initial={{ scale: 0.8, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  transition={{ duration: 0.3 }}
                >
                  <Link to="/auth">
                    <Button 
                      variant="ghost" 
                      className="text-gray-700 hover:text-blue-600 hover:bg-blue-50 rounded-full px-4 py-2 transition-all duration-300"
                    >
                      Sign in
                    </Button>
                  </Link>
                  <Link to="/auth">
                    <Button 
                      className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white rounded-full px-6 py-2 shadow-lg hover:shadow-xl transition-all duration-300"
                    >
                      Get Started
                    </Button>
                  </Link>
                </motion.div>
              )}
            </div>

            {/* Mobile Menu Button */}
            <div className="md:hidden">
              <motion.button
                onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
                className="p-2 rounded-full text-gray-700 hover:text-blue-600 hover:bg-blue-50 transition-all duration-300"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <AnimatePresence mode="wait">
                  {mobileMenuOpen ? (
                    <motion.div
                      key="close"
                      initial={{ rotate: -90, opacity: 0 }}
                      animate={{ rotate: 0, opacity: 1 }}
                      exit={{ rotate: 90, opacity: 0 }}
                      transition={{ duration: 0.2 }}
                    >
                      <X className="h-6 w-6" />
                    </motion.div>
                  ) : (
                    <motion.div
                      key="menu"
                      initial={{ rotate: 90, opacity: 0 }}
                      animate={{ rotate: 0, opacity: 1 }}
                      exit={{ rotate: -90, opacity: 0 }}
                      transition={{ duration: 0.2 }}
                    >
                      <Menu className="h-6 w-6" />
                    </motion.div>
                  )}
                </AnimatePresence>
              </motion.button>
            </div>
          </div>
        </div>

        {/* Mobile Menu */}
        <AnimatePresence>
          {mobileMenuOpen && (
            <>
              {/* Backdrop */}
              <motion.div
                className="fixed inset-0 bg-black/20 backdrop-blur-sm md:hidden z-40"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                onClick={() => setMobileMenuOpen(false)}
              />
              
              {/* Mobile Menu Panel */}
              <motion.div
                className="absolute top-full left-0 right-0 bg-white/95 backdrop-blur-xl border-b border-gray-200/50 shadow-2xl md:hidden z-50"
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3, ease: "easeOut" }}
              >
                <div className="px-6 py-6 space-y-4">
                  {/* Mobile Navigation Items */}
                  <div className="space-y-2">
                    {navigationItems.map((item, index) => (
                      <motion.div
                        key={item.to}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.3, delay: index * 0.1 }}
                      >
                        <Link to={item.to}>
                          <div
                            className={`block px-4 py-3 rounded-xl text-base font-medium transition-all duration-300 ${
                              isActiveRoute(item.to)
                                ? "bg-gradient-to-r from-blue-50 to-purple-50 text-blue-600 border border-blue-200/50"
                                : "text-gray-700 hover:text-blue-600 hover:bg-blue-50"
                            }`}
                            onClick={() => setMobileMenuOpen(false)}
                          >
                            {item.label}
                          </div>
                        </Link>
                      </motion.div>
                    ))}
                  </div>

                  {/* Mobile Auth Section */}
                  <div className="pt-4 border-t border-gray-200/50">
                    {user ? (
                      <motion.div 
                        className="space-y-2"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.3, delay: 0.4 }}
                      >
                        <Link to="/dashboard">
                          <div
                            className="block px-4 py-3 rounded-xl text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-blue-50 transition-all duration-300"
                            onClick={() => setMobileMenuOpen(false)}
                          >
                            Dashboard
                          </div>
                        </Link>
                        <div className="px-4 py-2">
                          <SubscriptionBadge user={user} />
                        </div>
                      </motion.div>
                    ) : (
                      <motion.div 
                        className="space-y-3"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.3, delay: 0.4 }}
                      >
                        <Link to="/auth">
                          <div
                            className="block px-4 py-3 rounded-xl text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-blue-50 transition-all duration-300"
                            onClick={() => setMobileMenuOpen(false)}
                          >
                            Sign in
                          </div>
                        </Link>
                        <Link to="/auth">
                          <Button 
                            className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white rounded-xl py-3 shadow-lg hover:shadow-xl transition-all duration-300"
                            onClick={() => setMobileMenuOpen(false)}
                          >
                            Get Started
                          </Button>
                        </Link>
                      </motion.div>
                    )}
                  </div>
                </div>
              </motion.div>
            </>
          )}
        </AnimatePresence>
      </motion.header>

      {/* Hero Section */}
      <section ref={heroRef} className="relative overflow-hidden pt-16">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50" />
        <div className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiM5QzkyQUMiIGZpbGwtb3BhY2l0eT0iMC4wMyI+PGNpcmNsZSBjeD0iMzAiIGN5PSIzMCIgcj0iMiIvPjwvZz48L2c+PC9zdmc+')] opacity-40" />
        
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-16">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={isHeroInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8 }}
            className="text-center max-w-4xl mx-auto"
          >
            <Badge variant="outline" className="mb-6 bg-white/80 backdrop-blur-sm border-blue-200 text-blue-700">
              <Sparkles className="h-3 w-3 mr-1" />
              AI-Powered Course Creation Platform
            </Badge>
            
            <h1 className="text-5xl md:text-7xl font-bold bg-gradient-to-r from-gray-900 via-blue-900 to-purple-900 bg-clip-text text-transparent mb-6 leading-tight">
              Create Professional Courses with AI Magic
            </h1>
            
            <p className="text-xl md:text-2xl text-gray-600 mb-8 leading-relaxed">
              Transform your ideas into engaging video courses with AI-generated content, 
              avatar presenters, and professional voice synthesis. No technical skills required.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
              <Button 
                size="lg" 
                className="text-lg px-8 py-6 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 shadow-lg hover:shadow-xl transition-all duration-300"
                onClick={() => setLocation('/create')}
              >
                <Rocket className="h-5 w-5 mr-2" />
                Start Creating Free
              </Button>
              <Button 
                size="lg" 
                variant="outline" 
                className="text-lg px-8 py-6 border-2 hover:bg-gray-50 transition-all duration-300"
                onClick={() => setLocation('/product/how-it-works')}
              >
                <Play className="h-5 w-5 mr-2" />
                Watch Demo
              </Button>
            </div>

            {/* Stats Section */}
            <motion.div
              variants={staggerChildren}
              initial="initial"
              animate={isHeroInView ? "animate" : "initial"}
              className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-3xl mx-auto"
            >
              {stats.map((stat, index) => (
                <motion.div key={index} variants={fadeInUp} className="text-center">
                  <div className="text-3xl md:text-4xl font-bold text-gray-900 mb-1">
                    <AnimatedCounter value={stat.value} suffix={stat.suffix} />
                  </div>
                  <div className="text-sm md:text-base text-gray-600">{stat.label}</div>
                </motion.div>
              ))}
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="text-center mb-16"
          >
            <Badge variant="outline" className="mb-4 border-blue-200 text-blue-700">
              <Zap className="h-3 w-3 mr-1" />
              Powerful Features
            </Badge>
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Everything You Need to Create Amazing Courses
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              From AI-powered content generation to professional video production, 
              our platform provides all the tools for modern course creation.
            </p>
          </motion.div>

          <motion.div
            variants={staggerChildren}
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
          >
            {features.map((feature, index) => (
              <FeatureHighlight key={index} {...feature} />
            ))}
          </motion.div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-20 bg-gradient-to-b from-gray-50 to-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="text-center mb-16"
          >
            <Badge variant="outline" className="mb-4 border-green-200 text-green-700">
              <Clock className="h-3 w-3 mr-1" />
              Simple Process
            </Badge>
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              From Idea to Course in Minutes
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Our streamlined workflow makes professional course creation accessible to everyone.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {[
              {
                step: "1",
                title: "Describe Your Course",
                description: "Tell our AI what you want to teach. It generates a complete course structure with modules and lessons.",
                icon: Brain,
                gradient: "from-blue-500 to-indigo-600"
              },
              {
                step: "2",
                title: "Customize & Generate",
                description: "Choose your avatar, voice, and style. Our AI creates professional videos with slides and narration.",
                icon: Video,
                gradient: "from-purple-500 to-pink-600"
              },
              {
                step: "3",
                title: "Publish & Share",
                description: "Export to any format or publish directly to major platforms. Your course is ready for students.",
                icon: Globe,
                gradient: "from-green-500 to-emerald-600"
              }
            ].map((step, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: index * 0.2 }}
                className="relative"
              >
                <Card className="h-full border-0 shadow-lg hover:shadow-xl transition-all duration-300">
                  <CardHeader className="text-center pb-4">
                    <div className={`inline-flex w-16 h-16 items-center justify-center rounded-full bg-gradient-to-br ${step.gradient} mb-4 mx-auto`}>
                      {React.createElement(step.icon, { className: "h-8 w-8 text-white" })}
                    </div>
                    <div className={`inline-flex w-8 h-8 items-center justify-center rounded-full bg-gradient-to-br ${step.gradient} text-white font-bold text-sm mb-2`}>
                      {step.step}
                    </div>
                    <CardTitle className="text-xl">{step.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <CardDescription className="text-base leading-relaxed">
                      {step.description}
                    </CardDescription>
                  </CardContent>
                </Card>
                
                {index < 2 && (
                  <div className="hidden lg:block absolute top-1/2 -right-4 w-8 h-8 transform -translate-y-1/2">
                    <ArrowRight className="h-6 w-6 text-gray-400" />
                  </div>
                )}
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="text-center mb-16"
          >
            <Badge variant="outline" className="mb-4 border-yellow-200 text-yellow-700">
              <Trophy className="h-3 w-3 mr-1" />
              Success Stories
            </Badge>
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Loved by Course Creators Worldwide
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Join thousands of educators and trainers who have transformed their teaching with our platform.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                <Card className="h-full">
                  <CardHeader>
                    <div className="flex items-center gap-1 mb-3">
                      {[...Array(testimonial.rating)].map((_, i) => (
                        <Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                      ))}
                    </div>
                    <CardDescription className="text-base leading-relaxed italic">
                      "{testimonial.content}"
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center text-white font-semibold">
                        {testimonial.name.charAt(0)}
                      </div>
                      <div>
                        <div className="font-semibold">{testimonial.name}</div>
                        <div className="text-sm text-gray-600">{testimonial.role}</div>
                        <div className="text-sm text-gray-500">{testimonial.company}</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Ready to Transform Your Teaching?
            </h2>
            <p className="text-xl text-blue-100 mb-8 leading-relaxed">
              Join thousands of educators creating professional courses with AI. 
              Start your free trial today and experience the future of course creation.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button 
                size="lg" 
                className="text-lg px-8 py-6 bg-white text-blue-600 hover:bg-gray-50 shadow-lg hover:shadow-xl transition-all duration-300"
                onClick={() => setLocation('/create')}
              >
                <Rocket className="h-5 w-5 mr-2" />
                Start Free Trial
              </Button>
              <Button 
                size="lg" 
                variant="outline" 
                className="text-lg px-8 py-6 border-2 border-white text-white hover:bg-white hover:text-blue-600 transition-all duration-300"
                onClick={() => setLocation('/product/pricing')}
              >
                <TrendingUp className="h-5 w-5 mr-2" />
                View Pricing
              </Button>
            </div>

            <div className="mt-8 flex items-center justify-center gap-6 text-blue-100">
              <div className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                <span>No Credit Card Required</span>
              </div>
              <div className="flex items-center gap-2">
                <Clock className="h-5 w-5" />
                <span>5-Minute Setup</span>
              </div>
              <div className="flex items-center gap-2">
                <Check className="h-5 w-5" />
                <span>Free Forever Plan</span>
              </div>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
}

export default LandingPage;