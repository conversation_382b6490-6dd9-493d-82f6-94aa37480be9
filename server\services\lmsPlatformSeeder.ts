import { db } from '../db';
import { platforms } from '@shared/platform-schema';
import { eq } from 'drizzle-orm';

// Function to seed default LMS platforms
export async function seedLMSPlatforms() {
  try {
    // Check if LMS platforms already exist
    const existingPlatforms = await db.select().from(platforms).where(eq(platforms.category, 'lms'));
    
    if (existingPlatforms.length > 0) {
      console.log(`Found ${existingPlatforms.length} existing LMS platforms. Skipping seeding.`);
      return;
    }
    
    // Define default LMS platforms to seed
    const defaultLMSPlatforms = [
      {
        name: 'Moodle',
        slug: 'moodle',
        category: 'lms',
        description: 'Moodle is a free and open-source learning management system written in PHP and distributed under the GNU General Public License.',
        iconUrl: 'moodle',
        website: 'https://moodle.org/',
        apiDocsUrl: 'https://docs.moodle.org/dev/Web_service_API_functions',
        features: ['Open Source', 'Customizable', 'Mobile-Friendly', 'SCORM Compliant']
      },
      {
        name: 'Canvas',
        slug: 'canvas',
        category: 'lms',
        description: 'Canvas is a learning management system by Instructure built on Ruby on Rails framework and made available as open-source software.',
        iconUrl: 'canvas',
        website: 'https://www.instructure.com/canvas/',
        apiDocsUrl: 'https://canvas.instructure.com/doc/api/',
        features: ['Cloud-Based', 'Mobile Apps', 'Analytics', 'Integrated Tools']
      },
      {
        name: 'Blackboard',
        slug: 'blackboard',
        category: 'lms',
        description: 'Blackboard Learn is a virtual learning environment and learning management system developed by Blackboard Inc.',
        iconUrl: 'blackboard',
        website: 'https://www.blackboard.com/',
        apiDocsUrl: 'https://developer.blackboard.com/',
        features: ['Assessment Tools', 'Content Management', 'Collaboration Tools', 'Enterprise Integration']
      },
      {
        name: 'Teachable',
        slug: 'teachable',
        category: 'lms',
        description: 'Teachable is an online learning platform that allows instructors to create and sell courses on their own branded websites.',
        iconUrl: 'teachable',
        website: 'https://teachable.com/',
        apiDocsUrl: 'https://teachable.com/blog/teachable-api',
        features: ['Course Creator', 'Sales Pages', 'Payment Processing', 'Student Management']
      },
      {
        name: 'Thinkific',
        slug: 'thinkific',
        category: 'lms',
        description: 'Thinkific is a software platform that enables entrepreneurs to create, market, sell, and deliver their own online courses.',
        iconUrl: 'thinkific',
        website: 'https://www.thinkific.com/',
        apiDocsUrl: 'https://developers.thinkific.com/api/api-documentation/',
        features: ['Drag & Drop Builder', 'Marketing Tools', 'Student Progress Tracking', 'Certificates']
      },
      {
        name: 'LearnWorlds',
        slug: 'learnworlds',
        category: 'lms',
        description: 'LearnWorlds is an all-in-one course platform to create, market and sell online courses through your own branded website.',
        iconUrl: 'learnworlds',
        website: 'https://www.learnworlds.com/',
        apiDocsUrl: 'https://docs.learnworlds.com/',
        features: ['Interactive eBooks', 'Video Learning', 'Social Learning', 'White-Label Platform']
      },
      {
        name: 'LearnDash',
        slug: 'learndash',
        category: 'lms',
        description: 'LearnDash is a WordPress LMS plugin that makes it easy to create & sell online courses, deliver quizzes, award certificates, and more.',
        iconUrl: 'learndash',
        website: 'https://www.learndash.com/',
        apiDocsUrl: 'https://developers.learndash.com/',
        features: ['WordPress Integration', 'Drip-Feed Content', 'Assignments', 'Community Forums']
      },
      {
        name: 'Kajabi',
        slug: 'kajabi',
        category: 'lms',
        description: 'Kajabi is an all-in-one business platform to create, market, and sell digital products including online courses.',
        iconUrl: 'kajabi',
        website: 'https://kajabi.com/',
        apiDocsUrl: 'https://developer.kajabi.com/reference/',
        features: ['Website Builder', 'Email Marketing', 'Sales Funnels', 'Analytics Dashboard']
      },
      {
        name: 'Podia',
        slug: 'podia',
        category: 'lms',
        description: 'Podia is a digital storefront platform where creators can sell online courses, memberships, webinars, and digital downloads.',
        iconUrl: 'podia',
        website: 'https://www.podia.com/',
        apiDocsUrl: 'https://www.podia.com/api',
        features: ['Email Marketing', 'Zero Transaction Fees', 'Unlimited Everything', 'Membership Sites']
      },
      {
        name: 'TalentLMS',
        slug: 'talentlms',
        category: 'lms',
        description: 'TalentLMS is a cloud-based learning management system that focuses on simplicity and user-friendliness.',
        iconUrl: 'talentlms',
        website: 'https://www.talentlms.com/',
        apiDocsUrl: 'https://www.talentlms.com/pages/docs/TalentLMS-API-Documentation.pdf',
        features: ['Gamification', 'Videoconferencing', 'Certification', 'Responsive Design']
      },
      {
        name: 'Docebo',
        slug: 'docebo',
        category: 'lms',
        description: 'Docebo is an AI-powered enterprise learning platform for companies who see learning as a way to maintain a competitive edge.',
        iconUrl: 'docebo',
        website: 'https://www.docebo.com/',
        apiDocsUrl: 'https://www.docebo.com/learning-platform/api/',
        features: ['AI-Powered', 'Mobile Learning', 'Social Learning', 'Advanced Reporting']
      }
    ];
    
    // Insert LMS platforms
    await db.insert(platforms).values(defaultLMSPlatforms);
    
    console.log(`Successfully seeded ${defaultLMSPlatforms.length} LMS platforms.`);
  } catch (error) {
    console.error('Error seeding LMS platforms:', error);
  }
}