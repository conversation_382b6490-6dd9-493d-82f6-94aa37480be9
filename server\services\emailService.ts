import { Resend } from 'resend';
import { notificationsService } from './notificationsService';

// Initialize Resend with the API key
const resend = new Resend(process.env.RESEND_API_KEY);

// Standard email templates
interface EmailTemplate {
  subject: string;
  body: string;
}

// Email configurations
const FROM_EMAIL = '<EMAIL>';
const DEFAULT_REPLY_TO = '<EMAIL>';

// Define email types that can be sent in the application
export enum EmailType {
  WELCOME = 'welcome',
  PASSWORD_RESET = 'password_reset',
  COURSE_COLLABORATION_INVITE = 'course_collaboration_invite',
  TEAM_INVITE = 'team_invite',
  COURSE_PUBLISHED = 'course_published',
  COURSE_COMPLETED = 'course_completed',
  SUBSCRIPTION_EXPIRING = 'subscription_expiring',
  AI_GENERATION_COMPLETE = 'ai_generation_complete',
  CREDITS_LOW = 'credits_low',
  NOTIFICATION_DIGEST = 'notification_digest',
  CUSTOM_EMAIL = 'custom_email'
}

// User info needed for personalization
interface UserInfo {
  id: number;
  email: string;
  name?: string;
  username: string;
}

class EmailService {
  private async getEmailTemplate(type: EmailType, data: Record<string, any> = {}): Promise<EmailTemplate> {
    // Here you can implement more sophisticated templating using a library like handlebars
    // For now, we'll use simple template string interpolation

    switch (type) {
      case EmailType.WELCOME:
        return {
          subject: 'Welcome to Your Course Platform',
          body: `
            <h1>Welcome, ${data.name || data.username}!</h1>
            <p>Thank you for joining our platform. We're excited to have you on board!</p>
            <p>Get started by exploring our courses or creating your own.</p>
            <p><a href="${data.loginUrl}">Click here to log in</a></p>
          `
        };

      case EmailType.PASSWORD_RESET:
        return {
          subject: 'Reset Your Password',
          body: `
            <h1>Password Reset Request</h1>
            <p>You requested to reset your password. Click the link below to set a new password:</p>
            <p><a href="${data.resetUrl}">Reset Password</a></p>
            <p>If you didn't request this, please ignore this email.</p>
            <p>This link will expire in 24 hours.</p>
          `
        };

      case EmailType.COURSE_COLLABORATION_INVITE:
        return {
          subject: `Invitation to Collaborate on "${data.courseName}"`,
          body: `
            <h1>You've Been Invited to Collaborate</h1>
            <p>${data.inviterName} has invited you to collaborate on the course "${data.courseName}".</p>
            <p><a href="${data.inviteUrl}">Accept Invitation</a></p>
          `
        };

      case EmailType.TEAM_INVITE:
        return {
          subject: `Invitation to Join Team "${data.teamName}"`,
          body: `
            <h1>You've Been Invited to Join a Team</h1>
            <p>${data.inviterName} has invited you to join the team "${data.teamName}".</p>
            <p><a href="${data.inviteUrl}">Accept Invitation</a></p>
          `
        };

      case EmailType.COURSE_PUBLISHED:
        return {
          subject: `Your Course "${data.courseName}" Is Now Published`,
          body: `
            <h1>Congratulations!</h1>
            <p>Your course "${data.courseName}" has been successfully published and is now available to learners.</p>
            <p><a href="${data.courseUrl}">View Your Course</a></p>
          `
        };

      case EmailType.COURSE_COMPLETED:
        return {
          subject: `Congratulations on Completing "${data.courseName}"`,
          body: `
            <h1>Course Completed!</h1>
            <p>Congratulations on completing the course "${data.courseName}".</p>
            <p><a href="${data.certificateUrl}">View Your Certificate</a></p>
          `
        };

      case EmailType.SUBSCRIPTION_EXPIRING:
        return {
          subject: 'Your Subscription Is About to Expire',
          body: `
            <h1>Subscription Expiring Soon</h1>
            <p>Your subscription will expire on ${data.expiryDate}.</p>
            <p><a href="${data.renewUrl}">Renew Now</a></p>
          `
        };

      case EmailType.AI_GENERATION_COMPLETE:
        return {
          subject: 'Your AI Content Generation Is Complete',
          body: `
            <h1>AI Generation Complete</h1>
            <p>Your requested AI content "${data.contentName}" has been generated successfully.</p>
            <p><a href="${data.contentUrl}">View Content</a></p>
          `
        };

      case EmailType.CREDITS_LOW:
        return {
          subject: 'Your AI Credits Are Running Low',
          body: `
            <h1>AI Credits Alert</h1>
            <p>Your AI credits are running low. You currently have ${data.remainingCredits} credits remaining.</p>
            <p><a href="${data.topUpUrl}">Add More Credits</a></p>
          `
        };

      case EmailType.NOTIFICATION_DIGEST:
        return {
          subject: 'Your Daily Notification Digest',
          body: `
            <h1>Daily Digest</h1>
            <p>Here's a summary of your recent notifications:</p>
            <ul>
              ${data.notifications.map((notification: any) => 
                `<li>${notification.message}</li>`
              ).join('')}
            </ul>
            <p><a href="${data.notificationsUrl}">View All Notifications</a></p>
          `
        };

      case EmailType.CUSTOM_EMAIL:
        return {
          subject: data.subject || 'Important Information from Your Course Platform',
          body: data.body || '<p>No content provided.</p>'
        };

      default:
        return {
          subject: 'Notification from Your Course Platform',
          body: '<p>You have a new notification.</p>'
        };
    }
  }

  /**
   * Send an email to a user
   */
  async sendEmail(to: string, type: EmailType, data: Record<string, any> = {}): Promise<{ success: boolean; id?: string; error?: any }> {
    try {
      const template = await this.getEmailTemplate(type, data);
      
      const result = await resend.emails.send({
        from: data.from || FROM_EMAIL,
        replyTo: data.replyTo || DEFAULT_REPLY_TO,
        to: to,
        subject: template.subject,
        html: template.body,
        // Optional fields
        cc: data.cc,
        bcc: data.bcc,
        attachments: data.attachments
      });

      // The Resend types may be out of date, checking actual data
      const responseData = result as any;
      console.log(`Email sent [${type}] to ${to} with ID: ${responseData.id}`);
      return { success: true, id: responseData.id };
    } catch (error) {
      console.error(`Failed to send email [${type}] to ${to}:`, error);
      return { success: false, error };
    }
  }

  /**
   * Send a welcome email to a new user
   */
  async sendWelcomeEmail(user: UserInfo, data: { loginUrl: string } = { loginUrl: process.env.APP_URL || 'https://your-course-platform.com' }) {
    return this.sendEmail(user.email, EmailType.WELCOME, {
      name: user.name,
      username: user.username,
      ...data
    });
  }

  /**
   * Send a password reset email
   */
  async sendPasswordResetEmail(user: UserInfo, resetToken: string) {
    const resetUrl = `${process.env.APP_URL || 'https://your-course-platform.com'}/reset-password?token=${resetToken}&email=${encodeURIComponent(user.email)}`;
    
    return this.sendEmail(user.email, EmailType.PASSWORD_RESET, {
      name: user.name,
      username: user.username,
      resetUrl
    });
  }

  /**
   * Send a course collaboration invitation
   */
  async sendCourseCollaborationInvite(invitee: UserInfo, inviter: UserInfo, courseId: number, courseName: string) {
    const inviteUrl = `${process.env.APP_URL || 'https://your-course-platform.com'}/courses/${courseId}/collaboration/accept`;
    
    return this.sendEmail(invitee.email, EmailType.COURSE_COLLABORATION_INVITE, {
      inviterName: inviter.name || inviter.username,
      courseName,
      inviteUrl
    });
  }

  /**
   * Send a team invitation
   */
  async sendTeamInvite(invitee: UserInfo, inviter: UserInfo, teamId: number, teamName: string) {
    const inviteUrl = `${process.env.APP_URL || 'https://your-course-platform.com'}/teams/${teamId}/accept-invite`;
    
    return this.sendEmail(invitee.email, EmailType.TEAM_INVITE, {
      inviterName: inviter.name || inviter.username,
      teamName,
      inviteUrl
    });
  }

  /**
   * Send a notification that a course has been published
   */
  async sendCoursePublishedEmail(user: UserInfo, courseId: number, courseName: string) {
    const courseUrl = `${process.env.APP_URL || 'https://your-course-platform.com'}/courses/${courseId}`;
    
    return this.sendEmail(user.email, EmailType.COURSE_PUBLISHED, {
      courseName,
      courseUrl
    });
  }

  /**
   * Send a notification that a user has completed a course
   */
  async sendCourseCompletedEmail(user: UserInfo, courseId: number, courseName: string) {
    const certificateUrl = `${process.env.APP_URL || 'https://your-course-platform.com'}/courses/${courseId}/certificate`;
    
    return this.sendEmail(user.email, EmailType.COURSE_COMPLETED, {
      courseName,
      certificateUrl
    });
  }

  /**
   * Send notification about subscription expiry
   */
  async sendSubscriptionExpiringEmail(user: UserInfo, expiryDate: string) {
    const renewUrl = `${process.env.APP_URL || 'https://your-course-platform.com'}/profile/subscription`;
    
    return this.sendEmail(user.email, EmailType.SUBSCRIPTION_EXPIRING, {
      expiryDate,
      renewUrl
    });
  }

  /**
   * Send notification that AI content generation is complete
   */
  async sendAIGenerationCompleteEmail(user: UserInfo, contentId: number, contentName: string, contentType: string) {
    const contentUrl = `${process.env.APP_URL || 'https://your-course-platform.com'}/${contentType}/${contentId}`;
    
    return this.sendEmail(user.email, EmailType.AI_GENERATION_COMPLETE, {
      contentName,
      contentUrl
    });
  }

  /**
   * Send low credits notification
   */
  async sendCreditsLowEmail(user: UserInfo, remainingCredits: number) {
    const topUpUrl = `${process.env.APP_URL || 'https://your-course-platform.com'}/profile/credits`;
    
    return this.sendEmail(user.email, EmailType.CREDITS_LOW, {
      remainingCredits,
      topUpUrl
    });
  }

  /**
   * Send daily notification digest
   */
  async sendNotificationDigestEmail(user: UserInfo, notifications: any[]) {
    const notificationsUrl = `${process.env.APP_URL || 'https://your-course-platform.com'}/notifications`;
    
    return this.sendEmail(user.email, EmailType.NOTIFICATION_DIGEST, {
      notifications,
      notificationsUrl
    });
  }

  /**
   * Send a custom email
   */
  async sendCustomEmail(to: string, subject: string, body: string, options: Record<string, any> = {}) {
    return this.sendEmail(to, EmailType.CUSTOM_EMAIL, {
      subject,
      body,
      ...options
    });
  }

  /**
   * Send email for a notification
   */
  async sendNotificationEmail(userId: number, notification: any) {
    try {
      // Get user information
      // This would need to be implemented based on your storage structure
      const user = await this.getUserById(userId);
      if (!user) {
        console.error(`Failed to send notification email: User ${userId} not found`);
        return { success: false, error: 'User not found' };
      }

      // Get notification preferences for this user
      const userPreferences = await notificationsService.getUserNotificationPreferences(userId);
      const typePreference = userPreferences.find(pref => pref.typeId === notification.typeId);
      
      // Check if email notifications are enabled for this notification type
      // Using any here because UserNotificationPreferences might have different structure
      const preferenceSettings = typePreference as any;
      if (!preferenceSettings || !preferenceSettings.emailEnabled) {
        return { success: false, error: 'Email notifications disabled for this type' };
      }

      // Get the notification type
      const notificationType = await notificationsService.getNotificationTypeById(notification.typeId);
      if (!notificationType) {
        return { success: false, error: 'Notification type not found' };
      }

      // Send email based on notification type
      const emailType = this.mapNotificationTypeToEmailType(notificationType.type);
      const emailData = await this.prepareEmailDataFromNotification(notification);
      
      return this.sendEmail(user.email, emailType, emailData);
    } catch (error) {
      console.error('Error sending notification email:', error);
      return { success: false, error };
    }
  }

  /**
   * Map notification type to email type
   */
  private mapNotificationTypeToEmailType(notificationType: string): EmailType {
    const mapping: Record<string, EmailType> = {
      'course_published': EmailType.COURSE_PUBLISHED,
      'course_completed': EmailType.COURSE_COMPLETED,
      'team_invite': EmailType.TEAM_INVITE,
      'course_collaboration': EmailType.COURSE_COLLABORATION_INVITE,
      'subscription_expiring': EmailType.SUBSCRIPTION_EXPIRING,
      'ai_generation_complete': EmailType.AI_GENERATION_COMPLETE,
      'credit_low': EmailType.CREDITS_LOW
      // Add other mappings as needed
    };

    return mapping[notificationType] || EmailType.CUSTOM_EMAIL;
  }

  /**
   * Prepare email data from notification
   */
  private async prepareEmailDataFromNotification(notification: any): Promise<Record<string, any>> {
    // Extract data from notification.data
    // This is a simplified example - adjust based on your actual notification data structure
    const data = notification.data || {};
    
    // Return formatted data for email template
    return {
      ...data,
      notificationId: notification.id,
      message: notification.message,
      createdAt: notification.createdAt
    };
  }

  /**
   * Get user by ID - this is a placeholder to be implemented based on your storage
   */
  private async getUserById(userId: number): Promise<UserInfo | null> {
    try {
      // This should be implemented based on your actual storage
      // For example:
      // const user = await db.select().from(users).where(eq(users.id, userId)).first();
      // return user ? { id: user.id, email: user.email, name: user.name, username: user.username } : null;
      
      // Placeholder implementation
      const { storage } = await import('../storage');
      const user = await storage.getUser(userId);
      return user ? { 
        id: user.id, 
        email: user.email, 
        name: user.name || '', 
        username: user.username 
      } : null;
    } catch (error) {
      console.error('Error fetching user:', error);
      return null;
    }
  }
}

export const emailService = new EmailService();
