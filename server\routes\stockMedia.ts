import { Router, Request, Response } from 'express';
import { stockMediaService } from '../services/stockMediaService';
import { z } from 'zod';

const router = Router();

// Search photos endpoint
router.get('/photos', async (req: Request, res: Response) => {
  try {
    const { query, page = 1, per_page = 20 } = req.query;
    
    if (!query || typeof query !== 'string') {
      return res.status(400).json({ error: 'Search query is required' });
    }

    const results = await stockMediaService.searchPhotos(
      query,
      parseInt(page as string),
      parseInt(per_page as string)
    );

    return res.json(results);
  } catch (error) {
    console.error('Error searching photos:', error);
    return res.status(500).json({ error: 'Failed to search photos' });
  }
});

// Search videos endpoint
router.get('/videos', async (req: Request, res: Response) => {
  try {
    const { query, page = 1, per_page = 20 } = req.query;
    
    if (!query || typeof query !== 'string') {
      return res.status(400).json({ error: 'Search query is required' });
    }

    const results = await stockMediaService.searchVideos(
      query,
      parseInt(page as string),
      parseInt(per_page as string)
    );

    return res.json(results);
  } catch (error) {
    console.error('Error searching videos:', error);
    return res.status(500).json({ error: 'Failed to search videos' });
  }
});

// Import media to library endpoint
router.post('/import', async (req: Request, res: Response) => {
  try {
    if (!req.session?.userId) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const schema = z.object({
      mediaData: z.object({
        id: z.string(),
        url: z.string(),
        width: z.number().optional(),
        height: z.number().optional(),
        duration: z.number().optional(),
        alt: z.string().optional(),
        tags: z.array(z.string()).optional(),
        photographer: z.string().optional(),
        photographer_url: z.string().optional()
      }),
      type: z.enum(['photo', 'video'])
    });

    const validation = schema.safeParse(req.body);
    if (!validation.success) {
      return res.status(400).json({ 
        error: 'Invalid data', 
        details: validation.error.format() 
      });
    }

    const { mediaData, type } = validation.data;
    const userId = req.session.userId;

    const imported = await stockMediaService.importToLibrary(userId, mediaData, type);
    
    return res.json({ 
      success: true, 
      media: imported,
      message: `${type === 'photo' ? 'Photo' : 'Video'} imported to your library successfully` 
    });
  } catch (error) {
    console.error('Error importing media:', error);
    return res.status(500).json({ error: 'Failed to import media' });
  }
});

// Get trending/popular media
router.get('/trending/:type', async (req: Request, res: Response) => {
  try {
    const { type } = req.params;
    const { limit = 20 } = req.query;

    if (type !== 'photo' && type !== 'video') {
      return res.status(400).json({ error: 'Type must be "photo" or "video"' });
    }

    const results = await stockMediaService.getTrendingMedia(
      type, 
      parseInt(limit as string)
    );

    return res.json(results);
  } catch (error) {
    console.error('Error getting trending media:', error);
    return res.status(500).json({ error: 'Failed to get trending media' });
  }
});

// Get user's imported stock media
router.get('/imported', async (req: Request, res: Response) => {
  try {
    if (!req.session?.userId) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const { type } = req.query;
    const userId = req.session.userId;

    const imported = await stockMediaService.getUserImportedMedia(
      userId, 
      type as 'photo' | 'video' | undefined
    );

    return res.json({ media: imported });
  } catch (error) {
    console.error('Error getting imported media:', error);
    return res.status(500).json({ error: 'Failed to get imported media' });
  }
});

// Get categories/suggestions for search
router.get('/categories', async (req: Request, res: Response) => {
  try {
    const categories = {
      photos: [
        'business', 'technology', 'education', 'nature', 'people', 'abstract',
        'workplace', 'teamwork', 'success', 'innovation', 'creativity',
        'presentation', 'meeting', 'computer', 'data', 'growth'
      ],
      videos: [
        'business', 'technology', 'nature', 'abstract', 'motion', 'presentation',
        'teamwork', 'workplace', 'animation', 'background', 'loop',
        'corporate', 'professional', 'modern', 'dynamic'
      ]
    };

    return res.json(categories);
  } catch (error) {
    console.error('Error getting categories:', error);
    return res.status(500).json({ error: 'Failed to get categories' });
  }
});

export default router;