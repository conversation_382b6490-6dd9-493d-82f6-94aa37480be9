import React, { useState, useRef } from 'react';
import { Card, CardContent, TextField, Button, Grid, Typography, CircularProgress, Slider, FormControl, InputLabel, Select, MenuItem, Chip } from '@mui/material';
import { VolumeUp, Download, Add, Clear } from '@mui/icons-material';
import apiService from '../services/apiService';

const TTSChatterbox = () => {
  const [singleText, setSingleText] = useState('');
  const [batchTexts, setBatchTexts] = useState(['']);
  const [formData, setFormData] = useState({
    voice: 'v2/en_speaker_6',
    temperature: 0.7,
    speed: 1.0
  });
  
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState([]);
  const [error, setError] = useState('');
  const [mode, setMode] = useState('single'); // 'single' or 'batch'
  const audioRefs = useRef({});

  const voiceOptions = [
    { value: 'v2/en_speaker_0', label: 'Speaker 0 (Male, Professional)' },
    { value: 'v2/en_speaker_1', label: 'Speaker 1 (Female, Warm)' },
    { value: 'v2/en_speaker_2', label: 'Speaker 2 (Male, Energetic)' },
    { value: 'v2/en_speaker_3', label: 'Speaker 3 (Female, Clear)' },
    { value: 'v2/en_speaker_4', label: 'Speaker 4 (Male, Calm)' },
    { value: 'v2/en_speaker_5', label: 'Speaker 5 (Female, Friendly)' },
    { value: 'v2/en_speaker_6', label: 'Speaker 6 (Male, Narrator)' },
    { value: 'v2/en_speaker_7', label: 'Speaker 7 (Female, Confident)' },
    { value: 'v2/en_speaker_8', label: 'Speaker 8 (Male, Gentle)' },
    { value: 'v2/en_speaker_9', label: 'Speaker 9 (Female, Dynamic)' }
  ];

  const sampleTexts = [
    "Welcome to our comprehensive course on artificial intelligence and machine learning.",
    "In this lesson, we'll explore the fundamental concepts of neural networks.",
    "Data preprocessing is a crucial step in any machine learning pipeline.",
    "Let's examine how gradient descent optimization works in practice.",
    "Understanding the bias-variance tradeoff is essential for model performance."
  ];

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const addBatchText = () => {
    setBatchTexts(prev => [...prev, '']);
  };

  const removeBatchText = (index) => {
    setBatchTexts(prev => prev.filter((_, i) => i !== index));
  };

  const updateBatchText = (index, value) => {
    setBatchTexts(prev => prev.map((text, i) => i === index ? value : text));
  };

  const useSampleTexts = () => {
    setBatchTexts(sampleTexts);
    setMode('batch');
  };

  const handleGenerate = async () => {
    const textsToProcess = mode === 'single' ? [singleText] : batchTexts.filter(text => text.trim());
    
    if (textsToProcess.length === 0 || textsToProcess.every(text => !text.trim())) {
      setError('Please enter text to convert to speech');
      return;
    }

    setLoading(true);
    setError('');
    
    try {
      const requestData = {
        text: textsToProcess[0],
        batchTexts: mode === 'batch' ? textsToProcess : null,
        ...formData
      };

      const cacheKey = apiService.generateCacheKey(requestData);
      const cached = apiService.getCachedResult(cacheKey);
      
      if (cached) {
        setResults(cached.results.map((result, index) => ({ 
          ...result, 
          id: Date.now() + index,
          cached: true 
        })));
        setLoading(false);
        return;
      }

      const result = await apiService.generateChatterboxTTS(requestData);
      
      if (result.status === 'success') {
        const newResults = result.results.map((res, index) => ({
          ...res,
          id: Date.now() + index,
          cached: res.cached || false
        }));
        setResults(newResults);
        
        if (!result.cached) {
          apiService.setCachedResult(cacheKey, result);
        }
      } else {
        setError(result.error || 'TTS generation failed');
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handlePlay = (audioBase64, id) => {
    const audio = audioRefs.current[id];
    if (audio) {
      audio.src = `data:audio/wav;base64,${audioBase64}`;
      audio.play();
    }
  };

  const handleDownload = (audioBase64, index, text) => {
    const filename = `chatterbox-tts-${Date.now()}-${index}.wav`;
    apiService.downloadBase64File(audioBase64, filename, 'audio/wav');
  };

  const clearAll = () => {
    setSingleText('');
    setBatchTexts(['']);
    setResults([]);
    setError('');
  };

  return (
    <div className="tts-chatterbox">
      <Typography variant="h4" gutterBottom>
        <VolumeUp /> Chatterbox TTS Generator
      </Typography>
      
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>Text Input</Typography>
              
              <FormControl fullWidth margin="normal">
                <InputLabel>Mode</InputLabel>
                <Select
                  value={mode}
                  onChange={(e) => setMode(e.target.value)}
                >
                  <MenuItem value="single">Single Text</MenuItem>
                  <MenuItem value="batch">Batch Processing</MenuItem>
                </Select>
              </FormControl>

              {mode === 'single' ? (
                <TextField
                  fullWidth
                  multiline
                  rows={4}
                  label="Text to Speech"
                  value={singleText}
                  onChange={(e) => setSingleText(e.target.value)}
                  margin="normal"
                  placeholder="Enter the text you want to convert to speech..."
                />
              ) : (
                <div>
                  <Typography variant="subtitle2" gutterBottom sx={{ mt: 2 }}>
                    Batch Texts:
                  </Typography>
                  {batchTexts.map((text, index) => (
                    <div key={index} style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>
                      <TextField
                        fullWidth
                        multiline
                        rows={2}
                        label={`Text ${index + 1}`}
                        value={text}
                        onChange={(e) => updateBatchText(index, e.target.value)}
                        size="small"
                      />
                      {batchTexts.length > 1 && (
                        <Button
                          onClick={() => removeBatchText(index)}
                          size="small"
                          sx={{ ml: 1 }}
                        >
                          <Clear />
                        </Button>
                      )}
                    </div>
                  ))}
                  <Button
                    startIcon={<Add />}
                    onClick={addBatchText}
                    size="small"
                    sx={{ mr: 1 }}
                  >
                    Add Text
                  </Button>
                  <Button
                    variant="outlined"
                    onClick={useSampleTexts}
                    size="small"
                  >
                    Use Course Samples
                  </Button>
                </div>
              )}

              <Button
                variant="outlined"
                onClick={clearAll}
                fullWidth
                sx={{ mt: 2 }}
              >
                Clear All
              </Button>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>Voice Settings</Typography>
              
              <FormControl fullWidth margin="normal">
                <InputLabel>Voice</InputLabel>
                <Select
                  value={formData.voice}
                  onChange={(e) => handleInputChange('voice', e.target.value)}
                >
                  {voiceOptions.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>

              <Typography gutterBottom sx={{ mt: 2 }}>
                Temperature: {formData.temperature}
              </Typography>
              <Slider
                value={formData.temperature}
                onChange={(e, value) => handleInputChange('temperature', value)}
                min={0.1}
                max={1.5}
                step={0.1}
                marks
                valueLabelDisplay="auto"
              />

              <Typography gutterBottom sx={{ mt: 2 }}>
                Speed: {formData.speed}
              </Typography>
              <Slider
                value={formData.speed}
                onChange={(e, value) => handleInputChange('speed', value)}
                min={0.5}
                max={2.0}
                step={0.1}
                marks
                valueLabelDisplay="auto"
              />

              <Button
                fullWidth
                variant="contained"
                size="large"
                onClick={handleGenerate}
                disabled={loading}
                sx={{ mt: 3 }}
              >
                {loading ? <CircularProgress size={24} /> : `Generate ${mode === 'batch' ? 'Batch ' : ''}TTS`}
              </Button>
            </CardContent>
          </Card>
        </Grid>

        {error && (
          <Grid item xs={12}>
            <Card sx={{ backgroundColor: '#ffebee' }}>
              <CardContent>
                <Typography color="error">{error}</Typography>
              </CardContent>
            </Card>
          </Grid>
        )}

        {results.length > 0 && (
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Generated Audio {loading && '(Processing...)'}
                </Typography>
                <Grid container spacing={2}>
                  {results.map((result, index) => (
                    <Grid item xs={12} sm={6} md={4} key={result.id}>
                      <Card variant="outlined">
                        <CardContent>
                          <Typography variant="subtitle2" gutterBottom>
                            Text {result.index + 1}
                            {result.cached && <Chip label="Cached" size="small" sx={{ ml: 1 }} />}
                          </Typography>
                          <Typography variant="body2" color="textSecondary" sx={{ mb: 2 }}>
                            {result.text.substring(0, 60)}...
                          </Typography>
                          
                          <audio
                            ref={el => audioRefs.current[result.id] = el}
                            controls
                            style={{ width: '100%', marginBottom: '8px' }}
                            src={`data:audio/wav;base64,${result.audio_base64}`}
                          />
                          
                          <Grid container spacing={1}>
                            <Grid item xs={6}>
                              <Button
                                fullWidth
                                size="small"
                                startIcon={<VolumeUp />}
                                onClick={() => handlePlay(result.audio_base64, result.id)}
                              >
                                Play
                              </Button>
                            </Grid>
                            <Grid item xs={6}>
                              <Button
                                fullWidth
                                size="small"
                                startIcon={<Download />}
                                onClick={() => handleDownload(result.audio_base64, index, result.text)}
                              >
                                Download
                              </Button>
                            </Grid>
                          </Grid>
                        </CardContent>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        )}
      </Grid>
    </div>
  );
};

export default TTSChatterbox;