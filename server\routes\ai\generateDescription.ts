import { Request, Response } from 'express';
import OpenAI from 'openai';

// The newest OpenAI model is "gpt-4o" which was released May 13, 2024. do not change this unless explicitly requested by the user
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export async function generateDescription(req: Request, res: Response) {
  try {
    const { title, category } = req.body;

    if (!title) {
      return res.status(400).json({ error: 'Title is required' });
    }

    console.log('Generating description for:', title, 'in category:', category);

    const prompt = `Write a compelling course description for "${title}" in the ${category || 'general'} category.

Create a professional 2-3 paragraph description that:
- Highlights the value and practical outcomes
- Explains what students will learn and achieve
- Uses engaging but professional language
- Focuses on real skills and knowledge gained

Write in plain text without markdown formatting.`;

    let description;

    try {
      // Try OpenAI first
      const response = await openai.chat.completions.create({
        model: "gpt-4o",
        messages: [
          {
            role: "system",
            content: "You are an expert course creator who writes clear, engaging course descriptions."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        temperature: 0.7,
        max_tokens: 500
      });

      description = response.choices[0].message.content?.trim();
      console.log('OpenAI description generated successfully');

    } catch (openaiError: any) {
      console.log('OpenAI failed, trying Gemini fallback:', openaiError?.message);
      
      // Fallback to Gemini
      try {
        const { GoogleGenerativeAI } = require('@google/generative-ai');
        const genAI = new GoogleGenerativeAI(process.env.GOOGLE_GENERATIVE_AI_API_KEY);
        const model = genAI.getGenerativeModel({ model: 'gemini-pro' });
        
        const result = await model.generateContent(prompt);
        const response = await result.response;
        description = response.text().trim();
        console.log('Gemini description generated successfully');
      } catch (geminiError: any) {
        console.log('Gemini also failed:', geminiError?.message);
        // Use intelligent fallback
        description = `Master ${title} with this comprehensive course designed for practical learning. You'll gain essential skills and knowledge that can be applied immediately in real-world scenarios. This course covers everything from fundamentals to advanced techniques, making it perfect for learners at any level who want to excel in ${category || 'this field'}.`;
      }
    }

    return res.status(200).json({ description });
  } catch (error) {
    console.error('Error generating description:', error);
    return res.status(500).json({ error: 'Failed to generate description' });
  }
}

export async function generateAudience(req: Request, res: Response) {
  try {
    const { title, category, description } = req.body;

    if (!title) {
      return res.status(400).json({ error: 'Title is required' });
    }

    console.log('Generating audience for:', title, 'in category:', category);

    const prompt = `Based on the course titled "${title}" in the category "${category || 'general'}"${description ? ` with description: "${description}"` : ''}, identify the ideal target audience for this course. Consider factors like skill level, profession, interests, and learning goals. Write a clear, concise description of who would benefit most from this course. Keep it to 2-3 sentences and avoid jargon.`;

    let audience;

    try {
      // Try OpenAI first
      const response = await openai.chat.completions.create({
        model: "gpt-4o",
        messages: [
          {
            role: "system",
            content: "You are an expert course creator who understands different learning audiences and their needs."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        temperature: 0.7,
        max_tokens: 300
      });

      audience = response.choices[0].message.content?.trim();
      console.log('OpenAI audience generated successfully');

    } catch (openaiError: any) {
      console.log('OpenAI failed, trying Gemini fallback:', openaiError?.message);
      
      // Fallback to Gemini
      try {
        const { GoogleGenerativeAI } = require('@google/generative-ai');
        const genAI = new GoogleGenerativeAI(process.env.GOOGLE_GENERATIVE_AI_API_KEY);
        const model = genAI.getGenerativeModel({ model: 'gemini-pro' });
        
        const result = await model.generateContent(prompt);
        const response = await result.response;
        audience = response.text().trim();
        console.log('Gemini audience generated successfully');
      } catch (geminiError: any) {
        console.log('Gemini also failed:', geminiError?.message);
        // Use intelligent fallback
        audience = `This course is designed for ${category === 'technology' ? 'aspiring developers and tech professionals' : category === 'business' ? 'entrepreneurs and business professionals' : category === 'design' ? 'creative professionals and designers' : 'learners'} who want to master ${title}. Perfect for beginners to intermediate level students seeking practical skills they can apply immediately.`;
      }
    }

    return res.status(200).json({ targetAudience: audience });
  } catch (error) {
    console.error('Error generating audience:', error);
    return res.status(500).json({ error: 'Failed to generate target audience' });
  }
}

export async function generateCourseStructure(req: Request, res: Response) {
  try {
    const { title, description, category, targetAudience, moduleCount } = req.body;

    if (!title) {
      return res.status(400).json({ error: 'Title is required' });
    }

    console.log('Generating course structure for:', title);

    const numModules = moduleCount || 5;
    
    // Enhanced prompt for course structure generation
    const prompt = `Create a detailed course structure for: "${title}"

${description ? `Course Description: ${description}` : ''}
${category ? `Category: ${category}` : ''}
${targetAudience ? `Target Audience: ${targetAudience}` : ''}

Generate a comprehensive course structure with exactly ${numModules} modules. Each module should have 3-5 lessons.

Respond with ONLY valid JSON in this exact format:
{
  "title": "${title}",
  "description": "${description || `Learn ${title} comprehensively with structured lessons and practical applications.`}",
  "modules": [
    {
      "title": "Module 1 Title",
      "description": "Clear description of what this module covers",
      "lessons": [
        {
          "title": "Lesson 1 Title",
          "description": "What students will learn in this lesson"
        },
        {
          "title": "Lesson 2 Title", 
          "description": "What students will learn in this lesson"
        }
      ]
    }
  ]
}`;

    let courseStructure;

    try {
      // Try OpenAI first
      const response = await openai.chat.completions.create({
        model: "gpt-4o",
        messages: [
          {
            role: "system",
            content: "You are an expert instructional designer. Create logical, progressive course structures. Respond with valid JSON only."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        temperature: 0.7,
        max_tokens: 2000,
        response_format: { type: "json_object" }
      });

      const content = response.choices[0].message.content?.trim();
      console.log('OpenAI response:', content);
      
      courseStructure = JSON.parse(content || '{}');
      
    } catch (openaiError: any) {
      console.log('OpenAI failed, trying Gemini fallback:', openaiError?.message || 'Unknown error');
      
      // Fallback to Gemini
      try {
        const { GoogleGenerativeAI } = require('@google/generative-ai');
        const genAI = new GoogleGenerativeAI(process.env.GOOGLE_GENERATIVE_AI_API_KEY);
        const model = genAI.getGenerativeModel({ model: 'gemini-1.5-pro' });
        
        const result = await model.generateContent(prompt);
        const response = await result.response;
        const text = response.text();
        
        // Extract JSON from the response
        const jsonMatch = text.match(/```json\n([\s\S]*)\n```/) || text.match(/```\n([\s\S]*)\n```/) || [null, text];
        const jsonStr = jsonMatch[1] || text;
        courseStructure = JSON.parse(jsonStr.trim());
        console.log('Gemini response successful');
      } catch (geminiError: any) {
        console.log('Gemini also failed:', geminiError?.message || 'Unknown error');
        throw new Error('Both AI services failed');
      }
    }

    // Validate and ensure proper structure
    const validatedStructure = {
      title: courseStructure.title || title,
      description: courseStructure.description || description || `Learn ${title} comprehensively with structured lessons and practical applications.`,
      modules: (courseStructure.modules || []).map((module: any, index: number) => ({
        title: module.title || `Module ${index + 1}`,
        description: module.description || `Learn the fundamentals of ${module.title || `Module ${index + 1}`}`,
        lessons: (module.lessons || []).map((lesson: any, lessonIndex: number) => ({
          title: lesson.title || `Lesson ${lessonIndex + 1}`,
          description: lesson.description || `Key concepts and practical applications`
        }))
      }))
    };

    console.log('Final course data:', validatedStructure);
    return res.status(200).json(validatedStructure);

  } catch (error) {
    console.error('Error generating course structure:', error);
    
    // Create a basic fallback structure
    const fallbackStructure = {
      title: req.body.title,
      description: req.body.description || `Learn ${req.body.title} comprehensively.`,
      modules: [
        {
          title: `Introduction to ${req.body.title}`,
          description: `Get started with the basics and fundamentals`,
          lessons: [
            { title: "Course Overview", description: "What you'll learn in this course" },
            { title: "Getting Started", description: "Setting up and preparation" },
            { title: "Key Concepts", description: "Essential concepts you need to know" }
          ]
        }
      ]
    };
    
    console.log('Using fallback structure:', fallbackStructure);
    return res.status(200).json(fallbackStructure);
  }
}

function inferCategory(title: string): string {
  const titleLower = title.toLowerCase();
  if (titleLower.includes('marketing') || titleLower.includes('social media') || titleLower.includes('advertising')) return 'marketing';
  if (titleLower.includes('programming') || titleLower.includes('coding') || titleLower.includes('web') || titleLower.includes('software')) return 'technology';
  if (titleLower.includes('business') || titleLower.includes('entrepreneur') || titleLower.includes('management')) return 'business';
  if (titleLower.includes('design') || titleLower.includes('ui') || titleLower.includes('ux') || titleLower.includes('graphic')) return 'design';
  if (titleLower.includes('finance') || titleLower.includes('money') || titleLower.includes('investment')) return 'finance';
  if (titleLower.includes('health') || titleLower.includes('fitness') || titleLower.includes('wellness')) return 'health';
  if (titleLower.includes('language') || titleLower.includes('english') || titleLower.includes('spanish')) return 'language';
  if (titleLower.includes('art') || titleLower.includes('craft') || titleLower.includes('creative')) return 'arts';
  if (titleLower.includes('science') || titleLower.includes('physics') || titleLower.includes('chemistry')) return 'science';
  if (titleLower.includes('education') || titleLower.includes('teaching') || titleLower.includes('learning')) return 'education';
  return 'other';
}

function createFallbackCourse(title: string, description?: string, category?: string, targetAudience?: string) {
  const inferredCategory = category || inferCategory(title);
  
  return {
    title: title.includes('Master') || title.includes('Complete') ? title : `Complete ${title} Course`,
    description: description || `Master ${title} with this comprehensive course designed for practical learning. You'll gain essential skills and knowledge that can be applied immediately in real-world scenarios. This course covers everything from fundamentals to advanced techniques, making it perfect for learners at any level.`,
    category: inferredCategory,
    targetAudience: targetAudience || `Anyone interested in learning ${title}, from beginners to intermediate learners looking to enhance their skills`
  };
}

export async function generateTopics(req: Request, res: Response) {
  try {
    const { title, category, description, targetAudience } = req.body;

    if (!title) {
      return res.status(400).json({ error: 'Title is required' });
    }

    const prompt = `
      For the course titled "${title}" in the category "${category || 'general'}"${description ? ` with description: "${description}"` : ''}${targetAudience ? ` targeting: "${targetAudience}"` : ''}, 
      list the key topics and skills that should be covered in this course.
      Focus on the most important concepts, skills, and knowledge areas.
      Present them as a clear, organized list of topics without bullet points or numbers.
      Keep it concise but comprehensive, around 4-8 main topic areas.
    `;

    const response = await openai.chat.completions.create({
      model: "gpt-4o",
      messages: [
        {
          role: "system",
          content: "You are an expert curriculum designer who knows how to structure effective learning content."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.7,
      max_tokens: 400
    });

    const topics = response.choices[0].message.content?.trim();

    return res.status(200).json({ topics });
  } catch (error) {
    console.error('Error generating topics:', error);
    return res.status(500).json({ error: 'Failed to generate key topics' });
  }
}