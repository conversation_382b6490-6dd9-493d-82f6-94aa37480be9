import { Router } from 'express';
import { generateDescription, generateAudience, generateTopics, generateCourseStructure } from './generateDescription';
import { 
  generateModuleScript as cleanModuleScript, 
  generate<PERSON>essonScript as cleanLessonScript 
} from './cleanScriptGeneration';
import { generateAvatar, processAvatarPhoto, getAvatarGallery, avatarUpload } from './avatarGeneration';
import { generateAvatarScript } from './avatarScriptGeneration';
import { generateAvatarCourse } from './courseGeneration';
import { generateLesson } from './generateLesson';
import unifiedTTSRoutes from './unifiedTTS';

const aiRouter = Router();

// AI generation endpoints
aiRouter.post('/generate-description', generateDescription);
aiRouter.post('/generate-audience', generateAudience);
aiRouter.post('/generate-topics', generateTopics);
aiRouter.post('/generate-course-structure', generateCourseStructure);

// Avatar generation endpoints
aiRouter.post('/generate-avatar', generateAvatar);
aiRouter.post('/process-avatar-photo', avatarUpload, processAvatarPhoto);
aiRouter.get('/avatar-gallery', getAvatarGallery);

// Avatar script generation endpoint
aiRouter.post('/generate-avatar-script', generateAvatarScript);

// Avatar course generation endpoint
aiRouter.post('/generate-course', generateAvatarCourse);

// Script generation endpoints - clean output for voice generation
aiRouter.post('/generate-module-script', cleanModuleScript);
aiRouter.post('/generate-lesson-script', cleanLessonScript);

// Lesson expansion endpoint
aiRouter.post('/generate-lesson', generateLesson);

// Unified TTS endpoints (OpenAI + ElevenLabs)
aiRouter.use('/', unifiedTTSRoutes);

export default aiRouter;