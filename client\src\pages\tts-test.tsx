import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Slider } from "@/components/ui/slider";
import { apiRequest } from "@/lib/queryClient";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Loader2, Play, Volume2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

type Voice = {
  voice_id: string;
  name: string;
  preview_url?: string;
  category?: string;
  description?: string;
  source?: string;  // 'coqui' or 'elevenlabs'
};

type Model = {
  model_id: string;
  name: string;
  description?: string;
};

const TTSTest = () => {
  const [voices, setVoices] = useState<Voice[]>([]);
  const [coquiVoices, setCoquiVoices] = useState<Voice[]>([]);
  const [elevenLabsVoices, setElevenLabsVoices] = useState<Voice[]>([]);
  const [models, setModels] = useState<Model[]>([]);
  const [selectedVoice, setSelectedVoice] = useState<string>("");
  const [selectedModel, setSelectedModel] = useState<string>("");
  const [text, setText] = useState<string>("Hello, this is a test of the text-to-speech integration. I hope you're having a great day!");
  const [stability, setStability] = useState<number>(0.5);
  const [similarityBoost, setSimilarityBoost] = useState<number>(0.75);
  const [style, setStyle] = useState<number>(0);
  const [speakerBoost, setSpeakerBoost] = useState<boolean>(true);
  const [loading, setLoading] = useState<boolean>(false);
  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  const [fetchingData, setFetchingData] = useState<boolean>(true);
  const [selectedProvider, setSelectedProvider] = useState<string>("coqui");
  const { toast } = useToast();

  useEffect(() => {
    // Fetch all voices from both Coqui TTS and ElevenLabs
    const fetchVoices = async () => {
      try {
        const response = await apiRequest("GET", "/api/ai/all-voices");
        const data = await response.json();
        
        // Separate voices by provider
        const coqui = data.filter((voice: Voice) => voice.source === 'coqui');
        const elevenLabs = data.filter((voice: Voice) => voice.source === 'elevenlabs');
        
        setCoquiVoices(coqui);
        setElevenLabsVoices(elevenLabs);
        
        // Prioritize Coqui TTS voices
        if (coqui.length > 0) {
          setVoices(coqui);
          setSelectedVoice(coqui[0].voice_id);
          setSelectedProvider("coqui");
        } else if (elevenLabs.length > 0) {
          setVoices(elevenLabs);
          setSelectedVoice(elevenLabs[0].voice_id);
          setSelectedProvider("elevenlabs");
        } else {
          setVoices([]);
        }
      } catch (error) {
        console.error("Failed to fetch voices:", error);
        toast({
          title: "Error fetching voices",
          description: "Could not load voice options. Please check your API keys.",
          variant: "destructive",
        });
      }
    };

    // Fetch models
    const fetchModels = async () => {
      try {
        const response = await apiRequest("GET", "/api/ai/tts-models");
        const data = await response.json();
        setModels(data);
        if (data.length > 0) {
          setSelectedModel(data[0].model_id);
        }
      } catch (error) {
        console.error("Failed to fetch models:", error);
        toast({
          title: "Error fetching models",
          description: "Could not load TTS model options. Please check your ElevenLabs API key.",
          variant: "destructive",
        });
      } finally {
        setFetchingData(false);
      }
    };

    fetchVoices();
    fetchModels();
  }, [toast]);

  const handleGenerate = async () => {
    if (!selectedVoice || !text) {
      toast({
        title: "Missing information",
        description: "Please select a voice and enter text to convert to speech.",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);
    setAudioUrl(null);

    try {
      // Use different endpoint based on selected provider
      const response = await apiRequest("POST", "/api/ai/text-to-speech", {
        text,
        voiceId: selectedVoice,
        modelId: selectedModel,
        stability,
        similarityBoost,
        style,
        speakerBoost,
        provider: selectedProvider  // Pass the provider information
      });

      const data = await response.json();

      if (response.ok) {
        setAudioUrl(data.url);
        toast({
          title: "Success!",
          description: `Audio generated successfully using ${selectedProvider === "coqui" ? "Coqui TTS" : "ElevenLabs"}`,
        });
      } else {
        throw new Error(data.message || "Unknown error");
      }
    } catch (error) {
      console.error("Error generating audio:", error);
      
      // If using Coqui and it failed, try ElevenLabs
      if (selectedProvider === "coqui" && elevenLabsVoices.length > 0) {
        toast({
          title: "Trying fallback service",
          description: "Coqui TTS failed, trying ElevenLabs as fallback...",
        });
        
        try {
          setSelectedProvider("elevenlabs");
          setVoices(elevenLabsVoices);
          setSelectedVoice(elevenLabsVoices[0].voice_id);
          
          const fallbackResponse = await apiRequest("POST", "/api/ai/text-to-speech", {
            text,
            voiceId: elevenLabsVoices[0].voice_id,
            modelId: selectedModel,
            stability,
            similarityBoost,
            style,
            speakerBoost,
            provider: "elevenlabs"  // Explicitly use ElevenLabs as fallback
          });
          
          const fallbackData = await fallbackResponse.json();
          
          if (fallbackResponse.ok) {
            setAudioUrl(fallbackData.url);
            toast({
              title: "Success with fallback!",
              description: "Audio generated successfully using ElevenLabs as fallback",
            });
          } else {
            throw new Error(fallbackData.message || "Unknown error with fallback service");
          }
        } catch (fallbackError) {
          console.error("Error with fallback service:", fallbackError);
          toast({
            title: "Error generating audio",
            description: "Both Coqui TTS and ElevenLabs fallback failed. Please try again later.",
            variant: "destructive",
          });
        }
      } else {
        toast({
          title: "Error generating audio",
          description: error instanceof Error ? error.message : "An unknown error occurred",
          variant: "destructive",
        });
      }
    } finally {
      setLoading(false);
    }
  };

  const playPreviewAudio = (previewUrl?: string) => {
    if (previewUrl) {
      const audio = new Audio(previewUrl);
      audio.play();
    }
  };

  return (
    <div className="container mx-auto py-10">
      <h1 className="text-3xl font-bold mb-6">Text-to-Speech Test</h1>
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle>Generate Speech</CardTitle>
            <CardDescription>
              Convert text to natural-sounding speech using Coqui TTS (default) or ElevenLabs
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="text">Text to convert</Label>
              <Textarea
                id="text"
                value={text}
                onChange={(e) => setText(e.target.value)}
                placeholder="Enter text to convert to speech..."
                className="min-h-[150px]"
              />
            </div>

            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="provider">Voice Provider</Label>
                <Select
                  value={selectedProvider}
                  onValueChange={(value) => {
                    setSelectedProvider(value);
                    // Update available voices based on provider
                    if (value === "coqui" && coquiVoices.length > 0) {
                      setVoices(coquiVoices);
                      setSelectedVoice(coquiVoices[0].voice_id);
                    } else if (value === "elevenlabs" && elevenLabsVoices.length > 0) {
                      setVoices(elevenLabsVoices);
                      setSelectedVoice(elevenLabsVoices[0].voice_id);
                    }
                  }}
                  disabled={fetchingData}
                >
                  <SelectTrigger id="provider">
                    <SelectValue placeholder="Select voice provider" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="coqui">Coqui TTS (Default)</SelectItem>
                    <SelectItem value="elevenlabs">ElevenLabs</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="voice">Voice</Label>
                  <Select
                    value={selectedVoice}
                    onValueChange={setSelectedVoice}
                    disabled={fetchingData}
                  >
                    <SelectTrigger id="voice">
                      <SelectValue placeholder="Select a voice" />
                    </SelectTrigger>
                    <SelectContent>
                      {voices.map((voice) => (
                        <SelectItem key={voice.voice_id} value={voice.voice_id}>
                          <div className="flex items-center justify-between w-full">
                            <span>{voice.name}</span>
                            {voice.preview_url && (
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-6 w-6 ml-2"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  playPreviewAudio(voice.preview_url);
                                }}
                              >
                                <Play className="h-4 w-4" />
                              </Button>
                            )}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="model">Model</Label>
                  <Select
                    value={selectedModel}
                    onValueChange={setSelectedModel}
                    disabled={fetchingData || selectedProvider === "coqui"}
                  >
                    <SelectTrigger id="model">
                      <SelectValue placeholder="Select a model" />
                    </SelectTrigger>
                    <SelectContent>
                      {models.map((model) => (
                        <SelectItem key={model.model_id} value={model.model_id}>
                          {model.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {selectedProvider === "coqui" && (
                    <p className="text-xs text-muted-foreground mt-1">
                      Model selection not applicable for Coqui TTS
                    </p>
                  )}
                </div>
              </div>
            </div>

            <div className="space-y-6 pt-4">
              {selectedProvider === "elevenlabs" && (
                <>
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <Label htmlFor="stability">Stability ({stability})</Label>
                    </div>
                    <Slider
                      id="stability"
                      min={0}
                      max={1}
                      step={0.01}
                      value={[stability]}
                      onValueChange={(value) => setStability(value[0])}
                    />
                    <p className="text-sm text-muted-foreground">
                      Higher values give more consistent results between generation but may sound monotonous.
                    </p>
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <Label htmlFor="similarity-boost">Similarity Boost ({similarityBoost})</Label>
                    </div>
                    <Slider
                      id="similarity-boost"
                      min={0}
                      max={1}
                      step={0.01}
                      value={[similarityBoost]}
                      onValueChange={(value) => setSimilarityBoost(value[0])}
                    />
                    <p className="text-sm text-muted-foreground">
                      Higher values make the voice more closely resemble the reference sample.
                    </p>
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <Label htmlFor="style">Style ({style})</Label>
                    </div>
                    <Slider
                      id="style"
                      min={0}
                      max={1}
                      step={0.01}
                      value={[style]}
                      onValueChange={(value) => setStyle(value[0])}
                    />
                    <p className="text-sm text-muted-foreground">
                      Higher values enhance the style of speech.
                    </p>
                  </div>

                  <div className="flex items-center space-x-2 pt-2">
                    <Switch
                      id="speaker-boost"
                      checked={speakerBoost}
                      onCheckedChange={setSpeakerBoost}
                    />
                    <Label htmlFor="speaker-boost">Speaker Boost</Label>
                  </div>
                </>
              )}
              
              {selectedProvider === "coqui" && (
                <div className="bg-muted/50 p-4 rounded-md">
                  <p className="text-sm text-muted-foreground">
                    Using Coqui TTS as the primary voice service. Voice customization options are simplified with this provider.
                  </p>
                </div>
              )}
            </div>
          </CardContent>
          <CardFooter>
            <Button onClick={handleGenerate} disabled={loading || !selectedVoice || !text}>
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Generating...
                </>
              ) : (
                "Generate Speech"
              )}
            </Button>
          </CardFooter>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Result</CardTitle>
            <CardDescription>Listen to the generated audio</CardDescription>
          </CardHeader>
          <CardContent className="flex flex-col items-center justify-center min-h-[200px]">
            {loading ? (
              <div className="flex flex-col items-center space-y-4">
                <Loader2 className="h-16 w-16 animate-spin text-primary" />
                <p>Generating audio, please wait...</p>
              </div>
            ) : audioUrl ? (
              <div className="w-full space-y-4">
                <div className="flex justify-center">
                  <Volume2 className="h-16 w-16 text-primary" />
                </div>
                <audio controls className="w-full mt-4" src={audioUrl}>
                  Your browser does not support the audio element.
                </audio>
              </div>
            ) : (
              <div className="text-center text-muted-foreground">
                <p>Generated audio will appear here</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default TTSTest;