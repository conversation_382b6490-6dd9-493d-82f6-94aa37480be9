# AI Course Builder

A comprehensive GPU-accelerated platform for creating professional educational content using artificial intelligence. Features a React frontend with tabbed interface for uploading media, entering prompts, and viewing generated outputs including images, videos, audio, and presentations.

## Features

### 🎨 **Stable Diffusion XL Image Generation** (`/gen_image`)
- High-quality 1024x1024 image generation
- Custom prompts and negative prompts
- Batch processing support
- Multiple preset sizes and styles
- Intelligent caching system

### 🎬 **SadTalker Video Synthesis** (`/gen_video`)
- Talking head video generation from static images
- Audio-driven facial animation
- Multiple enhancement options (GFPGAN, Real-ESRGAN)
- Customizable output sizes (256px to 1024px)
- Expression scale control

### 🎙️ **Chatterbox TTS** (`/tts_chatter`)
- 10 high-quality voice presets
- Temperature and speed controls
- Batch text processing
- Enterprise-grade voice synthesis
- Course narration optimization

### 🗣️ **Coqui TTS** (`/tts_coqui`)
- Multi-speaker VCTK model support
- Voice cloning capabilities
- Emotion and style controls
- Advanced neural voice synthesis
- Multiple output formats

### 📊 **Marp Slide Generation** (`/slides`)
- Markdown-to-presentation conversion
- Multiple themes (<PERSON>fault, Gaia, Uncover, Academic)
- PDF, HTML, and PowerPoint output
- Batch slide generation
- Custom styling support

### 🎓 **Complete Course Builder**
- Step-by-step course creation wizard
- Integrated asset generation workflow
- Template-based quick start
- Multi-lesson batch processing
- Export functionality

## Architecture

### Frontend
- **React 18** with TypeScript support
- **Material-UI** components with custom theming
- **Tabbed interface** for organized workflow
- **Drag & drop** file uploads
- **Real-time previews** and caching
- **Responsive design** for all devices

### Backend
- **FastAPI** coordination server
- **Modal A100 80GB GPU** backend
- **Asynchronous processing** with httpx
- **Intelligent fallback** systems
- **Comprehensive error handling**
- **Static file serving** for production

### GPU Infrastructure
- **A100 80GB GPU** acceleration
- **Containerized deployment** with Modal
- **Auto-scaling** based on demand
- **Shared storage** for model caching
- **Health monitoring** and status reporting

## Quick Start

### Prerequisites
1. **Modal Account**: Create at https://modal.com
2. **API Credentials**: Get from https://modal.com/tokens
3. **Node.js 20+** and **Python 3.12+**

### Environment Setup
```bash
export MODAL_TOKEN_ID="your-token-id"
export MODAL_TOKEN_SECRET="your-token-secret"
```

### One-Command Deployment
```bash
cd course-ai-app
./deploy.sh
```

This script will:
1. Install all dependencies
2. Deploy the Modal A100 GPU backend
3. Build the React frontend
4. Start the integrated application server

### Manual Deployment

#### 1. Backend Setup
```bash
cd backend
pip install -r requirements.txt
modal deploy modal_gpu_backend.py
```

#### 2. Frontend Setup
```bash
cd frontend
npm install
npm run build
```

#### 3. Start Application
```bash
cd backend
python server.py
```

## API Endpoints

### Core Generation Services
- `GET /api/health` - API health status
- `GET /api/modal/health` - GPU backend status
- `POST /api/gen_image` - Image generation
- `POST /api/gen_video` - Video synthesis
- `POST /api/tts_chatter` - Chatterbox TTS
- `POST /api/tts_coqui` - Coqui TTS
- `POST /api/slides` - Slide generation

### Example Usage

#### Image Generation
```javascript
const response = await fetch('/api/gen_image', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    prompt: "Professional instructor in modern classroom",
    width: 1024,
    height: 1024,
    num_inference_steps: 30,
    guidance_scale: 7.5
  })
});
```

#### Video Generation
```javascript
const response = await fetch('/api/gen_video', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    image_base64: "base64-encoded-image",
    audio_base64: "base64-encoded-audio",
    enhancer: "gfpgan",
    size: 512
  })
});
```

## Development

### Frontend Development
```bash
cd frontend
npm start  # Development server on http://localhost:3000
```

### Backend Development
```bash
cd backend
uvicorn server:app --reload --port 8000
```

### Modal Development
```bash
cd backend
modal serve modal_gpu_backend.py  # Local development mode
```

## Configuration

### Environment Variables
- `MODAL_TOKEN_ID` - Modal authentication token ID
- `MODAL_TOKEN_SECRET` - Modal authentication secret
- `MODAL_API_URL` - Modal backend URL (auto-configured)
- `PORT` - Application server port (default: 8000)
- `HOST` - Application server host (default: 0.0.0.0)

### Customization
- Modify `frontend/src/App.js` for UI changes
- Update `backend/server.py` for API modifications
- Edit `backend/modal_gpu_backend.py` for GPU service changes

## Performance Optimization

### Caching
- Automatic result caching for repeated requests
- Local storage cache with 1-hour expiration
- Server-side model caching on GPU instances

### Batching
- Batch processing for multiple texts/slides
- Concurrent request handling
- Optimized GPU memory usage

### Scaling
- Auto-scaling GPU instances based on demand
- Connection pooling for high throughput
- Efficient resource cleanup

## Troubleshooting

### Common Issues

#### Modal Authentication
```bash
# Check credentials
echo $MODAL_TOKEN_ID
echo $MODAL_TOKEN_SECRET

# Re-authenticate if needed
modal token set --token-id YOUR_ID --token-secret YOUR_SECRET
```

#### GPU Backend Issues
```bash
# Check backend status
curl http://localhost:8000/api/modal/health

# Redeploy if needed
cd backend && modal deploy modal_gpu_backend.py
```

#### Frontend Build Issues
```bash
# Clear cache and rebuild
cd frontend
rm -rf node_modules package-lock.json
npm install
npm run build
```

### Performance Monitoring
- Check GPU utilization in Modal dashboard
- Monitor API response times
- Review server logs for errors

## Production Deployment

### Replit Deployment
1. Upload project to Replit
2. Set Modal credentials in Secrets
3. Run deployment workflow
4. Application auto-deploys with SSL

### Custom Deployment
- Use Docker containers for reproducible builds
- Configure reverse proxy (nginx/Apache)
- Set up SSL certificates
- Configure environment variables

## Contributing

1. Fork the repository
2. Create feature branch
3. Implement changes with tests
4. Submit pull request with documentation

## License

MIT License - see LICENSE file for details

## Support

- Modal Documentation: https://modal.com/docs
- React Documentation: https://react.dev
- FastAPI Documentation: https://fastapi.tiangolo.com

---

**AI Course Builder** - Professional educational content creation powered by A100 GPU acceleration