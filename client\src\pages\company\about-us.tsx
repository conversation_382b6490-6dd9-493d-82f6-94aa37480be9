import React from 'react';
import { 
  <PERSON>, 
  Heart, 
  Lightbulb, 
  Award, 
  Rocket, 
  CheckCircle,
  Bar<PERSON>hart,
  Target,
  Clock,
  Globe,
  Book
} from 'lucide-react';

export default function AboutUsPage() {
  return (
    <div className="bg-white">
      {/* Hero section */}
      <div className="relative bg-gradient-to-r from-primary-100 to-primary-50 overflow-hidden">
        <div className="max-w-7xl mx-auto py-16 px-4 sm:py-24 sm:px-6 lg:px-8">
          <div className="relative z-10">
            <div className="text-center">
              <h1 className="text-4xl font-extrabold tracking-tight text-gray-900 sm:text-5xl lg:text-6xl">
                About <span className="text-primary">CourseAI</span>
              </h1>
              <p className="mt-6 max-w-2xl mx-auto text-xl text-gray-600">
                Revolutionizing education with AI-powered course creation and delivery
              </p>
            </div>
          </div>
        </div>

        {/* Background circles */}
        <div className="absolute top-0 right-0 -mt-20 -mr-20 opacity-50">
          <div className="w-80 h-80 rounded-full bg-primary-200"></div>
        </div>
        <div className="absolute bottom-0 left-0 -mb-20 -ml-20 opacity-40">
          <div className="w-64 h-64 rounded-full bg-primary-300"></div>
        </div>
      </div>

      {/* Our story section */}
      <div className="relative py-16 bg-white overflow-hidden">
        <div className="hidden lg:block lg:absolute lg:inset-y-0 lg:h-full lg:w-full">
          <div className="relative h-full text-lg max-w-prose mx-auto" aria-hidden="true">
            <svg
              className="absolute top-12 left-full transform translate-x-32"
              width="404"
              height="384"
              fill="none"
              viewBox="0 0 404 384"
            >
              <defs>
                <pattern
                  id="74b3fd99-0a6f-4271-bef2-e80eeafdf357"
                  x="0"
                  y="0"
                  width="20"
                  height="20"
                  patternUnits="userSpaceOnUse"
                >
                  <rect x="0" y="0" width="4" height="4" className="text-primary-200" fill="currentColor" />
                </pattern>
              </defs>
              <rect width="404" height="384" fill="url(#74b3fd99-0a6f-4271-bef2-e80eeafdf357)" />
            </svg>
            <svg
              className="absolute top-1/2 right-full transform -translate-y-1/2 -translate-x-32"
              width="404"
              height="384"
              fill="none"
              viewBox="0 0 404 384"
            >
              <defs>
                <pattern
                  id="f210dbf6-a58d-4871-961e-36d5016a0f49"
                  x="0"
                  y="0"
                  width="20"
                  height="20"
                  patternUnits="userSpaceOnUse"
                >
                  <rect x="0" y="0" width="4" height="4" className="text-primary-200" fill="currentColor" />
                </pattern>
              </defs>
              <rect width="404" height="384" fill="url(#f210dbf6-a58d-4871-961e-36d5016a0f49)" />
            </svg>
          </div>
        </div>
        <div className="relative px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div>
                <h2 className="text-3xl font-extrabold text-gray-900 tracking-tight sm:text-4xl">
                  Our Story
                </h2>
                <div className="mt-6 text-gray-500 space-y-6">
                  <p className="text-lg">
                    Founded in 2023, CourseAI was born from a vision to democratize education and empower creators to share knowledge without technical barriers.
                  </p>
                  <p className="text-lg">
                    Our team of educators, technologists, and AI experts came together with a shared mission: to build a platform that makes professional course creation accessible to everyone, regardless of technical expertise.
                  </p>
                  <p className="text-lg">
                    What started as a simple tool for generating course outlines has evolved into a comprehensive platform that handles everything from content creation to distribution—all powered by cutting-edge AI.
                  </p>
                </div>
              </div>
              <div className="relative">
                <div className="aspect-w-16 aspect-h-9 rounded-xl overflow-hidden shadow-xl">
                  <img
                    src="https://images.unsplash.com/photo-1522202176988-66273c2fd55f?ixlib=rb-1.2.1&auto=format&fit=crop&w=1650&q=80"
                    alt="CourseAI team collaborating"
                    className="object-cover"
                  />
                </div>
                <div className="absolute -bottom-6 -right-6 bg-primary rounded-lg shadow-lg p-4 text-white">
                  <div className="text-4xl font-bold">15K+</div>
                  <div className="text-sm">Course Creators</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Mission and Vision */}
      <div className="bg-gray-50 py-16 sm:py-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 gap-12 lg:grid-cols-2">
            <div className="bg-white rounded-2xl shadow-xl overflow-hidden">
              <div className="p-8">
                <div className="flex items-center">
                  <div className="flex-shrink-0 bg-primary-100 rounded-full p-3">
                    <Target className="h-8 w-8 text-primary" />
                  </div>
                  <h3 className="ml-4 text-2xl font-bold text-gray-900">Our Mission</h3>
                </div>
                <p className="mt-6 text-lg text-gray-500">
                  To empower individuals and organizations to create, share, and monetize knowledge through high-quality online courses, breaking down technological barriers with AI-powered tools.
                </p>
                <div className="mt-8 space-y-4">
                  {[
                    'Making professional content creation accessible to everyone',
                    'Reducing course creation time by up to 80%',
                    'Enabling global reach with multi-language support',
                    'Providing creators with flexible monetization options'
                  ].map((item, index) => (
                    <div key={index} className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-primary mt-1 mr-2" />
                      <span className="text-gray-600">{item}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
            <div className="bg-white rounded-2xl shadow-xl overflow-hidden">
              <div className="p-8">
                <div className="flex items-center">
                  <div className="flex-shrink-0 bg-primary-100 rounded-full p-3">
                    <Lightbulb className="h-8 w-8 text-primary" />
                  </div>
                  <h3 className="ml-4 text-2xl font-bold text-gray-900">Our Vision</h3>
                </div>
                <p className="mt-6 text-lg text-gray-500">
                  To create a world where anyone with expertise can create professional educational content and reach a global audience without technical limitations.
                </p>
                <div className="mt-8 space-y-4">
                  {[
                    'A global learning ecosystem powered by AI and human expertise',
                    'Democratized access to course creation tools',
                    'Personalized learning experiences for all students',
                    'Continuous innovation in educational technology'
                  ].map((item, index) => (
                    <div key={index} className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-primary mt-1 mr-2" />
                      <span className="text-gray-600">{item}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Core values */}
      <div className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h2 className="text-3xl font-extrabold text-gray-900 sm:text-4xl">
              Our Core Values
            </h2>
            <p className="mt-4 max-w-2xl mx-auto text-xl text-gray-500">
              The principles that guide everything we do
            </p>
          </div>

          <div className="mt-16 grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
            {[
              {
                icon: <Heart className="h-8 w-8 text-red-500" />,
                title: 'Passion for Education',
                description: 'We believe in the transformative power of education and are committed to making it accessible to everyone.'
              },
              {
                icon: <Rocket className="h-8 w-8 text-blue-500" />,
                title: 'Innovation',
                description: "We continuously push the boundaries of what's possible with AI and educational technology."
              },
              {
                icon: <Users className="h-8 w-8 text-green-500" />,
                title: 'Community',
                description: 'We foster a supportive community of creators who learn from and inspire each other.'
              },
              {
                icon: <Award className="h-8 w-8 text-yellow-500" />,
                title: 'Excellence',
                description: 'We strive for excellence in everything we do, from our platform to our customer support.'
              },
              {
                icon: <Globe className="h-8 w-8 text-purple-500" />,
                title: 'Global Perspective',
                description: 'We embrace diversity and build solutions that work for creators and learners worldwide.'
              },
              {
                icon: <Book className="h-8 w-8 text-indigo-500" />,
                title: 'Lifelong Learning',
                description: "We're committed to continuous improvement and growth, both as a company and as individuals."
              }
            ].map((value, index) => (
              <div key={index} className="relative bg-white p-6 rounded-lg shadow-md border border-gray-100 hover:shadow-xl transition-shadow duration-300">
                <div className="absolute -top-4 -right-4 bg-white rounded-full p-2 shadow-md">
                  {value.icon}
                </div>
                <h3 className="text-xl font-bold text-gray-900 mt-4">{value.title}</h3>
                <p className="mt-2 text-gray-500">{value.description}</p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Stats section */}
      <div className="bg-primary">
        <div className="max-w-7xl mx-auto py-12 px-4 sm:py-16 sm:px-6 lg:px-8 lg:py-20">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-extrabold text-white sm:text-4xl">
              Trusted by educators and creators worldwide
            </h2>
            <p className="mt-3 text-xl text-primary-100 sm:mt-4">
              Our platform is helping thousands of creators transform their expertise into engaging courses
            </p>
          </div>
          <dl className="mt-10 text-center grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-4">
            {[
              { number: '15,000+', label: 'Active creators', icon: <Users className="h-8 w-8 text-primary-200 mx-auto" /> },
              { number: '200,000+', label: 'Courses created', icon: <Book className="h-8 w-8 text-primary-200 mx-auto" /> },
              { number: '98%', label: 'Customer satisfaction', icon: <Heart className="h-8 w-8 text-primary-200 mx-auto" /> },
              { number: '25+', label: 'Languages supported', icon: <Globe className="h-8 w-8 text-primary-200 mx-auto" /> }
            ].map((stat, index) => (
              <div key={index} className="bg-primary-800 rounded-lg px-4 py-10">
                {stat.icon}
                <dt className="order-2 mt-2 text-lg leading-6 font-medium text-primary-200">
                  {stat.label}
                </dt>
                <dd className="order-1 text-5xl font-extrabold text-white">
                  {stat.number}
                </dd>
              </div>
            ))}
          </dl>
        </div>
      </div>

      {/* Team section */}
      <div className="bg-white py-16 sm:py-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h2 className="text-3xl font-extrabold tracking-tight text-gray-900 sm:text-4xl">
              Meet Our Leadership Team
            </h2>
            <p className="mt-4 max-w-2xl mx-auto text-xl text-gray-500">
              Passionate experts committed to transforming online education
            </p>
          </div>
          <div className="mt-12 mx-auto grid gap-8 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
            {[
              {
                name: 'Sarah Johnson',
                role: 'CEO & Co-Founder',
                bio: 'Former EdTech executive with 15+ years experience revolutionizing digital learning platforms.',
                image: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=8&w=1024&h=1024&q=80'
              },
              {
                name: 'Michael Chen',
                role: 'CTO & Co-Founder',
                bio: 'AI researcher and engineer with expertise in machine learning and natural language processing.',
                image: 'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=8&w=1024&h=1024&q=80'
              },
              {
                name: 'Priya Patel',
                role: 'Chief Product Officer',
                bio: 'Product visionary with a background in UX design and a passion for creating intuitive user experiences.',
                image: 'https://images.unsplash.com/photo-1573497019940-1c28c88b4f3e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=8&w=1024&h=1024&q=80'
              },
              {
                name: 'James Wilson',
                role: 'VP of Marketing',
                bio: 'Digital marketing strategist specialized in building EdTech brands and scaling growth.',
                image: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=8&w=1024&h=1024&q=80'
              },
              {
                name: 'Elena Rodriguez',
                role: 'VP of AI Research',
                bio: 'PhD in Computational Linguistics leading our AI content generation and optimization systems.',
                image: 'https://images.unsplash.com/photo-1569124589354-615739ae007b?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=8&w=1024&h=1024&q=80'
              },
              {
                name: 'David Okafor',
                role: 'VP of Customer Success',
                bio: 'Former educator passionate about helping creators achieve their teaching goals.',
                image: 'https://images.unsplash.com/photo-1531427186611-ecfd6d936c79?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=8&w=1024&h=1024&q=80'
              }
            ].map((person) => (
              <div key={person.name} className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                <div className="aspect-w-3 aspect-h-2">
                  <img className="object-cover" src={person.image} alt={person.name} />
                </div>
                <div className="px-6 py-4">
                  <h3 className="text-xl font-bold text-gray-900">{person.name}</h3>
                  <p className="text-primary font-medium">{person.role}</p>
                  <p className="mt-2 text-gray-500">{person.bio}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
      
      {/* Call to action */}
      <div className="bg-primary-50">
        <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:py-16 lg:px-8 lg:flex lg:items-center lg:justify-between">
          <h2 className="text-3xl font-extrabold tracking-tight text-gray-900 sm:text-4xl">
            <span className="block">Ready to create your first course?</span>
            <span className="block text-primary">Join thousands of successful course creators.</span>
          </h2>
          <div className="mt-8 flex lg:mt-0 lg:flex-shrink-0">
            <div className="inline-flex rounded-md shadow">
              <a
                href="/auth?action=register"
                className="inline-flex items-center justify-center px-5 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary hover:bg-primary-dark"
              >
                Get started
              </a>
            </div>
            <div className="ml-3 inline-flex rounded-md shadow">
              <a
                href="/support/contact-us"
                className="inline-flex items-center justify-center px-5 py-3 border border-transparent text-base font-medium rounded-md text-primary bg-white hover:bg-gray-50"
              >
                Contact us
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}