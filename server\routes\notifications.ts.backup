import express from 'express';
import { z } from 'zod';
import { insertNotificationSchema, insertUserNotificationPreferenceSchema } from '@shared/schema';
import { notificationsService } from '../services/notificationsService';

interface AuthenticatedRequest extends express.Request {
  session: {
    userId: number;
  } & express.Request['session'];
}

const router = express.Router();

// Middleware to ensure user is authenticated
const ensureAuthenticated = (req: express.Request, res: express.Response, next: express.NextFunction) => {
  if (req.session && 'userId' in req.session && req.session.userId) {
    return next();
  }
  res.status(401).json({ message: 'Unauthorized' });
};

// Get unread notification count
router.get('/count', ensureAuthenticated, async (req: any, res) => {
  try {
    const count = await notificationsService.countUnreadNotifications(req.session.userId!);
    res.json({ count });
  } catch (error) {
    console.error('Error fetching notification count:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Get all user notifications with pagination
router.get('/', ensureAuthenticated, async (req, res) => {
  try {
    const limit = req.query.limit ? parseInt(req.query.limit as string) : 20;
    const offset = req.query.offset ? parseInt(req.query.offset as string) : 0;
    
    const notifications = await notificationsService.getUserNotifications(req.session.userId, limit, offset);
    res.json(notifications);
  } catch (error) {
    console.error('Error fetching notifications:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Get only unread notifications
router.get('/unread', ensureAuthenticated, async (req, res) => {
  try {
    const notifications = await notificationsService.getUserUnreadNotifications(req.session.userId);
    res.json(notifications);
  } catch (error) {
    console.error('Error fetching unread notifications:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Mark a notification as read
router.patch('/:id/read', ensureAuthenticated, async (req, res) => {
  try {
    const id = parseInt(req.params.id);
    const notification = await notificationsService.markNotificationAsRead(id, req.session.userId);
    
    if (!notification) {
      return res.status(404).json({ message: 'Notification not found' });
    }
    
    res.json(notification);
  } catch (error) {
    console.error('Error marking notification as read:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Mark all notifications as read
router.post('/mark-all-read', ensureAuthenticated, async (req, res) => {
  try {
    await notificationsService.markAllNotificationsAsRead(req.session.userId);
    res.json({ message: 'All notifications marked as read' });
  } catch (error) {
    console.error('Error marking all notifications as read:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Delete a notification
router.delete('/:id', ensureAuthenticated, async (req, res) => {
  try {
    const id = parseInt(req.params.id);
    await notificationsService.deleteNotification(id, req.session.userId);
    res.json({ message: 'Notification deleted' });
  } catch (error) {
    console.error('Error deleting notification:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Get user notification preferences
router.get('/preferences', ensureAuthenticated, async (req, res) => {
  try {
    const preferences = await notificationsService.getUserNotificationPreferences(req.session.userId);
    res.json(preferences);
  } catch (error) {
    console.error('Error fetching notification preferences:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Update notification preference
router.patch('/preferences/:typeId', ensureAuthenticated, async (req, res) => {
  try {
    const typeId = parseInt(req.params.typeId);
    
    // Validate request body
    const schema = insertUserNotificationPreferenceSchema.partial();
    const validationResult = schema.safeParse(req.body);
    
    if (!validationResult.success) {
      return res.status(400).json({ message: 'Invalid input', errors: validationResult.error.format() });
    }
    
    const data = validationResult.data;
    
    // Ensure userId and typeId
    const preference = await notificationsService.updateUserNotificationPreference({
      ...data,
      userId: req.session.userId,
      typeId,
    });
    
    res.json(preference);
  } catch (error) {
    console.error('Error updating notification preference:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Get the count of unread notifications
router.get('/count', ensureAuthenticated, async (req, res) => {
  try {
    const unreadNotifications = await notificationsService.getUserUnreadNotifications(req.session.userId);
    res.json({ count: unreadNotifications.length });
  } catch (error) {
    console.error('Error fetching notification count:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Get all notification types
router.get('/types', ensureAuthenticated, async (req, res) => {
  try {
    const types = await notificationsService.getNotificationTypes();
    res.json(types);
  } catch (error) {
    console.error('Error fetching notification types:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// For testing: Create a test notification
router.post('/test', ensureAuthenticated, async (req, res) => {
  try {
    // Validate request body
    const schema = z.object({
      type: z.string(),
      title: z.string(),
      message: z.string(),
      linkUrl: z.string().optional(),
    });
    
    const validationResult = schema.safeParse(req.body);
    
    if (!validationResult.success) {
      return res.status(400).json({ message: 'Invalid input', errors: validationResult.error.format() });
    }
    
    const { type, title, message, linkUrl } = validationResult.data;
    
    const notification = await notificationsService.sendNotification({
      userId: req.session.userId,
      type,
      title,
      message,
      linkUrl,
    });
    
    res.json(notification);
  } catch (error) {
    console.error('Error creating test notification:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

export default router;
