import { useState } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import {
  <PERSON>,
  Card<PERSON>ontent,
  <PERSON><PERSON><PERSON>er,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Ta<PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Trigger,
} from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { PexelsMediaSearch } from "./PexelsMediaSearch";
import { PixabayMediaSearch } from "./PixabayMediaSearch";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { Loader2, Upload, Trash2, Image, Video, FileText, DatabaseIcon, Plus } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

// Helper function for formatting file sizes
const getFileSize = (size: number) => {
  if (size < 1024) {
    return `${size} B`;
  } else if (size < 1024 * 1024) {
    return `${(size / 1024).toFixed(1)} KB`;
  } else {
    return `${(size / (1024 * 1024)).toFixed(1)} MB`;
  }
};

interface Media {
  id: number;
  userId: number;
  name: string;
  type: string;
  mimeType: string;
  fileSize: number;
  url: string;
  originalFilename: string | null;
  courseId: number | null;
  lessonId: number | null;
  createdAt: string;
  source: string | null;
  sourceId: string | null;
}

interface MediaLibraryProps {
  onSelect?: (media: Media) => void;
  filter?: string;
  courseId?: number;
  lessonId?: number;
}

export function MediaLibrary({ onSelect, filter, courseId, lessonId }: MediaLibraryProps) {
  const [isUploadOpen, setIsUploadOpen] = useState(false);
  const [isPexelsOpen, setIsPexelsOpen] = useState(false);
  const [isPixabayOpen, setIsPixabayOpen] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [uploadName, setUploadName] = useState("");
  const { toast } = useToast();

  const {
    data: mediaItems,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["/api/media"],
    queryFn: async () => {
      const response = await fetch("/api/media");
      if (!response.ok) {
        throw new Error("Failed to fetch media");
      }
      return response.json() as Promise<Media[]>;
    },
  });

  const uploadMutation = useMutation({
    mutationFn: async (formData: FormData) => {
      const response = await fetch("/api/media/upload", {
        method: "POST",
        body: formData,
      });
      
      if (!response.ok) {
        throw new Error("Upload failed");
      }
      
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/media"] });
      setIsUploadOpen(false);
      setSelectedFile(null);
      setUploadName("");
      toast({
        title: "Upload successful",
        description: "Your file has been uploaded to the media library",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Upload failed",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const deleteMutation = useMutation({
    mutationFn: async (id: number) => {
      const response = await apiRequest("DELETE", `/api/media/${id}`);
      
      if (!response.ok) {
        throw new Error("Delete failed");
      }
      
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/media"] });
      toast({
        title: "Media deleted",
        description: "The media item has been removed from your library",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Delete failed",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setSelectedFile(file);
      setUploadName(file.name);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedFile) {
      toast({
        title: "No file selected",
        description: "Please select a file to upload",
        variant: "destructive",
      });
      return;
    }
    
    const formData = new FormData();
    formData.append("file", selectedFile);
    formData.append("name", uploadName || selectedFile.name);
    
    if (courseId) {
      formData.append("courseId", courseId.toString());
    }
    
    if (lessonId) {
      formData.append("lessonId", lessonId.toString());
    }
    
    uploadMutation.mutate(formData);
  };

  const handleDelete = (id: number) => {
    if (confirm("Are you sure you want to delete this media item?")) {
      deleteMutation.mutate(id);
    }
  };

  const getMediaIcon = (type: string) => {
    switch (type) {
      case "image":
        return <Image className="h-6 w-6" />;
      case "video":
        return <Video className="h-6 w-6" />;
      case "document":
        return <FileText className="h-6 w-6" />;
      default:
        return <DatabaseIcon className="h-6 w-6" />;
    }
  };

  // File size formatting is now handled by the global getFileSize function

  // Make sure mediaItems is an array before filtering
  const mediaItemsArray = Array.isArray(mediaItems) ? mediaItems : [];
  
  const filteredMedia = filter 
    ? mediaItemsArray.filter(item => item.type === filter)
    : mediaItemsArray;

  const handleMediaSelect = (media: { id: number; type: string; url: string; name: string; size: number }) => {
    // Find the newly imported media item in the updated media list
    queryClient.invalidateQueries({ queryKey: ["/api/media"] }).then(() => {
      const mediaList = queryClient.getQueryData<Media[]>(["/api/media"]);
      if (mediaList) {
        // The most recently added media will be the last one with matching name
        const newMedia = [...mediaList].reverse().find(item => 
          item.name === media.name && item.url === media.url
        );
        
        if (newMedia && onSelect) {
          onSelect(newMedia);
        }
      }
    });
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Media Library</h2>
        <div className="flex space-x-2">
          <Button onClick={() => setIsUploadOpen(true)}>
            <Upload className="mr-2 h-4 w-4" />
            Upload
          </Button>
          <Button variant="outline" onClick={() => setIsPexelsOpen(true)}>
            <Image className="mr-2 h-4 w-4" />
            Pexels
          </Button>
          <Button variant="outline" onClick={() => setIsPixabayOpen(true)}>
            <Image className="mr-2 h-4 w-4" />
            Pixabay
          </Button>
        </div>
      </div>

      <Tabs defaultValue="all">
        <TabsList>
          <TabsTrigger value="all">All</TabsTrigger>
          <TabsTrigger value="image">Images</TabsTrigger>
          <TabsTrigger value="video">Videos</TabsTrigger>
          <TabsTrigger value="document">Documents</TabsTrigger>
          <TabsTrigger value="pexels">Pexels Media</TabsTrigger>
          <TabsTrigger value="pixabay">Pixabay Media</TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="mt-4">
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {isLoading ? (
              <div className="col-span-full flex justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
              </div>
            ) : error ? (
              <div className="col-span-full text-center text-red-500 py-8">
                Error loading media: {(error as Error).message}
              </div>
            ) : filteredMedia && filteredMedia.length > 0 ? (
              filteredMedia.map((item) => (
                <MediaItem 
                  key={item.id} 
                  item={item} 
                  onSelect={onSelect}
                  onDelete={handleDelete}
                />
              ))
            ) : (
              <div className="col-span-full text-center py-8">
                No media found in your library.
              </div>
            )}
          </div>
        </TabsContent>

        <TabsContent value="image" className="mt-4">
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {isLoading ? (
              <div className="col-span-full flex justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
              </div>
            ) : error ? (
              <div className="col-span-full text-center text-red-500 py-8">
                Error loading media: {(error as Error).message}
              </div>
            ) : filteredMedia && filteredMedia.filter(item => item.type === "image").length ? (
              filteredMedia.filter(item => item.type === "image").map((item) => (
                <MediaItem 
                  key={item.id} 
                  item={item} 
                  onSelect={onSelect}
                  onDelete={handleDelete}
                />
              ))
            ) : (
              <div className="col-span-full text-center py-8">
                No images found in your library.
              </div>
            )}
          </div>
        </TabsContent>

        <TabsContent value="video" className="mt-4">
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {isLoading ? (
              <div className="col-span-full flex justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
              </div>
            ) : error ? (
              <div className="col-span-full text-center text-red-500 py-8">
                Error loading media: {(error as Error).message}
              </div>
            ) : filteredMedia && filteredMedia.filter(item => item.type === "video").length ? (
              filteredMedia.filter(item => item.type === "video").map((item) => (
                <MediaItem 
                  key={item.id} 
                  item={item} 
                  onSelect={onSelect}
                  onDelete={handleDelete}
                />
              ))
            ) : (
              <div className="col-span-full text-center py-8">
                No videos found in your library.
              </div>
            )}
          </div>
        </TabsContent>

        <TabsContent value="document" className="mt-4">
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {isLoading ? (
              <div className="col-span-full flex justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
              </div>
            ) : error ? (
              <div className="col-span-full text-center text-red-500 py-8">
                Error loading media: {(error as Error).message}
              </div>
            ) : filteredMedia && filteredMedia.filter(item => item.type === "document").length ? (
              filteredMedia.filter(item => item.type === "document").map((item) => (
                <MediaItem 
                  key={item.id} 
                  item={item} 
                  onSelect={onSelect}
                  onDelete={handleDelete}
                />
              ))
            ) : (
              <div className="col-span-full text-center py-8">
                No documents found in your library.
              </div>
            )}
          </div>
        </TabsContent>

        <TabsContent value="pexels" className="mt-4">
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {isLoading ? (
              <div className="col-span-full flex justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
              </div>
            ) : error ? (
              <div className="col-span-full text-center text-red-500 py-8">
                Error loading media: {(error as Error).message}
              </div>
            ) : filteredMedia && filteredMedia.filter(item => item.source === "pexels").length ? (
              filteredMedia.filter(item => item.source === "pexels").map((item) => (
                <MediaItem 
                  key={item.id} 
                  item={item} 
                  onSelect={onSelect}
                  onDelete={handleDelete}
                />
              ))
            ) : (
              <div className="col-span-full text-center py-8 space-y-4">
                <p>No Pexels media found in your library.</p>
                <Button onClick={() => setIsPexelsOpen(true)}>
                  <Plus className="mr-2 h-4 w-4" />
                  Find Pexels Media
                </Button>
              </div>
            )}
          </div>
        </TabsContent>
        
        <TabsContent value="pixabay" className="mt-4">
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {isLoading ? (
              <div className="col-span-full flex justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
              </div>
            ) : error ? (
              <div className="col-span-full text-center text-red-500 py-8">
                Error loading media: {(error as Error).message}
              </div>
            ) : filteredMedia && filteredMedia.filter(item => item.source === "pixabay").length ? (
              filteredMedia.filter(item => item.source === "pixabay").map((item) => (
                <MediaItem 
                  key={item.id} 
                  item={item} 
                  onSelect={onSelect}
                  onDelete={handleDelete}
                />
              ))
            ) : (
              <div className="col-span-full text-center py-8 space-y-4">
                <p>No Pixabay media found in your library.</p>
                <Button onClick={() => setIsPixabayOpen(true)}>
                  <Plus className="mr-2 h-4 w-4" />
                  Find Pixabay Media
                </Button>
              </div>
            )}
          </div>
        </TabsContent>
      </Tabs>

      {/* Upload Dialog */}
      <Dialog open={isUploadOpen} onOpenChange={setIsUploadOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Upload Media</DialogTitle>
            <DialogDescription>
              Upload a file to your media library
            </DialogDescription>
          </DialogHeader>

          <form onSubmit={handleSubmit}>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="file">File</Label>
                <Input
                  id="file"
                  type="file"
                  onChange={handleFileChange}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="name">Name</Label>
                <Input
                  id="name"
                  value={uploadName}
                  onChange={(e) => setUploadName(e.target.value)}
                  placeholder="Enter a name for this file"
                />
              </div>
            </div>

            <DialogFooter className="mt-4">
              <Button type="button" variant="outline" onClick={() => setIsUploadOpen(false)}>
                Cancel
              </Button>
              <Button type="submit" disabled={uploadMutation.isPending}>
                {uploadMutation.isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Uploading...
                  </>
                ) : (
                  <>
                    <Upload className="mr-2 h-4 w-4" />
                    Upload
                  </>
                )}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Pexels Media Search Dialog */}
      <PexelsMediaSearch 
        isOpen={isPexelsOpen} 
        onClose={() => setIsPexelsOpen(false)} 
        onSelect={handleMediaSelect}
      />
      
      {/* Pixabay Media Search Dialog */}
      <PixabayMediaSearch 
        isOpen={isPixabayOpen} 
        onClose={() => setIsPixabayOpen(false)} 
        onSelect={handleMediaSelect}
      />
    </div>
  );
}



interface MediaItemProps {
  item: Media;
  onSelect?: (media: Media) => void;
  onDelete: (id: number) => void;
}

function MediaItem({ item, onSelect, onDelete }: MediaItemProps) {
  // Determine preview based on media type
  const renderPreview = () => {
    if (item.type === "image") {
      return (
        <div className="aspect-video bg-muted overflow-hidden">
          <img
            src={item.url}
            alt={item.name}
            className="w-full h-full object-cover"
          />
        </div>
      );
    } else if (item.type === "video") {
      return (
        <div className="aspect-video bg-muted flex items-center justify-center">
          <Video className="h-10 w-10 text-muted-foreground" />
        </div>
      );
    } else {
      return (
        <div className="aspect-video bg-muted flex items-center justify-center">
          <FileText className="h-10 w-10 text-muted-foreground" />
        </div>
      );
    }
  };

  const isPexels = item.source === "pexels";
  const isPixabay = item.source === "pixabay";

  return (
    <Card className="overflow-hidden">
      <div 
        className="cursor-pointer hover:opacity-90 transition-opacity" 
        onClick={() => onSelect && onSelect(item)}
      >
        {renderPreview()}
      </div>
      <CardHeader className="p-3">
        <CardTitle className="text-sm truncate flex items-center gap-2">
          {isPexels && (
            <span className="text-xs bg-blue-100 text-blue-800 px-1.5 py-0.5 rounded">
              Pexels
            </span>
          )}
          {isPixabay && (
            <span className="text-xs bg-green-100 text-green-800 px-1.5 py-0.5 rounded">
              Pixabay
            </span>
          )}
          {item.name}
        </CardTitle>
      </CardHeader>
      <CardFooter className="p-3 pt-0 flex justify-between items-center">
        <div className="text-xs text-muted-foreground">
          {getFileSize(item.fileSize)}
        </div>
        <Button 
          variant="ghost" 
          size="sm" 
          className="h-8 w-8 p-0"
          onClick={() => onDelete(item.id)}
        >
          <Trash2 className="h-4 w-4" />
        </Button>
      </CardFooter>
    </Card>
  );
}