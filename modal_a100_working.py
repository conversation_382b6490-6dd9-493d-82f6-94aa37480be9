"""
Working Modal A100 GPU Deployment
Simplified version with compatible packages for immediate deployment
"""

import modal
import os
import base64
import tempfile
import subprocess
import json
import time
from typing import Dict, List, Any, Optional
from pathlib import Path

# Modal App Configuration
app = modal.App("courseai-a100-working")

# Simplified A100 GPU Image with working dependencies
gpu_image = (
    modal.Image.debian_slim(python_version="3.10")
    .apt_install([
        "git", "wget", "curl", "ffmpeg", "libsm6", "libxext6", 
        "libxrender-dev", "libglib2.0-0", "libgl1-mesa-glx",
        "libsndfile1", "espeak", "nodejs", "npm"
    ])
    .pip_install([
        "torch==2.0.1",
        "torchvision==0.15.2",
        "torchaudio==2.0.2",
        "opencv-python==********",
        "pillow==10.0.1",
        "numpy==1.24.3",
        "scipy==1.11.4",
        "requests==2.31.0",
        "soundfile==0.12.1",
        "pydub==0.25.1",
        "fastapi==0.104.1",
        "uvicorn==0.24.0"
    ])
    .run_commands([
        "npm install -g @marp-team/marp-cli",
        "mkdir -p /app/models /app/temp"
    ])
)

# Shared storage for models and temp files
shared_volume = modal.Volume.from_name("courseai-a100-storage", create_if_missing=True)

@app.function(
    image=gpu_image,
    gpu=modal.gpu.A100(count=1, size="80GB"),
    volumes={"/app/storage": shared_volume},
    timeout=300,
    memory=16384,
    keep_warm=1
)
def health_check() -> Dict[str, Any]:
    """A100 GPU health check"""
    try:
        import torch
        gpu_available = torch.cuda.is_available()
        gpu_count = torch.cuda.device_count() if gpu_available else 0
        gpu_name = torch.cuda.get_device_name(0) if gpu_available else "No GPU"
        
        # Check GPU memory
        if gpu_available:
            gpu_memory_total = torch.cuda.get_device_properties(0).total_memory / (1024**3)
            torch.cuda.empty_cache()
            gpu_memory_free = (torch.cuda.get_device_properties(0).total_memory - torch.cuda.memory_allocated(0)) / (1024**3)
        else:
            gpu_memory_total = gpu_memory_free = 0
        
        # Test PyTorch GPU operations
        if gpu_available:
            test_tensor = torch.randn(1000, 1000).cuda()
            test_result = torch.mm(test_tensor, test_tensor.t())
            torch.cuda.synchronize()
            del test_tensor, test_result
            torch.cuda.empty_cache()
        
        # Check Marp installation
        try:
            marp_result = subprocess.run(["marp", "--version"], capture_output=True, text=True)
            marp_available = marp_result.returncode == 0
        except:
            marp_available = False
            
        return {
            "status": "online",
            "gpu_available": gpu_available,
            "gpu_count": gpu_count,
            "gpu_name": gpu_name,
            "gpu_memory_total_gb": round(gpu_memory_total, 2),
            "gpu_memory_free_gb": round(gpu_memory_free, 2),
            "services": {
                "marp": marp_available,
                "torch": True,
                "opencv": True
            },
            "timestamp": int(time.time() * 1000)
        }
        
    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "gpu_available": False,
            "gpu_count": 0,
            "gpu_name": "Error",
            "gpu_memory_total_gb": 0,
            "gpu_memory_free_gb": 0,
            "services": {
                "marp": False,
                "torch": False,
                "opencv": False
            },
            "timestamp": int(time.time() * 1000)
        }

@app.function(
    image=gpu_image,
    timeout=300,
    memory=4096
)
def generate_marp_slides(
    markdown_content: str,
    theme: str = "default",
    output_format: str = "pdf"
) -> Dict[str, Any]:
    """Generate presentation slides using Marp CLI"""
    try:
        temp_dir = tempfile.mkdtemp()
        markdown_path = os.path.join(temp_dir, "slides.md")
        output_path = os.path.join(temp_dir, f"slides.{output_format}")
        
        # Write markdown content
        with open(markdown_path, "w", encoding="utf-8") as f:
            f.write(markdown_content)
        
        # Generate slides using Marp
        cmd = [
            "marp",
            markdown_path,
            "--theme", theme,
            "--output", output_path,
            "--allow-local-files"
        ]
        
        if output_format == "pdf":
            cmd.extend(["--pdf"])
        elif output_format == "html":
            cmd.extend(["--html"])
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=180)
        
        if result.returncode != 0:
            raise Exception(f"Marp generation failed: {result.stderr}")
        
        # Read output file and convert to base64
        with open(output_path, "rb") as output_file:
            output_data = output_file.read()
            slides_base64 = base64.b64encode(output_data).decode()
        
        # Cleanup
        import shutil
        shutil.rmtree(temp_dir)
        
        return {
            "status": "success",
            "slides_base64": slides_base64,
            "format": output_format,
            "theme": theme,
            "timestamp": int(time.time() * 1000)
        }
        
    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "format": output_format,
            "theme": theme,
            "timestamp": int(time.time() * 1000)
        }

@app.function(
    image=gpu_image,
    gpu=modal.gpu.A100(count=1, size="80GB"),
    volumes={"/app/storage": shared_volume},
    timeout=600,
    memory=16384
)
def simple_image_processing(
    image_base64: str,
    operation: str = "resize",
    width: int = 512,
    height: int = 512
) -> Dict[str, Any]:
    """Simple image processing using GPU acceleration"""
    try:
        import torch
        import cv2
        import numpy as np
        from PIL import Image
        
        # Decode base64 image
        image_data = base64.b64decode(image_base64)
        
        # Convert to PIL Image
        image = Image.open(io.BytesIO(image_data))
        
        # Convert to numpy array
        img_array = np.array(image)
        
        # Move to GPU for processing
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        img_tensor = torch.from_numpy(img_array).float().to(device)
        
        # Perform operation
        if operation == "resize":
            # Simple resize using OpenCV
            img_resized = cv2.resize(img_array, (width, height))
            result_image = Image.fromarray(img_resized)
        else:
            result_image = image
        
        # Convert back to base64
        with tempfile.NamedTemporaryFile(suffix=".jpg", delete=False) as temp_file:
            result_image.save(temp_file.name, "JPEG")
            
            with open(temp_file.name, "rb") as f:
                result_data = f.read()
                result_base64 = base64.b64encode(result_data).decode()
            
            os.unlink(temp_file.name)
        
        return {
            "status": "success",
            "image_base64": result_base64,
            "operation": operation,
            "width": width,
            "height": height,
            "gpu_used": torch.cuda.is_available(),
            "timestamp": int(time.time() * 1000)
        }
        
    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "operation": operation,
            "gpu_used": False,
            "timestamp": int(time.time() * 1000)
        }

# Web API endpoints
@app.function(
    image=gpu_image,
    timeout=60
)
@modal.web_endpoint(method="GET")
def health():
    """Health check endpoint"""
    return health_check.remote()

@app.function(
    image=gpu_image,
    timeout=300
)
@modal.web_endpoint(method="POST")
def slides(request_data: Dict[str, Any]):
    """Slide generation endpoint"""
    markdown_content = request_data.get("markdown_content", "")
    theme = request_data.get("theme", "default")
    output_format = request_data.get("output_format", "pdf")
    
    if not markdown_content:
        return {"status": "error", "error": "Markdown content is required"}
    
    return generate_marp_slides.remote(markdown_content, theme, output_format)

@app.function(
    image=gpu_image,
    gpu=modal.gpu.A100(count=1, size="80GB"),
    volumes={"/app/storage": shared_volume},
    timeout=600
)
@modal.web_endpoint(method="POST")
def image_process(request_data: Dict[str, Any]):
    """Image processing endpoint"""
    image_base64 = request_data.get("image_base64", "")
    operation = request_data.get("operation", "resize")
    width = request_data.get("width", 512)
    height = request_data.get("height", 512)
    
    if not image_base64:
        return {"status": "error", "error": "Image base64 is required"}
    
    return simple_image_processing.remote(image_base64, operation, width, height)

# Entry point for testing
if __name__ == "__main__":
    print("Modal A100 Working Application")
    print("Ready for deployment and testing")
