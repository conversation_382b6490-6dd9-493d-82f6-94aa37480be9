import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Search, Image, Video, Download, X, Globe, Sparkles, Play, CheckCircle, AlertCircle } from 'lucide-react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';

interface StockMedia {
  id: string;
  type: 'image' | 'video';
  url: string;
  thumbnail?: string;
  title: string;
  source: 'pexels' | 'pixabay';
  tags?: string[];
}

interface StockMediaBrowserProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectMedia?: (media: StockMedia) => void;
}

export function StockMediaBrowser({ isOpen, onClose, onSelectMedia }: StockMediaBrowserProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState('photos');
  const [importingItems, setImportingItems] = useState<Set<string>>(new Set());
  const [importProgress, setImportProgress] = useState<Map<string, { progress: number; status: 'importing' | 'success' | 'error' }>>(new Map());
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Use Pexels as primary source with Pixabay as fallback
  const { data: pexelsPhotos, isLoading: loadingPexelsPhotos, error: pexelsPhotosError } = useQuery({
    queryKey: ['/stock/pexels/photos', searchQuery],
    queryFn: () => fetch(`/stock/pexels/photos?query=${encodeURIComponent(searchQuery)}`).then(res => res.json()),
    enabled: !!searchQuery && searchQuery.length >= 3,
    staleTime: 5 * 60 * 1000,
  });

  const { data: pixabayPhotos, isLoading: loadingPixabayPhotos } = useQuery({
    queryKey: ['/stock/pixabay/photos', searchQuery],
    queryFn: () => fetch(`/stock/pixabay/photos?query=${encodeURIComponent(searchQuery)}`).then(res => res.json()),
    enabled: !!searchQuery && searchQuery.length >= 3 && (!pexelsPhotos || !!pexelsPhotosError),
    staleTime: 5 * 60 * 1000,
  });

  // Use Pexels as primary source for videos with Pixabay as fallback
  const { data: pexelsVideos, isLoading: loadingPexelsVideos, error: pexelsVideosError } = useQuery({
    queryKey: ['/stock/pexels/videos', searchQuery],
    queryFn: () => fetch(`/stock/pexels/videos?query=${encodeURIComponent(searchQuery)}`).then(res => res.json()),
    enabled: !!searchQuery && searchQuery.length >= 3,
    staleTime: 5 * 60 * 1000,
  });

  const { data: pixabayVideos, isLoading: loadingPixabayVideos } = useQuery({
    queryKey: ['/stock/pixabay/videos', searchQuery],
    queryFn: () => fetch(`/stock/pixabay/videos?query=${encodeURIComponent(searchQuery)}`).then(res => res.json()),
    enabled: !!searchQuery && searchQuery.length >= 3 && (!pexelsVideos || !!pexelsVideosError),
    staleTime: 5 * 60 * 1000,
  });

  // Import stock media to user's library
  const importMutation = useMutation({
    mutationFn: async (media: StockMedia) => {
      const itemKey = `${media.source}-${media.id}`;
      
      // Start import progress
      setImportingItems(prev => new Set(prev).add(itemKey));
      setImportProgress(prev => new Map(prev).set(itemKey, { progress: 10, status: 'importing' }));
      
      // Simulate progress updates
      const progressInterval = setInterval(() => {
        setImportProgress(prev => {
          const current = prev.get(itemKey);
          if (current && current.progress < 90) {
            const newMap = new Map(prev);
            newMap.set(itemKey, { ...current, progress: current.progress + 20 });
            return newMap;
          }
          return prev;
        });
      }, 300);
      
      try {
        const endpoint = media.source === 'pexels' ? '/api/pexels/import' : '/api/pixabay/import';
        const result = await apiRequest('POST', endpoint, {
          id: media.id,
          type: media.type === 'video' ? 'video' : 'image',
          url: media.url,
          title: media.title,
        });
        
        clearInterval(progressInterval);
        
        // Complete progress
        setImportProgress(prev => new Map(prev).set(itemKey, { progress: 100, status: 'success' }));
        
        // Show success for 2 seconds then remove
        setTimeout(() => {
          setImportingItems(prev => {
            const newSet = new Set(prev);
            newSet.delete(itemKey);
            return newSet;
          });
          setImportProgress(prev => {
            const newMap = new Map(prev);
            newMap.delete(itemKey);
            return newMap;
          });
        }, 2000);
        
        return result;
      } catch (error) {
        clearInterval(progressInterval);
        setImportProgress(prev => new Map(prev).set(itemKey, { progress: 0, status: 'error' }));
        
        // Remove error state after 3 seconds
        setTimeout(() => {
          setImportingItems(prev => {
            const newSet = new Set(prev);
            newSet.delete(itemKey);
            return newSet;
          });
          setImportProgress(prev => {
            const newMap = new Map(prev);
            newMap.delete(itemKey);
            return newMap;
          });
        }, 3000);
        
        throw error;
      }
    },
    onSuccess: () => {
      toast({
        title: 'Success',
        description: 'Media imported to your library!',
      });
      queryClient.invalidateQueries({ queryKey: ['/api/media'] });
    },
    onError: (error: any) => {
      toast({
        title: 'Import Failed',
        description: error.message || 'Failed to import media',
        variant: 'destructive',
      });
    },
  });

  const handleImportMedia = (media: StockMedia) => {
    importMutation.mutate(media);
    if (onSelectMedia) {
      onSelectMedia(media);
    }
  };

  // Prioritize Pexels results, use Pixabay as fallback
  const stockPhotos = Array.isArray(pexelsPhotos) && pexelsPhotos.length > 0 
    ? pexelsPhotos 
    : Array.isArray(pixabayPhotos) ? pixabayPhotos : [];
  
  const stockVideos = Array.isArray(pexelsVideos) && pexelsVideos.length > 0 
    ? pexelsVideos 
    : Array.isArray(pixabayVideos) ? pixabayVideos : [];
  
  const isLoadingPhotos = loadingPexelsPhotos || (loadingPixabayPhotos && !pexelsPhotos);
  const isLoadingVideos = loadingPexelsVideos || (loadingPixabayVideos && !pexelsVideos);
  
  // Determine primary source for user feedback
  const primaryPhotoSource = Array.isArray(pexelsPhotos) && pexelsPhotos.length > 0 ? 'Pexels' : 'Pixabay';
  const primaryVideoSource = Array.isArray(pexelsVideos) && pexelsVideos.length > 0 ? 'Pexels' : 'Pixabay';

  // Helper function to render import progress indicator
  const renderImportProgress = (media: StockMedia) => {
    const itemKey = `${media.source}-${media.id}`;
    const progress = importProgress.get(itemKey);
    const isImporting = importingItems.has(itemKey);

    if (!isImporting && !progress) {
      return (
        <Button
          size="sm"
          onClick={() => handleImportMedia(media)}
          className="bg-blue-600 hover:bg-blue-700 text-white"
        >
          <Download className="w-4 h-4 mr-2" />
          Import
        </Button>
      );
    }

    if (progress?.status === 'success') {
      return (
        <div className="flex items-center gap-2 px-3 py-2 bg-green-100 text-green-800 rounded-md">
          <CheckCircle className="w-4 h-4" />
          <span className="text-sm font-medium">Imported!</span>
        </div>
      );
    }

    if (progress?.status === 'error') {
      return (
        <div className="flex items-center gap-2 px-3 py-2 bg-red-100 text-red-800 rounded-md">
          <AlertCircle className="w-4 h-4" />
          <span className="text-sm font-medium">Failed</span>
        </div>
      );
    }

    if (progress?.status === 'importing') {
      return (
        <div className="space-y-2">
          <div className="flex items-center gap-2 px-3 py-2 bg-blue-100 text-blue-800 rounded-md">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
            <span className="text-sm font-medium">Importing...</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${progress.progress}%` }}
            ></div>
          </div>
        </div>
      );
    }

    return null;
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center">
              <Globe className="w-4 h-4 text-white" />
            </div>
            <div>
              <DialogTitle className="text-xl">Stock Media Library</DialogTitle>
              <DialogDescription>
                Search and import high-quality images and videos from professional stock libraries
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        <div className="space-y-6">
          {/* Enhanced Search Bar */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
            <Input
              placeholder="Search for images and videos..."
              className="pl-12 h-12 text-lg border-gray-300 focus:border-green-500 focus:ring-green-500"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            {searchQuery && (
              <Button
                variant="ghost"
                size="sm"
                className="absolute right-2 top-1/2 transform -translate-y-1/2"
                onClick={() => setSearchQuery('')}
              >
                <X className="w-4 h-4" />
              </Button>
            )}
          </div>

          {/* Content Tabs */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="photos" className="flex items-center gap-2">
                <Image className="w-4 h-4" />
                Photos ({stockPhotos.length})
              </TabsTrigger>
              <TabsTrigger value="videos" className="flex items-center gap-2">
                <Video className="w-4 h-4" />
                Videos ({stockVideos.length})
              </TabsTrigger>
            </TabsList>

            <TabsContent value="photos" className="mt-6">
              {!searchQuery ? (
                <div className="text-center py-16">
                  <Image className="w-16 h-16 mx-auto text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Start Your Search</h3>
                  <p className="text-gray-500">Enter keywords to find amazing stock photos</p>
                </div>
              ) : isLoadingPhotos ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {[...Array(9)].map((_, i) => (
                    <div key={i} className="aspect-video bg-gray-200 animate-pulse rounded-lg"></div>
                  ))}
                </div>
              ) : stockPhotos.length === 0 ? (
                <div className="text-center py-16">
                  <Search className="w-16 h-16 mx-auto text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No Photos Found</h3>
                  <p className="text-gray-500">Try different keywords or check spelling</p>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-96 overflow-y-auto">
                  {stockPhotos.map((photo) => (
                    <Card key={`${photo.source}-${photo.id}`} className="group hover:shadow-lg transition-all cursor-pointer">
                      <CardContent className="p-0">
                        <div className="relative aspect-video">
                          <img
                            src={photo.thumbnail || photo.url}
                            alt={photo.title}
                            className="w-full h-full object-cover rounded-t-lg"
                          />
                          <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all"></div>
                        </div>
                        <div className="p-3">
                          <p className="text-sm font-medium truncate">{photo.title}</p>
                          <div className="flex items-center justify-between mt-2 mb-3">
                            <Badge variant={photo.source === 'pexels' ? 'default' : 'secondary'}>
                              {photo.source}
                            </Badge>
                            <Badge variant="outline">Photo</Badge>
                          </div>
                          {renderImportProgress(photo)}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </TabsContent>

            <TabsContent value="videos" className="mt-6">
              {!searchQuery ? (
                <div className="text-center py-16">
                  <Video className="w-16 h-16 mx-auto text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Start Your Search</h3>
                  <p className="text-gray-500">Enter keywords to find professional stock videos</p>
                </div>
              ) : isLoadingVideos ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {[...Array(9)].map((_, i) => (
                    <div key={i} className="aspect-video bg-gray-200 animate-pulse rounded-lg"></div>
                  ))}
                </div>
              ) : stockVideos.length === 0 ? (
                <div className="text-center py-16">
                  <Search className="w-16 h-16 mx-auto text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No Videos Found</h3>
                  <p className="text-gray-500">Try different keywords or check spelling</p>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-96 overflow-y-auto">
                  {stockVideos.map((video) => (
                    <Card key={`${video.source}-${video.id}`} className="group hover:shadow-lg transition-all cursor-pointer">
                      <CardContent className="p-0">
                        <div className="relative aspect-video">
                          <img
                            src={video.thumbnail || video.url}
                            alt={video.title}
                            className="w-full h-full object-cover rounded-t-lg"
                          />
                          <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center">
                            <Play className="w-8 h-8 text-white" />
                          </div>
                        </div>
                        <div className="p-3">
                          <p className="text-sm font-medium truncate">{video.title}</p>
                          <div className="flex items-center justify-between mt-2 mb-3">
                            <Badge variant={video.source === 'pexels' ? 'default' : 'secondary'}>
                              {video.source}
                            </Badge>
                            <Badge variant="outline">Video</Badge>
                          </div>
                          {renderImportProgress(video)}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </TabsContent>
          </Tabs>

          {/* Search Tips */}
          {!searchQuery && (
            <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
              <div className="flex items-start gap-3">
                <Sparkles className="w-5 h-5 text-blue-600 mt-0.5" />
                <div>
                  <h4 className="font-medium text-blue-900 mb-1">Search Tips</h4>
                  <ul className="text-sm text-blue-700 space-y-1">
                    <li>• Use specific keywords like "sunset beach" or "business meeting"</li>
                    <li>• Try different variations of your search terms</li>
                    <li>• Search works with both Pexels and Pixabay automatically</li>
                  </ul>
                </div>
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}