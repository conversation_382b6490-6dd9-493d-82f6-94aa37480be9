import fetch from 'cross-fetch';

interface RunPodRequestOptions {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
  headers?: Record<string, string>;
  body?: any;
}

interface RunPodConfig {
  apiKey: string;
  endpoint: string;
}

interface RunPodTextGenerationParams {
  prompt: string;
  maxTokens?: number;
  temperature?: number;
  topP?: number;
  presencePenalty?: number;
  frequencyPenalty?: number;
  stop?: string[];
}

interface RunPodTextToSpeechParams {
  text: string;
  voice?: string;
  speed?: number;
  language?: string;
  quality?: 'standard' | 'high';
  service?: 'kokoro' | 'elevenlabs' | 'coqui';
}

interface RunPodSadTalkerParams {
  audioPath: string;
  imagePath: string;
  enhanceMode?: boolean;
  emotion?: string;
}

interface RunPodImageGenerationParams {
  prompt: string;
  negative_prompt?: string;
  width?: number;
  height?: number;
  num_images?: number;
  guidance_scale?: number;
  steps?: number;
  seed?: number;
}

interface RunPodAnimationParams {
  audioPath: string;      // Path to audio file
  imagePath: string;      // Path to image file
  enhanceMode?: boolean;  // Whether to enhance the face
  emotion?: string;       // Emotion to apply ('neutral', 'happy', 'sad', etc.)
  frames?: number;        // Number of frames (calculated from audio length)
  fps?: number;           // Frames per second
  seed?: number;          // Random seed for reproducibility
}

// RunPod endpoint configurations for our specific AI models
const ENDPOINTS = {
  sadtalker: process.env.RUNPOD_SADTALKER_ENDPOINT,  // Talking avatar generation
  kokoro: process.env.RUNPOD_KOKORO_ENDPOINT,        // High-quality TTS
  coqui: process.env.RUNPOD_COQUI_ENDPOINT || 'https://8x1dneg4ifrfbc-5000.proxy.runpod.net/speak',  // TTS with default endpoint
  marp: process.env.RUNPOD_MARP_ENDPOINT             // Slide generator
};

// Default headers for all RunPod requests
const getDefaultHeaders = (apiKey: string) => ({
  'Content-Type': 'application/json',
  'Authorization': `Bearer ${apiKey}`
});

// Validate required endpoints
const validateEndpoints = () => {
  const missing = Object.entries(ENDPOINTS)
    .filter(([_, value]) => !value)
    .map(([key]) => key);
    
  if (missing.length > 0) {
    throw new Error(`Missing RunPod endpoints: ${missing.join(', ')}`);
  }
};

// Make a request to RunPod API
async function runpodRequest(config: RunPodConfig, path: string, options: RunPodRequestOptions = {}) {
  const { apiKey, endpoint } = config;
  
  if (!apiKey) {
    throw new Error('RunPod API key is required');
  }
  
  if (!endpoint) {
    throw new Error('RunPod endpoint is required');
  }
  
  const url = `${endpoint}${path}`;
  const headers = {
    ...getDefaultHeaders(apiKey),
    ...options.headers
  };
  
  const requestOptions: RequestInit = {
    method: options.method || 'POST',
    headers,
    body: options.body ? JSON.stringify(options.body) : undefined
  };
  
  try {
    const response = await fetch(url, requestOptions);
    
    if (!response.ok) {
      let errorText = await response.text();
      try {
        const errorJson = JSON.parse(errorText);
        errorText = errorJson.error || errorText;
      } catch (e) {
        // Keep errorText as is if it's not valid JSON
      }
      throw new Error(`RunPod API error (${response.status}): ${errorText}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error(`RunPod request failed: ${(error as Error).message}`);
    throw error;
  }
}

// Check if RunPod is properly configured
function isConfigured() {
  return Boolean(process.env.RUNPOD_API_KEY);
}

// Check if a specific endpoint is configured
function isEndpointConfigured(endpointName: string) {
  // Special case for Coqui which has a default value
  if (endpointName === 'coqui') {
    return true; // Always return true for Coqui since it has a default endpoint
  }
  
  const envKey = `RUNPOD_${endpointName.toUpperCase()}_ENDPOINT`;
  return Boolean(process.env[envKey]);
}

// Get config for a specific model
function getModelConfig(model: string): RunPodConfig {
  // Import secure secrets manager
  const { getSecret } = require('../security-fixes/secure-secrets-manager');

  const apiKey = getSecret('RUNPOD_API_KEY') || process.env.RUNPOD_API_KEY;

  if (!apiKey) {
    throw new Error('RunPod API key is not configured');
  }

  let endpoint;

  // Special case for Coqui which has a default endpoint
  if (model === 'coqui') {
    endpoint = getSecret('RUNPOD_COQUI_ENDPOINT') || process.env.RUNPOD_COQUI_ENDPOINT || 'https://8x1dneg4ifrfbc-5000.proxy.runpod.net/speak';
  } else {
    const envKey = `RUNPOD_${model.toUpperCase()}_ENDPOINT`;
    endpoint = getSecret(envKey) || process.env[envKey];

    if (!endpoint) {
      throw new Error(`RunPod endpoint for ${model} is not configured`);
    }
  }

  return { apiKey, endpoint };
}

// Generate text using Mistral or Mixtral
async function generateText(model: 'mistral' | 'mixtral', params: RunPodTextGenerationParams) {
  const config = getModelConfig(model);
  
  const body = {
    input: {
      prompt: params.prompt,
      max_tokens: params.maxTokens || 1024,
      temperature: params.temperature || 0.7,
      top_p: params.topP || 0.9,
      presence_penalty: params.presencePenalty || 0,
      frequency_penalty: params.frequencyPenalty || 0,
      stop: params.stop || []
    }
  };
  
  try {
    const response = await runpodRequest(config, '/run', { body });
    
    // RunPod serverless endpoints return a job ID that we need to poll for results
    return await pollForResults(config, response.id);
  } catch (error) {
    console.error(`Error generating text with ${model}:`, error);
    throw error;
  }
}

// Generate speech using Kokoro or Coqui
async function generateSpeech(model: 'kokoro' | 'coqui', params: RunPodTextToSpeechParams) {
  // Special case for Coqui which has a direct API endpoint
  if (model === 'coqui') {
    try {
      // Import only when needed to avoid circular dependencies
      const coquiTTS = await import('./coqui-tts');
      
      // Call our direct Coqui TTS implementation
      return await coquiTTS.generateSpeech({
        text: params.text,
        voice: params.voice || 'english_standard',
        speed: params.speed || 1.0
      });
    } catch (error) {
      console.error(`Error generating speech with Coqui TTS:`, error);
      throw error;
    }
  }
  
  // For other models, use RunPod
  const config = getModelConfig(model);
  
  const body = {
    input: {
      text: params.text,
      voice: params.voice || 'default',
      speed: params.speed || 1.0,
      language: params.language || 'en',
      quality: params.quality || 'standard'
    }
  };
  
  try {
    const response = await runpodRequest(config, '/run', { body });
    
    // RunPod serverless endpoints return a job ID that we need to poll for results
    return await pollForResults(config, response.id);
  } catch (error) {
    console.error(`Error generating speech with ${model}:`, error);
    throw error;
  }
}

// Generate image using Kandinsky or Wan
async function generateImage(model: 'kandinsky' | 'wan', params: RunPodImageGenerationParams) {
  const config = getModelConfig(model);
  
  const body = {
    input: {
      prompt: params.prompt,
      negative_prompt: params.negative_prompt || '',
      width: params.width || 768,
      height: params.height || 768,
      num_outputs: params.num_images || 1,
      guidance_scale: params.guidance_scale || 7.5,
      num_inference_steps: params.steps || 30,
      seed: params.seed || Math.floor(Math.random() * 1000000)
    }
  };
  
  try {
    const response = await runpodRequest(config, '/run', { body });
    
    // RunPod serverless endpoints return a job ID that we need to poll for results
    return await pollForResults(config, response.id);
  } catch (error) {
    console.error(`Error generating image with ${model}:`, error);
    throw error;
  }
}

// Generate animation using AnimateDiff
async function generateAnimation(params: RunPodAnimationParams) {
  // SadTalker is our talking avatar generation model
  const config = getModelConfig('sadtalker');
  
  const body = {
    input: {
      audio_path: params.audioPath || '',
      image_path: params.imagePath || '',
      enhance_mode: params.enhanceMode || true,
      emotion: params.emotion || 'neutral',
      pose_style: 0, // default pose style
      still: false,  // we want animation, not a still image
      use_enhancer: true, // use face enhancer for better quality
      batch_size: 1, // process one face at a time
      fps: params.fps || 24
    }
  };
  
  try {
    const response = await runpodRequest(config, '/run', { body });
    
    // RunPod serverless endpoints return a job ID that we need to poll for results
    return await pollForResults(config, response.id);
  } catch (error) {
    console.error('Error generating talking avatar with SadTalker:', error);
    throw error;
  }
}

// Poll for results of an async job
async function pollForResults(config: RunPodConfig, jobId: string, maxRetries = 60, delayMs = 2000) {
  let retries = 0;
  
  while (retries < maxRetries) {
    try {
      const response = await runpodRequest(config, `/status/${jobId}`, { method: 'GET' });
      
      if (response.status === 'COMPLETED') {
        return response.output;
      } else if (response.status === 'FAILED') {
        throw new Error(`RunPod job failed: ${response.error || 'Unknown error'}`);
      }
      
      // Job is still running, wait and try again
      await new Promise(resolve => setTimeout(resolve, delayMs));
      retries++;
    } catch (error) {
      console.error('Error polling for results:', error);
      throw error;
    }
  }
  
  throw new Error(`RunPod job timed out after ${maxRetries} retries`);
}

// Get the status of an async job
async function getJobStatus(model: string, jobId: string) {
  const config = getModelConfig(model);
  
  try {
    return await runpodRequest(config, `/status/${jobId}`, { method: 'GET' });
  } catch (error) {
    console.error('Error getting job status:', error);
    throw error;
  }
}

async function generateKokoroSpeech(params: RunPodTextToSpeechParams) {
  const config = getModelConfig('kokoro');
  
  const body = {
    input: {
      text: params.text,
      voice: params.voice || 'default',
      speed: params.speed || 1.0,
      language: params.language || 'en'
    }
  };

  try {
    const response = await runpodRequest(config, '/run', { body });
    return await pollForResults(config, response.id);
  } catch (error) {
    console.error('Error generating speech with Kokoro:', error);
    throw error;
  }
}

async function generateTalkingVideo(params: RunPodSadTalkerParams) {
  const config = getModelConfig('sadtalker');
  
  const body = {
    input: {
      audio_path: params.audioPath,
      image_path: params.imagePath,
      enhance: params.enhanceMode || false,
      emotion: params.emotion || 'neutral'
    }
  };

  try {
    const response = await runpodRequest(config, '/run', { body });
    return await pollForResults(config, response.id);
  } catch (error) {
    console.error('Error generating talking video:', error);
    throw error;
  }
}

export default {
  isConfigured,
  isEndpointConfigured,
  generateText,
  generateSpeech,
  generateImage,
  generateAnimation,
  generateKokoroSpeech,
  generateTalkingVideo,
  getJobStatus
};