interface Step {
  number: number;
  label: string;
}

interface StepIndicatorProps {
  steps: Step[];
  currentStep: number;
}

export function StepIndicator({ steps, currentStep }: StepIndicatorProps) {
  return (
    <div className="flex items-center space-x-1">
      {steps.map((step, index) => (
        <div key={step.number} className="flex-1">
          <div className="relative">
            <div className="flex items-center">
              <div className={`z-10 flex items-center justify-center w-8 h-8 rounded-full text-white ${
                step.number <= currentStep ? "bg-primary" : "bg-slate-200 text-slate-700"
              }`}>
                {step.number}
              </div>
              <div className="flex-1 ml-4">
                <p className={`text-sm font-medium ${
                  step.number <= currentStep ? "text-primary" : "text-slate-500"
                }`}>
                  {step.label}
                </p>
              </div>
            </div>
            {index < steps.length - 1 && (
              <div className={`absolute top-4 left-4 h-full w-px ${
                step.number < currentStep ? "bg-primary" : "bg-slate-200"
              } -ml-px`}></div>
            )}
          </div>
        </div>
      ))}
    </div>
  );
}
