import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { CollaborationStatusBadge } from './CollaborationStatusBadge';
import { ActivityFeed } from './ActivityFeed';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  Users, 
  MessageSquare, 
  History, 
  PlusCircle, 
  Settings,
  Share2,
  ChevronDown
} from 'lucide-react';
import { CollaboratorsDialog } from './CollaboratorsDialog';

interface SharedCourseEditingPanelProps {
  courseId: number;
  courseTitle: string;
  teamId?: number;
}

export function SharedCourseEditingPanel({ 
  courseId, 
  courseTitle,
  teamId
}: SharedCourseEditingPanelProps) {
  const [activeTab, setActiveTab] = useState('collaborators');
  const [showCollaboratorsDialog, setShowCollaboratorsDialog] = useState(false);

  // Demo team members for display
  const teamMembers = [
    {
      id: 1,
      name: 'John Smith',
      avatarUrl: '/avatars/john.jpg',
      role: 'owner',
      isActive: true
    },
    {
      id: 2,
      name: 'Sarah Johnson',
      avatarUrl: '/avatars/sarah.jpg',
      role: 'editor',
      isActive: true
    },
    {
      id: 3,
      name: 'Michael Chen',
      role: 'editor',
      isActive: false
    },
    {
      id: 4,
      name: 'Emma Wilson',
      avatarUrl: '/avatars/emma.jpg',
      role: 'viewer',
      isActive: false
    }
  ];

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <h2 className="text-lg font-semibold">Collaboration</h2>
          <CollaborationStatusBadge courseId={courseId} />
        </div>
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="sm" className="gap-1 h-8">
            <MessageSquare className="h-4 w-4" />
            <span className="hidden sm:inline">Comments</span>
          </Button>
          <Button variant="ghost" size="sm" className="gap-1 h-8">
            <History className="h-4 w-4" />
            <span className="hidden sm:inline">History</span>
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button size="sm" className="gap-1 h-8">
                <Share2 className="h-4 w-4" />
                <span className="hidden sm:inline">Share</span>
                <ChevronDown className="h-3 w-3 ml-1" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => setShowCollaboratorsDialog(true)}>
                <Users className="h-4 w-4 mr-2" />
                Manage Collaborators
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Settings className="h-4 w-4 mr-2" />
                Collaboration Settings
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      <Card>
        <CardHeader className="pb-2">
          <Tabs defaultValue={activeTab} value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid grid-cols-2 w-full">
              <TabsTrigger value="collaborators">Collaborators</TabsTrigger>
              <TabsTrigger value="activity">Activity</TabsTrigger>
            </TabsList>
          </Tabs>
        </CardHeader>
        <CardContent className="pt-2">
          <TabsContent value="collaborators" className="mt-0">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-sm font-medium">People with access</h3>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="h-7"
                  onClick={() => setShowCollaboratorsDialog(true)}
                >
                  <PlusCircle className="h-3.5 w-3.5 mr-1" />
                  Add People
                </Button>
              </div>
              <div className="space-y-3">
                {teamMembers.map(member => (
                  <div key={member.id} className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Avatar className="h-8 w-8">
                        {member.avatarUrl ? (
                          <AvatarImage src={member.avatarUrl} alt={member.name} />
                        ) : (
                          <AvatarFallback>
                            {member.name.charAt(0)}
                          </AvatarFallback>
                        )}
                        {member.isActive && (
                          <div className="absolute bottom-0 right-0 h-2 w-2 rounded-full bg-green-500 border border-white"></div>
                        )}
                      </Avatar>
                      <div>
                        <p className="text-sm font-medium flex items-center">
                          {member.name}
                          {member.role === 'owner' && (
                            <Badge variant="outline" className="ml-2 text-xs py-0 px-2">Owner</Badge>
                          )}
                        </p>
                        <p className="text-xs text-slate-500 capitalize">{member.role}</p>
                      </div>
                    </div>
                    <Badge 
                      variant={member.isActive ? 'secondary' : 'outline'} 
                      className="text-xs"
                    >
                      {member.isActive ? 'Active now' : 'Offline'}
                    </Badge>
                  </div>
                ))}
              </div>
            </div>
          </TabsContent>
          <TabsContent value="activity" className="mt-0">
            <ActivityFeed courseId={courseId} />
          </TabsContent>
        </CardContent>
      </Card>

      {/* Collaborators Dialog */}
      <CollaboratorsDialog
        open={showCollaboratorsDialog}
        onOpenChange={setShowCollaboratorsDialog}
        courseId={courseId}
        courseTitle={courseTitle}
      />
    </div>
  );
}