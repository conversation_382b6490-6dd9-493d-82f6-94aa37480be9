import express, { Request, Response } from 'express';
import { z } from 'zod';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';
import { getSecret, setSecret } from '../security-fixes/secure-secrets-manager';

const router = express.Router();

// Validation schemas
const runpodKeysSchema = z.object({
  service: z.literal('runpod'),
  apiKey: z.string().min(1),
  endpoints: z.object({
    mistral: z.string().optional(),
    mixtral: z.string().optional(),
    kokoro: z.string().optional(),
    coqui: z.string().optional(),
    kandinsky: z.string().optional(),
    wan: z.string().optional(),
    animateDiff: z.string().optional(),
  }),
});

const awsKeysSchema = z.object({
  service: z.literal('aws'),
  accessKeyId: z.string().min(1),
  secretAccessKey: z.string().min(1),
  region: z.string().min(1),
  bucket: z.string().min(1),
  cloudfrontDomain: z.string().optional(),
});

// Combine schemas
const updateKeysSchema = z.discriminatedUnion('service', [
  runpodKeysSchema,
  awsKeysSchema,
]);

// Route to handle API key updates
router.post('/update', async (req: Request, res: Response) => {
  try {
    // Validate request
    const validationResult = updateKeysSchema.safeParse(req.body);
    
    if (!validationResult.success) {
      return res.status(400).json({ 
        message: 'Invalid data provided', 
        errors: validationResult.error.format() 
      });
    }
    
    const data = validationResult.data;
    
    // Path to .env file
    const envPath = path.resolve(process.cwd(), '.env');
    
    // Read current .env content
    let envContent = '';
    if (fs.existsSync(envPath)) {
      envContent = fs.readFileSync(envPath, 'utf-8');
    }
    
    // Function to update or add environment variables
    const updateEnvFile = (key: string, value: string) => {
      const regex = new RegExp(`^${key}=.*$`, 'm');
      
      if (regex.test(envContent)) {
        // Update existing key
        envContent = envContent.replace(regex, `${key}=${value}`);
      } else {
        // Add new key
        envContent += `\n${key}=${value}`;
      }
    };
    
    // Update keys using secure secrets manager
    if (data.service === 'runpod') {
      setSecret('RUNPOD_API_KEY', data.apiKey, 'vault');

      Object.entries(data.endpoints).forEach(([key, value]) => {
        if (value) {
          setSecret(`RUNPOD_${key.toUpperCase()}_ENDPOINT`, value, 'vault');
        }
      });
    } else if (data.service === 'aws') {
      setSecret('AWS_ACCESS_KEY_ID', data.accessKeyId, 'vault');
      setSecret('AWS_SECRET_ACCESS_KEY', data.secretAccessKey, 'vault');
      setSecret('AWS_REGION', data.region, 'vault');
      setSecret('AWS_S3_BUCKET', data.bucket, 'vault');

      if (data.cloudfrontDomain) {
        setSecret('AWS_CLOUDFRONT_DOMAIN', data.cloudfrontDomain, 'vault');
      }
    }
    
    // Write updated .env content
    fs.writeFileSync(envPath, envContent);
    
    // Reload environment variables
    dotenv.config();
    
    // Return success
    res.status(200).json({
      message: `${data.service.toUpperCase()} API keys successfully updated`,
      service: data.service,
    });
  } catch (error) {
    console.error('Error updating API keys:', error);
    res.status(500).json({ 
      message: 'Failed to update API keys',
      error: (error as Error).message 
    });
  }
});

// Route to get available services
router.get('/services', (req: Request, res: Response) => {
  const runpodStatus = {
    service: 'runpod',
    configured: Boolean(getSecret('RUNPOD_API_KEY') || process.env.RUNPOD_API_KEY),
    endpoints: {
      mistral: Boolean(getSecret('RUNPOD_MISTRAL_ENDPOINT') || process.env.RUNPOD_MISTRAL_ENDPOINT),
      mixtral: Boolean(getSecret('RUNPOD_MIXTRAL_ENDPOINT') || process.env.RUNPOD_MIXTRAL_ENDPOINT),
      kokoro: Boolean(getSecret('RUNPOD_KOKORO_ENDPOINT') || process.env.RUNPOD_KOKORO_ENDPOINT),
      coqui: Boolean(getSecret('RUNPOD_COQUI_ENDPOINT') || process.env.RUNPOD_COQUI_ENDPOINT),
      kandinsky: Boolean(getSecret('RUNPOD_KANDINSKY_ENDPOINT') || process.env.RUNPOD_KANDINSKY_ENDPOINT),
      wan: Boolean(getSecret('RUNPOD_WAN_ENDPOINT') || process.env.RUNPOD_WAN_ENDPOINT),
      animateDiff: Boolean(getSecret('RUNPOD_ANIMATEDIFF_ENDPOINT') || process.env.RUNPOD_ANIMATEDIFF_ENDPOINT),
    }
  };
  
  const awsStatus = {
    service: 'aws',
    configured: Boolean(
      process.env.AWS_ACCESS_KEY_ID && 
      process.env.AWS_SECRET_ACCESS_KEY && 
      process.env.AWS_S3_BUCKET
    ),
    region: process.env.AWS_REGION || 'us-east-1',
    bucket: process.env.AWS_S3_BUCKET ? true : false,
    cloudfront: Boolean(process.env.AWS_CLOUDFRONT_DOMAIN)
  };
  
  res.status(200).json({
    services: [runpodStatus, awsStatus],
    timestamp: new Date().toISOString()
  });
});

export default router;