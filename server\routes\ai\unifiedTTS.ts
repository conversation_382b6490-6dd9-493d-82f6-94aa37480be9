import { Router, Request, Response } from 'express';
import { z } from 'zod';
import { unifiedTTS } from '../../services/unifiedTTS';

const router = Router();

// Request validation schemas
const generateSpeechSchema = z.object({
  text: z.string().min(1, "Text is required").max(4096, "Text is too long"),
  voice: z.string().min(1, "Voice is required").optional(),
  voiceId: z.string().min(1, "Voice ID is required").optional(),
  speed: z.number().min(0.25).max(4.0).default(1.0),
  service: z.enum(['neural', 'elevenlabs']).default('neural'),
  quality: z.enum(['standard', 'hd']).default('standard'),
  moduleId: z.string().optional(),
  lessonId: z.string().optional(),
  moduleTitle: z.string().optional(),
  lessonTitle: z.string().optional(),
}).refine(data => data.voice || data.voiceId, {
  message: "Either voice or voiceId is required",
  path: ["voice"]
});

const previewSchema = z.object({
  voiceId: z.string().min(1, "Voice ID is required"),
  service: z.enum(['neural', 'elevenlabs']).default('neural'),
});

// Generate speech endpoint
router.post('/text-to-speech', async (req: Request, res: Response) => {
  try {
    console.log('TTS request received:', JSON.stringify(req.body, null, 2));
    
    const validationResult = generateSpeechSchema.safeParse(req.body);
    if (!validationResult.success) {
      console.log('TTS validation failed:', validationResult.error.format());
      return res.status(400).json({
        error: 'Invalid request data',
        details: validationResult.error.format()
      });
    }

    const { text, voice, voiceId, speed, service, quality, moduleId, lessonId, moduleTitle, lessonTitle } = validationResult.data;

    // Use voiceId if provided, otherwise use voice
    const selectedVoice = voiceId || voice || 'alloy';

    const result = await unifiedTTS.generateSpeech({
      text,
      voice: selectedVoice,
      speed: speed || 1.0,
      service: service || 'neural',
      quality: quality || 'standard'
    });

    if (!result.success) {
      return res.status(400).json({
        error: result.error,
        service: result.service
      });
    }

    return res.status(200).json({
      success: true,
      audioUrl: result.audioUrl,
      fileName: result.fileName,
      duration: result.duration,
      service: result.service,
      moduleId,
      lessonId,
      moduleTitle,
      lessonTitle,
      message: 'Voice generated successfully'
    });

  } catch (error: any) {
    console.error('TTS generation error:', error);
    return res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to generate speech'
    });
  }
});

// Voice preview endpoint
router.post('/voice-preview', async (req: Request, res: Response) => {
  try {
    const validationResult = previewSchema.safeParse(req.body);
    if (!validationResult.success) {
      return res.status(400).json({
        error: 'Invalid request data',
        details: validationResult.error.format()
      });
    }

    const { voiceId, service } = validationResult.data;

    const result = await unifiedTTS.generatePreview(voiceId, service);

    if (!result.success) {
      return res.status(404).json({
        message: 'Preview not available for this voice',
        error: result.error
      });
    }

    return res.status(200).json({
      success: true,
      audioUrl: result.audioUrl,
      service: result.service
    });

  } catch (error: any) {
    console.error('Voice preview error:', error);
    return res.status(500).json({
      error: 'Failed to generate preview',
      message: error.message
    });
  }
});

// Get available voices endpoint
router.get('/voices', async (req: Request, res: Response) => {
  try {
    const voices = await unifiedTTS.getAvailableVoices();
    
    return res.status(200).json({
      voices: voices,
      services: {
        neural: {
          name: 'Neural Voice',
          description: 'High-quality AI-powered voices',
          available: !!process.env.OPENAI_API_KEY,
          models: ['standard', 'hd']
        },
        elevenlabs: {
          name: 'ElevenLabs',
          description: 'Premium voice synthesis',
          available: !!process.env.ELEVENLABS_API_KEY,
          models: ['premium']
        }
      }
    });

  } catch (error: any) {
    console.error('Error fetching voices:', error);
    return res.status(500).json({
      error: 'Failed to fetch voice options',
      message: 'Could not retrieve available voices'
    });
  }
});

// Get TTS models endpoint
router.get('/models', async (req: Request, res: Response) => {
  try {
    const models = [
      {
        id: 'standard',
        name: 'Standard Quality',
        description: 'Fast generation with good quality',
        service: 'neural'
      },
      {
        id: 'hd',
        name: 'HD Quality',
        description: 'Higher quality audio generation',
        service: 'neural'
      }
    ];

    return res.status(200).json({
      models: models,
      services: ['neural', 'elevenlabs']
    });

  } catch (error: any) {
    console.error('Error fetching models:', error);
    return res.status(500).json({
      error: 'Failed to fetch model options',
      message: 'Could not retrieve available models'
    });
  }
});

export default router;