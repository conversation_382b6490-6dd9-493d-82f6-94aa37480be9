modules = ["python-3.12", "nodejs-20"]

[nix]
channel = "stable-24_05"

[workflows]

[workflows.ai-course-builder]
name = "AI Course Builder"
author = "replit"

[[workflows.ai-course-builder.tasks]]
name = "Install Dependencies"
command = "cd frontend && npm install && cd ../backend && pip install -r requirements.txt"

[[workflows.ai-course-builder.tasks]]
name = "Build Frontend"
command = "cd frontend && npm run build"

[[workflows.ai-course-builder.tasks]]
name = "Deploy Modal GPU Backend"
command = "cd backend && modal deploy modal_gpu_backend.py"

[[workflows.ai-course-builder.tasks]]
name = "Start Application"
command = "cd backend && python server.py"

[deployment]
run = ["sh", "-c", "cd backend && python server.py"]

[[ports]]
localPort = 8000
externalPort = 80