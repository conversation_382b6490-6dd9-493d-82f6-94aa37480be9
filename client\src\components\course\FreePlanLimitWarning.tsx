import React from 'react';
import { AlertCircle } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { Link } from "wouter";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useAuth } from "@/hooks/use-auth";
import { getPremiumFeatures } from "@/lib/plan-restrictions";

interface FreePlanLimitWarningProps {
  coursesCount: number;
  maxAllowed: number;
  isDialog?: boolean;
  onClose?: () => void;
}

export function FreePlanLimitWarning({
  coursesCount,
  maxAllowed,
  isDialog = false,
  onClose
}: FreePlanLimitWarningProps) {
  const { user } = useAuth();
  const premiumFeatures = getPremiumFeatures();
  
  const AlertContent = () => (
    <>
      <AlertTitle className="flex items-center">
        <AlertCircle className="h-4 w-4 mr-2" />
        Free Plan Limitation
      </AlertTitle>
      <AlertDescription className="pt-2">
        <p>
          You've reached the maximum limit of {maxAllowed} {maxAllowed === 1 ? 'course' : 'courses'} on the free plan. 
          Upgrade to create more courses and access premium features.
        </p>
        <div className="mt-4">
          <Button asChild>
            <Link href="/pricing">View Pricing Plans</Link>
          </Button>
        </div>
      </AlertDescription>
    </>
  );
  
  const DialogContent = () => (
    <>
      <DialogHeader>
        <DialogTitle>Course Limit Reached</DialogTitle>
        <DialogDescription>
          Free plan users can only create {maxAllowed} {maxAllowed === 1 ? 'course' : 'courses'}.
          Upgrade your plan to create more courses and unlock premium features.
        </DialogDescription>
      </DialogHeader>
      
      <div className="py-4">
        <h3 className="text-sm font-medium mb-2">Premium features include:</h3>
        <ul className="list-disc list-inside space-y-1 text-sm">
          {premiumFeatures.map((feature, index) => (
            <li key={index}>{feature}</li>
          ))}
        </ul>
      </div>
      
      <DialogFooter className="flex flex-col sm:flex-row gap-2">
        {onClose && (
          <Button variant="outline" onClick={onClose} className="w-full sm:w-auto">
            Maybe Later
          </Button>
        )}
        <Button className="w-full sm:w-auto" asChild>
          <Link href="/pricing">View Pricing Plans</Link>
        </Button>
      </DialogFooter>
    </>
  );
  
  if (isDialog) {
    return <DialogContent />;
  }
  
  return (
    <Alert className="my-6 border-yellow-200 bg-yellow-50">
      <AlertContent />
    </Alert>
  );
}