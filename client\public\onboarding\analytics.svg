<?xml version="1.0" encoding="UTF-8"?>
<svg width="400" height="300" viewBox="0 0 400 300" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Analytics dashboard background -->
  <rect x="50" y="50" width="300" height="180" rx="8" fill="white" stroke="#ec4899" stroke-width="3"/>
  
  <!-- Header section -->
  <rect x="50" y="50" width="300" height="30" rx="8" fill="#ec4899"/>
  <text x="70" y="70" font-family="Arial" font-size="14" font-weight="bold" fill="white">Course Analytics Dashboard</text>
  
  <!-- Chart section dividers -->
  <line x1="50" y1="120" x2="350" y2="120" stroke="#f3f4f6" stroke-width="1"/>
  <line x1="200" y1="80" x2="200" y2="230" stroke="#f3f4f6" stroke-width="1"/>
  
  <!-- Bar Chart -->
  <g transform="translate(120, 170)">
    <text x="0" y="-55" font-family="Arial" font-size="10" font-weight="bold" fill="#333">Student Engagement</text>
    
    <!-- Axes -->
    <line x1="-40" y1="0" x2="40" y2="0" stroke="#999" stroke-width="1"/>
    <line x1="-40" y1="0" x2="-40" y2="-50" stroke="#999" stroke-width="1"/>
    
    <!-- Bars -->
    <rect x="-35" y="-20" width="10" height="20" fill="#ec4899" opacity="0.7">
      <animate attributeName="height" values="0;20" dur="1s" begin="0.1s" fill="freeze"/>
      <animate attributeName="y" values="0;-20" dur="1s" begin="0.1s" fill="freeze"/>
    </rect>
    
    <rect x="-20" y="-40" width="10" height="40" fill="#ec4899" opacity="0.8">
      <animate attributeName="height" values="0;40" dur="1s" begin="0.2s" fill="freeze"/>
      <animate attributeName="y" values="0;-40" dur="1s" begin="0.2s" fill="freeze"/>
    </rect>
    
    <rect x="-5" y="-30" width="10" height="30" fill="#ec4899" opacity="0.9">
      <animate attributeName="height" values="0;30" dur="1s" begin="0.3s" fill="freeze"/>
      <animate attributeName="y" values="0;-30" dur="1s" begin="0.3s" fill="freeze"/>
    </rect>
    
    <rect x="10" y="-45" width="10" height="45" fill="#ec4899">
      <animate attributeName="height" values="0;45" dur="1s" begin="0.4s" fill="freeze"/>
      <animate attributeName="y" values="0;-45" dur="1s" begin="0.4s" fill="freeze"/>
    </rect>
    
    <rect x="25" y="-25" width="10" height="25" fill="#ec4899" opacity="0.8">
      <animate attributeName="height" values="0;25" dur="1s" begin="0.5s" fill="freeze"/>
      <animate attributeName="y" values="0;-25" dur="1s" begin="0.5s" fill="freeze"/>
    </rect>
  </g>
  
  <!-- Line Chart -->
  <g transform="translate(280, 170)">
    <text x="0" y="-55" font-family="Arial" font-size="10" font-weight="bold" fill="#333">Completion Rate</text>
    
    <!-- Axes -->
    <line x1="-40" y1="0" x2="40" y2="0" stroke="#999" stroke-width="1"/>
    <line x1="-40" y1="0" x2="-40" y2="-50" stroke="#999" stroke-width="1"/>
    
    <!-- Line chart with animation -->
    <polyline 
      points="-35,0 -25,-15 -15,-10 -5,-25 5,-20 15,-35 25,-30 35,-45" 
      fill="none" 
      stroke="#ec4899" 
      stroke-width="2">
      <animate attributeName="stroke-dasharray" values="0,1000;1000,0" dur="2s" fill="freeze"/>
    </polyline>
    
    <!-- Data points -->
    <circle cx="-35" cy="0" r="3" fill="#ec4899">
      <animate attributeName="opacity" values="0;1" dur="0.1s" begin="0.2s" fill="freeze"/>
    </circle>
    <circle cx="-25" cy="-15" r="3" fill="#ec4899">
      <animate attributeName="opacity" values="0;1" dur="0.1s" begin="0.4s" fill="freeze"/>
    </circle>
    <circle cx="-15" cy="-10" r="3" fill="#ec4899">
      <animate attributeName="opacity" values="0;1" dur="0.1s" begin="0.6s" fill="freeze"/>
    </circle>
    <circle cx="-5" cy="-25" r="3" fill="#ec4899">
      <animate attributeName="opacity" values="0;1" dur="0.1s" begin="0.8s" fill="freeze"/>
    </circle>
    <circle cx="5" cy="-20" r="3" fill="#ec4899">
      <animate attributeName="opacity" values="0;1" dur="0.1s" begin="1.0s" fill="freeze"/>
    </circle>
    <circle cx="15" cy="-35" r="3" fill="#ec4899">
      <animate attributeName="opacity" values="0;1" dur="0.1s" begin="1.2s" fill="freeze"/>
    </circle>
    <circle cx="25" cy="-30" r="3" fill="#ec4899">
      <animate attributeName="opacity" values="0;1" dur="0.1s" begin="1.4s" fill="freeze"/>
    </circle>
    <circle cx="35" cy="-45" r="3" fill="#ec4899">
      <animate attributeName="opacity" values="0;1" dur="0.1s" begin="1.6s" fill="freeze"/>
    </circle>
  </g>
  
  <!-- Stats Cards -->
  <g transform="translate(120, 100)">
    <rect x="-60" y="-10" width="50" height="30" rx="4" fill="#fdf2f8" stroke="#ec4899" stroke-width="1"/>
    <text x="-35" y="-2" font-family="Arial" font-size="6" text-anchor="middle" fill="#ec4899">STUDENTS</text>
    <text x="-35" y="12" font-family="Arial" font-size="12" font-weight="bold" text-anchor="middle" fill="#ec4899">1,248</text>
    <text x="-35" y="22" font-family="Arial" font-size="6" text-anchor="middle" fill="#666">+8.5% ↑</text>
    
    <rect x="0" y="-10" width="50" height="30" rx="4" fill="#fdf2f8" stroke="#ec4899" stroke-width="1"/>
    <text x="25" y="-2" font-family="Arial" font-size="6" text-anchor="middle" fill="#ec4899">ENROLLMENTS</text>
    <text x="25" y="12" font-family="Arial" font-size="12" font-weight="bold" text-anchor="middle" fill="#ec4899">3,724</text>
    <text x="25" y="22" font-family="Arial" font-size="6" text-anchor="middle" fill="#666">+12.3% ↑</text>
  </g>
  
  <g transform="translate(280, 100)">
    <rect x="-60" y="-10" width="50" height="30" rx="4" fill="#fdf2f8" stroke="#ec4899" stroke-width="1"/>
    <text x="-35" y="-2" font-family="Arial" font-size="6" text-anchor="middle" fill="#ec4899">COMPLETION</text>
    <text x="-35" y="12" font-family="Arial" font-size="12" font-weight="bold" text-anchor="middle" fill="#ec4899">76.4%</text>
    <text x="-35" y="22" font-family="Arial" font-size="6" text-anchor="middle" fill="#666">+4.2% ↑</text>
    
    <rect x="0" y="-10" width="50" height="30" rx="4" fill="#fdf2f8" stroke="#ec4899" stroke-width="1"/>
    <text x="25" y="-2" font-family="Arial" font-size="6" text-anchor="middle" fill="#ec4899">SATISFACTION</text>
    <text x="25" y="12" font-family="Arial" font-size="12" font-weight="bold" text-anchor="middle" fill="#ec4899">4.8/5</text>
    <text x="25" y="22" font-family="Arial" font-size="6" text-anchor="middle" fill="#666">+0.2 ↑</text>
  </g>
  
  <!-- Floating elements -->
  <circle cx="40" cy="100" r="15" fill="#ec4899" opacity="0.2"/>
  <circle cx="360" cy="80" r="10" fill="#ec4899" opacity="0.3"/>
  <circle cx="200" cy="250" r="12" fill="#ec4899" opacity="0.2"/>
</svg>