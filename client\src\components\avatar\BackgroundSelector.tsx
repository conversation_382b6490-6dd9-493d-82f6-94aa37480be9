import React, { useState, useRef } from "react";
import { useQuery } from "@tanstack/react-query";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Label } from "@/components/ui/label";
import { 
  ArrowLeft, 
  Check, 
  Upload, 
  Image, 
  Search, 
  Library, 
  X,
  Loader2,
  PaintBucket
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { cn } from "@/lib/utils";

export interface BackgroundSelectorProps {
  onSelect: (data: { id?: string; url?: string }) => void;
  defaultBackgroundUrl?: string;
  onBack: () => void;
}

export function BackgroundSelector({ 
  onSelect, 
  defaultBackgroundUrl = "", 
  onBack 
}: BackgroundSelectorProps) {
  const [selectedTab, setSelectedTab] = useState<string>("solid");
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedBackground, setSelectedBackground] = useState<{
    id?: string;
    type: 'solid' | 'stock' | 'upload' | 'library';
    url?: string;
    color?: string;
  } | null>(defaultBackgroundUrl ? {
    id: "default",
    type: 'stock',
    url: defaultBackgroundUrl
  } : {
    type: 'solid',
    color: '#f8fafc' // A light gray color as default
  });
  
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  // Fetch stock backgrounds
  const { data: stockBackgrounds, isLoading: isLoadingStock } = useQuery({
    queryKey: ['/api/media', { type: 'backgrounds', source: 'stock', q: searchQuery }],
    queryFn: async () => {
      const res = await apiRequest('GET', `/api/media?type=backgrounds&source=stock&q=${encodeURIComponent(searchQuery)}`);
      if (!res.ok) throw new Error('Failed to fetch stock backgrounds');
      return res.json();
    }
  });

  // Fetch library backgrounds
  const { data: libraryBackgrounds, isLoading: isLoadingLibrary } = useQuery({
    queryKey: ['/api/media', { type: 'backgrounds', source: 'library', q: searchQuery }],
    queryFn: async () => {
      const res = await apiRequest('GET', `/api/media?type=backgrounds&source=library&q=${encodeURIComponent(searchQuery)}`);
      if (!res.ok) throw new Error('Failed to fetch library backgrounds');
      return res.json();
    }
  });

  // Predefined solid colors
  const solidColors = [
    { name: 'Light Gray', color: '#f8fafc' },
    { name: 'Blue', color: '#bfdbfe' },
    { name: 'Green', color: '#bbf7d0' },
    { name: 'Purple', color: '#e9d5ff' },
    { name: 'Pink', color: '#fbcfe8' },
    { name: 'Yellow', color: '#fef08a' },
    { name: 'Orange', color: '#fed7aa' },
    { name: 'Red', color: '#fecaca' },
    { name: 'Teal', color: '#99f6e4' },
    { name: 'Indigo', color: '#c7d2fe' },
    { name: 'Slate', color: '#cbd5e1' },
    { name: 'White', color: '#ffffff' },
  ];

  const handleUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file
    if (!file.type.startsWith('image/')) {
      toast({
        title: "Invalid file type",
        description: "Please upload an image file (JPEG, PNG, etc.)",
        variant: "destructive"
      });
      return;
    }

    // Max file size: 5MB
    if (file.size > 5 * 1024 * 1024) {
      toast({
        title: "File too large",
        description: "Please upload an image smaller than 5MB",
        variant: "destructive"
      });
      return;
    }

    try {
      setIsUploading(true);
      
      const formData = new FormData();
      formData.append('file', file);
      formData.append('type', 'background');
      
      // Using FormData requires us to let the browser set the content type with boundary
      const res = await fetch('/api/media/upload', {
        method: 'POST',
        body: formData,
        credentials: 'include'
      });
      
      if (!res.ok) {
        throw new Error('Failed to upload background');
      }
      
      const data = await res.json();
      
      toast({
        title: "Background uploaded",
        description: "Your background has been uploaded successfully",
      });
      
      setSelectedBackground({
        id: data.id,
        type: 'upload',
        url: data.url
      });
      
    } catch (error: any) {
      toast({
        title: "Upload failed",
        description: error.message,
        variant: "destructive"
      });
    } finally {
      setIsUploading(false);
    }
  };

  const handleColorSelect = (color: string) => {
    setSelectedBackground({
      type: 'solid',
      color
    });
  };

  const handleBackgroundSelect = (background: any, type: 'stock' | 'upload' | 'library') => {
    setSelectedBackground({
      id: background.id,
      type,
      url: background.url
    });
  };

  const handleContinue = () => {
    if (selectedBackground) {
      if (selectedBackground.type === 'solid') {
        onSelect({ 
          id: selectedBackground.color,
          url: selectedBackground.color 
        });
      } else if (selectedBackground.url) {
        onSelect({ 
          id: selectedBackground.id,
          url: selectedBackground.url 
        });
      } else {
        // Use nothing if no background selected
        onSelect({});
      }
    } else {
      // Use nothing if no background selected
      onSelect({});
    }
  };

  const handleRemoveBackground = () => {
    setSelectedBackground(null);
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold mb-2">Choose a Background</h2>
        <p className="text-muted-foreground mb-6">
          Select a background for your avatar. You can choose a solid color, use a stock background,
          or upload your own.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-[2fr,1fr] gap-6">
        <div>
          <Tabs value={selectedTab} onValueChange={setSelectedTab} className="w-full">
            <TabsList className="grid grid-cols-4 mb-6">
              <TabsTrigger value="solid" className="flex items-center gap-2">
                <PaintBucket className="h-4 w-4" />
                <span>Solid Color</span>
              </TabsTrigger>
              <TabsTrigger value="stock" className="flex items-center gap-2">
                <Image className="h-4 w-4" />
                <span>Stock</span>
              </TabsTrigger>
              <TabsTrigger value="upload" className="flex items-center gap-2">
                <Upload className="h-4 w-4" />
                <span>Upload</span>
              </TabsTrigger>
              <TabsTrigger value="library" className="flex items-center gap-2">
                <Library className="h-4 w-4" />
                <span>My Library</span>
              </TabsTrigger>
            </TabsList>
            
            {/* Search Bar for stock and library tabs */}
            {(selectedTab === "stock" || selectedTab === "library") && (
              <div className="relative mb-4">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  className="pl-9"
                  placeholder={`Search ${selectedTab === "stock" ? "stock backgrounds" : "your background library"}...`}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            )}

            <TabsContent value="solid" className="space-y-4">
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
                {solidColors.map((colorOption, index) => (
                  <div
                    key={index}
                    className={cn(
                      "cursor-pointer border-2 rounded-md overflow-hidden h-16",
                      selectedBackground?.type === 'solid' && selectedBackground?.color === colorOption.color
                        ? "border-primary ring-2 ring-primary/20"
                        : "border-border hover:border-primary/50"
                    )}
                    onClick={() => handleColorSelect(colorOption.color)}
                  >
                    <div 
                      className="w-full h-full relative" 
                      style={{ backgroundColor: colorOption.color }}
                    >
                      {selectedBackground?.type === 'solid' && 
                       selectedBackground?.color === colorOption.color && (
                        <div className="absolute top-1 right-1 bg-primary text-primary-foreground rounded-full p-1 shadow-sm">
                          <Check className="h-3 w-3" />
                        </div>
                      )}
                    </div>
                    <div className="text-xs p-1 text-center truncate">
                      {colorOption.name}
                    </div>
                  </div>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="stock" className="space-y-4">
              {isLoadingStock ? (
                <div className="flex items-center justify-center h-[300px]">
                  <Loader2 className="h-8 w-8 animate-spin text-primary" />
                </div>
              ) : stockBackgrounds?.length ? (
                <div className="grid grid-cols-2 sm:grid-cols-3 gap-4">
                  {stockBackgrounds.map((background: any) => (
                    <BackgroundCard
                      key={background.id}
                      background={background}
                      isSelected={selectedBackground?.id === background.id && selectedBackground?.type === 'stock'}
                      onClick={() => handleBackgroundSelect(background, 'stock')}
                    />
                  ))}
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center h-[300px] text-center">
                  <Image className="h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium">No stock backgrounds found</h3>
                  <p className="text-muted-foreground mt-1">
                    {searchQuery
                      ? `No results for "${searchQuery}"`
                      : "There are no stock backgrounds available at the moment"}
                  </p>
                </div>
              )}
            </TabsContent>

            <TabsContent value="upload" className="space-y-6">
              <div 
                className="border-2 border-dashed rounded-md p-8 text-center cursor-pointer hover:border-primary/50 transition-colors"
                onClick={() => fileInputRef.current?.click()}
              >
                <input
                  type="file"
                  ref={fileInputRef}
                  className="hidden"
                  accept="image/*"
                  onChange={handleUpload}
                  disabled={isUploading}
                />
                <Upload className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-1">Upload a background image</h3>
                <p className="text-muted-foreground mb-4 max-w-md mx-auto">
                  Drag and drop your image here, or click to browse. Use high-quality images for the best results.
                </p>
                <div className="text-xs text-muted-foreground">
                  Supported formats: JPEG, PNG, WEBP • Max size: 5MB
                </div>
                
                {isUploading && (
                  <div className="mt-4 flex items-center justify-center">
                    <Loader2 className="h-5 w-5 animate-spin text-primary mr-2" />
                    <span>Uploading...</span>
                  </div>
                )}
              </div>
              
              {selectedBackground?.type === 'upload' && selectedBackground?.url && (
                <div className="mt-4">
                  <Label>Selected Background</Label>
                  <div className="mt-2">
                    <BackgroundCard
                      background={{ id: selectedBackground.id, url: selectedBackground.url }}
                      isSelected={true}
                      onClick={() => {}}
                    />
                  </div>
                </div>
              )}
            </TabsContent>

            <TabsContent value="library" className="space-y-4">
              {isLoadingLibrary ? (
                <div className="flex items-center justify-center h-[300px]">
                  <Loader2 className="h-8 w-8 animate-spin text-primary" />
                </div>
              ) : libraryBackgrounds?.length ? (
                <div className="grid grid-cols-2 sm:grid-cols-3 gap-4">
                  {libraryBackgrounds.map((background: any) => (
                    <BackgroundCard
                      key={background.id}
                      background={background}
                      isSelected={selectedBackground?.id === background.id && selectedBackground?.type === 'library'}
                      onClick={() => handleBackgroundSelect(background, 'library')}
                    />
                  ))}
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center h-[300px] text-center">
                  <Library className="h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium">No backgrounds in your library</h3>
                  <p className="text-muted-foreground mt-1 max-w-md">
                    {searchQuery
                      ? `No results for "${searchQuery}"`
                      : "You haven't uploaded any backgrounds yet. Upload a background or use a stock background."}
                  </p>
                </div>
              )}
            </TabsContent>
          </Tabs>
        </div>

        <div>
          <Card>
            <CardContent className="p-6 space-y-6">
              <div>
                <h3 className="text-lg font-medium mb-1">Preview</h3>
                <p className="text-sm text-muted-foreground">
                  This is how your background will appear behind your avatar
                </p>
              </div>

              <div className="aspect-video relative rounded-md overflow-hidden border">
                {selectedBackground ? (
                  <>
                    {selectedBackground.type === 'solid' ? (
                      <div 
                        className="w-full h-full flex items-center justify-center"
                        style={{ backgroundColor: selectedBackground.color }}
                      >
                        <div className="bg-black/20 text-white px-3 py-1 rounded-full text-xs font-medium">
                          Solid Color
                        </div>
                      </div>
                    ) : selectedBackground.url ? (
                      <img
                        src={selectedBackground.url}
                        alt="Selected Background"
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full bg-muted flex items-center justify-center">
                        <span className="text-muted-foreground text-sm">No background</span>
                      </div>
                    )}
                    <Button
                      variant="outline"
                      size="icon"
                      className="absolute top-2 right-2 h-7 w-7 bg-background/80 backdrop-blur-sm"
                      onClick={handleRemoveBackground}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </>
                ) : (
                  <div className="w-full h-full bg-muted flex items-center justify-center">
                    <span className="text-muted-foreground text-sm">No background selected</span>
                  </div>
                )}
              </div>
              
              <div className="bg-muted p-3 rounded-md">
                <h4 className="text-sm font-medium flex items-center mb-1">
                  <Info className="h-4 w-4 mr-1" /> Tips
                </h4>
                <ul className="text-xs text-muted-foreground space-y-1">
                  <li>• Simple, clean backgrounds work best</li>
                  <li>• Use colors that complement your avatar</li>
                  <li>• Avoid busy patterns that could distract viewers</li>
                  <li>• Solid colors are great for professional courses</li>
                </ul>
              </div>

              <div className="flex flex-col gap-2 pt-2">
                <Button 
                  variant="default"
                  onClick={handleContinue}
                >
                  <Check className="h-4 w-4 mr-2" /> Continue
                </Button>
                <Button 
                  variant="outline"
                  onClick={onBack}
                >
                  <ArrowLeft className="h-4 w-4 mr-2" /> Back
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}

interface BackgroundCardProps {
  background: {
    id?: string;
    url: string;
    name?: string;
  };
  isSelected: boolean;
  onClick: () => void;
}

function BackgroundCard({ background, isSelected, onClick }: BackgroundCardProps) {
  return (
    <div
      className={cn(
        "relative cursor-pointer transition-all",
        "border-2 rounded-md overflow-hidden hover:shadow-md",
        isSelected 
          ? "border-primary ring-2 ring-primary/20" 
          : "border-border hover:border-primary/50"
      )}
      onClick={onClick}
    >
      <div className="aspect-video relative">
        <img
          src={background.url}
          alt={background.name || "Background"}
          className="w-full h-full object-cover"
        />
        {isSelected && (
          <div className="absolute top-2 right-2 bg-primary text-primary-foreground rounded-full p-1 shadow-md">
            <Check className="h-3 w-3" />
          </div>
        )}
      </div>
      {background.name && (
        <div className="p-2 text-xs font-medium truncate">{background.name}</div>
      )}
    </div>
  );
}

// Add the Info icon that was used in the component
function Info(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <circle cx="12" cy="12" r="10" />
      <path d="M12 16v-4" />
      <path d="M12 8h.01" />
    </svg>
  );
}