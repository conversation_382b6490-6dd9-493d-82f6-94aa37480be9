// Notification types
export interface Notification {
  id: number;
  userId: number;
  typeId: number;
  type?: string; // Type name (system, course, etc.)
  title: string;
  message: string;
  isRead: boolean | null;
  createdAt: string | Date | null;
  updatedAt?: string | Date | null;
  expiresAt?: string | Date | null;
  linkUrl: string | null;
  sourceId: number | null;
  sourceName: string | null;
  sourceType: string | null;
  iconOverride: string | null;
  metadata?: any;
}

export interface NotificationType {
  id: number;
  name: string;
  description: string | null;
  icon: string | null;
}

export interface NotificationPreference {
  userId: number;
  typeId: number;
  typeName?: string;
  email: boolean;
  push: boolean;
  inApp: boolean;
}
