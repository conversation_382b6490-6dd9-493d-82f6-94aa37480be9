import { useLoading } from '@/components/providers/LoadingProvider';

type TaskType = 
  | 'generating-content'
  | 'saving-course'
  | 'processing-video'
  | 'generating-speech'
  | 'analyzing-content'
  | 'creating-quiz'
  | 'generating-thumbnail'
  | 'building-structure';

interface LoadingConfig {
  text: string;
  subText: string;
  expression: 'happy' | 'thinking' | 'surprised' | 'excited' | 'working';
  estimatedTime?: number; // in ms
}

// Task-specific loading configurations
const loadingConfigs: Record<TaskType, LoadingConfig> = {
  'generating-content': {
    text: 'Generating Course Content',
    subText: 'Our AI is crafting engaging content for your course...',
    expression: 'thinking',
    estimatedTime: 8000,
  },
  'saving-course': {
    text: 'Saving Your Course',
    subText: 'Preserving your hard work. Almost done!',
    expression: 'happy',
    estimatedTime: 2000,
  },
  'processing-video': {
    text: 'Processing Video',
    subText: 'Creating an engaging visual experience...',
    expression: 'working',
    estimatedTime: 15000,
  },
  'generating-speech': {
    text: 'Generating Voice-Over',
    subText: 'Creating natural-sounding narration...',
    expression: 'working',
    estimatedTime: 6000,
  },
  'analyzing-content': {
    text: 'Analyzing Content',
    subText: 'Evaluating and optimizing your course materials...',
    expression: 'thinking',
    estimatedTime: 4000,
  },
  'creating-quiz': {
    text: 'Creating Quiz Questions',
    subText: 'Generating engaging questions to test knowledge...',
    expression: 'excited',
    estimatedTime: 5000,
  },
  'generating-thumbnail': {
    text: 'Creating Course Thumbnail',
    subText: 'Designing an eye-catching thumbnail for your course...',
    expression: 'excited',
    estimatedTime: 5000,
  },
  'building-structure': {
    text: 'Building Course Structure',
    subText: 'Organizing your content into a structured learning path...',
    expression: 'thinking',
    estimatedTime: 3500,
  }
};

export function usePlayfulLoading() {
  const { showLoading, hideLoading, isLoading } = useLoading();
  
  /**
   * Shows a playful loading screen for a specific task
   * @param task The type of task being performed
   * @param customConfig Optional overrides for the default config
   * @param onComplete Optional callback when loading completes
   */
  const showTaskLoading = (
    task: TaskType, 
    customConfig?: Partial<LoadingConfig>,
    onComplete?: () => void
  ) => {
    const config = { ...loadingConfigs[task], ...customConfig };
    
    showLoading({
      text: config.text,
      subText: config.subText,
      mascotExpression: config.expression,
      duration: config.estimatedTime,
      withFunFacts: true,
      withTips: true,
      fullScreen: true,
      withProgressBar: true,
    });
    
    if (onComplete && config.estimatedTime) {
      setTimeout(onComplete, config.estimatedTime);
    }
  };
  
  /**
   * Shows a playful loading screen for a specified duration
   * @param duration Duration in milliseconds
   * @param config Loading screen configuration
   * @param onComplete Optional callback when loading completes
   */
  const showTimedLoading = (
    duration: number,
    config: Partial<LoadingConfig> = {},
    onComplete?: () => void
  ) => {
    showLoading({
      text: config.text || 'Loading...',
      subText: config.subText,
      mascotExpression: config.expression || 'working',
      duration,
      withFunFacts: true,
      withTips: true,
      fullScreen: true,
      withProgressBar: true,
    });
    
    if (onComplete) {
      setTimeout(onComplete, duration);
    }
  };
  
  return {
    showTaskLoading,
    showTimedLoading,
    hideLoading,
    isLoading
  };
}