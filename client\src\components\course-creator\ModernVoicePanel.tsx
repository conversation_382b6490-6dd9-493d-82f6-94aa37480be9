import { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { AlertCircle, Play, Volume2, Sparkles, Star, Clock, User, CheckCircle } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';

interface Voice {
  id: string;
  name: string;
  gender: 'male' | 'female';
  description: string;
  language: string;
  accent?: string;
  service: 'neural' | 'elevenlabs';
}

interface VoiceService {
  name: string;
  description: string;
  available: boolean;
  models: string[];
}

interface ModernVoicePanelProps {
  script: string;
  moduleId: string;
  lessonId: string;
  moduleTitle: string;
  lessonTitle: string;
  onVoiceGenerated: (audioUrl: string, metadata: any) => void;
}

export function ModernVoicePanel({
  script,
  moduleId,
  lessonId,
  moduleTitle,
  lessonTitle,
  onVoiceGenerated
}: ModernVoicePanelProps) {
  const [selectedService, setSelectedService] = useState<'neural' | 'elevenlabs'>('neural');
  const [selectedVoice, setSelectedVoice] = useState<string>('');
  const [selectedQuality, setSelectedQuality] = useState<'standard' | 'hd'>('standard');
  const [speechSpeed, setSpeechSpeed] = useState<number[]>([1.0]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationProgress, setGenerationProgress] = useState(0);
  const [audioUrl, setAudioUrl] = useState<string>('');

  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch available voices and services
  const { data: voicesData, isLoading: voicesLoading } = useQuery({
    queryKey: ['/api/ai/voices'],
    queryFn: async () => {
      const response = await fetch('/api/ai/voices', { credentials: 'include' });
      if (!response.ok) throw new Error('Failed to fetch voices');
      return response.json();
    }
  });

  const voices: Voice[] = voicesData?.voices || [];
  const services: Record<string, VoiceService> = voicesData?.services || {};

  // Filter voices by selected service
  const availableVoices = voices.filter(voice => voice.service === selectedService);

  // Voice generation mutation
  const generateMutation = useMutation({
    mutationFn: async (requestData: any) => {
      const response = await apiRequest('POST', '/api/ai/text-to-speech', requestData);
      return response.json();
    },
    onMutate: () => {
      setIsGenerating(true);
      setGenerationProgress(0);
      
      // Simulate progress
      const progressInterval = setInterval(() => {
        setGenerationProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 300);
      
      return { progressInterval };
    },
    onSuccess: (data: any, variables, context) => {
      if (context?.progressInterval) {
        clearInterval(context.progressInterval);
      }
      setGenerationProgress(100);
      
      if (data?.success) {
        // Set the audio URL for immediate preview
        setAudioUrl(data.audioUrl);
        
        onVoiceGenerated(data.audioUrl, {
          service: data.service,
          voice: selectedVoice,
          quality: selectedQuality,
          speed: speechSpeed[0],
          duration: data.duration,
          fileName: data.fileName
        });
        
        toast({
          title: "Voice Generated Successfully",
          description: `Professional audio created using ${selectedService === 'neural' ? 'Neural Voice' : 'ElevenLabs'}`,
        });
      }
      
      setTimeout(() => {
        setIsGenerating(false);
        setGenerationProgress(0);
      }, 1000);
    },
    onError: (error: any, variables, context) => {
      if (context?.progressInterval) {
        clearInterval(context.progressInterval);
      }
      setIsGenerating(false);
      setGenerationProgress(0);
      
      toast({
        title: "Generation Failed",
        description: error.message || 'Failed to generate voice',
        variant: "destructive",
      });
    }
  });

  // Voice preview mutation
  const previewMutation = useMutation({
    mutationFn: async (voiceId: string) => {
      const response = await apiRequest('POST', '/api/ai/voice-preview', {
        voiceId,
        service: selectedService
      });
      return response.json();
    },
    onSuccess: (data: any) => {
      if (data?.success && data?.audioUrl) {
        const audio = new Audio(data.audioUrl);
        audio.play().catch(console.error);
      }
    },
    onError: (error: any) => {
      toast({
        title: "Preview Not Available",
        description: "Voice preview is temporarily unavailable",
        variant: "destructive",
      });
    }
  });

  // Auto-select first voice when service changes
  useEffect(() => {
    if (availableVoices.length > 0 && !selectedVoice) {
      setSelectedVoice(availableVoices[0].id);
    }
  }, [availableVoices, selectedVoice]);

  const handleGenerate = () => {
    if (!script.trim()) {
      toast({
        title: "No Script Content",
        description: "Please add content to the script before generating voice",
        variant: "destructive",
      });
      return;
    }

    if (!selectedVoice) {
      toast({
        title: "No Voice Selected",
        description: "Please select a voice before generating",
        variant: "destructive",
      });
      return;
    }

    const requestData = {
      text: script,
      voice: selectedVoice,
      speed: speechSpeed[0],
      service: selectedService,
      quality: selectedQuality,
      moduleId,
      lessonId,
      moduleTitle,
      lessonTitle
    };

    generateMutation.mutate(requestData);
  };

  const handlePreview = (voiceId: string) => {
    previewMutation.mutate(voiceId);
  };

  // Calculate estimated duration
  const wordCount = script.split(/\s+/).length;
  const estimatedMinutes = Math.ceil((wordCount / 150) * (1 / speechSpeed[0]));

  if (voicesLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Volume2 className="h-5 w-5" />
            Voice Generation
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-muted-foreground">Loading voice options...</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Volume2 className="h-5 w-5" />
          Voice Generation
        </CardTitle>
        <CardDescription>
          Transform your lesson script into professional audio narration
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Service Selection */}
        <div className="space-y-3">
          <label className="text-sm font-medium">Voice Service</label>
          <Tabs value={selectedService} onValueChange={(value) => setSelectedService(value as 'neural' | 'elevenlabs')}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="neural" className="flex items-center gap-2">
                <Sparkles className="h-4 w-4" />
                Neural Voice
                {services.neural?.available && <Badge variant="secondary" className="text-xs">Available</Badge>}
              </TabsTrigger>
              <TabsTrigger value="elevenlabs" disabled={!services.elevenlabs?.available} className="flex items-center gap-2">
                <Star className="h-4 w-4" />
                ElevenLabs
                {!services.elevenlabs?.available && <Badge variant="outline" className="text-xs">API Key Required</Badge>}
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>

        {/* Service Not Available Warning */}
        {selectedService === 'elevenlabs' && !services.elevenlabs?.available && (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              ElevenLabs requires an API key to be configured. Neural Voice is available and provides high-quality results.
            </AlertDescription>
          </Alert>
        )}

        {/* Voice Selection */}
        <div className="space-y-3">
          <label className="text-sm font-medium">Voice</label>
          <Select value={selectedVoice} onValueChange={setSelectedVoice}>
            <SelectTrigger>
              <SelectValue placeholder="Select a voice" />
            </SelectTrigger>
            <SelectContent>
              {availableVoices.map((voice) => (
                <SelectItem key={voice.id} value={voice.id} className="space-y-1">
                  <div className="flex items-center justify-between w-full">
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4" />
                      <div>
                        <div className="font-medium">{voice.name}</div>
                        <div className="text-xs text-muted-foreground">{voice.description}</div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className="text-xs">
                        {voice.gender}
                      </Badge>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={(e) => {
                          e.stopPropagation();
                          handlePreview(voice.id);
                        }}
                        disabled={previewMutation.isPending}
                      >
                        <Play className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Quality Settings (Neural Voice only) */}
        {selectedService === 'neural' && (
          <div className="space-y-3">
            <label className="text-sm font-medium">Quality</label>
            <Tabs value={selectedQuality} onValueChange={(value) => setSelectedQuality(value as 'standard' | 'hd')}>
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="standard">Standard</TabsTrigger>
                <TabsTrigger value="hd">HD Quality</TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
        )}

        {/* Speech Speed */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <label className="text-sm font-medium">Speech Speed</label>
            <span className="text-sm text-muted-foreground">{speechSpeed[0]}x</span>
          </div>
          <Slider
            value={speechSpeed}
            onValueChange={setSpeechSpeed}
            min={0.25}
            max={4.0}
            step={0.25}
            className="w-full"
          />
          <div className="flex justify-between text-xs text-muted-foreground">
            <span>Slow (0.25x)</span>
            <span>Normal (1x)</span>
            <span>Fast (4x)</span>
          </div>
        </div>

        {/* Script Info */}
        <div className="grid grid-cols-2 gap-4 p-4 bg-muted/50 rounded-lg">
          <div className="text-center">
            <div className="text-2xl font-bold">{wordCount}</div>
            <div className="text-xs text-muted-foreground">Words</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold flex items-center justify-center gap-1">
              <Clock className="h-4 w-4" />
              ~{estimatedMinutes}m
            </div>
            <div className="text-xs text-muted-foreground">Estimated Duration</div>
          </div>
        </div>

        {/* Generation Progress */}
        {isGenerating && (
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span>Generating voice...</span>
              <span>{generationProgress}%</span>
            </div>
            <Progress value={generationProgress} className="w-full" />
          </div>
        )}

        {/* Generated Audio Player */}
        {audioUrl && !isGenerating && (
          <div className="space-y-3 p-4 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center gap-2 text-green-700">
              <CheckCircle className="h-5 w-5" />
              <span className="font-medium">Voice Generated Successfully!</span>
            </div>
            <p className="text-sm text-green-600">
              Your professional narration is ready. Listen to the preview below or continue to video generation.
            </p>
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">Audio Preview</label>
              <audio 
                controls 
                className="w-full"
                preload="metadata"
                style={{ height: '40px' }}
              >
                <source src={audioUrl} type="audio/mpeg" />
                Your browser does not support the audio element.
              </audio>
            </div>
            <div className="flex items-center justify-between text-xs text-gray-500">
              <span>Voice: {availableVoices.find(v => v.id === selectedVoice)?.name}</span>
              <span>Quality: {selectedQuality === 'hd' ? 'HD' : 'Standard'}</span>
              <span>Speed: {speechSpeed[0]}x</span>
            </div>
          </div>
        )}

        {/* Generate Button */}
        <Button 
          onClick={handleGenerate}
          disabled={isGenerating || !selectedVoice || !script.trim() || (selectedService === 'elevenlabs' && !services.elevenlabs?.available)}
          className="w-full"
          size="lg"
        >
          {isGenerating ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Generating...
            </>
          ) : (
            <>
              <Sparkles className="h-4 w-4 mr-2" />
              Generate Professional Narration
            </>
          )}
        </Button>
      </CardContent>
    </Card>
  );
}