import React, { useState, useEffect } from "react";
import { useLocation } from "wouter";
import { v4 as uuidv4 } from 'uuid';
import { useMutation } from '@tanstack/react-query';
import { apiRequest, queryClient } from '@/lib/queryClient';
import { HorizontalStepIndicator, Step } from '@/components/course-creator/HorizontalStepIndicator';
import { ModuleScriptEditor } from '@/components/course-creator/ModuleScriptEditor';
import EnhancedVideoProductionStudio from '@/components/course-creator/EnhancedVideoProductionStudio';
import { useCourseAutoSave } from '@/hooks/useAutoSave';
import { CourseDetailsForm } from "@/components/course-creator/CourseDetailsForm"; 
import AutoQuizGenerator from "@/components/quiz/AutoQuizGenerator";
import EnhancedCourseAssemblyStudio from "@/components/course-creator/EnhancedCourseAssemblyStudio";
import VoiceGenerationModal from "@/components/course-creator/VoiceGenerationModal";
import { 
  ArrowLeft, 
  ArrowRight,
  Layout, 
  BookOpen, 
  FileText, 
  Film, 
  HelpCircle, 
  Globe,
  Save,
  Sparkles,
  Volume2
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";

import { Card } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";

// Define interfaces for our course data
interface Module {
  id: string;
  title: string;
  description?: string;
  complexity?: string;
  lessons: Lesson[];
}

interface Lesson {
  id: string;
  title: string;
  type?: string;
  content?: string;
  duration?: number;
  complexity?: string;
}

interface CourseDetails {
  title: string;
  description: string;
  category: string;
  targetAudience?: string;
  keyTopics?: string;
  contentNotes?: string;
}

interface CourseStructure {
  title?: string;
  description?: string;
  modules: Module[];
}

interface CourseScripts {
  [moduleId: string]: {
    [lessonId: string]: string;
  };
}

// Define the steps for our horizontal step indicator
const steps: Step[] = [
  {
    id: 'details',
    title: 'Course Details',
    description: 'Basic information about your course',
    icon: <Layout className="h-4 w-4" />
  },
  {
    id: 'structure',
    title: 'Content Structure',
    description: 'Organize modules and lessons',
    icon: <BookOpen className="h-4 w-4" />
  },
  {
    id: 'scripts',
    title: 'Scripts',
    description: 'Create educational content',
    icon: <FileText className="h-4 w-4" />
  },
  {
    id: 'quizzes',
    title: 'Quizzes',
    description: 'Create assessments',
    icon: <HelpCircle className="h-4 w-4" />
  },
  {
    id: 'finalization',
    title: 'Video Production',
    description: 'Generate professional video lessons',
    icon: <Film className="h-4 w-4" />
  },
  {
    id: 'publish',
    title: 'Publish',
    description: 'Review and publish your course',
    icon: <Globe className="h-4 w-4" />
  },
];

export default function TraditionalCoursePage() {
  const [_, navigate] = useLocation();
  const { toast } = useToast();
  const [activeStep, setActiveStep] = useState(0);
  const [completedSteps, setCompletedSteps] = useState<number[]>([]);
  const [moduleCount, setModuleCount] = useState(5);
  
  // Generate unique draft ID for this course creation session
  const [draftId] = useState(() => `course-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`);
  
  // Initialize auto-save functionality
  const autoSave = useCourseAutoSave(draftId);
  
  // State for course details
  const [courseDetails, setCourseDetails] = useState<CourseDetails>({
    title: '',
    description: '',
    category: 'technology',
    targetAudience: '',
    keyTopics: '',
    contentNotes: '',
  });
  
  // State for generated structure and scripts
  const [generatedStructure, setGeneratedStructure] = useState<CourseStructure | null>(null);
  const [courseScripts, setCourseScripts] = useState<CourseScripts>({});

  // Migration function to convert old flat structure to new nested structure
  const migrateCourseScripts = (scripts: any): CourseScripts => {
    const migratedScripts: CourseScripts = {};
    
    if (scripts && typeof scripts === 'object') {
      Object.entries(scripts).forEach(([key, value]) => {
        if (typeof value === 'string') {
          // Old flat structure: convert to nested
          // If value looks like a lesson ID, create empty structure
          migratedScripts[key] = {};
        } else if (typeof value === 'object' && value !== null) {
          // Already new nested structure
          migratedScripts[key] = value as { [lessonId: string]: string };
        }
      });
    }
    
    return migratedScripts;
  };
  const [isGenerating, setIsGenerating] = useState(false);
  const [isGeneratingDescription, setIsGeneratingDescription] = useState(false);
  const [isGeneratingAudience, setIsGeneratingAudience] = useState(false);
  const [isGeneratingTopics, setIsGeneratingTopics] = useState(false);
  const [progress, setProgress] = useState(0);
  const [isBalancing, setIsBalancing] = useState(false);
  
  // Voice creation state
  const [showVoiceModal, setShowVoiceModal] = useState(false);
  const [generatedAudioFiles, setGeneratedAudioFiles] = useState<any[]>([]);

  // Legacy states for compatibility with existing components
  const [media, setMedia] = useState({});
  const [quizzes, setQuizzes] = useState({});
  const [publishData, setPublishData] = useState({});

  // Initialize auto-save with current data
  useEffect(() => {
    autoSave.initialize({
      courseDetails,
      courseStructure: generatedStructure,
      courseScripts,
      mediaAttachments: media,
      quizData: quizzes,
      publishData,
      stepProgress: activeStep,
      completedSteps,
      generatedAudioFiles,
    });
  }, []);

  // Auto-save whenever course details change
  useEffect(() => {
    autoSave.updateCourseDetails(courseDetails);
  }, [courseDetails, autoSave]);

  // Auto-save whenever course structure changes
  useEffect(() => {
    if (generatedStructure) {
      autoSave.updateCourseStructure(generatedStructure);
    }
  }, [generatedStructure, autoSave]);

  // Auto-save whenever scripts change
  useEffect(() => {
    autoSave.updateCourseScripts(courseScripts);
  }, [courseScripts, autoSave]);

  // Migration effect: convert old flat structure to new nested structure
  useEffect(() => {
    setCourseScripts(prevScripts => {
      // Check if any value in prevScripts is a string (old flat structure)
      const hasOldStructure = Object.values(prevScripts).some(value => typeof value === 'string');
      
      if (hasOldStructure) {
        console.log('Detected old flat structure, resetting to new nested structure');
        // Reset to empty nested structure since the old data is incompatible
        return {};
      }
      
      return prevScripts;
    });
  }, []); // Run only once on mount

  // Auto-save whenever media attachments change
  useEffect(() => {
    try {
      if (media && Array.isArray(media)) {
        autoSave.updateMediaAttachments(media);
      }
    } catch (error) {
      console.warn('Failed to update media attachments:', error);
    }
  }, [media, autoSave]);

  // Auto-save whenever step progress changes
  useEffect(() => {
    try {
      if (typeof activeStep === 'number' && Array.isArray(completedSteps)) {
        autoSave.updateStepProgress(activeStep, completedSteps);
      }
    } catch (error) {
      console.warn('Failed to update step progress:', error);
    }
  }, [activeStep, completedSteps, autoSave]);

  // Auto-save whenever generated audio files change
  useEffect(() => {
    try {
      if (generatedAudioFiles && Array.isArray(generatedAudioFiles)) {
        autoSave.updateGeneratedAudioFiles(generatedAudioFiles);
      }
    } catch (error) {
      console.warn('Failed to update generated audio files:', error);
    }
  }, [generatedAudioFiles, autoSave]);
  
  // Voice creation handlers
  const prepareScriptsForVoice = () => {
    if (!generatedStructure) return [];
    
    const scripts: Array<{
      moduleId: string;
      lessonId: string;
      text: string;
      moduleTitle: string;
      lessonTitle: string;
    }> = [];

    generatedStructure.modules?.forEach(module => {
      module.lessons?.forEach(lesson => {
        const moduleScripts = courseScripts[module.id];
        if (moduleScripts && typeof moduleScripts === 'object') {
          const script = moduleScripts[lesson.id];
          if (typeof script === 'string' && script.trim()) {
            scripts.push({
              moduleId: module.id,
              lessonId: lesson.id,
              text: script.trim(),
              moduleTitle: module.title,
              lessonTitle: lesson.title
            });
          }
        }
      });
    });

    return scripts;
  };

  const handleCreateVoices = () => {
    const scripts = prepareScriptsForVoice();
    if (scripts.length === 0) {
      toast({
        title: "No Scripts Available",
        description: "Please generate some scripts first before creating voices.",
        variant: "destructive",
      });
      return;
    }

    setShowVoiceModal(true);
  };

  const handleVoiceGenerated = (results: any[]) => {
    console.log('Voice generation results:', results);
    setGeneratedAudioFiles(results);
    
    const successCount = results.filter(r => r.success).length;
    
    toast({
      title: "Voice Creation Complete!",
      description: `Successfully created ${successCount} audio files for your course.`,
    });
    
    // Auto-advance to media step to manage the generated audio files
    if (successCount > 0) {
      setActiveStep(3); // Media step
      setCompletedSteps(prev => [...new Set([...prev, 2])]);
    }
  };

  // Media attachment handler
  const handleMediaAttach = (moduleId: string, lessonId: string, mediaId: string) => {
    setMedia(prev => {
      const key = `${moduleId}-${lessonId}`;
      return {
        ...prev,
        [key]: [...(prev[key as keyof typeof prev] || []), mediaId]
      };
    });
  };

  // Mutations
  const generateStructureMutation = useMutation({
    mutationFn: async (data: any) => {
      const response = await apiRequest('POST', '/api/ai/generate-course-structure', data);
      if (!response.ok) {
        throw new Error('Failed to generate course structure');
      }
      return response.json();
    },
    onSuccess: (data) => {
      console.log('Generated structure received:', data);
      
      // Ensure modules and lessons have IDs for the UI components
      const structureWithIds = {
        ...data,
        modules: data.modules?.map((module: any, moduleIndex: number) => ({
          ...module,
          id: module.id || `module-${moduleIndex}`,
          lessons: module.lessons?.map((lesson: any, lessonIndex: number) => ({
            ...lesson,
            id: lesson.id || `lesson-${moduleIndex}-${lessonIndex}`
          })) || []
        })) || []
      };
      
      setGeneratedStructure(structureWithIds);
      setIsGenerating(false);
      setProgress(100);
      
      // Mark the courseDetails step as completed
      if (!completedSteps.includes(0)) {
        setCompletedSteps(prev => [...prev, 0]);
      }
      
      // Move to the content structure step
      setActiveStep(1);
      
      toast({
        title: "Course structure generated",
        description: "Your course structure has been created successfully.",
      });
    },
    onError: (error) => {
      console.error('Error generating course structure:', error);
      setIsGenerating(false);
      setProgress(0);
      
      toast({
        title: "Generation failed",
        description: "An error occurred while generating the course structure. Please try again.",
        variant: "destructive"
      });
    }
  });

  const balanceComplexityMutation = useMutation({
    mutationFn: async (data: any) => {
      const response = await apiRequest('POST', '/api/ai/balance-course-complexity', data);
      if (!response.ok) {
        throw new Error('Failed to balance course complexity');
      }
      return response.json();
    },
    onSuccess: (data) => {
      setGeneratedStructure(data);
      setIsBalancing(false);
      
      toast({
        title: "Course complexity balanced",
        description: "The complexity of your course modules has been balanced for optimal learning progression.",
      });
    },
    onError: (error) => {
      console.error('Error balancing complexity:', error);
      setIsBalancing(false);
      
      toast({
        title: "Balancing failed",
        description: "An error occurred while balancing the course complexity. Please try again.",
        variant: "destructive"
      });
    }
  });

  const generateScriptMutation = useMutation({
    mutationFn: async (data: any) => {
      const response = await apiRequest('POST', '/api/ai/generate-module-script', data);
      if (!response.ok) {
        throw new Error('Failed to generate script');
      }
      const result = await response.json();
      return result.script;
    }
  });

  const saveCourseMutation = useMutation({
    mutationFn: async (data: any) => {
      console.log('Attempting to save course:', data.title);
      try {
        const response = await apiRequest('POST', '/api/courses', data);
        if (!response.ok) {
          const errorText = await response.text();
          console.error('Save course error response:', errorText);
          throw new Error(`Failed to save course: ${response.status} ${errorText}`);
        }
        const result = await response.json();
        console.log('Course saved successfully:', result);
        return result;
      } catch (error) {
        console.error('Course save error:', error);
        throw error;
      }
    },
    onSuccess: (data) => {
      console.log('Course save success callback:', data);
      // Invalidate courses cache to refresh dashboard
      queryClient.invalidateQueries({ queryKey: ['/api/courses'] });
      queryClient.invalidateQueries({ queryKey: ['/api/user-stats'] });
      
      toast({
        title: "Course saved successfully!",
        description: `"${data.title}" is now available in My Courses.`,
      });
      
      // Store course ID for future reference
      if (data.id) {
        localStorage.setItem('lastSavedCourseId', data.id.toString());
      }
      
      // Never auto-navigate during course creation workflow
      // Navigation only happens when user explicitly clicks "Publish Course"
    },
    onError: (error) => {
      console.error('Error saving course:', error);
      toast({
        title: "Save failed",
        description: "Could not save the course. Please try again.",
        variant: "destructive"
      });
    }
  });

  // Navigation functions
  const handleContinue = () => {
    // Add the current step to completed steps if not already there
    if (!completedSteps.includes(activeStep)) {
      setCompletedSteps(prev => [...prev, activeStep]);
    }
    
    // Move to the next step
    setActiveStep(prev => Math.min(prev + 1, steps.length - 1));
  };

  const handleBack = () => {
    setActiveStep(prev => Math.max(prev - 1, 0));
  };

  const navigateToStep = (stepIndex: number) => {
    if (stepIndex <= Math.max(...completedSteps) + 1) {
      setActiveStep(stepIndex);
    }
  };
  
  // Handle script changes
  const handleScriptChange = (moduleId: string, lessonId: string, script: string) => {
    setCourseScripts(prev => ({
      ...prev,
      [moduleId]: {
        ...(typeof prev[moduleId] === 'object' ? prev[moduleId] : {}),
        [lessonId]: script
      }
    }));
  };

  // Generate script for a module
  const handleGenerateScript = async (moduleId: string) => {
    if (!generatedStructure) return '';
    
    const module = generatedStructure.modules.find(m => m.id === moduleId);
    if (!module) return '';
    
    const lessonTitles = module.lessons.map(lesson => lesson.title);
    
    try {
      const script = await generateScriptMutation.mutateAsync({
        moduleId,
        moduleTitle: module.title,
        moduleDescription: module.description,
        lessonTitles
      });
      
      return script;
    } catch (error) {
      console.error('Error generating script:', error);
      toast({
        title: "Script generation failed",
        description: "Could not generate the script. Please try again.",
        variant: "destructive"
      });
      
      return '';
    }
  };

  // Save scripts
  const handleSaveScripts = async () => {
    // Mark the scripts step as completed
    if (!completedSteps.includes(2)) {
      setCompletedSteps(prev => [...prev, 2]);
    }
    
    // Move to the next step
    setActiveStep(3);
    
    // In a real implementation, you would save the scripts to the server here
    return Promise.resolve();
  };

  // Manual save function that works at any stage
  const handleManualSave = async () => {
    if (!courseDetails.title) {
      toast({
        title: "Cannot save",
        description: "Please provide a course title before saving.",
        variant: "destructive"
      });
      return;
    }

    try {
      const courseData: any = {
        title: courseDetails.title,
        description: courseDetails.description || '',
        category: courseDetails.category || 'General',
        targetAudience: courseDetails.targetAudience || '',
        keyTopics: courseDetails.keyTopics || '',
        status: 'draft'
      };

      // Add modules and lessons if structure exists
      if (generatedStructure?.modules) {
        courseData.modules = generatedStructure.modules.map(module => ({
          id: module.id,
          title: module.title,
          description: module.description || '',
          lessons: module.lessons?.map(lesson => ({
            id: lesson.id,
            title: lesson.title,
            content: courseScripts[module.id]?.[lesson.id] || lesson.content || '',
            type: lesson.type || 'video',
            duration: lesson.duration || 0
          })) || []
        }));
      }

      console.log('Manual save attempt with data:', courseData);
      // Use direct API call instead of mutation to avoid navigation
      const response = await apiRequest('POST', '/api/courses', courseData);
      if (response.ok) {
        const result = await response.json();
        toast({
          title: "Draft saved",
          description: "Your course has been saved as a draft.",
        });
        return result;
      }
    } catch (error) {
      console.error('Manual save error:', error);
      toast({
        title: "Save failed",
        description: "Could not save the course draft.",
        variant: "destructive"
      });
    }
  };

  // Save and publish the course (final publish only)
  const handleSaveCourse = async () => {
    if (!generatedStructure) {
      await handleManualSave();
      return;
    }
    
    try {
      const response = await apiRequest('POST', '/api/courses', {
        title: courseDetails.title,
        description: courseDetails.description,
        category: courseDetails.category,
        targetAudience: courseDetails.targetAudience,
        keyTopics: courseDetails.keyTopics,
        status: 'published',
        modules: generatedStructure.modules.map(module => ({
          title: module.title,
          description: module.description,
          lessons: module.lessons.map(lesson => ({
            title: lesson.title,
            content: courseScripts[module.id]?.[lesson.id] || lesson.content || '',
            type: lesson.type || 'video',
            duration: lesson.duration || 0
          }))
        }))
      });
      
      if (response.ok) {
        const result = await response.json();
        
        // Invalidate cache
        queryClient.invalidateQueries({ queryKey: ['/api/courses'] });
        queryClient.invalidateQueries({ queryKey: ['/api/user-stats'] });
        
        toast({
          title: "Course published!",
          description: "Your course has been successfully published.",
        });
        
        // Navigate to my courses after successful publish
        navigate('/my-courses');
      } else {
        throw new Error('Failed to publish course');
      }
    } catch (error) {
      console.error('Error publishing course:', error);
      toast({
        title: "Publish failed",
        description: "Could not publish the course. Please try again.",
        variant: "destructive"
      });
    }
  };
  
  // Handle course details change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setCourseDetails(prev => ({
      ...prev,
      [name]: value
    }));
  };
  
  // Handle category change
  const handleCategoryChange = (value: string) => {
    setCourseDetails(prev => ({
      ...prev,
      category: value
    }));
  };
  
  // Handle module count change
  const handleModuleCountChange = (values: number[]) => {
    setModuleCount(values[0]);
  };
  
  // Generate course structure
  const handleGenerateStructure = () => {
    if (!courseDetails.title || !courseDetails.description) {
      toast({
        title: "Missing information",
        description: "Please provide a title and description for your course.",
        variant: "destructive"
      });
      return;
    }
    
    setIsGenerating(true);
    setProgress(0);
    
    // Simulate progress while generating
    const interval = setInterval(() => {
      setProgress(prev => {
        if (prev >= 95) {
          clearInterval(interval);
          return prev;
        }
        return prev + 5;
      });
    }, 300);
    
    generateStructureMutation.mutate({
      title: courseDetails.title,
      description: courseDetails.description,
      category: courseDetails.category,
      targetAudience: courseDetails.targetAudience,
      moduleCount,
      format: 'traditional'
    });
  };
  
  // Balance module complexity
  const handleBalanceComplexity = () => {
    if (!generatedStructure) return;
    
    setIsBalancing(true);
    
    balanceComplexityMutation.mutate({
      modules: generatedStructure.modules,
      courseTitle: courseDetails.title,
      courseDescription: courseDetails.description
    });
  };
  
  // Generate course description using AI
  const handleGenerateDescription = async () => {
    if (!courseDetails.title) {
      toast({
        title: "Missing title",
        description: "Please provide a course title first.",
        variant: "destructive"
      });
      return;
    }
    
    setIsGeneratingDescription(true);
    
    try {
      const response = await apiRequest('POST', '/api/ai/generate-description', {
        title: courseDetails.title,
        category: courseDetails.category
      });
      
      if (!response.ok) {
        throw new Error('Failed to generate description');
      }
      
      const result = await response.json();
      
      setCourseDetails(prev => ({
        ...prev,
        description: result.description
      }));
      
      toast({
        title: "Description generated",
        description: "Your course description has been created. Feel free to edit it.",
      });
    } catch (error) {
      console.error('Error generating description:', error);
      toast({
        title: "Generation failed",
        description: "Could not generate the description. Please try again or write your own.",
        variant: "destructive"
      });
    } finally {
      setIsGeneratingDescription(false);
    }
  };
  
  // Generate target audience using AI
  const handleGenerateAudience = async () => {
    if (!courseDetails.title) {
      toast({
        title: "Missing title",
        description: "Please provide a course title first.",
        variant: "destructive"
      });
      return;
    }
    
    setIsGeneratingAudience(true);
    
    try {
      const response = await apiRequest('POST', '/api/ai/generate-audience', {
        title: courseDetails.title,
        category: courseDetails.category,
        description: courseDetails.description
      });
      
      if (!response.ok) {
        throw new Error('Failed to generate target audience');
      }
      
      const result = await response.json();
      
      setCourseDetails(prev => ({
        ...prev,
        targetAudience: result.targetAudience
      }));
      
      toast({
        title: "Target audience generated",
        description: "Your target audience has been created. Feel free to edit it.",
      });
    } catch (error) {
      console.error('Error generating audience:', error);
      toast({
        title: "Generation failed",
        description: "Could not generate the target audience. Please try again or write your own.",
        variant: "destructive"
      });
    } finally {
      setIsGeneratingAudience(false);
    }
  };
  
  // Generate key topics using AI
  const handleGenerateTopics = async () => {
    if (!courseDetails.title) {
      toast({
        title: "Missing title",
        description: "Please provide a course title first.",
        variant: "destructive"
      });
      return;
    }
    
    setIsGeneratingTopics(true);
    
    try {
      const response = await apiRequest('POST', '/api/ai/generate-topics', {
        title: courseDetails.title,
        category: courseDetails.category,
        description: courseDetails.description,
        targetAudience: courseDetails.targetAudience
      });
      
      if (!response.ok) {
        throw new Error('Failed to generate key topics');
      }
      
      const result = await response.json();
      
      setCourseDetails(prev => ({
        ...prev,
        keyTopics: result.topics
      }));
      
      toast({
        title: "Key topics generated",
        description: "Your key topics have been created. Feel free to edit them.",
      });
    } catch (error) {
      console.error('Error generating topics:', error);
      toast({
        title: "Generation failed",
        description: "Could not generate the key topics. Please try again or write your own.",
        variant: "destructive"
      });
    } finally {
      setIsGeneratingTopics(false);
    }
  };

  // Auto-save course when structure is generated
  useEffect(() => {
    if (generatedStructure && courseDetails.title && !saveCourseMutation.isPending) {
      const autoSaveCourse = async () => {
        try {
          const courseData = {
            title: courseDetails.title,
            description: courseDetails.description || '',
            category: courseDetails.category || 'General',
            targetAudience: courseDetails.targetAudience || '',
            keyTopics: courseDetails.keyTopics || '',
            modules: generatedStructure.modules || [],
            scripts: courseScripts,
            status: 'draft' as const,
            estimatedDuration: generatedStructure.modules?.reduce((total, module) => 
              total + (module.lessons?.length || 0) * 10, 0) || 0
          };

          await saveCourseMutation.mutateAsync(courseData);
        } catch (error) {
          console.error('Auto-save failed:', error);
        }
      };

      // Debounce auto-save by 3 seconds
      const timeoutId = setTimeout(autoSaveCourse, 3000);
      return () => clearTimeout(timeoutId);
    }
  }, [generatedStructure, courseDetails, courseScripts]);

  // Save course when completing final step
  const handlePublish = async () => {
    if (!generatedStructure || !courseDetails.title) {
      toast({
        title: "Missing information",
        description: "Please complete all required fields before publishing.",
        variant: "destructive"
      });
      return;
    }

    try {
      const courseData = {
        title: courseDetails.title,
        description: courseDetails.description || '',
        category: courseDetails.category || 'General',
        targetAudience: courseDetails.targetAudience || '',
        keyTopics: courseDetails.keyTopics || '',
        modules: generatedStructure.modules || [],
        scripts: courseScripts,
        status: 'published' as const,
        estimatedDuration: generatedStructure.modules?.reduce((total, module) => 
          total + (module.lessons?.length || 0) * 10, 0) || 0
      };

      await saveCourseMutation.mutateAsync(courseData);
    } catch (error) {
      console.error('Publishing failed:', error);
    }
  };

  return (
    <div className="px-6 py-8">
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8">
        <div>
          <div className="flex items-center gap-2 mb-1">
            <Button 
              variant="ghost" 
              size="icon" 
              onClick={() => navigate('/create')}
              className="h-8 w-8 rounded-full"
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <h1 className="text-3xl font-bold tracking-tight text-foreground">Create Traditional Course</h1>
          </div>
          <p className="text-muted-foreground">
            Build a comprehensive course with structured content, multimedia, and assessments
          </p>
        </div>
        
        {/* Manual Save Button */}
        <div className="flex items-center gap-3 mt-4 lg:mt-0">
          {saveCourseMutation.isPending && (
            <div className="text-sm text-muted-foreground flex items-center gap-2">
              <div className="h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
              Saving...
            </div>
          )}
          <Button
            onClick={handleManualSave}
            disabled={!courseDetails.title || saveCourseMutation.isPending}
            className="bg-primary hover:bg-primary/90 text-primary-foreground"
          >
            <Save className="h-4 w-4 mr-2" />
            Save Course
          </Button>
        </div>
      </div>

      <div className="mb-8">
        <HorizontalStepIndicator 
          steps={steps} 
          activeStep={activeStep}
          completedSteps={completedSteps}
          onStepClick={navigateToStep}
        />
      </div>



      <div className="bg-white rounded-lg shadow-sm border p-6">
        {/* Step 1: Course Details */}
        {activeStep === 0 && (
          <div className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2">
              <div>
                <Label htmlFor="title">Course Title</Label>
                <Input 
                  id="title" 
                  name="title" 
                  value={courseDetails.title} 
                  onChange={handleInputChange} 
                  className="mt-1" 
                  placeholder="e.g. Introduction to Machine Learning"
                />
              </div>
              <div>
                <Label htmlFor="category">Category</Label>
                <Select value={courseDetails.category} onValueChange={handleCategoryChange}>
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder="Select a category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="technology">Technology & Programming</SelectItem>
                    <SelectItem value="business">Business & Entrepreneurship</SelectItem>
                    <SelectItem value="design">Design & Creative Arts</SelectItem>
                    <SelectItem value="marketing">Marketing & Sales</SelectItem>
                    <SelectItem value="personal-development">Personal Development</SelectItem>
                    <SelectItem value="finance">Finance & Investing</SelectItem>
                    <SelectItem value="health">Health & Wellness</SelectItem>
                    <SelectItem value="education">Education & Teaching</SelectItem>
                    <SelectItem value="language">Language Learning</SelectItem>
                    <SelectItem value="lifestyle">Lifestyle & Hobbies</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div>
              <div className="flex justify-between items-center">
                <Label htmlFor="description">Course Description</Label>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  type="button" 
                  onClick={handleGenerateDescription}
                  className="h-8 text-xs flex items-center gap-1"
                  disabled={!courseDetails.title}
                >
                  <Sparkles className="h-3 w-3" />
                  Generate with AI
                </Button>
              </div>
              <Textarea 
                id="description" 
                name="description" 
                value={courseDetails.description} 
                onChange={handleInputChange} 
                className="mt-1 h-24" 
                placeholder="Describe your course in a few sentences..."
              />
              {isGeneratingDescription && (
                <div className="mt-2 text-xs text-muted-foreground flex items-center gap-1">
                  <span className="animate-spin h-3 w-3">⏳</span> Creating description...
                </div>
              )}
            </div>
            <div>
              <div className="flex justify-between items-center">
                <Label htmlFor="targetAudience">Target Audience</Label>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  type="button" 
                  onClick={handleGenerateAudience}
                  className="h-8 text-xs flex items-center gap-1"
                  disabled={!courseDetails.title || isGeneratingAudience}
                >
                  <Sparkles className="h-3 w-3" />
                  Generate with AI
                </Button>
              </div>
              <Textarea 
                id="targetAudience" 
                name="targetAudience" 
                value={courseDetails.targetAudience || ''} 
                onChange={handleInputChange} 
                className="mt-1 h-20" 
                placeholder="Who is this course for?"
              />
              {isGeneratingAudience && (
                <div className="mt-2 text-xs text-muted-foreground flex items-center gap-1">
                  <span className="animate-spin h-3 w-3">⏳</span> Creating target audience...
                </div>
              )}
            </div>
            <div>
              <div className="flex justify-between items-center">
                <Label htmlFor="keyTopics">Key Topics (optional)</Label>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  type="button" 
                  onClick={handleGenerateTopics}
                  className="h-8 text-xs flex items-center gap-1"
                  disabled={!courseDetails.title || isGeneratingTopics}
                >
                  <Sparkles className="h-3 w-3" />
                  Generate with AI
                </Button>
              </div>
              <Textarea 
                id="keyTopics" 
                name="keyTopics" 
                value={courseDetails.keyTopics || ''} 
                onChange={handleInputChange} 
                className="mt-1 h-20" 
                placeholder="What are the main topics covered?"
              />
              {isGeneratingTopics && (
                <div className="mt-2 text-xs text-muted-foreground flex items-center gap-1">
                  <span className="animate-spin h-3 w-3">⏳</span> Creating key topics...
                </div>
              )}
            </div>
            <div>
              <Label className="mb-2 block">Number of Modules ({moduleCount})</Label>
              <Slider 
                value={[moduleCount]}
                min={3}
                max={10}
                step={1}
                onValueChange={handleModuleCountChange}
                className="w-full max-w-xs"
              />
            </div>
            <div className="flex justify-end gap-4">
              <Button
                onClick={handleGenerateStructure}
                disabled={isGenerating}
                className="flex gap-2"
              >
                {isGenerating ? (
                  <>Generating <span className="animate-pulse">...</span></>
                ) : (
                  <>Generate Course <Sparkles className="h-4 w-4" /></>
                )}
              </Button>
            </div>
            {isGenerating && (
              <div className="mt-4">
                <Label className="text-sm text-muted-foreground mb-2 block">Generating course structure...</Label>
                <Progress value={progress} className="h-2" />
              </div>
            )}
          </div>
        )}

        {/* Step 2: Course Structure */}
        {activeStep === 1 && generatedStructure && generatedStructure.modules && (
          <div className="space-y-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold">Course Structure</h2>
            </div>
            
            <div className="grid gap-4">
              {generatedStructure.modules.map((module, index) => (
                <Card key={module.id || `module-${index}`} className="p-4 border transition-colors hover:border-primary/50">
                  <div className="flex items-center gap-3 mb-2">
                    <Badge variant="outline" className="h-6 w-6 rounded-full p-0 flex items-center justify-center font-medium">
                      {index + 1}
                    </Badge>
                    <h3 className="font-medium text-lg">{module.title}</h3>
                    {module.complexity && (
                      <Badge className="ml-auto" variant={
                        module.complexity === 'beginner' ? 'default' :
                        module.complexity === 'intermediate' ? 'secondary' : 'destructive'
                      }>
                        {module.complexity}
                      </Badge>
                    )}
                  </div>
                  {module.description && (
                    <p className="text-muted-foreground text-sm ml-9 mb-2">{module.description}</p>
                  )}
                  <div className="ml-9 space-y-1 mt-3">
                    {module.lessons && module.lessons.map((lesson, idx) => (
                      <div key={lesson.id || `lesson-${index}-${idx}`} className="text-sm flex items-center gap-2">
                        <span className="text-xs text-muted-foreground">{idx + 1}.</span>
                        <span>{lesson.title}</span>
                      </div>
                    ))}
                  </div>
                </Card>
              ))}
            </div>
            <div className="flex justify-between">
              <Button variant="outline" onClick={handleBack}>
                Back
              </Button>
              <Button onClick={handleContinue}>
                Continue to Scripts
              </Button>
            </div>
          </div>
        )}

        {/* Step 2: No Structure Generated */}
        {activeStep === 1 && !generatedStructure && (
          <div className="space-y-6">
            <div className="text-center py-8">
              <h2 className="text-xl font-semibold mb-2">No Course Structure</h2>
              <p className="text-muted-foreground mb-4">
                Please go back to Step 1 and generate a course structure first.
              </p>
              <Button variant="outline" onClick={handleBack}>
                Back to Course Details
              </Button>
            </div>
          </div>
        )}

        {/* Step 3: Module Scripts */}
        {activeStep === 2 && generatedStructure && (
          <div className="space-y-6">
            <ModuleScriptEditor
              modules={generatedStructure.modules || []}
              courseScripts={courseScripts}
              onScriptChange={handleScriptChange}
              onGenerateScript={handleGenerateScript}
              onSave={handleSaveScripts}
              onBack={handleBack}
              courseTitle={generatedStructure?.title || "Untitled Course"}
              courseDescription={generatedStructure?.description || ""}
            />
            
            {/* Voice Generation Panel */}
            <div className="border-t pt-6">
              <Button
                onClick={handleCreateVoices}
                className="w-full"
                size="lg"
                disabled={Object.keys(courseScripts).length === 0}
              >
                <Volume2 className="h-4 w-4 mr-2" />
                Create Voice Narration
              </Button>
            </div>
          </div>
        )}

        {/* Step 4: Quizzes */}
        {activeStep === 3 && (
          <div className="space-y-6">
            <AutoQuizGenerator
              modules={generatedStructure?.modules || []}
              courseScripts={courseScripts}
              onQuizzesGenerated={(generatedQuizzes) => {
                // Store generated quizzes in the component state
                const newQuizzes = generatedQuizzes.reduce((acc, quiz) => {
                  acc[quiz.id] = quiz;
                  return acc;
                }, {} as any);
                setQuizzes(prev => ({ ...prev, ...newQuizzes }));
                
                toast({
                  title: "Auto-Generated Quizzes Complete",
                  description: `Successfully created ${generatedQuizzes.length} quizzes for your course modules`,
                });
              }}
            />
            
            <div className="flex justify-between mt-8 pt-6 border-t">
              <Button variant="outline" onClick={handleBack}>
                Back to Scripts
              </Button>
              <Button onClick={handleContinue}>
                Continue to Video Production
                {Object.keys(quizzes).length > 0 && (
                  <span className="ml-2 text-xs bg-green-500 text-white px-2 py-1 rounded-full">
                    {Object.keys(quizzes).length} quiz{Object.keys(quizzes).length !== 1 ? 'es' : ''} ready
                  </span>
                )}
              </Button>
            </div>
          </div>
        )}

        {/* Step 5: Enhanced Video Production */}
        {activeStep === 4 && (
          <EnhancedVideoProductionStudio
            modules={generatedStructure?.modules || []}
            scripts={courseScripts}
            voices={[]}
            onBack={handleBack}
            onNext={handleContinue}
          />
        )}

        {/* Step 6: Publish */}
        {activeStep === 5 && (
          <div className="space-y-6">
            <h2 className="text-xl font-semibold">Review and Publish</h2>
            <p className="text-muted-foreground">Review your course details before publishing.</p>
            
            <div className="space-y-4 mt-6">
              <div>
                <h3 className="font-medium">Course Title</h3>
                <p>{courseDetails.title}</p>
              </div>
              <div>
                <h3 className="font-medium">Description</h3>
                <p>{courseDetails.description}</p>
              </div>
              <div>
                <h3 className="font-medium">Modules</h3>
                <p>{generatedStructure?.modules.length || 0} modules created</p>
              </div>
            </div>
            
            <div className="flex justify-between">
              <Button variant="outline" onClick={handleBack}>
                Back
              </Button>
              <div className="space-x-4">
                <Button variant="outline" onClick={handleManualSave}>
                  Save as Draft
                </Button>
                <Button onClick={handleSaveCourse}>
                  Publish Course
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Voice Generation Modal */}
        <VoiceGenerationModal
          isOpen={showVoiceModal}
          onClose={() => setShowVoiceModal(false)}
          modules={generatedStructure?.modules || []}
          scripts={courseScripts}
          onVoiceGenerated={handleVoiceGenerated}
        />


      </div>
    </div>
  );
}