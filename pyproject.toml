[project]
name = "repl-nix-workspace"
version = "0.1.0"
description = "Add your description here"
requires-python = ">=3.11"
dependencies = [
    "tts>=0.22.0",
    "modal",
]

[[tool.uv.index]]
explicit = true
name = "pytorch-cpu"
url = "https://download.pytorch.org/whl/cpu"

[tool.uv.sources]
AA-module = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
ABlooper = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
AnalysisG = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
AutoRAG = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
BERTeam = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
BxTorch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
Byaldi = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
CALM-Pytorch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
COPEX-high-rate-compression-quality-metrics = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
CityLearn = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
CoCa-pytorch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
CoLT5-attention = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
ComfyUI-EasyNodes = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
Crawl4AI = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
DALL-E = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
DI-toolkit = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
DatasetRising = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
DeepCache = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
DeepMatter = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
Draugr = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
ESRNN = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
En-transformer = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
ExpoSeq = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
FLAML = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
FSRS-Optimizer = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
GANDLF = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
GQLAlchemy = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
GhostScan = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
GraKeL = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
HEBO = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
IOPaint = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
ISLP = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
InvokeAI = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
JAEN = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
KapoorLabs-Lightning = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
LightAutoML = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
LingerGRN = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
MMEdu = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
MRzeroCore = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
Modeva = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
NeuralFoil = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
NiMARE = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
NinjaTools = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
OpenHosta = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
OpenNMT-py = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
POT = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
PVNet = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
PaLM-rlhf-pytorch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
PepperPepper = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
PiML = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
Poutyne = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
QNCP = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
RAGatouille = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
RareGO = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
RealtimeSTT = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
RelevanceAI-Workflows-Core = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
Resemblyzer = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
ScandEval = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
Simba-UW-tf-dev = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
SwissArmyTransformer = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
TPOT = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
TTS = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
TorchCRF = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
TotalSegmentator = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
UtilsRL = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
WhisperSpeech = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
XAISuite = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
a-unet = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
a5dev = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
accelerate = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
accelerated-scan = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
accern-xyme = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
achatbot = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
acids-rave = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
actorch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
acvl-utils = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
adabelief-pytorch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
adam-atan2-pytorch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
adan-pytorch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
adapters = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
admin-torch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
adtoolbox = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
adversarial-robustness-toolbox = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
aeiou = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
aeon = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
africanwhisper = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
ag-llama-api = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
agentdojo = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
agilerl = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
ai-edge-torch-nightly = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
ai-parrot = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
ai-python = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
ai-transform = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
ai2-olmo = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
ai2-olmo-core = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
ai2-tango = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
aicmder = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
aider-chat = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
aider-chat-x = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
aif360 = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
aihwkit = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
aimodelshare = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
airllm = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
airtestProject = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
airunner = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
aisak = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
aislib = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
aisquared = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
aistore = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
aithree = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
akasha-terminal = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
alibi = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
alibi-detect = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
alignn = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
all-clip = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
allennlp = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
allennlp-models = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
allennlp-pvt-nightly = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
allophant = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
allosaurus = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
aloy = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
alpaca-eval = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
alphafold2-pytorch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
alphafold3-pytorch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
alphamed-federated = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
alphawave = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
amazon-braket-pennylane-plugin = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
amazon-photos = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
anemoi-graphs = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
anemoi-models = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
anomalib = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
apache-beam = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
apache-tvm = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
aperturedb = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
aphrodite-engine = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
aqlm = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
arcAGI2024 = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
archisound = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
argbind = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
arize = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
arm-pytorch-utilities = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
array-api-compat = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
arus = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
assert-llm-tools = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
asteroid = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
asteroid-filterbanks = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
astra-llm = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
astrovision = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
atomate2 = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
attacut = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
audio-diffusion-pytorch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
audio-encoders-pytorch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
audio-separator = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
audiocraft = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
audiolm-pytorch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
auralis = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
auraloss = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
auto-gptq = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
autoawq = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
autoawq-kernels = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
"autogluon.multimodal" = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
"autogluon.tabular" = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
"autogluon.timeseries" = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
autotrain-advanced = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
avdeepfake1m = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
aws-fortuna = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
ax-platform = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
azureml-automl-dnn-vision = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
azureml-contrib-automl-dnn-forecasting = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
azureml-evaluate-mlflow = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
azureml-metrics = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
azureml-train-automl = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
b2bTools = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
backpack-for-pytorch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
balrog-nle = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
batch-face = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
batchalign = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
batchgeneratorsv2 = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
batchtensor = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
bbrl = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
benchpots = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
bent = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
bert-score = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
bertopic = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
bertviz = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
bestOf = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
betty-ml = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
big-sleep = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
bigdl-core-cpp = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
bigdl-core-npu = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
bigdl-llm = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
bigdl-nano = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
"bioimageio.core" = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
bitfount = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
bitsandbytes = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
bittensor = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
bittensor-cli = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
blackboxopt = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
blanc = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
blindai = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
bm25-pt = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
boltz = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
botorch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
boxmot = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
brainchain = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
braindecode = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
brevitas = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
briton = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
browsergym-visualwebarena = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
buzz-captions = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
byotrack = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
byzerllm = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
c4v-py = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
calflops = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
came-pytorch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
camel-ai = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
camel-tools = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
cannai = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
captum = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
carte-ai = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
carvekit-colab = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
catalyst = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
causalml = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
causalnex = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
causy = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
cbrkit = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
cca-zoo = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
cdp-backend = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
cellacdc = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
cellfinder = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
cellpose = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
cellxgene-census = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
chattts = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
chemprop = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
chgnet = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
chitra = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
circuitsvis = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
cjm-yolox-pytorch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
clarinpl-embeddings = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
class-resolver = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
classifier-free-guidance-pytorch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
classiq = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
classy-core = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
clean-fid = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
cleanvision = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
clip-anytorch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
clip-benchmark = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
clip-by-openai = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
clip-interrogator = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
clip-retrieval = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
cltk = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
clu = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
clusterops = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
cnocr = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
cnstd = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
coba = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
cofi = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
colbert-ai = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
colpali-engine = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
compel = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
composabl-ray = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
composabl-ray-dev = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
composabl-train = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
composabl-train-dev = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
composer = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
compressai = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
compressed-tensors = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
compressed-tensors-nightly = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
concrete-python = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
confit = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
conformer = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
contextualSpellCheck = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
continual-inference = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
controlnet-aux = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
convokit = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
coola = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
coqui-tts = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
coqui-tts-trainer = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
craft-text-detector = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
creme = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
crocodile = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
crowd-kit = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
cryoSPHERE = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
csle-common = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
csle-system-identification = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
ctgan = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
curated-transformers = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
cut-cross-entropy = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
cvat-sdk = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
cybertask = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
d3rlpy = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
dalle-pytorch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
dalle2-pytorch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
danila-lib = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
danling = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
darts = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
darwin-py = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
data-gradients = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
datachain = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
dataclass-array = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
dataeval = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
datarobot-drum = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
datarobotx = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
datasets = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
datumaro = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
dctorch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
deep-utils = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
deepchecks = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
deepchem = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
deepctr-torch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
deepecho = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
deepepochs = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
deepforest = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
deeplabcut = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
deepmd-kit = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
deepmultilingualpunctuation = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
deepparse = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
deeprobust = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
deepsparse = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
deepsparse-nightly = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
deepspeed = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
denoising-diffusion-pytorch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
descript-audio-codec = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
descript-audiotools = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
detecto = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
detoxify = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
dgenerate = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
dghs-imgutils = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
dgl = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
dialogy = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
dice-ml = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
diffgram = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
diffq = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
diffusers = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
distilabel = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
distrifuser = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
dnikit = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
docarray = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
doclayout-yolo = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
docling-ibm-models = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
docquery = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
domino-code-assist = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
dreamsim = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
dropblock = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
druida = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
dvclive = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
e2-tts-pytorch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
e2cnn = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
e3nn = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
easyocr = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
ebtorch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
ecallisto-ng = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
edsnlp = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
effdet = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
einx = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
eir-dl = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
eis1600 = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
eland = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
ema-pytorch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
embedchain = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
enformer-pytorch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
entmax = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
esm = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
espaloma-charge = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
espnet = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
etils = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
etna = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
evadb = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
evalscope = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
evaluate = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
exllamav2 = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
extractable = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
face-alignment = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
facenet-pytorch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
facexlib = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
fair-esm = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
fairseq = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
fairseq2 = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
fairseq2n = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
faker-file = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
farm = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
fast-bert = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
fast-pytorch-kmeans = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
fastai = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
fastcore = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
fastestimator-nightly = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
fasttreeshap = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
fedml = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
felupe = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
femr = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
fer = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
fft-conv-pytorch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
fickling = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
fireworks-ai = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
flair = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
flashrag-dev = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
flax = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
flexgen = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
flgo = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
flopth = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
flowcept = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
flytekitplugins-kfpytorch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
flytekitplugins-onnxpytorch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
fmbench = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
focal-frequency-loss = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
foldedtensor = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
fractal-tasks-core = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
freegenius = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
freqtrade = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
fschat = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
funasr = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
functorch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
funlbm = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
funsor = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
galore-torch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
garak = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
garf = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
gateloop-transformer = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
geffnet = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
genutility = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
gfpgan = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
gigagan-pytorch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
gin-config = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
glasflow = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
gliner = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
gluonts = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
gmft = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
google-cloud-aiplatform = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
gpforecaster = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
gpt3discord = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
gpytorch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
grad-cam = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
graph-weather = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
graphistry = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
gravitorch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
gretel-synthetics = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
gsplat = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
guardrails-ai = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
guidance = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
gymnasium = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
hanlp = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
happytransformer = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
hbutils = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
heavyball = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
hezar = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
hf-deepali = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
hf-doc-builder = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
higher = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
hjxdl = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
hkkang-utils = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
hordelib = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
hpsv2 = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
huggingface-hub = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
hummingbird-ml = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
hvae-backbone = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
hya = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
hypothesis-torch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
ibm-metrics-plugin = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
ibm-watson-machine-learning = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
ibm-watsonx-ai = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
icetk = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
icevision = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
iden = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
idvpackage = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
iglovikov-helper-functions = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
imagededup = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
imagen-pytorch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
imaginAIry = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
img2vec-pytorch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
incendio = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
inference = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
inference-gpu = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
infinity-emb = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
info-nce-pytorch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
infoapps-mlops-sdk = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
instructlab = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
instructlab-dolomite = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
instructlab-eval = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
instructlab-sdg = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
instructlab-training = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
invisible-watermark = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
iobm = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
ipex-llm = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
iree-turbine = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
irisml = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
irisml-tasks-azure-openai = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
irisml-tasks-torchvision = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
irisml-tasks-training = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
item-matching = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
ivadomed = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
jaqpotpy = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
jina = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
judo = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
junky = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
k-diffusion = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
k1lib = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
k2 = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
kappadata = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
kappamodules = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
karbonn = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
kats = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
kbnf = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
kedro-datasets = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
keybert = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
keytotext = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
khoj = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
kiui = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
konfuzio-sdk = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
kornia = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
kornia-moons = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
kraken = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
kwarray = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
kwimage = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
labml-nn = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
lagent = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
laion-clap = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
lale = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
lama-cleaner = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
lancedb = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
langcheck = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
langkit = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
langroid = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
langtest = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
layoutparser = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
ldp = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
leafmap = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
leap-ie = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
leibniz = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
leptonai = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
letmedoit = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
lhotse = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
lib310 = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
libpecos = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
librec-auto = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
libretranslate = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
liger-kernel = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
liger-kernel-nightly = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
lightly = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
lightning = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
lightning-bolts = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
lightning-fabric = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
lightning-habana = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
lightning-lite = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
lightrag = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
lightweight-gan = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
lightwood = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
linear-attention-transformer = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
linear-operator = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
linformer = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
linformer-pytorch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
liom-toolkit = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
lion-pytorch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
lit-nlp = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
litdata = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
litelama = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
litgpt = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
llama-index-embeddings-adapter = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
llama-index-embeddings-clip = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
llama-index-embeddings-instructor = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
llama-index-llms-huggingface = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
llama-index-postprocessor-colbert-rerank = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
llm-blender = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
llm-foundry = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
llm-guard = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
llm-rs = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
llm2vec = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
llmcompressor = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
llmlingua = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
llmvm-cli = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
lm-eval = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
lmdeploy = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
lmms-eval = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
local-attention = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
lovely-tensors = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
lpips = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
lycoris-lora = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
mace-torch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
magic-pdf = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
magicsoup = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
magvit2-pytorch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
maite = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
manga-ocr = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
manifest-ml = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
manipulation = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
marker-pdf = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
matgl = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
med-imagetools = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
medaka = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
medcat = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
medmnist = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
megablocks = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
megatron-energon = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
memos = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
meshgpt-pytorch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
metatensor-torch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
mflux = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
mia-vgg = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
miditok = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
minari = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
minicons = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
ml2rt = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
mlagents = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
mlbench-core = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
mlcroissant = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
mlpfile = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
mlx = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
mlx-whisper = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
mmaction2 = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
mmengine = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
mmengine-lite = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
mmocr = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
mmpose = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
mmsegmentation = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
modeci-mdf = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
model2vec = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
modelscope = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
modelspec = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
monai = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
monai-weekly = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
monotonic-alignment-search = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
monty = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
mosaicml = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
mosaicml-streaming = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
moshi = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
mteb = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
mtmtrain = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
multi-quantization = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
myhand = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
nGPT-pytorch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
naeural-core = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
napari = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
napatrackmater = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
nara-wpe = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
natten = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
nbeats-pytorch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
nebulae = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
nemo-toolkit = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
neptune = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
neptune-client = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
nerfacc = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
nerfstudio = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
nessai = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
netcal = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
neural-rag = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
neuralforecast = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
neuralnets = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
neuralprophet = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
neuspell = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
nevergrad = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
nexfort = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
nimblephysics = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
nirtorch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
nkululeko = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
nlp = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
nlptooltest = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
nnAudio = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
nnodely = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
nnsight = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
nnunetv2 = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
noisereduce = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
nonebot-plugin-nailongremove = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
nowcasting-dataloader = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
nowcasting-forecast = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
nshtrainer = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
nuwa-pytorch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
nvflare = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
nvidia-modelopt = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
ocf-datapipes = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
ocnn = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
ogb = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
ohmeow-blurr = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
olive-ai = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
omlt = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
ommlx = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
onediff = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
onediffx = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
onnx2pytorch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
onnx2torch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
opacus = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
open-clip-torch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
open-flamingo = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
open-interpreter = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
openbb-terminal-nightly = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
openmim = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
openparse = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
openunmix = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
openvino-dev = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
openvino-tokenizers = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
openvino-xai = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
openwakeword = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
opt-einsum-fx = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
optimum = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
optimum-habana = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
optimum-intel = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
optimum-neuron = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
optimum-quanto = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
optree = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
optuna = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
optuna-dashboard = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
optuna-integration = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
oracle-ads = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
orbit-ml = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
otx = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
outetts = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
outlines = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
outlines-core = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
paddlenlp = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
pai-easycv = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
pandasai = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
panns-inference = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
patchwork-cli = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
peft = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
pegasuspy = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
pelutils = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
penn = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
perforatedai-freemium = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
performer-pytorch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
petastorm = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
pfio = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
pgmpy = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
phenolrs = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
phobos = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
pi-zero-pytorch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
pinecone-text = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
piq = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
pix2tex = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
pix2text = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
pnnx = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
policyengine-us-data = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
polyfuzz = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
pomegranate = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
positional-encodings = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
prefigure = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
product-key-memory = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
ptflops = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
ptwt = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
pulser-core = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
punctuators = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
py2ls = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
pyabsa = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
"pyannote.audio" = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
pyawd = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
pyclarity = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
pycox = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
pyfemtet = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
pyg-nightly = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
pygrinder = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
pyhealth = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
pyhf = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
pyiqa = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
pykeen = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
pykeops = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
pylance = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
pylineaGT = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
pymanopt = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
pymde = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
pypots = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
pyqlib = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
pyqtorch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
pyro-ppl = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
pysentimiento = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
pyserini = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
pysr = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
pythainlp = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
python-doctr = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
pytorch-fid = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
pytorch-forecasting = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
pytorch-ignite = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
pytorch-kinematics = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
pytorch-lightning = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
pytorch-lightning-bolts = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
pytorch-metric-learning = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
pytorch-model-summary = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
pytorch-msssim = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
pytorch-pfn-extras = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
pytorch-pretrained-bert = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
pytorch-ranger = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
pytorch-seed = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
pytorch-tabnet = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
pytorch-tabular = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
pytorch-toolbelt = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
pytorch-transformers = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
pytorch-transformers-pvt-nightly = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
pytorch-triton-rocm = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
pytorch-warmup = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
pytorch-wavelets = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
pytorch_optimizer = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
pytorch_revgrad = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
pytorchcv = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
pytorchltr2 = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
pyvene = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
pyvespa = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
qianfan = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
qibo = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
qiskit-machine-learning = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
qtorch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
quanto = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
quick-anomaly-detector = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
rastervision = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
rastervision-pytorch-backend = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
rastervision-pytorch-learner = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
ray-lightning = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
rclip = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
realesrgan = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
recbole = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
recommenders = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
redcat = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
reformer-pytorch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
regex-sampler = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
replay-rec = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
rerankers = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
research-framework = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
resemble-enhance = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
resnest = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
rf-clip = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
rf-groundingdino = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
rfconv = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
rich-logger = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
ring-attention-pytorch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
rltrade-test = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
rotary-embedding-torch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
rsp-ml = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
rust-circuit = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
s2fft = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
s3prl = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
s3torchconnector = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
saferx = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
safetensors = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
sagemaker-huggingface-inference-toolkit = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
sagemaker-ssh-helper = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
salesforce-lavis = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
salesforce-merlion = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
samv2 = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
scib = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
scib-metrics = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
scvi-tools = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
sdmetrics = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
secretflow = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
segment-anything-hq = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
segment-anything-py = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
segmentation-models-pytorch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
self-rewarding-lm-pytorch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
semantic-kernel = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
semantic-router = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
senselab = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
sent2vec = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
sentence-transformers = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
sequence-model-train = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
serotiny = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
sevenn = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
sglang = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
shap = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
silero-api-server = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
silero-vad = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
silicondiff-npu = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
simclr = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
simple-lama-inpainting = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
sinabs = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
sixdrepnet = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
skforecast = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
skorch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
skrl = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
skt = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
sktime = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
sktmls = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
slangtorch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
smartnoise-synth = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
smashed = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
smplx = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
smqtk-descriptors = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
smqtk-detection = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
snntorch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
snorkel = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
snowflake-ml-python = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
so-vits-svc-fork = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
sonusai = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
sony-custom-layers = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
sotopia = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
spacr = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
spacy-curated-transformers = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
spacy-experimental = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
spacy-huggingface-pipelines = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
spacy-llm = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
spacy-transformers = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
span-marker = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
spandrel = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
spandrel-extra-arches = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
sparrow-python = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
spatialdata = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
speechbrain = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
speechtokenizer = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
spikeinterface = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
spikingjelly = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
spotiflow = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
spotpython = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
spotriver = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
squirrel-core = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
stable-baselines3 = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
stable-diffusion-sdkit = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
stable-ts = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
stanford-stk = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
stanfordnlp = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
stanza = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
startorch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
streamtasks = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
struct-eqtable = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
stylegan2-pytorch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
supar = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
super-gradients = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
super-image = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
superlinked = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
supervisely = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
surya-ocr = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
svdiff-pytorch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
swarm-models = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
swarmauri = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
swarms-memory = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
swebench = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
syft = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
sympytorch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
syne-tune = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
synthcity = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
t5 = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
tab-transformer-pytorch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
tabpfn = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
taming-transformers = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
taming-transformers-rom1504 = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
taskwiz = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
tbparse = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
tecton = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
tensor-parallel = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
tensorcircuit-nightly = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
tensordict = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
tensordict-nightly = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
tensorizer = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
tensorrt-llm = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
texify = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
text2text = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
textattack = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
tfkit = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
thepipe-api = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
thinc = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
thingsvision = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
thirdai = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
thop = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
tianshou = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
tidy3d = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
timesfm = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
timm = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
tipo-kgen = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
tmnt = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
toad = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
tomesd = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
top2vec = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torch-audiomentations = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torch-dct = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torch-delaunay = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torch-directml = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torch-ema = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torch-encoding = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torch-fidelity = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torch-geometric = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torch-geopooling = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torch-harmonics = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torch-kmeans = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torch-lr-finder = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torch-max-mem = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torch-npu = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torch-optimi = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torch-optimizer = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torch-ort = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torch-pitch-shift = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torch-ppr = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torch-pruning = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torch-snippets = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torch-stoi = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torch-struct = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torch-tensorrt = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torchani = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torchattacks = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torchaudio = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torchbiggraph = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torchcam = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torchcde = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torchcfm = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torchcrepe = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torchdata = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torchdatasets-nightly = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torchdiffeq = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torchdyn = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torchestra = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torcheval = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torcheval-nightly = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torchextractor = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torchfcpe = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torchfun = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torchfunc-nightly = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torchgeo = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torchgeometry = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torchio = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torchjpeg = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torchlayers-nightly = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torchmeta = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torchmetrics = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torchmocks = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torchpack = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torchpippy = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torchpq = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torchprofile = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torchquantlib = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torchrec = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torchrec-nightly = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torchrec-nightly-cpu = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torchrl = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torchrl-nightly = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torchscale = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torchsde = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torchseg = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torchserve = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torchserve-nightly = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torchsnapshot-nightly = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torchsr = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torchstain = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torchsummaryX = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torchtext = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torchtnt = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torchtnt-nightly = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torchtyping = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torchutil = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torchvinecopulib = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torchvision = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torchviz = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torchx = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torchx-nightly = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
torchxrayvision = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
totalspineseg = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
tracebloc-package-dev = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
trainer = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
transformer-engine = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
transformer-lens = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
transformer-smaller-training-vocab = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
transformers = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
transformers-domain-adaptation = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
transfusion-pytorch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
transparent-background = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
treescope = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
trolo = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
tsai = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
tslearn = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
ttspod = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
txtai = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
tyro = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
u8darts = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
uhg = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
uitestrunner-syberos = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
ultimate-rvc = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
ultralytics = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
ultralytics-thop = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
unav = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
unbabel-comet = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
underthesea = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
unfoldNd = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
unimernet = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
unitorch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
unitxt = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
unsloth = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
unsloth-zoo = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
unstructured = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
unstructured-inference = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
utilsd = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
v-diffusion-pytorch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
vIQA = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
vectice = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
vector-quantize-pytorch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
vectorhub-nightly = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
versatile-audio-upscaler = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
vertexai = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
vesin = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
vgg-pytorch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
video-representations-extractor = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
viser = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
vision-datasets = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
visionmetrics = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
visu3d = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
vit-pytorch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
viturka-nn = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
vllm = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
vllm-flash-attn = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
vocos = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
vollseg = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
vtorch = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
wavmark = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
wdoc = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
whisper-live = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
whisper-timestamped = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
whisperx = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
wilds = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
wordllama = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
worker-automate-hub = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
wxbtool = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
x-clip = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
x-transformers = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
xaitk_saliency = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
xformers = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
xgrammar = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
xinference = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
xtts-api-server = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
yolo-poser = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
yolov5 = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
yolov7-package = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
yta-general-utils = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
zensvi = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
zetascale = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
zuko = [{ index = "pytorch-cpu", marker = "platform_system == 'Linux'" }]
