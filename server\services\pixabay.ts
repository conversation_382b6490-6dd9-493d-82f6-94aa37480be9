import fetch from 'cross-fetch';

// Pixabay API base URL
const PIXABAY_API_URL = 'https://pixabay.com/api/';
const PIXABAY_VIDEOS_API_URL = 'https://pixabay.com/api/videos/';
const API_KEY = process.env.PIXABAY_API_KEY;

// Error check for missing API key
if (!API_KEY) {
  console.warn('PIXABAY_API_KEY environment variable is not set. Pixabay API will not work.');
}

// Response interfaces
export interface PixabayPhotoResponse {
  total: number;
  totalHits: number;
  hits: PixabayPhoto[];
}

export interface PixabayVideoResponse {
  total: number;
  totalHits: number;
  hits: PixabayVideo[];
}

// Photo interface
export interface PixabayPhoto {
  id: number;
  pageURL: string;
  type: string;
  tags: string;
  previewURL: string;
  previewWidth: number;
  previewHeight: number;
  webformatURL: string;
  webformatWidth: number;
  webformatHeight: number;
  largeImageURL: string;
  fullHDURL: string | null;
  imageURL: string | null;
  imageWidth: number;
  imageHeight: number;
  imageSize: number;
  views: number;
  downloads: number;
  likes: number;
  comments: number;
  user_id: number;
  user: string;
  userImageURL: string;
}

// Video interface
export interface PixabayVideo {
  id: number;
  pageURL: string;
  type: string;
  tags: string;
  duration: number;
  picture_id: string;
  videos: {
    large: {
      url: string;
      width: number;
      height: number;
      size: number;
    };
    medium: {
      url: string;
      width: number;
      height: number;
      size: number;
    };
    small: {
      url: string;
      width: number;
      height: number;
      size: number;
    };
    tiny: {
      url: string;
      width: number;
      height: number;
      size: number;
    };
  };
  views: number;
  downloads: number;
  likes: number;
  comments: number;
  user_id: number;
  user: string;
  userImageURL: string;
}

/**
 * Search for photos on Pixabay
 * @param query Search term
 * @param page Page number (default: 1)
 * @param perPage Results per page (default: 20, max 200)
 * @param orientation Image orientation (all, horizontal, vertical)
 * @param imageType Image type (all, photo, illustration, vector)
 * @param category Category to search in
 */
export async function searchPhotos(
  query: string, 
  page: number = 1, 
  perPage: number = 20,
  orientation: string = 'all',
  imageType: string = 'all',
  category: string = ''
): Promise<PixabayPhotoResponse> {
  try {
    if (!API_KEY) {
      throw new Error('Pixabay API key is not set');
    }

    // Calculate offset based on page and perPage
    const offset = (page - 1) * perPage;

    // Build URL with query parameters
    const url = new URL(PIXABAY_API_URL);
    url.searchParams.append('key', API_KEY);
    url.searchParams.append('q', encodeURIComponent(query));
    url.searchParams.append('per_page', perPage.toString());
    url.searchParams.append('page', page.toString());
    
    // Optional parameters
    if (orientation !== 'all') {
      url.searchParams.append('orientation', orientation);
    }
    
    if (imageType !== 'all') {
      url.searchParams.append('image_type', imageType);
    }
    
    if (category) {
      url.searchParams.append('category', category);
    }

    // Make API request
    const response = await fetch(url.toString());
    
    if (!response.ok) {
      throw new Error(`Pixabay API error: ${response.status} ${response.statusText}`);
    }
    
    const data = await response.json() as PixabayPhotoResponse;
    return data;
  } catch (error) {
    console.error('Error searching Pixabay photos:', error);
    throw error;
  }
}

/**
 * Search for videos on Pixabay
 * @param query Search term
 * @param page Page number (default: 1)
 * @param perPage Results per page (default: 20, max 200)
 * @param videoType Video type (all, film, animation)
 */
export async function searchVideos(
  query: string, 
  page: number = 1, 
  perPage: number = 20,
  videoType: string = 'all'
): Promise<PixabayVideoResponse> {
  try {
    if (!API_KEY) {
      throw new Error('Pixabay API key is not set');
    }

    // Calculate offset based on page and perPage
    const offset = (page - 1) * perPage;

    // Build URL with query parameters
    const url = new URL(PIXABAY_VIDEOS_API_URL);
    url.searchParams.append('key', API_KEY);
    url.searchParams.append('q', encodeURIComponent(query));
    url.searchParams.append('per_page', perPage.toString());
    url.searchParams.append('page', page.toString());
    
    // Optional parameters
    if (videoType !== 'all') {
      url.searchParams.append('video_type', videoType);
    }

    // Make API request
    const response = await fetch(url.toString());
    
    if (!response.ok) {
      throw new Error(`Pixabay API error: ${response.status} ${response.statusText}`);
    }
    
    const data = await response.json() as PixabayVideoResponse;
    return data;
  } catch (error) {
    console.error('Error searching Pixabay videos:', error);
    throw error;
  }
}

/**
 * Get a specific photo by ID
 * @param id Photo ID
 */
export async function getPhoto(id: number): Promise<PixabayPhoto> {
  try {
    if (!API_KEY) {
      throw new Error('Pixabay API key is not set');
    }

    // Build URL with query parameters
    const url = new URL(PIXABAY_API_URL);
    url.searchParams.append('key', API_KEY);
    url.searchParams.append('id', id.toString());

    // Make API request
    const response = await fetch(url.toString());
    
    if (!response.ok) {
      throw new Error(`Pixabay API error: ${response.status} ${response.statusText}`);
    }
    
    const data = await response.json() as PixabayPhotoResponse;
    
    if (data.hits.length === 0) {
      throw new Error(`Photo with ID ${id} not found`);
    }
    
    return data.hits[0];
  } catch (error) {
    console.error(`Error getting Pixabay photo with ID ${id}:`, error);
    throw error;
  }
}

/**
 * Get a specific video by ID
 * @param id Video ID
 */
export async function getVideo(id: number): Promise<PixabayVideo> {
  try {
    if (!API_KEY) {
      throw new Error('Pixabay API key is not set');
    }

    // Build URL with query parameters
    const url = new URL(PIXABAY_VIDEOS_API_URL);
    url.searchParams.append('key', API_KEY);
    url.searchParams.append('id', id.toString());

    // Make API request
    const response = await fetch(url.toString());
    
    if (!response.ok) {
      throw new Error(`Pixabay API error: ${response.status} ${response.statusText}`);
    }
    
    const data = await response.json() as PixabayVideoResponse;
    
    if (data.hits.length === 0) {
      throw new Error(`Video with ID ${id} not found`);
    }
    
    return data.hits[0];
  } catch (error) {
    console.error(`Error getting Pixabay video with ID ${id}:`, error);
    throw error;
  }
}

// Export all functions as a service object for consistency with other services
export const pixabayService = {
  searchPhotos,
  searchVideos,
  getPhoto,
  getVideo
};