import fetch from 'cross-fetch';

// Environment variables
const PEXELS_API_KEY = process.env.PEXELS_API_KEY;
const BASE_URL = 'https://api.pexels.com/v1';
const VIDEO_BASE_URL = 'https://api.pexels.com/videos';

// Types
export interface PexelsPhoto {
  id: number;
  width: number;
  height: number;
  url: string;
  photographer: string;
  photographer_url: string;
  photographer_id: number;
  avg_color: string;
  src: {
    original: string;
    large2x: string;
    large: string;
    medium: string;
    small: string;
    portrait: string;
    landscape: string;
    tiny: string;
  };
  liked: boolean;
  alt: string;
}

export interface PexelsVideo {
  id: number;
  width: number;
  height: number;
  url: string;
  image: string;
  duration: number;
  user: {
    id: number;
    name: string;
    url: string;
  };
  video_files: {
    id: number;
    quality: string;
    file_type: string;
    width: number;
    height: number;
    link: string;
  }[];
  video_pictures: {
    id: number;
    picture: string;
    nr: number;
  }[];
}

export interface PexelsPhotoResponse {
  total_results: number;
  page: number;
  per_page: number;
  photos: PexelsPhoto[];
  next_page?: string;
  prev_page?: string;
}

export interface PexelsVideoResponse {
  total_results: number;
  page: number;
  per_page: number;
  videos: PexelsVideo[];
  next_page?: string;
  prev_page?: string;
}

/**
 * Search for photos on Pexels
 * @param query Search term
 * @param page Page number
 * @param perPage Results per page (default 15, max 80)
 * @param orientation Desired photo orientation (landscape, portrait, square)
 * @param size Minimum photo size (large, medium, small)
 * @param color Desired photo color
 */
export const pexelsService = {
  searchPhotos,
  searchVideos,
  getCuratedPhotos,
  getPopularVideos,
  getPhoto: getPhotoById,
  getVideo: getVideoById,
};

export async function searchPhotos(
  query: string,
  page: number = 1,
  perPage: number = 15,
  orientation?: string,
  size?: string,
  color?: string
): Promise<PexelsPhotoResponse> {
  if (!PEXELS_API_KEY) {
    throw new Error('Pexels API key is not configured');
  }

  const params = new URLSearchParams({
    query,
    page: page.toString(),
    per_page: perPage.toString(),
  });

  if (orientation) params.append('orientation', orientation);
  if (size) params.append('size', size);
  if (color) params.append('color', color);

  const response = await fetch(`${BASE_URL}/search?${params.toString()}`, {
    headers: {
      Authorization: PEXELS_API_KEY,
    },
  });

  if (!response.ok) {
    throw new Error(`Pexels API error: ${response.status} ${response.statusText}`);
  }

  return response.json();
}

/**
 * Search for videos on Pexels
 * @param query Search term
 * @param page Page number
 * @param perPage Results per page (default 15, max 80)
 * @param orientation Desired video orientation (landscape, portrait, square)
 * @param size Minimum video size (large, medium, small)
 */
export async function searchVideos(
  query: string,
  page: number = 1,
  perPage: number = 15,
  orientation?: string,
  size?: string
): Promise<PexelsVideoResponse> {
  if (!PEXELS_API_KEY) {
    throw new Error('Pexels API key is not configured');
  }

  const params = new URLSearchParams({
    query,
    page: page.toString(),
    per_page: perPage.toString(),
  });

  if (orientation) params.append('orientation', orientation);
  if (size) params.append('size', size);

  const response = await fetch(`${VIDEO_BASE_URL}/search?${params.toString()}`, {
    headers: {
      Authorization: PEXELS_API_KEY,
    },
  });

  if (!response.ok) {
    throw new Error(`Pexels API error: ${response.status} ${response.statusText}`);
  }

  return response.json();
}

/**
 * Get curated photos from Pexels
 * @param page Page number
 * @param perPage Results per page (default 15, max 80)
 */
export async function getCuratedPhotos(
  page: number = 1,
  perPage: number = 15
): Promise<PexelsPhotoResponse> {
  if (!PEXELS_API_KEY) {
    throw new Error('Pexels API key is not configured');
  }

  const params = new URLSearchParams({
    page: page.toString(),
    per_page: perPage.toString(),
  });

  const response = await fetch(`${BASE_URL}/curated?${params.toString()}`, {
    headers: {
      Authorization: PEXELS_API_KEY,
    },
  });

  if (!response.ok) {
    throw new Error(`Pexels API error: ${response.status} ${response.statusText}`);
  }

  return response.json();
}

/**
 * Get popular videos from Pexels
 * @param page Page number
 * @param perPage Results per page (default 15, max 80)
 */
export async function getPopularVideos(
  page: number = 1,
  perPage: number = 15
): Promise<PexelsVideoResponse> {
  if (!PEXELS_API_KEY) {
    throw new Error('Pexels API key is not configured');
  }

  const params = new URLSearchParams({
    page: page.toString(),
    per_page: perPage.toString(),
  });

  const response = await fetch(`${VIDEO_BASE_URL}/popular?${params.toString()}`, {
    headers: {
      Authorization: PEXELS_API_KEY,
    },
  });

  if (!response.ok) {
    throw new Error(`Pexels API error: ${response.status} ${response.statusText}`);
  }

  return response.json();
}

/**
 * Get photo by ID
 * @param id Photo ID
 */
export async function getPhotoById(id: number): Promise<PexelsPhoto> {
  if (!PEXELS_API_KEY) {
    throw new Error('Pexels API key is not configured');
  }

  const response = await fetch(`${BASE_URL}/photos/${id}`, {
    headers: {
      Authorization: PEXELS_API_KEY,
    },
  });

  if (!response.ok) {
    throw new Error(`Pexels API error: ${response.status} ${response.statusText}`);
  }

  return response.json();
}

/**
 * Get video by ID
 * @param id Video ID
 */
export async function getVideoById(id: number): Promise<PexelsVideo> {
  if (!PEXELS_API_KEY) {
    throw new Error('Pexels API key is not configured');
  }

  const response = await fetch(`${VIDEO_BASE_URL}/videos/${id}`, {
    headers: {
      Authorization: PEXELS_API_KEY,
    },
  });

  if (!response.ok) {
    throw new Error(`Pexels API error: ${response.status} ${response.statusText}`);
  }

  return response.json();
}