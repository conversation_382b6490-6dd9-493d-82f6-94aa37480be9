import { GoogleGenerativeAI } from '@google/generative-ai';

// Interface for the course structure
interface CourseStructure {
  title: string;
  description: string;
  modules: {
    title: string;
    description: string;
    lessons: {
      title: string;
      description: string;
    }[];
  }[];
}

/**
 * Verifies that a Google API key is valid by making a test request
 * @param apiKey - The Google Gemini API key to verify
 * @returns Promise that resolves if the key is valid, or rejects with an error if invalid
 */
export async function verifyApiKey(apiKey: string): Promise<void> {
  try {
    const genAI = new GoogleGenerativeAI(apiKey);
    
    // Try to access a model to verify the API key is valid
    const model = genAI.getGenerativeModel({ model: "gemini-1.5-pro" });
    
    // Make a very small request to verify the key works
    const result = await model.generateContent("Hello, world!");
    
    if (result && result.response) {
      return; // API key is valid
    }
    
    throw new Error("Invalid Google Gemini API key");
  } catch (error: any) {
    if (error.message.includes('API key')) {
      throw new Error("Invalid Google Gemini API key");
    }
    throw error;
  }
}

/**
 * Gets a verified API key, either from the provided key or from environment variables
 * @param userApiKey - Optional user API key to check first
 * @returns The API key to use
 */
export function getApiKey(userApiKey?: string): string {
  // Use user-provided API key if available
  if (userApiKey) {
    return userApiKey;
  }
  
  // Fall back to system API key
  const systemApiKey = process.env.GOOGLE_API_KEY;
  
  if (!systemApiKey) {
    throw new Error("No Google Gemini API key available");
  }
  
  return systemApiKey;
}

/**
 * Generates a course structure using Google's Gemini AI
 * @param courseData - Course details including title, description, etc.
 * @returns Promise that resolves to the generated course structure
 */
/**
 * Generates a script for a lesson using Google's Gemini AI
 * @param lessonData - Lesson details including title, description, etc.
 * @returns Promise that resolves to the generated script
 */
export async function generateScript(data: {
  title: string;
  description: string;
  targetAudience?: string;
  // Adding optional fields to accommodate various callers
  courseTitle?: string;
  courseDescription?: string;
  lessonTitle?: string;
  lessonDescription?: string;
  keyPoints?: string[];
  duration?: number;
  tone?: string;
}): Promise<string> {
  try {
    const apiKey = getApiKey();
    const genAI = new GoogleGenerativeAI(apiKey);
    const model = genAI.getGenerativeModel({ model: "gemini-1.5-pro" });
    
    // Build prompt with all available information
    let prompt = `Write a comprehensive educational script with the following details:
    
Title: ${data.title}
Description: ${data.description}
`;
    
    // Add extended information if available (for course/lesson context)
    if (data.courseTitle) {
      prompt += `Course Title: ${data.courseTitle}\n`;
    }
    
    if (data.courseDescription) {
      prompt += `Course Description: ${data.courseDescription}\n`;
    }
    
    if (data.lessonTitle) {
      prompt += `Lesson Title: ${data.lessonTitle}\n`;
    }
    
    if (data.lessonDescription) {
      prompt += `Lesson Description: ${data.lessonDescription}\n`;
    }
    
    // Add optional information if provided
    if (data.keyPoints && data.keyPoints.length > 0) {
      prompt += `Key Points to Cover:\n${data.keyPoints.map(point => `- ${point}`).join('\n')}\n`;
    }
    
    if (data.targetAudience) {
      prompt += `Target Audience: ${data.targetAudience}\n`;
    }
    
    if (data.duration) {
      prompt += `Duration: Approximately ${data.duration} minutes\n`;
    }
    
    if (data.tone) {
      prompt += `Tone: ${data.tone}\n`;
    }
    
    prompt += `
Please write a complete script that:
1. Begins with a clear introduction
2. Covers all the key points in a logical order
3. Provides explanations and examples for complex concepts
4. Includes transitions between topics
5. Ends with a summary and call to action

Format the script as if it's being read by an instructor, with clear paragraphs and natural speech patterns.`;

    // Generate content
    const result = await model.generateContent(prompt);
    const response = result.response;
    const text = response.text();
    
    return text;
  } catch (error) {
    console.error("Error in Gemini script generation:", error);
    throw error;
  }
}

/**
 * Analyzes an image using Google's Gemini AI
 * @param imageBase64 - Base64-encoded image data
 * @param prompt - Optional prompt to guide the analysis
 * @returns Promise that resolves to the analysis text
 */
export async function analyzeImage(imageBase64: string, prompt: string = "Describe this image in detail"): Promise<string> {
  try {
    const apiKey = getApiKey();
    const genAI = new GoogleGenerativeAI(apiKey);
    const model = genAI.getGenerativeModel({ model: "gemini-1.5-pro" });
    
    // Prepare image parts for the API
    const imageParts = [
      {
        inlineData: {
          data: imageBase64,
          mimeType: "image/jpeg", // Assume JPEG, but could be determined dynamically
        },
      },
    ];
    
    // Generate content with image
    const result = await model.generateContent([prompt, ...imageParts]);
    const response = result.response;
    const text = response.text();
    
    return text;
  } catch (error) {
    console.error("Error in Gemini image analysis:", error);
    throw error;
  }
}

export async function generateCourseStructure(courseData: {
  title: string;
  description: string;
  category: string;
  targetAudience?: string;
  keyTopics?: string;
  contentNotes?: string;
  moduleCount?: number;
}): Promise<CourseStructure> {
  try {
    const apiKey = getApiKey();
    const genAI = new GoogleGenerativeAI(apiKey);
    const model = genAI.getGenerativeModel({ model: "gemini-1.5-pro" });
    
    // Build prompt with all available information
    let prompt = `Create a detailed course structure for an online course with the following information:
    
Title: ${courseData.title}
Description: ${courseData.description}
Category: ${courseData.category}
`;

    // Add optional information if provided
    if (courseData.targetAudience) {
      prompt += `Target Audience: ${courseData.targetAudience}\n`;
    }
    
    if (courseData.keyTopics) {
      prompt += `Key Topics: ${courseData.keyTopics}\n`;
    }
    
    if (courseData.contentNotes) {
      prompt += `Content Notes: ${courseData.contentNotes}\n`;
    }
    
    // Determine module count
    const moduleCount = courseData.moduleCount || 4; // Default to 4 if not specified
    
    prompt += `
Provide a complete course structure with:
- Exactly ${moduleCount} module${moduleCount === 1 ? '' : 's'} that follow a logical learning progression
- Each module should have 3-6 lessons
- Each module and lesson should have a clear title and description

Format your response as JSON matching this structure:
{
  "modules": [
    {
      "title": "Module Title",
      "description": "Module description text",
      "lessons": [
        {
          "title": "Lesson Title",
          "description": "Lesson description text"
        }
      ]
    }
  ]
}

Ensure the JSON is properly formatted with no trailing commas and all strings are properly quoted.`;

    // Generate content
    const result = await model.generateContent(prompt);
    const response = result.response;
    const text = response.text();
    
    // Extract JSON from the response
    const jsonMatch = text.match(/```json\n([\s\S]*)\n```/) || text.match(/```\n([\s\S]*)\n```/) || [null, text];
    const jsonStr = jsonMatch[1].trim();
    
    try {
      const parsedResponse = JSON.parse(jsonStr);
      
      // Format response according to CourseStructure interface
      return {
        title: courseData.title,
        description: courseData.description,
        modules: parsedResponse.modules || []
      };
    } catch (jsonError) {
      console.error("Error parsing JSON response from Gemini:", jsonError);
      console.log("Raw response:", text);
      
      // Fallback to a basic structure if JSON parsing fails
      return {
        title: courseData.title,
        description: courseData.description,
        modules: [
          {
            title: "Introduction to " + courseData.title,
            description: "Get started with the basics of " + courseData.title,
            lessons: [
              {
                title: "Course Overview",
                description: "A brief introduction to the course content"
              },
              {
                title: "Key Concepts",
                description: "Essential concepts you need to understand for this course"
              }
            ]
          }
        ]
      };
    }
  } catch (error) {
    console.error("Error in Gemini course structure generation:", error);
    throw error;
  }
}