import React, { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Play, 
  Pause, 
  Square, 
  Download, 
  Upload, 
  Video, 
  Image, 
  Eye,
  Wand2,
  FileVideo,
  Clock,
  CheckCircle,
  AlertCircle,
  RefreshCw,
  Trash2,
  Plus,
  Info,
  ArrowRight,
  GripVertical,
  X,
  Move,
  Edit,
  Save,
  Layout,
  BookOpen,
  FileText,
  Camera,
  Monitor,
  Settings
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { DragDropContext, Droppable, Draggable } from '@hello-pangea/dnd';
import { useQuery } from '@tanstack/react-query';

interface CourseElement {
  id: string;
  type: 'module' | 'lesson' | 'image' | 'video' | 'text' | 'slide';
  title: string;
  content?: string;
  url?: string;
  duration?: number;
  thumbnail?: string;
  order: number;
  moduleId?: string;
  lessonId?: string;
  isEditing?: boolean;
}

interface CourseStructure {
  id: string;
  title: string;
  description: string;
  elements: CourseElement[];
  estimatedDuration: number;
  status: 'draft' | 'ready' | 'published';
}

interface CourseAssemblyStudioProps {
  courseScripts?: Record<string, Record<string, string>>;
  generatedStructure?: {
    modules: Array<{
      id: string;
      title: string;
      description?: string;
      lessons: Array<{
        id: string;
        title: string;
        type?: string;
        content?: string;
        duration?: number;
      }>;
    }>;
  };
  onStructureChange?: (structure: CourseStructure) => void;
  initialStructure?: CourseStructure;
}

export default function CourseAssemblyStudio({ 
  courseScripts = {},
  generatedStructure,
  onStructureChange,
  initialStructure
}: CourseAssemblyStudioProps) {
  const [courseStructure, setCourseStructure] = useState<CourseStructure>(
    initialStructure || {
      id: 'course-1',
      title: 'My Course',
      description: 'Course description',
      elements: [],
      estimatedDuration: 0,
      status: 'draft'
    }
  );

  const [selectedElement, setSelectedElement] = useState<string | null>(null);
  const [previewMode, setPreviewMode] = useState(false);
  const [showMediaLibrary, setShowMediaLibrary] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const { toast } = useToast();

  // Fetch available media
  const { data: mediaLibrary = [] } = useQuery<any[]>({
    queryKey: ['/api/media'],
    enabled: showMediaLibrary
  });

  // Initialize structure from generated structure and course scripts
  useEffect(() => {
    if (generatedStructure && generatedStructure.modules && courseStructure.elements.length === 0) {
      const elements: CourseElement[] = [];
      let order = 0;

      generatedStructure.modules.forEach((module) => {
        // Add module element with proper title
        elements.push({
          id: `module-${module.id}`,
          type: 'module',
          title: module.title,
          content: module.description,
          moduleId: module.id,
          order: order++
        });

        // Add lesson elements with proper titles
        module.lessons.forEach((lesson) => {
          const script = courseScripts[module.id]?.[lesson.id] || '';
          elements.push({
            id: `lesson-${lesson.id}`,
            type: 'lesson',
            title: lesson.title,
            content: script,
            moduleId: module.id,
            lessonId: lesson.id,
            order: order++,
            duration: lesson.duration || Math.ceil(script.length / 200) // Use lesson duration or estimate
          });
        });
      });

      setCourseStructure(prev => ({
        ...prev,
        elements,
        estimatedDuration: elements.reduce((total, el) => total + (el.duration || 0), 0)
      }));
    }
  }, [generatedStructure, courseScripts, courseStructure.elements.length]);

  const handleDragEnd = (result: any) => {
    if (!result.destination) return;

    const items = Array.from(courseStructure.elements);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    // Update order values
    const updatedItems = items.map((item, index) => ({
      ...item,
      order: index
    }));

    setCourseStructure(prev => ({
      ...prev,
      elements: updatedItems
    }));

    onStructureChange?.({
      ...courseStructure,
      elements: updatedItems
    });
  };

  const addElement = (type: CourseElement['type']) => {
    const newElement: CourseElement = {
      id: `element-${Date.now()}`,
      type,
      title: `New ${type}`,
      order: courseStructure.elements.length,
      isEditing: true
    };

    setCourseStructure(prev => ({
      ...prev,
      elements: [...prev.elements, newElement]
    }));
  };

  const updateElement = (id: string, updates: Partial<CourseElement>) => {
    setCourseStructure(prev => ({
      ...prev,
      elements: prev.elements.map(el => 
        el.id === id ? { ...el, ...updates } : el
      )
    }));
  };

  const removeElement = (id: string) => {
    setCourseStructure(prev => ({
      ...prev,
      elements: prev.elements.filter(el => el.id !== id)
    }));
  };

  const addMediaElement = (mediaItem: any) => {
    const newElement: CourseElement = {
      id: `media-${Date.now()}`,
      type: mediaItem.type === 'video' ? 'video' : 'image',
      title: mediaItem.name,
      url: mediaItem.url,
      thumbnail: mediaItem.thumbnailUrl,
      duration: mediaItem.duration,
      order: courseStructure.elements.length
    };

    setCourseStructure(prev => ({
      ...prev,
      elements: [...prev.elements, newElement]
    }));

    setShowMediaLibrary(false);
  };

  const generateFinalCourse = async () => {
    setIsGenerating(true);
    
    try {
      const response = await fetch('/api/ai/assemble-course', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          structure: courseStructure,
          includeSubtitles: true,
          outputFormat: 'video'
        })
      });

      if (!response.ok) {
        throw new Error('Failed to generate course');
      }

      const result = await response.json();
      
      toast({
        title: "Course Generated Successfully!",
        description: "Your complete course is ready for preview and publishing.",
      });

      setCourseStructure(prev => ({
        ...prev,
        status: 'ready'
      }));

    } catch (error: any) {
      toast({
        title: "Generation Failed",
        description: error.message,
        variant: "destructive"
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const ElementCard = ({ element, index }: { element: CourseElement; index: number }) => {
    const getIcon = () => {
      switch (element.type) {
        case 'module': return <BookOpen className="h-4 w-4" />;
        case 'lesson': return <FileText className="h-4 w-4" />;
        case 'image': return <Image className="h-4 w-4" />;
        case 'video': return <Video className="h-4 w-4" />;
        case 'slide': return <Monitor className="h-4 w-4" />;
        default: return <FileText className="h-4 w-4" />;
      }
    };

    const getBadgeColor = () => {
      switch (element.type) {
        case 'module': return 'bg-blue-100 text-blue-800';
        case 'lesson': return 'bg-green-100 text-green-800';
        case 'image': return 'bg-purple-100 text-purple-800';
        case 'video': return 'bg-red-100 text-red-800';
        case 'slide': return 'bg-orange-100 text-orange-800';
        default: return 'bg-gray-100 text-gray-800';
      }
    };

    return (
      <Draggable draggableId={element.id} index={index}>
        {(provided, snapshot) => (
          <div
            ref={provided.innerRef}
            {...provided.draggableProps}
            className={`bg-white border rounded-lg p-4 mb-3 transition-all ${
              snapshot.isDragging ? 'shadow-lg rotate-1' : 'shadow-sm hover:shadow-md'
            } ${selectedElement === element.id ? 'ring-2 ring-primary' : ''}`}
            onClick={() => setSelectedElement(element.id)}
          >
            <div className="flex items-center gap-3">
              <div {...provided.dragHandleProps} className="cursor-grab active:cursor-grabbing">
                <GripVertical className="h-4 w-4 text-gray-400" />
              </div>
              
              <div className="flex-1 flex items-center gap-3">
                <div className="flex items-center gap-2">
                  {getIcon()}
                  <Badge className={getBadgeColor()}>
                    {element.type}
                  </Badge>
                </div>
                
                <div className="flex-1">
                  {element.isEditing ? (
                    <Input
                      value={element.title}
                      onChange={(e) => updateElement(element.id, { title: e.target.value })}
                      onBlur={() => updateElement(element.id, { isEditing: false })}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          updateElement(element.id, { isEditing: false });
                        }
                      }}
                      className="h-8"
                      autoFocus
                    />
                  ) : (
                    <div>
                      <h4 className="font-medium">{element.title}</h4>
                      {element.duration && (
                        <p className="text-sm text-muted-foreground">
                          {element.duration} min
                        </p>
                      )}
                    </div>
                  )}
                </div>

                <div className="flex items-center gap-2">
                  {element.type === 'lesson' && element.content && (
                    <Badge variant="outline" className="text-xs">
                      {Math.ceil(element.content.length / 100)} words
                    </Badge>
                  )}
                  
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={(e) => {
                      e.stopPropagation();
                      updateElement(element.id, { isEditing: true });
                    }}
                  >
                    <Edit className="h-3 w-3" />
                  </Button>
                  
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={(e) => {
                      e.stopPropagation();
                      removeElement(element.id);
                    }}
                  >
                    <Trash2 className="h-3 w-3" />
                  </Button>
                </div>
              </div>
            </div>

            {element.url && (
              <div className="mt-3 ml-7">
                {element.type === 'video' ? (
                  <video 
                    src={element.url} 
                    className="w-full max-w-sm h-32 object-cover rounded border"
                    controls={false}
                  />
                ) : element.type === 'image' ? (
                  <img 
                    src={element.url} 
                    alt={element.title}
                    className="w-full max-w-sm h-32 object-cover rounded border"
                  />
                ) : null}
              </div>
            )}

            {element.content && selectedElement === element.id && (
              <div className="mt-3 ml-7">
                <ScrollArea className="h-32 w-full border rounded p-2">
                  <p className="text-sm text-muted-foreground">
                    {element.content.substring(0, 300)}
                    {element.content.length > 300 && '...'}
                  </p>
                </ScrollArea>
              </div>
            )}
          </div>
        )}
      </Draggable>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <Layout className="h-6 w-6" />
            Course Assembly Studio
          </h2>
          <p className="text-muted-foreground">
            Organize your course structure with drag-and-drop interface
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={() => setPreviewMode(!previewMode)}
          >
            <Eye className="h-4 w-4 mr-2" />
            {previewMode ? 'Edit' : 'Preview'}
          </Button>
          
          <Button
            onClick={generateFinalCourse}
            disabled={isGenerating || courseStructure.elements.length === 0}
            className="flex items-center gap-2"
          >
            <Wand2 className="h-4 w-4" />
            {isGenerating ? 'Generating...' : 'Generate Course'}
          </Button>
        </div>
      </div>

      {/* Course Info */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Course Information</span>
            <Badge variant={
              courseStructure.status === 'published' ? 'default' :
              courseStructure.status === 'ready' ? 'secondary' : 'outline'
            }>
              {courseStructure.status}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label>Course Title</Label>
              <Input
                value={courseStructure.title}
                onChange={(e) => setCourseStructure(prev => ({
                  ...prev,
                  title: e.target.value
                }))}
              />
            </div>
            <div>
              <Label>Elements Count</Label>
              <div className="text-2xl font-bold text-muted-foreground">
                {courseStructure.elements.length}
              </div>
            </div>
            <div>
              <Label>Estimated Duration</Label>
              <div className="text-2xl font-bold text-muted-foreground">
                {courseStructure.estimatedDuration} min
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Toolbar */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Plus className="h-5 w-5" />
                Add Elements
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button
                variant="outline"
                className="w-full justify-start"
                onClick={() => addElement('module')}
              >
                <BookOpen className="h-4 w-4 mr-2" />
                Module
              </Button>
              
              <Button
                variant="outline"
                className="w-full justify-start"
                onClick={() => addElement('lesson')}
              >
                <FileText className="h-4 w-4 mr-2" />
                Lesson
              </Button>
              
              <Button
                variant="outline"
                className="w-full justify-start"
                onClick={() => addElement('text')}
              >
                <FileText className="h-4 w-4 mr-2" />
                Text Block
              </Button>
              
              <Button
                variant="outline"
                className="w-full justify-start"
                onClick={() => addElement('slide')}
              >
                <Monitor className="h-4 w-4 mr-2" />
                Slide
              </Button>
              
              <Separator />
              
              <Button
                variant="outline"
                className="w-full justify-start"
                onClick={() => setShowMediaLibrary(true)}
              >
                <Camera className="h-4 w-4 mr-2" />
                Media Library
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Course Structure */}
        <div className="lg:col-span-3">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Move className="h-5 w-5" />
                Course Structure
              </CardTitle>
            </CardHeader>
            <CardContent>
              {courseStructure.elements.length === 0 ? (
                <div className="text-center py-12 text-muted-foreground">
                  <Layout className="h-16 w-16 mx-auto mb-4 opacity-50" />
                  <p>No elements in your course yet.</p>
                  <p className="text-sm mt-2">
                    Add modules, lessons, and media from the toolbar.
                  </p>
                </div>
              ) : (
                <DragDropContext onDragEnd={handleDragEnd}>
                  <Droppable droppableId="course-elements">
                    {(provided) => (
                      <div
                        {...provided.droppableProps}
                        ref={provided.innerRef}
                        className="space-y-0"
                      >
                        {courseStructure.elements
                          .sort((a, b) => a.order - b.order)
                          .map((element, index) => (
                            <ElementCard
                              key={element.id}
                              element={element}
                              index={index}
                            />
                          ))}
                        {provided.placeholder}
                      </div>
                    )}
                  </Droppable>
                </DragDropContext>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Media Library Modal */}
      {showMediaLibrary && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg w-full max-w-6xl h-3/4 flex flex-col">
            <div className="p-4 border-b flex items-center justify-between">
              <h2 className="text-lg font-semibold">Media Library</h2>
              <Button
                variant="ghost"
                onClick={() => setShowMediaLibrary(false)}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
            <div className="flex-1 p-4 overflow-auto">
              <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                {mediaLibrary.map((item: any) => (
                  <div
                    key={item.id}
                    className="border rounded-lg p-3 cursor-pointer hover:border-primary transition-colors"
                    onClick={() => addMediaElement(item)}
                  >
                    <div className="aspect-video bg-muted rounded mb-2 flex items-center justify-center">
                      {item.type === 'video' ? (
                        <Video className="h-8 w-8 text-muted-foreground" />
                      ) : (
                        <Image className="h-8 w-8 text-muted-foreground" />
                      )}
                    </div>
                    <p className="text-sm font-medium truncate">{item.name}</p>
                    <p className="text-xs text-muted-foreground capitalize">{item.type}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Preview Mode */}
      {previewMode && (
        <Card>
          <CardHeader>
            <CardTitle>Course Preview</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {courseStructure.elements
                .sort((a, b) => a.order - b.order)
                .map((element, index) => (
                  <div key={element.id} className="border-l-4 border-primary pl-4">
                    <div className="flex items-center gap-2 mb-2">
                      <span className="text-sm font-medium text-muted-foreground">
                        {index + 1}.
                      </span>
                      <h4 className="font-medium">{element.title}</h4>
                      <Badge variant="outline" className="text-xs">
                        {element.type}
                      </Badge>
                    </div>
                    {element.content && (
                      <p className="text-sm text-muted-foreground ml-6">
                        {element.content.substring(0, 150)}...
                      </p>
                    )}
                  </div>
                ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}