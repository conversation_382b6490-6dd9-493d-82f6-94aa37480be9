export const generateDemoScriptTemplate = (lessonTitle: string, lessonDescription: string) => {
  return `# ${lessonTitle}

Welcome to our lesson on ${lessonTitle}. In this session, we'll explore ${lessonDescription}

## Learning Objectives

By the end of this lesson, you will be able to understand the core concepts of ${lessonTitle}, apply key principles in practical scenarios, and evaluate results effectively.

## Background & Context

${lessonTitle} has become increasingly important in today's landscape. Let's begin by examining why this topic matters and how it fits into the broader context.

Key concepts to remember include the historical development of the field, current applications and significance, and future trends to watch.

## Core Principles

The foundation of ${lessonTitle} rests on several fundamental principles.

First principle: Understanding the basic framework. This involves recognizing patterns and establishing connections between different elements. Pay special attention to how these patterns emerge in various contexts.

Second principle: Applying systematic analysis. When we analyze ${lessonTitle}, we need to consider multiple factors including environmental conditions, resource constraints, and stakeholder needs.

## Practical Applications

Let's explore how these concepts work in real-world scenarios.

In business settings, professionals are identifying opportunities for growth, measuring outcomes and impact, and adjusting strategies based on feedback.

In personal development applications, individuals are setting achievable goals, tracking progress effectively, and overcoming common obstacles.

## Summary

Today we've covered the essential aspects of ${lessonTitle}. We've explored its historical context, examined core principles, and investigated practical applications.

Remember that mastery comes through consistent practice and application of these principles in various contexts.`;
};