# AILearnMaster Environment Configuration
# Copy this file to .env and fill in your actual values

# =============================================================================
# DATABASE CONFIGURATION (PostgreSQL)
# =============================================================================

# Primary PostgreSQL Database URL (Neon Database recommended)
# Format: postgresql://username:password@host:port/database?sslmode=require
DATABASE_URL=postgresql://username:<EMAIL>/ailearn_master?sslmode=require

# Database Connection Pool Settings (Optional - defaults will be used if not set)
DB_MAX_CONNECTIONS=20
DB_IDLE_TIMEOUT_MS=60000
DB_CONNECTION_TIMEOUT_MS=10000
DB_STATEMENT_TIMEOUT_MS=30000
DB_QUERY_TIMEOUT_MS=25000

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================

# Environment (development, staging, production)
NODE_ENV=development

# Server Configuration
PORT=3001
HOST=localhost

# Session Configuration
SESSION_SECRET=your-super-secret-session-key-change-this-in-production

# =============================================================================
# AI SERVICES CONFIGURATION
# =============================================================================

# Modal A100 GPU Configuration (for open-source AI models)
MODAL_TOKEN_ID=your-modal-token-id
MODAL_TOKEN_SECRET=your-modal-token-secret
MODAL_GPU_BASE_URL=https://your-username--courseai-opensource

# OpenAI Configuration (fallback service)
OPENAI_API_KEY=sk-your-openai-api-key

# Google Gemini Configuration (fallback service)
GOOGLE_API_KEY=your-google-gemini-api-key

# ElevenLabs Configuration (legacy - being phased out)
ELEVENLABS_API_KEY=your-elevenlabs-api-key

# =============================================================================
# CLOUD STORAGE CONFIGURATION
# =============================================================================

# AWS S3 Configuration
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
AWS_S3_BUCKET=your-s3-bucket-name

# CloudFront CDN (optional)
CLOUDFRONT_DOMAIN=your-cloudfront-domain.cloudfront.net

# =============================================================================
# PAYMENT PROCESSING
# =============================================================================

# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_your-stripe-secret-key
STRIPE_PUBLISHABLE_KEY=pk_test_your-stripe-publishable-key
STRIPE_WEBHOOK_SECRET=whsec_your-stripe-webhook-secret

# =============================================================================
# EMAIL SERVICES
# =============================================================================

# Email Service Configuration (choose one)
EMAIL_SERVICE=smtp

# SMTP Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# SendGrid Configuration (alternative)
SENDGRID_API_KEY=SG.your-sendgrid-api-key

# =============================================================================
# STOCK MEDIA SERVICES
# =============================================================================

# Pexels API
PEXELS_API_KEY=your-pexels-api-key

# Pixabay API
PIXABAY_API_KEY=your-pixabay-api-key

# =============================================================================
# SOCIAL MEDIA INTEGRATIONS
# =============================================================================

# Facebook/Meta Integration
FACEBOOK_APP_ID=your-facebook-app-id
FACEBOOK_APP_SECRET=your-facebook-app-secret

# =============================================================================
# DEVELOPMENT TOOLS
# =============================================================================

# Enable detailed logging in development
DEBUG=true
LOG_LEVEL=debug

# Enable database query logging
DB_LOGGING=true

# Enable API request logging
API_LOGGING=true

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# CORS Configuration
CORS_ORIGIN=http://localhost:3000,https://your-domain.com

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# JWT Configuration (if using JWT)
JWT_SECRET=your-jwt-secret-key
JWT_EXPIRES_IN=7d

# =============================================================================
# MONITORING AND ANALYTICS
# =============================================================================

# Application Monitoring
SENTRY_DSN=your-sentry-dsn

# Analytics
GOOGLE_ANALYTICS_ID=GA-your-analytics-id

# =============================================================================
# FEATURE FLAGS
# =============================================================================

# Enable/disable features
ENABLE_AI_CREDITS=true
ENABLE_AVATAR_COURSES=true
ENABLE_MICRO_LEARNING=true
ENABLE_GAMIFICATION=true
ENABLE_EMAIL_CAMPAIGNS=true
ENABLE_LANDING_PAGES=true

# =============================================================================
# PERFORMANCE OPTIMIZATION
# =============================================================================

# Cache Configuration
REDIS_URL=redis://localhost:6379
CACHE_TTL_SECONDS=3600

# File Upload Limits
MAX_FILE_SIZE_MB=50
MAX_FILES_PER_UPLOAD=10

# =============================================================================
# BACKUP AND RECOVERY
# =============================================================================

# Database Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=your-backup-bucket

# =============================================================================
# DEPLOYMENT CONFIGURATION
# =============================================================================

# Deployment Environment
DEPLOYMENT_ENV=development

# Health Check Configuration
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_INTERVAL_MS=30000

# Graceful Shutdown
SHUTDOWN_TIMEOUT_MS=10000

# =============================================================================
# LEGACY CONFIGURATION (being phased out)
# =============================================================================

# Replit-specific settings (for backward compatibility)
REPL_ID=your-repl-id
REPL_SLUG=your-repl-slug
