import { Router } from 'express';
import { createEnhancedVideo } from '../services/enhanced-video-pipeline';
import path from 'path';
import fs from 'fs/promises';

const router = Router();

router.post('/create-enhanced', async (req, res) => {
  try {
    const { scenes, audio, style, title, settings } = req.body;
    
    console.log('Video creation request received:', {
      sceneCount: scenes?.length || 0,
      hasAudio: !!audio,
      style: style || 'professional',
      title: title || 'Untitled'
    });
    
    if (!scenes || !Array.isArray(scenes) || scenes.length === 0) {
      return res.status(400).json({ 
        error: 'Scenes are required',
        received: { scenes: scenes?.length || 0 }
      });
    }
    
    console.log('Processing video creation with FFmpeg...');
    
    try {
      // Create actual video using FFmpeg pipeline
      const result = await createEnhancedVideo({
        scenes,
        audio: audio || null,
        style: style || 'professional',
        title: title || 'Generated Lesson Video',
        settings: {
          resolution: settings?.resolution || '1920x1080',
          fps: settings?.fps || 30,
          duration: scenes.reduce((total: number, scene: any) => total + (scene.duration || 5), 0)
        }
      });
      
      console.log('Video creation successful:', result.videoUrl);
      res.json(result);
      
    } catch (videoError) {
      console.error('Video creation failed:', videoError);
      res.status(500).json({
        error: 'Video creation failed',
        details: videoError instanceof Error ? videoError.message : 'Unknown error',
        success: false
      });
    }
    
  } catch (error) {
    console.error('Enhanced video creation error:', error);
    res.status(500).json({ 
      error: 'Failed to create video',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Serve generated videos
router.get('/generated/:filename', async (req, res) => {
  try {
    const { filename } = req.params;
    const videoPath = path.join(process.cwd(), 'uploads', 'videos', filename);
    
    // Check if file exists
    await fs.access(videoPath);
    
    res.setHeader('Content-Type', 'video/mp4');
    res.sendFile(videoPath);
  } catch (error) {
    res.status(404).json({ error: 'Video not found' });
  }
});

export default router;