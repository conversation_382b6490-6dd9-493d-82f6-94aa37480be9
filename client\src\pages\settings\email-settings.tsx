import React, { useState } from 'react';
import { useAuth } from '@/hooks/use-auth';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Checkbox } from '@/components/ui/checkbox';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { MainLayout } from '@/components/layouts/MainLayout';
import { Loader2, Mail, Send } from 'lucide-react';

export default function EmailSettings() {
  const { toast } = useToast();
  const { user, isLoading } = useAuth();
  const [recipientEmail, setRecipientEmail] = useState('');
  const [subject, setSubject] = useState('');
  const [body, setBody] = useState('');
  const [emailType, setEmailType] = useState('custom_email');
  const [isSending, setIsSending] = useState(false);
  
  // State for notification preferences
  const [notificationPreferences, setNotificationPreferences] = useState({
    coursePublished: { enabled: true, email: true },
    courseCompleted: { enabled: true, email: true },
    teamInvite: { enabled: true, email: true },
    courseCollaboration: { enabled: true, email: true },
    aiGenerationComplete: { enabled: true, email: true },
    systemUpdate: { enabled: true, email: true },
    subscriptionExpiring: { enabled: true, email: true },
    creditLow: { enabled: true, email: true },
  });

  const handleSendTestEmail = async () => {
    if (!recipientEmail) {
      toast({
        title: 'Email required',
        description: 'Please enter a recipient email address',
        variant: 'destructive',
      });
      return;
    }

    setIsSending(true);

    try {
      const response = await apiRequest('POST', '/api/email/send-test', {
        to: recipientEmail,
        type: emailType,
        subject: emailType === 'custom_email' ? subject : undefined,
        body: emailType === 'custom_email' ? body : undefined,
      });

      if (!response.ok) {
        throw new Error('Failed to send email');
      }

      const data = await response.json();

      toast({
        title: 'Email sent successfully',
        description: `Email was sent to ${recipientEmail}`,
      });
    } catch (error) {
      console.error('Error sending email:', error);
      toast({
        title: 'Failed to send email',
        description: error instanceof Error ? error.message : 'An error occurred',
        variant: 'destructive',
      });
    } finally {
      setIsSending(false);
    }
  };

  const handleSavePreferences = async () => {
    try {
      const response = await apiRequest('POST', '/api/notifications/preferences', {
        preferences: notificationPreferences,
      });

      if (!response.ok) {
        throw new Error('Failed to save preferences');
      }

      toast({
        title: 'Preferences saved',
        description: 'Your notification preferences have been updated',
      });
    } catch (error) {
      console.error('Error saving preferences:', error);
      toast({
        title: 'Failed to save preferences',
        description: error instanceof Error ? error.message : 'An error occurred',
        variant: 'destructive',
      });
    }
  };

  return (
    <MainLayout user={user} isLoading={isLoading}>
      <div className="container py-10">
        <h1 className="text-3xl font-bold mb-8">Email & Notification Settings</h1>

        <Tabs defaultValue="preferences">
          <TabsList className="mb-6">
            <TabsTrigger value="preferences">Notification Preferences</TabsTrigger>
            <TabsTrigger value="test-email">Test Email Service</TabsTrigger>
          </TabsList>

          <TabsContent value="preferences">
            <Card>
              <CardHeader>
                <CardTitle>Notification Preferences</CardTitle>
                <CardDescription>
                  Configure which notifications you want to receive and how you want to receive them.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="grid grid-cols-12 gap-4 font-medium text-sm border-b pb-2">
                    <div className="col-span-6">Notification Type</div>
                    <div className="col-span-3 text-center">Enabled</div>
                    <div className="col-span-3 text-center">Email</div>
                  </div>

                  {/* Course Published */}
                  <div className="grid grid-cols-12 gap-4 items-center">
                    <div className="col-span-6">
                      <p className="font-medium">Course Published</p>
                      <p className="text-sm text-muted-foreground">Notifications when your courses are published</p>
                    </div>
                    <div className="col-span-3 flex justify-center">
                      <Checkbox 
                        checked={notificationPreferences.coursePublished.enabled}
                        onCheckedChange={(checked) => setNotificationPreferences(prev => ({
                          ...prev, 
                          coursePublished: { ...prev.coursePublished, enabled: !!checked }
                        }))}
                      />
                    </div>
                    <div className="col-span-3 flex justify-center">
                      <Checkbox 
                        checked={notificationPreferences.coursePublished.email}
                        onCheckedChange={(checked) => setNotificationPreferences(prev => ({
                          ...prev, 
                          coursePublished: { ...prev.coursePublished, email: !!checked }
                        }))}
                        disabled={!notificationPreferences.coursePublished.enabled}
                      />
                    </div>
                  </div>

                  {/* Course Completed */}
                  <div className="grid grid-cols-12 gap-4 items-center">
                    <div className="col-span-6">
                      <p className="font-medium">Course Completed</p>
                      <p className="text-sm text-muted-foreground">Notifications when you complete a course</p>
                    </div>
                    <div className="col-span-3 flex justify-center">
                      <Checkbox 
                        checked={notificationPreferences.courseCompleted.enabled}
                        onCheckedChange={(checked) => setNotificationPreferences(prev => ({
                          ...prev, 
                          courseCompleted: { ...prev.courseCompleted, enabled: !!checked }
                        }))}
                      />
                    </div>
                    <div className="col-span-3 flex justify-center">
                      <Checkbox 
                        checked={notificationPreferences.courseCompleted.email}
                        onCheckedChange={(checked) => setNotificationPreferences(prev => ({
                          ...prev, 
                          courseCompleted: { ...prev.courseCompleted, email: !!checked }
                        }))}
                        disabled={!notificationPreferences.courseCompleted.enabled}
                      />
                    </div>
                  </div>

                  {/* Team Invite */}
                  <div className="grid grid-cols-12 gap-4 items-center">
                    <div className="col-span-6">
                      <p className="font-medium">Team Invitations</p>
                      <p className="text-sm text-muted-foreground">Notifications for team invitations</p>
                    </div>
                    <div className="col-span-3 flex justify-center">
                      <Checkbox 
                        checked={notificationPreferences.teamInvite.enabled}
                        onCheckedChange={(checked) => setNotificationPreferences(prev => ({
                          ...prev, 
                          teamInvite: { ...prev.teamInvite, enabled: !!checked }
                        }))}
                      />
                    </div>
                    <div className="col-span-3 flex justify-center">
                      <Checkbox 
                        checked={notificationPreferences.teamInvite.email}
                        onCheckedChange={(checked) => setNotificationPreferences(prev => ({
                          ...prev, 
                          teamInvite: { ...prev.teamInvite, email: !!checked }
                        }))}
                        disabled={!notificationPreferences.teamInvite.enabled}
                      />
                    </div>
                  </div>

                  {/* Course Collaboration */}
                  <div className="grid grid-cols-12 gap-4 items-center">
                    <div className="col-span-6">
                      <p className="font-medium">Course Collaboration</p>
                      <p className="text-sm text-muted-foreground">Notifications for course collaboration invites</p>
                    </div>
                    <div className="col-span-3 flex justify-center">
                      <Checkbox 
                        checked={notificationPreferences.courseCollaboration.enabled}
                        onCheckedChange={(checked) => setNotificationPreferences(prev => ({
                          ...prev, 
                          courseCollaboration: { ...prev.courseCollaboration, enabled: !!checked }
                        }))}
                      />
                    </div>
                    <div className="col-span-3 flex justify-center">
                      <Checkbox 
                        checked={notificationPreferences.courseCollaboration.email}
                        onCheckedChange={(checked) => setNotificationPreferences(prev => ({
                          ...prev, 
                          courseCollaboration: { ...prev.courseCollaboration, email: !!checked }
                        }))}
                        disabled={!notificationPreferences.courseCollaboration.enabled}
                      />
                    </div>
                  </div>

                  {/* AI Generation Complete */}
                  <div className="grid grid-cols-12 gap-4 items-center">
                    <div className="col-span-6">
                      <p className="font-medium">AI Generation Complete</p>
                      <p className="text-sm text-muted-foreground">Notifications when AI content is generated</p>
                    </div>
                    <div className="col-span-3 flex justify-center">
                      <Checkbox 
                        checked={notificationPreferences.aiGenerationComplete.enabled}
                        onCheckedChange={(checked) => setNotificationPreferences(prev => ({
                          ...prev, 
                          aiGenerationComplete: { ...prev.aiGenerationComplete, enabled: !!checked }
                        }))}
                      />
                    </div>
                    <div className="col-span-3 flex justify-center">
                      <Checkbox 
                        checked={notificationPreferences.aiGenerationComplete.email}
                        onCheckedChange={(checked) => setNotificationPreferences(prev => ({
                          ...prev, 
                          aiGenerationComplete: { ...prev.aiGenerationComplete, email: !!checked }
                        }))}
                        disabled={!notificationPreferences.aiGenerationComplete.enabled}
                      />
                    </div>
                  </div>

                  {/* System Updates */}
                  <div className="grid grid-cols-12 gap-4 items-center">
                    <div className="col-span-6">
                      <p className="font-medium">System Updates</p>
                      <p className="text-sm text-muted-foreground">Important platform updates and announcements</p>
                    </div>
                    <div className="col-span-3 flex justify-center">
                      <Checkbox 
                        checked={notificationPreferences.systemUpdate.enabled}
                        onCheckedChange={(checked) => setNotificationPreferences(prev => ({
                          ...prev, 
                          systemUpdate: { ...prev.systemUpdate, enabled: !!checked }
                        }))}
                      />
                    </div>
                    <div className="col-span-3 flex justify-center">
                      <Checkbox 
                        checked={notificationPreferences.systemUpdate.email}
                        onCheckedChange={(checked) => setNotificationPreferences(prev => ({
                          ...prev, 
                          systemUpdate: { ...prev.systemUpdate, email: !!checked }
                        }))}
                        disabled={!notificationPreferences.systemUpdate.enabled}
                      />
                    </div>
                  </div>

                  {/* Subscription Expiring */}
                  <div className="grid grid-cols-12 gap-4 items-center">
                    <div className="col-span-6">
                      <p className="font-medium">Subscription Expiring</p>
                      <p className="text-sm text-muted-foreground">Notifications when your subscription is about to expire</p>
                    </div>
                    <div className="col-span-3 flex justify-center">
                      <Checkbox 
                        checked={notificationPreferences.subscriptionExpiring.enabled}
                        onCheckedChange={(checked) => setNotificationPreferences(prev => ({
                          ...prev, 
                          subscriptionExpiring: { ...prev.subscriptionExpiring, enabled: !!checked }
                        }))}
                      />
                    </div>
                    <div className="col-span-3 flex justify-center">
                      <Checkbox 
                        checked={notificationPreferences.subscriptionExpiring.email}
                        onCheckedChange={(checked) => setNotificationPreferences(prev => ({
                          ...prev, 
                          subscriptionExpiring: { ...prev.subscriptionExpiring, email: !!checked }
                        }))}
                        disabled={!notificationPreferences.subscriptionExpiring.enabled}
                      />
                    </div>
                  </div>

                  {/* Low Credits */}
                  <div className="grid grid-cols-12 gap-4 items-center">
                    <div className="col-span-6">
                      <p className="font-medium">Low AI Credits</p>
                      <p className="text-sm text-muted-foreground">Notifications when your AI credits are running low</p>
                    </div>
                    <div className="col-span-3 flex justify-center">
                      <Checkbox 
                        checked={notificationPreferences.creditLow.enabled}
                        onCheckedChange={(checked) => setNotificationPreferences(prev => ({
                          ...prev, 
                          creditLow: { ...prev.creditLow, enabled: !!checked }
                        }))}
                      />
                    </div>
                    <div className="col-span-3 flex justify-center">
                      <Checkbox 
                        checked={notificationPreferences.creditLow.email}
                        onCheckedChange={(checked) => setNotificationPreferences(prev => ({
                          ...prev, 
                          creditLow: { ...prev.creditLow, email: !!checked }
                        }))}
                        disabled={!notificationPreferences.creditLow.enabled}
                      />
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <Button onClick={handleSavePreferences}>Save Preferences</Button>
              </CardFooter>
            </Card>
          </TabsContent>

          <TabsContent value="test-email">
            <Card>
              <CardHeader>
                <CardTitle>Test Email Service</CardTitle>
                <CardDescription>
                  Send a test email to verify your email service configuration.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="space-y-2">
                    <Label htmlFor="emailType">Email Type</Label>
                    <Select
                      value={emailType}
                      onValueChange={setEmailType}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select email type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="welcome">Welcome Email</SelectItem>
                        <SelectItem value="password_reset">Password Reset</SelectItem>
                        <SelectItem value="course_collaboration_invite">Course Collaboration Invite</SelectItem>
                        <SelectItem value="team_invite">Team Invite</SelectItem>
                        <SelectItem value="course_published">Course Published</SelectItem>
                        <SelectItem value="course_completed">Course Completed</SelectItem>
                        <SelectItem value="subscription_expiring">Subscription Expiring</SelectItem>
                        <SelectItem value="ai_generation_complete">AI Generation Complete</SelectItem>
                        <SelectItem value="credits_low">Credits Low</SelectItem>
                        <SelectItem value="custom_email">Custom Email</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="recipientEmail">Recipient Email</Label>
                    <Input
                      id="recipientEmail"
                      placeholder="<EMAIL>"
                      value={recipientEmail}
                      onChange={(e) => setRecipientEmail(e.target.value)}
                    />
                  </div>

                  {emailType === 'custom_email' && (
                    <>
                      <div className="space-y-2">
                        <Label htmlFor="subject">Subject</Label>
                        <Input
                          id="subject"
                          placeholder="Email subject"
                          value={subject}
                          onChange={(e) => setSubject(e.target.value)}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="body">Email Body (HTML)</Label>
                        <Textarea
                          id="body"
                          placeholder="<h1>Hello!</h1><p>This is a test email.</p>"
                          value={body}
                          onChange={(e) => setBody(e.target.value)}
                          className="min-h-[200px] font-mono"
                        />
                      </div>
                    </>
                  )}
                </div>
              </CardContent>
              <CardFooter>
                <Button onClick={handleSendTestEmail} disabled={isSending}>
                  {isSending ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Sending...
                    </>
                  ) : (
                    <>
                      <Send className="mr-2 h-4 w-4" />
                      Send Test Email
                    </>
                  )}
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </MainLayout>
  );
}
