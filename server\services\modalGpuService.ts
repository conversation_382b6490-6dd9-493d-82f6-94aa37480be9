/**
 * Modal A100 GPU Service Integration
 * Provides HTTP API integration with Modal A100 GPU backend
 */

import axios, { AxiosResponse } from 'axios';
import { LocalGpuFallback } from './localGpuFallback.js';

interface ModalGpuConfig {
  baseUrl: string;
  timeout: number;
  retries: number;
}

interface GpuHealthStatus {
  status: string;
  gpu_available: boolean;
  gpu_count: number;
  gpu_name: string;
  gpu_memory_total_gb: number;
  gpu_memory_free_gb: number;
  timestamp: number;
}

interface ImageGenerationRequest {
  prompt: string;
  width?: number;
  height?: number;
  num_inference_steps?: number;
}

interface ImageGenerationResponse {
  status: string;
  prompt: string;
  image_base64: string;
  dimensions: string;
  inference_steps: number;
  timestamp: number;
  error?: string;
}

interface TextAnalysisRequest {
  text: string;
}

interface TextAnalysisResponse {
  status: string;
  text: string;
  sentiment: {
    label: string;
    score: number;
  };
  error?: string;
}

interface AvatarCourseRequest {
  course_title: string;
  lesson_scripts: Array<{ title: string; script: string }>;
  avatar_image_base64: string;
  voice_preset?: string;
}

interface AvatarCourseResponse {
  status: string;
  course_title: string;
  total_lessons: number;
  lessons: Array<{
    lesson_id: number;
    title: string;
    tts_status: string;
    video_status: string;
    audio_base64?: string;
    video_base64?: string;
  }>;
  slides?: {
    status: string;
    slides_base64?: string;
  };
  gpu_used: boolean;
  timestamp: number;
}

export class ModalGpuService {
  private config: ModalGpuConfig;
  private isAvailable: boolean = false;
  private lastHealthCheck: number = 0;
  private healthCheckInterval: number = 300000; // 5 minutes
  private localFallback: LocalGpuFallback;

  constructor(baseUrl?: string) {
    this.config = {
      baseUrl: baseUrl || process.env.MODAL_GPU_BASE_URL || 'https://courseai-a100-gpu.modal.run',
      timeout: 60000, // 60 seconds
      retries: 3
    };
    
    this.localFallback = new LocalGpuFallback();
    
    // Initial health check (delayed to avoid blocking constructor)
    setTimeout(() => this.checkHealth(), 1000);
  }

  /**
   * Check if Modal A100 GPU service is available
   */
  async checkHealth(): Promise<GpuHealthStatus | null> {
    try {
      const response: AxiosResponse<GpuHealthStatus> = await axios.get(
        `${this.config.baseUrl}/health`,
        {
          timeout: this.config.timeout,
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );

      this.isAvailable = response.data.status === 'online';
      this.lastHealthCheck = Date.now();
      
      console.log('Modal A100 GPU service health check:', {
        available: this.isAvailable,
        gpu_status: response.data
      });

      return response.data;
    } catch (error: any) {
      this.isAvailable = false;
      
      // Enhanced error logging for debugging
      if (error.response?.status === 404) {
        console.error('Modal A100 GPU service not found (404). Check deployment and credentials.');
      } else if (error.response?.status === 401 || error.response?.status === 403) {
        console.error('Modal A100 GPU service authentication failed. Check API credentials.');
      } else if (error.code === 'ECONNREFUSED') {
        console.error('Modal A100 GPU service connection refused. Service may not be running.');
      } else {
        console.warn('Modal A100 GPU service unavailable:', error.message);
      }
      
      return null;
    }
  }

  /**
   * Test GPU connectivity with echo function
   */
  async testEcho(message: string = "Hello from Course AI Platform"): Promise<any> {
    if (!this.isHealthy()) {
      throw new Error('Modal A100 GPU service is not available');
    }

    try {
      const response = await axios.post(
        `${this.config.baseUrl}/api_echo`,
        { message },
        {
          timeout: this.config.timeout,
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );

      return response.data;
    } catch (error: any) {
      console.error('Modal GPU echo test failed:', error.message);
      throw new Error(`GPU echo test failed: ${error.message}`);
    }
  }

  /**
   * Generate images using A100 GPU acceleration
   */
  async generateImage(request: ImageGenerationRequest): Promise<ImageGenerationResponse> {
    if (!this.isHealthy()) {
      throw new Error('Modal A100 GPU service is not available');
    }

    try {
      const response: AxiosResponse<ImageGenerationResponse> = await axios.post(
        `${this.config.baseUrl}/api_generate_image`,
        {
          prompt: request.prompt,
          width: request.width || 512,
          height: request.height || 512,
          num_inference_steps: request.num_inference_steps || 20
        },
        {
          timeout: this.config.timeout * 2, // Longer timeout for image generation
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );

      if (response.data.status !== 'success') {
        throw new Error(response.data.error || 'Image generation failed');
      }

      return response.data;
    } catch (error: any) {
      console.error('Modal GPU image generation failed:', error.message);
      throw new Error(`Image generation failed: ${error.message}`);
    }
  }

  /**
   * Analyze text using A100 GPU acceleration
   */
  async analyzeText(request: TextAnalysisRequest): Promise<TextAnalysisResponse> {
    if (!this.isHealthy()) {
      throw new Error('Modal A100 GPU service is not available');
    }

    try {
      const response: AxiosResponse<TextAnalysisResponse> = await axios.post(
        `${this.config.baseUrl}/api_analyze_text`,
        { text: request.text },
        {
          timeout: this.config.timeout,
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );

      if (response.data.status !== 'success') {
        throw new Error(response.data.error || 'Text analysis failed');
      }

      return response.data;
    } catch (error: any) {
      console.error('Modal GPU text analysis failed:', error.message);
      throw new Error(`Text analysis failed: ${error.message}`);
    }
  }

  /**
   * Check if service is healthy and available
   */
  isHealthy(): boolean {
    const now = Date.now();
    
    // Refresh health check if needed
    if (now - this.lastHealthCheck > this.healthCheckInterval) {
      this.checkHealth().catch(() => {
        // Silent fail for background health checks
      });
    }

    return this.isAvailable;
  }

  /**
   * Get service configuration
   */
  getConfig(): ModalGpuConfig {
    return { ...this.config };
  }

  /**
   * Update service base URL
   */
  updateBaseUrl(baseUrl: string): void {
    this.config.baseUrl = baseUrl;
    this.isAvailable = false;
    this.checkHealth();
  }

  /**
   * Generate Avatar Course using A100 GPU or local fallback
   */
  async generateAvatarCourse(request: AvatarCourseRequest): Promise<AvatarCourseResponse> {
    try {
      // Try A100 GPU first if available
      if (this.isAvailable) {
        console.log('Using A100 GPU for Avatar Course generation');
        
        const response = await axios.post(
          `${this.config.baseUrl}/avatar-course`,
          request,
          {
            timeout: 300000, // 5 minutes for complete course
            headers: {
              'Content-Type': 'application/json'
            }
          }
        );

        return {
          ...response.data,
          gpu_used: true,
          timestamp: Date.now()
        };
      }
    } catch (error) {
      console.log('A100 GPU unavailable, using local fallback for Avatar Course generation');
    }

    // Use local fallback
    const result = await this.localFallback.processAvatarCourse(
      request.course_title,
      request.lesson_scripts,
      request.avatar_image_base64,
      request.voice_preset
    );

    // Transform local fallback result to match AvatarCourseResponse interface
    const avatarCourseResult: AvatarCourseResponse = {
      status: result.status === 'error' ? 'error' : 'success',
      course_title: request.course_title,
      total_lessons: request.lesson_scripts.length,
      lessons: request.lesson_scripts.map((script, index) => ({
        lesson_id: index + 1,
        title: script.title,
        tts_status: result.status === 'success' ? 'completed' : 'error',
        video_status: result.status === 'success' ? 'completed' : 'error',
        audio_base64: result.status === 'success' ? 'generated' : undefined,
        video_base64: result.status === 'success' ? 'generated' : undefined
      })),
      slides: result.status === 'success' ? {
        status: 'completed',
        slides_base64: 'generated'
      } : undefined,
      gpu_used: false,
      timestamp: Date.now()
    };

    return avatarCourseResult;
  }

  /**
   * Generate high-quality TTS
   */
  async generateTts(text: string, voicePreset: string = 'en-US-AriaNeural') {
    try {
      if (this.isAvailable) {
        const response = await axios.post(
          `${this.config.baseUrl}/tts`,
          { text, voice_preset: voicePreset },
          {
            timeout: 60000,
            headers: { 'Content-Type': 'application/json' }
          }
        );
        return { ...response.data, gpu_used: true };
      }
    } catch (error) {
      console.log('A100 GPU unavailable for TTS, using local fallback');
    }

    const result = await this.localFallback.generateTts(text, voicePreset);
    return { ...result, gpu_used: false };
  }

  /**
   * Generate avatar video
   */
  async generateAvatarVideo(imageBase64: string, audioBase64: string) {
    try {
      if (this.isAvailable) {
        const response = await axios.post(
          `${this.config.baseUrl}/sadtalker`,
          { image_base64: imageBase64, audio_base64: audioBase64 },
          {
            timeout: 180000, // 3 minutes
            headers: { 'Content-Type': 'application/json' }
          }
        );
        return { ...response.data, gpu_used: true };
      }
    } catch (error) {
      console.log('A100 GPU unavailable for video generation, using local fallback');
    }

    const result = await this.localFallback.generateAvatarVideo(imageBase64, audioBase64);
    return { ...result, gpu_used: false };
  }

  /**
   * Generate presentation slides
   */
  async generateSlides(markdownContent: string, theme: string = 'default', format: string = 'pdf') {
    try {
      if (this.isAvailable) {
        const response = await axios.post(
          `${this.config.baseUrl}/slides`,
          { markdown_content: markdownContent, theme, output_format: format },
          {
            timeout: 120000,
            headers: { 'Content-Type': 'application/json' }
          }
        );
        return { ...response.data, gpu_used: true };
      }
    } catch (error) {
      console.log('A100 GPU unavailable for slide generation, using local fallback');
    }

    const result = await this.localFallback.generateSlides(markdownContent, theme, format);
    return { ...result, gpu_used: false };
  }
}

// Export singleton instance
export const modalGpuService = new ModalGpuService();