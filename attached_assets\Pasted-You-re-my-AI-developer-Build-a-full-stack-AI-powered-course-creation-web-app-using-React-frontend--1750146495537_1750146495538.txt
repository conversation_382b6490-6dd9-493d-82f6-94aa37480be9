You're my AI developer. Build a full-stack AI-powered course creation web app using <PERSON><PERSON> (frontend) and Python with Modal (backend). This app must allow users to generate video courses using AI models like SDXL, SadTalker, TTS, and slide generation.

🎯 Your goals:
1. Backend using Modal with GPU support (A100 and A10)
2. Frontend using React, styled cleanly with Tailwind
3. Connect both with working API calls
4. Use async and batching where possible
5. Structure code for deployment on Replit

---

🔧 Backend Requirements (Python + Modal):

Create a Python app (`modal_app.py`) with the following Modal-powered functions:

✅ 1. Image Generation (Stable Diffusion XL)
- GPU: A100
- Endpoint: `/gen_image`
- Input: `{"prompt": "..."}` → returns PNG image bytes

✅ 2. Talking Video Generation (SadTalker)
- GPU: A100
- Endpoint: `/gen_video`
- Input: `image`, `audio` (multipart/form-data)
- Output: Talking video (.mp4) with voice synced to image

✅ 3. Chatterbox TTS
- GPU: A10
- Endpoint: `/tts_chatter`
- Input: `{"text": "..."}` → returns audio (.wav)

✅ 4. Coqui TTS
- GPU: A10
- Endpoint: `/tts_coqui`
- Input: `{"text": "..."}` → returns audio (.wav)

✅ 5. Marp Slides Generator
- CPU-only
- Endpoint: `/slides`
- Input: `{"markdown": "..."}` → returns PDF bytes of rendered slides

📦 Use caching to avoid repeating the same TTS/image generations. Use batching where applicable (e.g., TTS).

---

🎨 Frontend Requirements (React + TailwindCSS):

Create a React app with the following:

✅ 1. Upload + Prompt UI
- User uploads image + audio
- Inputs text prompts for TTS and image gen
- Inputs markdown for slides

✅ 2. Endpoint calls using Axios
- Connect to Modal backend
- Upload files using `multipart/form-data`
- Display returned images, video, audio, and PDFs

✅ 3. Layout
- Tabs or sections for each tool (Image → Video → TTS → Slides)
- Clean layout using Tailwind
- Loading states while waiting for generation

---

🧰 Helpers:

1. Use `ffmpeg` for merging audio and video
2. Use `pydub` for audio re-encoding
3. Use Modal Volumes or hashes to cache repeated prompts
4. Use `.remote()` properly and support Modal `@modal.web_endpoint`

---

📂 Folder structure suggestion:
```

course-ai-app/
│
├── backend/
│ └── modal\_app.py
│
├── frontend/
│ ├── public/
│ ├── src/
│ │ ├── App.jsx
│ │ └── api.js
│ └── tailwind.config.js
│
├── replit.nix # to include Node, Python, ffmpeg
└── README.md

```

---

📦 Replit Notes:
- Add `replit.nix` with ffmpeg, Python 3.12, and Node.js
- Set up `.env` if needed for API keys (like HuggingFace)
- Host both backend and frontend in same Replit workspace

---

🤖 Final Goal:
Deploy a full working Course AI web app on Replit with endpoints running on Modal, and a frontend React interface to generate images, voices, slides, and avatar videos.