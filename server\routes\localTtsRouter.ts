
import express, { Request, Response } from 'express';
import { localCoquiTTS } from '../services/localCoquiTTS';

const router = express.Router();

// Initialize local TTS service
router.post('/initialize', async (req: Request, res: Response) => {
  try {
    const success = await localCoquiTTS.initialize();
    
    if (success) {
      return res.status(200).json({
        message: 'Local Coqui TTS initialized successfully',
        isReady: localCoquiTTS.isReady(),
        availableModels: localCoquiTTS.getAvailableModels(),
        availableVoices: localCoquiTTS.getAvailableVoices()
      });
    } else {
      return res.status(500).json({
        error: 'Failed to initialize local Coqui TTS'
      });
    }
  } catch (error) {
    console.error('Error initializing local TTS:', error);
    return res.status(500).json({
      error: 'Failed to initialize local TTS service'
    });
  }
});

// Get service status
router.get('/status', (req: Request, res: Response) => {
  return res.status(200).json({
    isReady: localCoquiTTS.isReady(),
    availableModels: localCoquiTTS.getAvailableModels(),
    availableVoices: localCoquiTTS.getAvailableVoices()
  });
});

// Download a specific model
router.post('/download-model', async (req: Request, res: Response) => {
  try {
    const { modelId } = req.body;
    
    if (!modelId) {
      return res.status(400).json({ error: 'Model ID is required' });
    }
    
    const success = await localCoquiTTS.downloadModel(modelId);
    
    if (success) {
      return res.status(200).json({
        message: `Model ${modelId} downloaded successfully`
      });
    } else {
      return res.status(500).json({
        error: `Failed to download model ${modelId}`
      });
    }
  } catch (error) {
    console.error('Error downloading model:', error);
    return res.status(500).json({
      error: 'Failed to download model'
    });
  }
});

// Set active model
router.post('/set-model', (req: Request, res: Response) => {
  try {
    const { modelId } = req.body;
    
    if (!modelId) {
      return res.status(400).json({ error: 'Model ID is required' });
    }
    
    const success = localCoquiTTS.setModel(modelId);
    
    if (success) {
      return res.status(200).json({
        message: `Model set to ${modelId}`
      });
    } else {
      return res.status(400).json({
        error: `Invalid model ID: ${modelId}`
      });
    }
  } catch (error) {
    console.error('Error setting model:', error);
    return res.status(500).json({
      error: 'Failed to set model'
    });
  }
});

// Test TTS generation
router.post('/test', async (req: Request, res: Response) => {
  try {
    const { text = 'Hello, this is a test of the local Coqui TTS service!' } = req.body;
    
    if (!localCoquiTTS.isReady()) {
      return res.status(400).json({
        error: 'Local TTS service is not ready. Please initialize first.'
      });
    }
    
    const result = await localCoquiTTS.generateSpeech(text);
    
    return res.status(200).json({
      message: 'TTS test completed successfully',
      audioPath: result.audioPath,
      duration: result.duration
    });
  } catch (error) {
    console.error('Error testing TTS:', error);
    return res.status(500).json({
      error: 'TTS test failed'
    });
  }
});

export default router;
