import React, { useState, useCallback, useRef, useEffect } from 'react';
import { MainLayout } from '@/components/layouts/MainLayout';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { FormField, FormItem, FormLabel, FormControl, FormDescription, FormMessage, Form } from '@/components/ui/form';
import { useToast } from '@/hooks/use-toast';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { apiRequest } from '@/lib/queryClient';
import { useQuery } from '@tanstack/react-query';
import { useAuth } from '@/hooks/use-auth';
import { AIAssistButton } from '@/components/ui/ai-assist-button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter, DialogTrigger, DialogClose } from '@/components/ui/dialog';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { X, Edit, ChevronLeft, Pencil, Eye, Palette, Save, Download, Maximize2, Minimize2 } from 'lucide-react';
import { useAiTextGenerator } from '@/hooks/use-ai-text-generator';

const landingPageSchema = z.object({
  courseId: z.string().min(1, { message: 'Please select a course' }),
  headline: z.string().min(5, { message: 'Headline must be at least 5 characters' }),
  subHeadline: z.string().min(5, { message: 'Sub-headline must be at least 5 characters' }),
  description: z.string().min(20, { message: 'Description must be at least 20 characters' }),
  callToAction: z.string().min(2, { message: 'Call to action text is required' }),
  theme: z.string().min(1, { message: 'Please select a theme' }),
  colorScheme: z.string().default('blue'),
  layoutStyle: z.string().default('classic'),
  bannerImageUrl: z.string().optional(),
  seoTitle: z.string().optional(),
  seoDescription: z.string().optional(),
  seoKeywords: z.string().optional(),
  googleAnalyticsId: z.string().optional(),
  facebookPixelId: z.string().optional(),
  customDomain: z.string().optional(),
});

type LandingPageFormValues = z.infer<typeof landingPageSchema>;

const LandingPageGenerator = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedPreview, setGeneratedPreview] = useState("");
  const [showFullPagePreview, setShowFullPagePreview] = useState(false);
  const [editingSectionId, setEditingSectionId] = useState<string | null>(null);
  const previewContainerRef = useRef<HTMLDivElement>(null);
  const { generateText, isGenerating: isAiGenerating } = useAiTextGenerator();

  // Fetch courses for the user
  const { data: courses, isLoading: isLoadingCourses } = useQuery({
    queryKey: ["/api/courses"],
    enabled: !!user,
  });

  const form = useForm<LandingPageFormValues>({
    resolver: zodResolver(landingPageSchema),
    defaultValues: {
      courseId: '',
      headline: '',
      subHeadline: '',
      description: '',
      callToAction: 'Enroll Now',
      theme: 'professional',
      colorScheme: 'blue',
      layoutStyle: 'classic',
      bannerImageUrl: '',
      seoTitle: '',
      seoDescription: '',
      seoKeywords: '',
      googleAnalyticsId: '',
      facebookPixelId: '',
      customDomain: '',
    },
  });

  // Function to handle in-place editing of landing page content
  const handleEditSection = useCallback((sectionId: string, content: string) => {
    // Update the corresponding form field
    if (sectionId === 'headline') {
      form.setValue('headline', content);
    } else if (sectionId === 'subHeadline') {
      form.setValue('subHeadline', content);
    } else if (sectionId === 'description') {
      form.setValue('description', content);
    } else if (sectionId === 'callToAction') {
      form.setValue('callToAction', content);
    }
    
    // Regenerate the preview with updated content
    generatePreview(form.getValues());
    
    // Close the editing UI
    setEditingSectionId(null);
  }, [form]);

  // Function to handle AI-assisted editing
  const handleAiAssist = useCallback(async (sectionId: string) => {
    const courseId = form.getValues('courseId');
    const selectedCourse = Array.isArray(courses) ? courses.find((c: any) => c.id.toString() === courseId) : null;
    const courseName = selectedCourse?.title || '';
    const currentValues = form.getValues();
    
    // Import types from the AI text generator hook
    type FieldType = 'headline' | 'subheadline' | 'description' | 'callToAction' | 'paragraph';
    type Tone = 'persuasive' | 'professional' | 'friendly' | 'casual' | 'urgent';
    type Length = 'short' | 'medium' | 'long';
    
    let fieldType: FieldType = 'paragraph';
    let context = `Course: ${courseName}`;
    let tone: Tone = 'persuasive';
    let length: Length = 'medium';
    
    if (sectionId === 'headline') {
      fieldType = 'headline';
      length = 'short';
    } else if (sectionId === 'subHeadline') {
      fieldType = 'subheadline';
      context = `Course: ${courseName}, Headline: ${currentValues.headline}`;
      length = 'short';
    } else if (sectionId === 'description') {
      fieldType = 'description';
      context = `Course: ${courseName}, Headline: ${currentValues.headline}, Sub-headline: ${currentValues.subHeadline}`;
      length = 'medium';
    } else if (sectionId === 'callToAction') {
      fieldType = 'callToAction';
      length = 'short';
    }
    
    const generatedText = await generateText({
      fieldType,
      context,
      tone,
      length
    });
    
    if (generatedText) {
      handleEditSection(sectionId, generatedText);
    }
  }, [courses, form, generateText, handleEditSection]);

  // Function to generate preview HTML
  const generatePreview = useCallback((values: LandingPageFormValues) => {
    const selectedTheme = values.theme;
    const selectedColorScheme = values.colorScheme || 'blue';
    const selectedLayout = values.layoutStyle || 'classic';
    const bannerImageUrl = values.bannerImageUrl;
    
    // Theme base styles with fonts and text styles
    const themeColors: Record<string, { bg: string; text: string; fontFamily: string; headerStyle: string }> = {
      professional: { 
        bg: '#f8fafc', 
        text: '#1e293b',
        fontFamily: 'system-ui, -apple-system, sans-serif',
        headerStyle: 'normal'
      },
      creative: { 
        bg: '#fdf4ff', 
        text: '#581c87',
        fontFamily: 'Georgia, serif',
        headerStyle: 'italic'
      },
      minimalist: { 
        bg: '#ffffff', 
        text: '#404040',
        fontFamily: 'Inter, system-ui, sans-serif',
        headerStyle: 'normal'
      },
      bold: { 
        bg: '#1a1a1a', 
        text: '#f8fafc',
        fontFamily: 'Poppins, system-ui, sans-serif',
        headerStyle: 'normal'
      },
      elegant: { 
        bg: '#f8f5ff', 
        text: '#1e1b4b',
        fontFamily: 'Playfair Display, serif',
        headerStyle: 'normal'
      },
    };
    
    // Enhanced color schemes with secondary colors
    const colorSchemes: Record<string, { primary: string; secondary: string; buttonText: string }> = {
      blue: { primary: '#3b82f6', secondary: '#dbeafe', buttonText: '#ffffff' },
      purple: { primary: '#8b5cf6', secondary: '#ede9fe', buttonText: '#ffffff' },
      green: { primary: '#10b981', secondary: '#d1fae5', buttonText: '#ffffff' },
      amber: { primary: '#f59e0b', secondary: '#fef3c7', buttonText: '#ffffff' },
      rose: { primary: '#e11d48', secondary: '#ffe4e6', buttonText: '#ffffff' }
    };
    
    const baseTheme = themeColors[selectedTheme] || themeColors.professional;
    const colorScheme = colorSchemes[selectedColorScheme] || colorSchemes.blue;
    
    const theme = {
      bg: baseTheme.bg,
      text: baseTheme.text,
      accent: colorScheme.primary,
      secondary: colorScheme.secondary,
      buttonText: colorScheme.buttonText,
      fontFamily: baseTheme.fontFamily,
      headerStyle: baseTheme.headerStyle
    };
    
    // Generate different layout based on selection
    let layoutHtml = '';
    
    // Banner image HTML - show if provided, otherwise hide
    const bannerHtml = bannerImageUrl 
      ? `<div style="width: 100%; height: 250px; margin-bottom: 2rem; overflow: hidden; border-radius: 8px;">
          <img src="${bannerImageUrl}" style="width: 100%; height: 100%; object-fit: cover;" alt="Banner image" />
        </div>`
      : '';
    
    // Generate different layouts based on selection
    if (selectedLayout === 'classic') {
      layoutHtml = `
        ${bannerHtml}
        <header style="text-align: center; margin-bottom: 2rem;">
          <h1 data-section-id="headline" style="font-size: 2.5rem; font-weight: bold; margin-bottom: 0.5rem; color: ${theme.accent}; font-style: ${theme.headerStyle};">${values.headline}</h1>
          <p data-section-id="subHeadline" style="font-size: 1.25rem; opacity: 0.9;">${values.subHeadline}</p>
        </header>
        
        <div style="display: flex; gap: 2rem; margin-bottom: 2rem;">
          <div style="flex: 1;">
            <div style="height: 250px; background-color: rgba(0,0,0,0.05); border-radius: 8px; display: flex; align-items: center; justify-content: center; font-size: 0.875rem; color: rgba(0,0,0,0.5);">Course Preview Image</div>
          </div>
          <div style="flex: 1;">
            <h2 style="font-size: 1.5rem; font-weight: bold; margin-bottom: 1rem; color: ${theme.accent};">About This Course</h2>
            <p data-section-id="description" style="margin-bottom: 1.5rem; line-height: 1.6;">${values.description}</p>
            <button data-section-id="callToAction" style="background-color: ${theme.accent}; color: ${theme.buttonText}; border: none; padding: 0.75rem 1.5rem; border-radius: 4px; font-weight: bold; cursor: pointer;">${values.callToAction}</button>
          </div>
        </div>
      `;
    } else if (selectedLayout === 'split') {
      layoutHtml = `
        ${bannerHtml}
        <div style="display: flex; flex-direction: row; gap: 2rem; margin-bottom: 2rem;">
          <div style="flex: 1;">
            <header style="margin-bottom: 2rem;">
              <h1 data-section-id="headline" style="font-size: 2.5rem; font-weight: bold; margin-bottom: 0.5rem; color: ${theme.accent}; font-style: ${theme.headerStyle};">${values.headline}</h1>
              <p data-section-id="subHeadline" style="font-size: 1.25rem; opacity: 0.9;">${values.subHeadline}</p>
            </header>
            
            <h2 style="font-size: 1.5rem; font-weight: bold; margin-bottom: 1rem; color: ${theme.accent};">About This Course</h2>
            <p data-section-id="description" style="margin-bottom: 1.5rem; line-height: 1.6;">${values.description}</p>
            <button data-section-id="callToAction" style="background-color: ${theme.accent}; color: ${theme.buttonText}; border: none; padding: 0.75rem 1.5rem; border-radius: 4px; font-weight: bold; cursor: pointer;">${values.callToAction}</button>
          </div>
          
          <div style="flex: 1;">
            <div style="background-color: ${theme.secondary}; padding: 1.5rem; border-radius: 8px; height: 100%;">
              <h2 style="font-size: 1.5rem; font-weight: bold; margin-bottom: 1rem; color: ${theme.accent};">What You'll Learn</h2>
              <ul style="list-style: none; padding: 0; margin: 0; display: flex; flex-direction: column; gap: 1rem;">
                <li style="padding: 0.75rem; border-radius: 4px; border-left: 3px solid ${theme.accent}; background-color: rgba(255,255,255,0.7);">
                  Introduction to the fundamentals
                </li>
                <li style="padding: 0.75rem; border-radius: 4px; border-left: 3px solid ${theme.accent}; background-color: rgba(255,255,255,0.7);">
                  Building your first project
                </li>
                <li style="padding: 0.75rem; border-radius: 4px; border-left: 3px solid ${theme.accent}; background-color: rgba(255,255,255,0.7);">
                  Advanced techniques and best practices
                </li>
                <li style="padding: 0.75rem; border-radius: 4px; border-left: 3px solid ${theme.accent}; background-color: rgba(255,255,255,0.7);">
                  Real-world application and case studies
                </li>
              </ul>
            </div>
          </div>
        </div>
      `;
    } else if (selectedLayout === 'grid') {
      layoutHtml = `
        ${bannerHtml}
        <header style="text-align: center; margin-bottom: 2rem;">
          <h1 data-section-id="headline" style="font-size: 2.5rem; font-weight: bold; margin-bottom: 0.5rem; color: ${theme.accent}; font-style: ${theme.headerStyle};">${values.headline}</h1>
          <p data-section-id="subHeadline" style="font-size: 1.25rem; opacity: 0.9;">${values.subHeadline}</p>
        </header>
        
        <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 1.5rem; margin-bottom: 2rem;">
          <div style="background-color: ${theme.secondary}; padding: 1.5rem; border-radius: 8px; display: flex; flex-direction: column; align-items: center; text-align: center;">
            <div style="width: 50px; height: 50px; border-radius: 50%; background-color: ${theme.accent}; margin-bottom: 1rem; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">1</div>
            <h3 style="font-weight: bold; margin-bottom: 0.5rem;">Learn</h3>
            <p style="font-size: 0.875rem; opacity: 0.8;">Access comprehensive course materials and resources</p>
          </div>
          <div style="background-color: ${theme.secondary}; padding: 1.5rem; border-radius: 8px; display: flex; flex-direction: column; align-items: center; text-align: center;">
            <div style="width: 50px; height: 50px; border-radius: 50%; background-color: ${theme.accent}; margin-bottom: 1rem; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">2</div>
            <h3 style="font-weight: bold; margin-bottom: 0.5rem;">Practice</h3>
            <p style="font-size: 0.875rem; opacity: 0.8;">Apply your knowledge with hands-on exercises</p>
          </div>
          <div style="background-color: ${theme.secondary}; padding: 1.5rem; border-radius: 8px; display: flex; flex-direction: column; align-items: center; text-align: center;">
            <div style="width: 50px; height: 50px; border-radius: 50%; background-color: ${theme.accent}; margin-bottom: 1rem; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">3</div>
            <h3 style="font-weight: bold; margin-bottom: 0.5rem;">Master</h3>
            <p style="font-size: 0.875rem; opacity: 0.8;">Become proficient through real-world projects</p>
          </div>
        </div>
      `;
    }
    
    // Standard features section for all layouts
    const featuresHtml = `
      <div style="margin-bottom: 2rem;">
        <h2 style="font-size: 1.5rem; font-weight: bold; margin-bottom: 1rem; color: ${theme.accent};">What You'll Learn</h2>
        <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 1rem;">
          <div style="background-color: ${theme.secondary}; padding: 1rem; border-radius: 4px;">
            <h3 style="font-weight: bold; margin-bottom: 0.5rem;">Comprehensive Curriculum</h3>
            <p>Master all essential concepts with our carefully structured lessons.</p>
          </div>
          <div style="background-color: ${theme.secondary}; padding: 1rem; border-radius: 4px;">
            <h3 style="font-weight: bold; margin-bottom: 0.5rem;">Expert Instruction</h3>
            <p>Learn from industry professionals with years of experience.</p>
          </div>
          <div style="background-color: ${theme.secondary}; padding: 1rem; border-radius: 4px;">
            <h3 style="font-weight: bold; margin-bottom: 0.5rem;">Practical Projects</h3>
            <p>Apply your knowledge through hands-on exercises and real-world projects.</p>
          </div>
          <div style="background-color: ${theme.secondary}; padding: 1rem; border-radius: 4px;">
            <h3 style="font-weight: bold; margin-bottom: 0.5rem;">Community Support</h3>
            <p>Join a community of like-minded learners for collaboration and networking.</p>
          </div>
        </div>
      </div>
    `;
    
    // Only add features section if not in grid layout (which already has features)
    const contentHtml = selectedLayout !== 'grid' 
      ? `${layoutHtml}${featuresHtml}`
      : `${layoutHtml}
         <div style="margin-bottom: 2rem;">
           <h2 style="font-size: 1.5rem; font-weight: bold; margin-bottom: 1rem; color: ${theme.accent};">About This Course</h2>
           <p data-section-id="description" style="margin-bottom: 1.5rem; line-height: 1.6;">${values.description}</p>
         </div>`;
    
    // Add SEO meta tags in comments
    let seoComments = '';
    if (values.seoTitle || values.seoDescription || values.seoKeywords) {
      seoComments = `
        <!-- 
        SEO Meta Tags:
        Title: ${values.seoTitle || values.headline}
        Description: ${values.seoDescription || values.subHeadline}
        Keywords: ${values.seoKeywords || ''}
        -->
      `;
    }
    
    // Add tracking code placeholders in comments
    let analyticsComments = '';
    if (values.googleAnalyticsId) {
      analyticsComments += `<!-- Google Analytics ID: ${values.googleAnalyticsId} -->`;
    }
    if (values.facebookPixelId) {
      analyticsComments += `<!-- Facebook Pixel ID: ${values.facebookPixelId} -->`;
    }
    
    const mockHtml = `
      ${seoComments}
      ${analyticsComments}
      <div style="font-family: ${theme.fontFamily}; max-width: 800px; margin: 0 auto; padding: 2rem; background-color: ${theme.bg}; color: ${theme.text}; border-radius: 8px;">
        ${contentHtml}
        
        <div style="text-align: center; margin-top: 2rem; margin-bottom: 2rem;">
          <button data-section-id="callToAction" style="background-color: ${theme.accent}; color: ${theme.buttonText}; border: none; padding: 0.75rem 1.5rem; border-radius: 4px; font-weight: bold; cursor: pointer; font-size: 1.125rem;">${values.callToAction}</button>
        </div>
        
        <footer style="text-align: center; padding-top: 1rem; border-top: 1px solid rgba(0,0,0,0.1); font-size: 0.875rem; opacity: 0.7;">
          <p>© ${new Date().getFullYear()} Course Creator. All rights reserved.</p>
          ${values.customDomain ? `<p>Available at: ${values.customDomain}</p>` : ''}
        </footer>
      </div>
    `;
    
    setGeneratedPreview(mockHtml);
    return mockHtml;
  }, []);

  // Add an effect to set up event listeners for the preview
  useEffect(() => {
    if (showFullPagePreview && previewContainerRef.current) {
      const container = previewContainerRef.current;
      
      // Find all interactive elements
      const editableElements = container.querySelectorAll('[data-section-id]');
      
      // Add click event listeners to handle editing
      const clickHandler = (e: Event) => {
        const target = e.target as HTMLElement;
        const sectionId = target.getAttribute('data-section-id');
        if (sectionId) {
          setEditingSectionId(sectionId);
          e.preventDefault();
          e.stopPropagation();
        }
      };
      
      editableElements.forEach(el => {
        el.addEventListener('click', clickHandler as EventListener);
      });
      
      // Clean up event listeners
      return () => {
        editableElements.forEach(el => {
          el.removeEventListener('click', clickHandler as EventListener);
        });
      };
    }
  }, [showFullPagePreview, generatedPreview]);

  const onSubmit = async (values: LandingPageFormValues) => {
    setIsGenerating(true);
    try {
      // Generate the landing page preview with all content, design and settings options
      const previewHtml = generatePreview(values);
      
      // In a real implementation, we would save the landing page to the database
      // Currently we're just simulating this with a timeout
      setTimeout(() => {
        // Once generated, show the full-page preview
        setShowFullPagePreview(true);
        
        // Show success message
        toast({
          title: "Landing page generated",
          description: "Your landing page has been successfully generated with your design and SEO settings. Click on any text to edit it directly.",
        });
        setIsGenerating(false);
      }, 800); // Small delay to simulate server processing
    } catch (error) {
      console.error("Landing page generation error:", error);
      toast({
        title: "Error",
        description: "Failed to generate landing page. Please check your inputs and try again.",
        variant: "destructive",
      });
      setIsGenerating(false);
    }
  };

  return (
      <div className="container mx-auto py-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold">AI Landing Page Generator</h1>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle>Create Landing Page</CardTitle>
                <CardDescription>
                  Generate a professional landing page for your course in seconds using AI
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="content">
                  <TabsList className="grid w-full grid-cols-3">
                    <TabsTrigger value="content">Content</TabsTrigger>
                    <TabsTrigger value="design">Design</TabsTrigger>
                    <TabsTrigger value="settings">Settings</TabsTrigger>
                  </TabsList>

                  <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                      <TabsContent value="content" className="space-y-4">
                        <FormField
                          control={form.control}
                          name="courseId"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Course</FormLabel>
                              <Select
                                onValueChange={field.onChange}
                                defaultValue={field.value}
                              >
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select course" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  {isLoadingCourses ? (
                                    <SelectItem value="loading">Loading...</SelectItem>
                                  ) : (
                                    Array.isArray(courses) && courses.map((course: any) => (
                                      <SelectItem key={course.id} value={course.id.toString()}>
                                        {course.title}
                                      </SelectItem>
                                    ))
                                  )}
                                </SelectContent>
                              </Select>
                              <FormDescription>
                                The course to create a landing page for
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="headline"
                          render={({ field }) => (
                            <FormItem>
                              <div className="flex justify-between items-center">
                                <FormLabel>Headline</FormLabel>
                                <AIAssistButton 
                                  tooltipText="Generate headline with AI"
                                  isLoading={isAiGenerating}
                                  onClick={async () => {
                                    const courseId = form.getValues('courseId');
                                    const selectedCourse = Array.isArray(courses) ? courses.find((c: any) => c.id.toString() === courseId) : null;
                                    const courseName = selectedCourse?.title || '';
                                    
                                    // Using proper types from useAiTextGenerator
                                    const generatedText = await generateText({
                                      fieldType: 'headline',
                                      context: `Course: ${courseName}`,
                                      tone: 'persuasive',
                                      length: 'short'
                                    });
                                    if (generatedText) {
                                      field.onChange(generatedText);
                                    }
                                  }}
                                />
                              </div>
                              <FormControl>
                                <Input placeholder="Enter headline" {...field} />
                              </FormControl>
                              <FormDescription>
                                The main headline for your landing page
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="subHeadline"
                          render={({ field }) => (
                            <FormItem>
                              <div className="flex justify-between items-center">
                                <FormLabel>Sub-headline</FormLabel>
                                <AIAssistButton 
                                  tooltipText="Generate sub-headline with AI"
                                  isLoading={isAiGenerating}
                                  onClick={async () => {
                                    const courseId = form.getValues('courseId');
                                    const headline = form.getValues('headline');
                                    const selectedCourse = Array.isArray(courses) ? courses.find((c: any) => c.id.toString() === courseId) : null;
                                    const courseName = selectedCourse?.title || '';
                                    
                                    // Using proper types from useAiTextGenerator
                                    const generatedText = await generateText({
                                      fieldType: 'subheadline',
                                      context: `Course: ${courseName}, Headline: ${headline}`,
                                      tone: 'persuasive',
                                      length: 'short'
                                    });
                                    if (generatedText) {
                                      field.onChange(generatedText);
                                    }
                                  }}
                                />
                              </div>
                              <FormControl>
                                <Input placeholder="Enter sub-headline" {...field} />
                              </FormControl>
                              <FormDescription>
                                A secondary headline that provides more context
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="description"
                          render={({ field }) => (
                            <FormItem>
                              <div className="flex justify-between items-center">
                                <FormLabel>Description</FormLabel>
                                <AIAssistButton 
                                  tooltipText="Generate description with AI"
                                  isLoading={isAiGenerating}
                                  onClick={async () => {
                                    const courseId = form.getValues('courseId');
                                    const headline = form.getValues('headline');
                                    const subHeadline = form.getValues('subHeadline');
                                    const selectedCourse = Array.isArray(courses) ? courses.find((c: any) => c.id.toString() === courseId) : null;
                                    const courseName = selectedCourse?.title || '';
                                    const courseDetails = selectedCourse?.description || '';
                                    
                                    // Using proper types from useAiTextGenerator
                                    const generatedText = await generateText({
                                      fieldType: 'description',
                                      context: `Course: ${courseName}, Course Description: ${courseDetails}, Headline: ${headline}, Sub-headline: ${subHeadline}`,
                                      tone: 'professional',
                                      length: 'long'
                                    });
                                    if (generatedText) {
                                      field.onChange(generatedText);
                                    }
                                  }}
                                />
                              </div>
                              <FormControl>
                                <Textarea 
                                  placeholder="Describe your course and its benefits" 
                                  className="min-h-[120px]"
                                  {...field} 
                                />
                              </FormControl>
                              <FormDescription>
                                A detailed description of your course and what students will learn
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="callToAction"
                          render={({ field }) => (
                            <FormItem>
                              <div className="flex justify-between items-center">
                                <FormLabel>Call to Action</FormLabel>
                                <AIAssistButton 
                                  tooltipText="Generate call to action with AI"
                                  isLoading={isAiGenerating}
                                  onClick={async () => {
                                    const courseId = form.getValues('courseId');
                                    const headline = form.getValues('headline');
                                    const selectedCourse = Array.isArray(courses) ? courses.find((c: any) => c.id.toString() === courseId) : null;
                                    const courseName = selectedCourse?.title || '';
                                    
                                    // Using proper types from useAiTextGenerator
                                    const generatedText = await generateText({
                                      fieldType: 'callToAction',
                                      context: `Course: ${courseName}, Headline: ${headline}`,
                                      tone: 'persuasive',
                                      length: 'short'
                                    });
                                    if (generatedText) {
                                      field.onChange(generatedText);
                                    }
                                  }}
                                />
                              </div>
                              <FormControl>
                                <Input placeholder="Enter call to action text" {...field} />
                              </FormControl>
                              <FormDescription>
                                The text for your call-to-action button
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </TabsContent>

                      <TabsContent value="design" className="space-y-4">
                        <div className="space-y-6">
                          <div>
                            <h3 className="text-lg font-medium">Theme</h3>
                            <p className="text-sm text-slate-500">The visual theme for your landing page</p>
                            <div className="mt-3">
                              <FormField
                                control={form.control}
                                name="theme"
                                render={({ field }) => (
                                  <FormItem>
                                    <Select
                                      onValueChange={(value) => {
                                        field.onChange(value);
                                        // Update preview when theme changes
                                        generatePreview(form.getValues());
                                      }}
                                      value={field.value}
                                    >
                                      <FormControl>
                                        <SelectTrigger>
                                          <SelectValue placeholder="Select theme" />
                                        </SelectTrigger>
                                      </FormControl>
                                      <SelectContent>
                                        <SelectItem value="professional">Professional</SelectItem>
                                        <SelectItem value="creative">Creative</SelectItem>
                                        <SelectItem value="minimalist">Minimalist</SelectItem>
                                        <SelectItem value="bold">Bold</SelectItem>
                                        <SelectItem value="elegant">Elegant</SelectItem>
                                      </SelectContent>
                                    </Select>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            </div>
                          </div>

                          <div>
                            <h3 className="text-lg font-medium">Color Scheme</h3>
                            <p className="text-sm text-slate-500">Choose a color palette for your landing page</p>
                            <div className="mt-3">
                              <FormField
                                control={form.control}
                                name="colorScheme"
                                render={({ field }) => (
                                  <FormItem>
                                    <div className="grid grid-cols-5 gap-2 mt-2">
                                      <div 
                                        className={`h-10 rounded-md bg-blue-500 cursor-pointer transition-all ${field.value === 'blue' ? 'ring-4 ring-offset-2 ring-blue-500' : 'hover:ring-2 hover:ring-offset-1 hover:ring-blue-700'}`}
                                        onClick={() => {
                                          field.onChange('blue');
                                          // Update preview when color scheme changes
                                          generatePreview(form.getValues());
                                        }}
                                      />
                                      <div 
                                        className={`h-10 rounded-md bg-purple-500 cursor-pointer transition-all ${field.value === 'purple' ? 'ring-4 ring-offset-2 ring-purple-500' : 'hover:ring-2 hover:ring-offset-1 hover:ring-purple-700'}`}
                                        onClick={() => {
                                          field.onChange('purple');
                                          // Update preview when color scheme changes
                                          generatePreview(form.getValues());
                                        }}
                                      />
                                      <div 
                                        className={`h-10 rounded-md bg-green-500 cursor-pointer transition-all ${field.value === 'green' ? 'ring-4 ring-offset-2 ring-green-500' : 'hover:ring-2 hover:ring-offset-1 hover:ring-green-700'}`}
                                        onClick={() => {
                                          field.onChange('green');
                                          // Update preview when color scheme changes
                                          generatePreview(form.getValues());
                                        }}
                                      />
                                      <div 
                                        className={`h-10 rounded-md bg-amber-500 cursor-pointer transition-all ${field.value === 'amber' ? 'ring-4 ring-offset-2 ring-amber-500' : 'hover:ring-2 hover:ring-offset-1 hover:ring-amber-700'}`}
                                        onClick={() => {
                                          field.onChange('amber');
                                          // Update preview when color scheme changes
                                          generatePreview(form.getValues());
                                        }}
                                      />
                                      <div 
                                        className={`h-10 rounded-md bg-rose-500 cursor-pointer transition-all ${field.value === 'rose' ? 'ring-4 ring-offset-2 ring-rose-500' : 'hover:ring-2 hover:ring-offset-1 hover:ring-rose-700'}`}
                                        onClick={() => {
                                          field.onChange('rose');
                                          // Update preview when color scheme changes
                                          generatePreview(form.getValues());
                                        }}
                                      />
                                    </div>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            </div>
                          </div>

                          <div>
                            <h3 className="text-lg font-medium">Layout Style</h3>
                            <p className="text-sm text-slate-500">Select the structure for your landing page</p>
                            <div className="mt-3">
                              <FormField
                                control={form.control}
                                name="layoutStyle"
                                render={({ field }) => (
                                  <FormItem>
                                    <div className="grid grid-cols-3 gap-2 mt-2">
                                      <div 
                                        className={`border rounded-md p-3 cursor-pointer transition-all ${field.value === 'classic' ? 'ring-2 ring-primary border-primary' : 'hover:bg-slate-50'}`}
                                        onClick={() => {
                                          field.onChange('classic');
                                          // Update preview when layout changes
                                          generatePreview(form.getValues());
                                        }}
                                      >
                                        <div className="h-4 w-full bg-slate-200 mb-2 rounded" />
                                        <div className="h-20 w-full bg-slate-200 mb-2 rounded" />
                                        <div className="h-4 w-1/2 bg-slate-200 rounded mx-auto" />
                                      </div>
                                      <div 
                                        className={`border rounded-md p-3 cursor-pointer transition-all ${field.value === 'split' ? 'ring-2 ring-primary border-primary' : 'hover:bg-slate-50'}`}
                                        onClick={() => {
                                          field.onChange('split');
                                          // Update preview when layout changes
                                          generatePreview(form.getValues());
                                        }}
                                      >
                                        <div className="h-4 w-full bg-slate-200 mb-2 rounded" />
                                        <div className="grid grid-cols-2 gap-2">
                                          <div className="h-20 bg-slate-200 rounded" />
                                          <div className="h-20 bg-slate-200 rounded" />
                                        </div>
                                        <div className="h-4 w-1/2 bg-slate-200 rounded mx-auto mt-2" />
                                      </div>
                                      <div 
                                        className={`border rounded-md p-3 cursor-pointer transition-all ${field.value === 'grid' ? 'ring-2 ring-primary border-primary' : 'hover:bg-slate-50'}`}
                                        onClick={() => {
                                          field.onChange('grid');
                                          // Update preview when layout changes
                                          generatePreview(form.getValues());
                                        }}
                                      >
                                        <div className="h-4 w-full bg-slate-200 mb-2 rounded" />
                                        <div className="grid grid-cols-3 gap-1">
                                          <div className="h-16 bg-slate-200 rounded" />
                                          <div className="h-16 bg-slate-200 rounded" />
                                          <div className="h-16 bg-slate-200 rounded" />
                                        </div>
                                        <div className="h-4 w-1/2 bg-slate-200 rounded mx-auto mt-2" />
                                      </div>
                                    </div>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            </div>
                          </div>

                          <div>
                            <h3 className="text-lg font-medium">Banner Image</h3>
                            <p className="text-sm text-slate-500">Add a banner image to your landing page</p>
                            <div className="mt-3">
                              <FormField
                                control={form.control}
                                name="bannerImageUrl"
                                render={({ field }) => (
                                  <FormItem>
                                    <FormControl>
                                      <Input 
                                        placeholder="Enter banner image URL" 
                                        {...field} 
                                        onChange={(e) => {
                                          field.onChange(e.target.value);
                                          // Update preview when banner image changes
                                          generatePreview(form.getValues());
                                        }}
                                      />
                                    </FormControl>
                                    <FormDescription>
                                      URL to an image that will be used as the banner
                                    </FormDescription>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            </div>
                          </div>
                        </div>
                      </TabsContent>

                      <TabsContent value="settings" className="space-y-4">
                        <div className="space-y-6">
                          <div>
                            <h3 className="text-lg font-medium">SEO Settings</h3>
                            <p className="text-sm text-slate-500">Optimize your landing page for search engines</p>
                            <div className="mt-3 space-y-3">
                              <FormField
                                control={form.control}
                                name="seoTitle"
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>Meta Title</FormLabel>
                                    <FormControl>
                                      <Input 
                                        placeholder="Enter meta title" 
                                        {...field} 
                                      />
                                    </FormControl>
                                    <FormDescription>
                                      The title that appears in search engine results (50-60 characters recommended)
                                    </FormDescription>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                              
                              <FormField
                                control={form.control}
                                name="seoDescription"
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>Meta Description</FormLabel>
                                    <FormControl>
                                      <Textarea 
                                        placeholder="Enter meta description" 
                                        {...field} 
                                      />
                                    </FormControl>
                                    <FormDescription>
                                      The description that appears in search engine results (150-160 characters recommended)
                                    </FormDescription>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                              
                              <FormField
                                control={form.control}
                                name="seoKeywords"
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>Keywords</FormLabel>
                                    <FormControl>
                                      <Input 
                                        placeholder="Enter keywords, separated by commas" 
                                        {...field} 
                                      />
                                    </FormControl>
                                    <FormDescription>
                                      Keywords related to your course (not as important for SEO as they once were, but still helpful)
                                    </FormDescription>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            </div>
                          </div>

                          <Separator />

                          <div>
                            <h3 className="text-lg font-medium">Tracking & Analytics</h3>
                            <p className="text-sm text-slate-500">Add tracking codes to monitor your landing page performance</p>
                            <div className="mt-3 space-y-3">
                              <FormField
                                control={form.control}
                                name="googleAnalyticsId"
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>Google Analytics ID</FormLabel>
                                    <FormControl>
                                      <Input 
                                        placeholder="UA-XXXXXXXXX-X or G-XXXXXXXXXX" 
                                        {...field} 
                                      />
                                    </FormControl>
                                    <FormDescription>
                                      Your Google Analytics tracking ID
                                    </FormDescription>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                              
                              <FormField
                                control={form.control}
                                name="facebookPixelId"
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>Facebook Pixel ID</FormLabel>
                                    <FormControl>
                                      <Input 
                                        placeholder="XXXXXXXXXXXXXXXXXX" 
                                        {...field} 
                                      />
                                    </FormControl>
                                    <FormDescription>
                                      Your Facebook Pixel tracking ID
                                    </FormDescription>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            </div>
                          </div>
                          
                          <Separator />
                          
                          <div>
                            <h3 className="text-lg font-medium">Custom Domain</h3>
                            <p className="text-sm text-slate-500">Set up a custom domain for your landing page</p>
                            <div className="mt-3">
                              <FormField
                                control={form.control}
                                name="customDomain"
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>Domain</FormLabel>
                                    <FormControl>
                                      <Input 
                                        placeholder="yourdomain.com" 
                                        {...field} 
                                      />
                                    </FormControl>
                                    <FormDescription>
                                      A custom domain for your landing page (optional)
                                    </FormDescription>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            </div>
                          </div>
                        </div>
                      </TabsContent>

                      <div className="flex justify-end">
                        <Button type="submit" disabled={isGenerating}>
                          {isGenerating ? "Generating..." : "Generate Landing Page"}
                        </Button>
                      </div>
                    </form>
                  </Form>
                </Tabs>
              </CardContent>
            </Card>
          </div>

          <div>
            <Card>
              <CardHeader>
                <CardTitle>Preview</CardTitle>
                <CardDescription>
                  See a preview of your landing page
                </CardDescription>
              </CardHeader>
              <CardContent>
                {generatedPreview ? (
                  <div 
                    className="border rounded-md p-4 min-h-[400px] bg-white"
                    dangerouslySetInnerHTML={{ __html: generatedPreview }}
                  />
                ) : (
                  <div className="border rounded-md p-4 min-h-[400px] bg-white flex items-center justify-center text-slate-400">
                    Landing page preview will appear here
                  </div>
                )}
              </CardContent>
              {generatedPreview && (
                <CardFooter className="flex flex-wrap gap-2 justify-between">
                  <div className="flex gap-2">
                    <Button 
                      variant="outline"
                      onClick={() => {
                        // In a real implementation, this would download the HTML file
                        toast({
                          title: "HTML Downloaded",
                          description: "Your landing page HTML has been downloaded.",
                        });
                      }}
                    >
                      Download HTML
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => setShowFullPagePreview(true)}
                    >
                      <Maximize2 className="mr-2 h-4 w-4" />
                      Full Screen
                    </Button>
                  </div>
                  <Button
                    onClick={() => {
                      // Get all current form values
                      const values = form.getValues();
                      
                      // In a real implementation, this would publish the landing page
                      // with all content, design and settings options to the server
                      
                      // Show a small loading state
                      toast({
                        title: "Publishing...",
                        description: "Your landing page is being published.",
                      });
                      
                      // Simulate server processing
                      setTimeout(() => {
                        // Generate the public URL based on custom domain or default slug
                        const publicUrl = values.customDomain 
                          ? `https://${values.customDomain}` 
                          : `https://courses.example.com/l/${values.headline.toLowerCase().replace(/\s+/g, '-').slice(0, 30)}`;
                          
                        toast({
                          title: "Landing Page Published",
                          description: (
                            <div className="space-y-2">
                              <p>Your landing page has been published successfully with all design and SEO settings.</p>
                              <p className="text-xs font-medium">Available at: <a href="#" className="underline text-blue-500">{publicUrl}</a></p>
                            </div>
                          ),
                        });
                      }, 1500);
                    }}
                  >
                    <span className="mr-2">Publish Page</span>
                  </Button>
                </CardFooter>
              )}
            </Card>

            <div className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>Recent Landing Pages</CardTitle>
                  <CardDescription>
                    Your recently created landing pages
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-start justify-between border-b pb-3">
                      <div>
                        <p className="font-medium">Master the Art of Photography</p>
                        <div className="mt-1">
                          <div className="flex items-center text-xs text-slate-500 space-x-2">
                            <span>Created 2 days ago</span>
                            <span>•</span>
                            <span className="flex items-center">
                              <div className="w-2 h-2 rounded-full bg-blue-500 mr-1"></div>
                              Professional
                            </span>
                          </div>
                          <div className="flex items-center mt-1 space-x-1">
                            <span className="text-xs px-1.5 py-0.5 bg-blue-50 text-blue-700 rounded">SEO Optimized</span>
                            <span className="text-xs px-1.5 py-0.5 bg-green-50 text-green-700 rounded">Analytics</span>
                          </div>
                        </div>
                      </div>
                      <div className="flex space-x-2">
                        <Button variant="ghost" size="sm">Edit</Button>
                        <Button variant="ghost" size="sm">View</Button>
                      </div>
                    </div>
                    
                    <div className="flex items-start justify-between border-b pb-3">
                      <div>
                        <p className="font-medium">Advanced Digital Marketing</p>
                        <div className="mt-1">
                          <div className="flex items-center text-xs text-slate-500 space-x-2">
                            <span>Created 1 week ago</span>
                            <span>•</span>
                            <span className="flex items-center">
                              <div className="w-2 h-2 rounded-full bg-purple-500 mr-1"></div>
                              Creative
                            </span>
                          </div>
                          <div className="flex items-center mt-1 space-x-1">
                            <span className="text-xs px-1.5 py-0.5 bg-blue-50 text-blue-700 rounded">SEO Optimized</span>
                            <span className="text-xs px-1.5 py-0.5 bg-amber-50 text-amber-700 rounded">Custom Domain</span>
                          </div>
                        </div>
                      </div>
                      <div className="flex space-x-2">
                        <Button variant="ghost" size="sm">Edit</Button>
                        <Button variant="ghost" size="sm">View</Button>
                      </div>
                    </div>
                    
                    <div className="flex items-start justify-between">
                      <div>
                        <p className="font-medium">Complete Web Development</p>
                        <div className="mt-1">
                          <div className="flex items-center text-xs text-slate-500 space-x-2">
                            <span>Created 2 weeks ago</span>
                            <span>•</span>
                            <span className="flex items-center">
                              <div className="w-2 h-2 rounded-full bg-green-500 mr-1"></div>
                              Minimalist
                            </span>
                          </div>
                          <div className="flex items-center mt-1 space-x-1">
                            <span className="text-xs px-1.5 py-0.5 bg-green-50 text-green-700 rounded">Analytics</span>
                            <span className="text-xs px-1.5 py-0.5 bg-rose-50 text-rose-700 rounded">Pixel Tracking</span>
                          </div>
                        </div>
                      </div>
                      <div className="flex space-x-2">
                        <Button variant="ghost" size="sm">Edit</Button>
                        <Button variant="ghost" size="sm">View</Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="mt-6">
              <Alert>
                <AlertTitle>AI-Powered Landing Pages</AlertTitle>
                <AlertDescription>
                  <div className="space-y-2">
                    <p>Our AI landing page generator creates conversion-optimized pages using best practices in design and copywriting.</p>
                    <div className="text-sm text-slate-800 grid grid-cols-2 gap-2 pt-1">
                      <div className="flex items-start">
                        <div className="w-4 h-4 rounded-full bg-blue-500 mt-0.5 mr-2 flex-shrink-0"></div>
                        <div>
                          <span className="font-medium">Design Options</span>
                          <p className="text-xs text-slate-500">Multiple themes, color schemes, and layouts</p>
                        </div>
                      </div>
                      <div className="flex items-start">
                        <div className="w-4 h-4 rounded-full bg-purple-500 mt-0.5 mr-2 flex-shrink-0"></div>
                        <div>
                          <span className="font-medium">SEO Settings</span>
                          <p className="text-xs text-slate-500">Optimize for search engines with meta tags</p>
                        </div>
                      </div>
                      <div className="flex items-start">
                        <div className="w-4 h-4 rounded-full bg-green-500 mt-0.5 mr-2 flex-shrink-0"></div>
                        <div>
                          <span className="font-medium">Analytics Integration</span>
                          <p className="text-xs text-slate-500">Track performance with Google and Facebook</p>
                        </div>
                      </div>
                      <div className="flex items-start">
                        <div className="w-4 h-4 rounded-full bg-amber-500 mt-0.5 mr-2 flex-shrink-0"></div>
                        <div>
                          <span className="font-medium">Custom Domains</span>
                          <p className="text-xs text-slate-500">Use your own domain for branding</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </AlertDescription>
              </Alert>
            </div>
          </div>
        </div>
        
        {/* Responsive Full-Page Preview Dialog */}
        <Dialog open={showFullPagePreview} onOpenChange={setShowFullPagePreview}>
          <DialogContent className="max-w-screen-xl w-[95vw] max-h-[90vh] p-0 bg-white overflow-hidden">
            <div className="flex flex-col h-full">
              <div className="flex justify-between items-center bg-slate-50 px-4 py-2 border-b">
                <div className="flex items-center gap-2">
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    onClick={() => setShowFullPagePreview(false)}
                  >
                    <ChevronLeft className="h-4 w-4 mr-1" />
                    Back to Editor
                  </Button>
                </div>
                <div className="flex items-center gap-2">
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => {
                      const values = form.getValues();
                      
                      // Create a blob of the HTML content
                      const htmlContent = generatedPreview;
                      const blob = new Blob([htmlContent], { type: 'text/html' });
                      
                      // Create download link
                      const url = URL.createObjectURL(blob);
                      const link = document.createElement('a');
                      
                      // Generate filename from course name or headline
                      const selectedCourse = Array.isArray(courses) 
                        ? courses.find((c: any) => c.id.toString() === values.courseId) 
                        : null;
                      const courseName = selectedCourse?.title || 'course';
                      const filename = `${courseName.toLowerCase().replace(/\s+/g, '-')}-landing-page.html`;
                      
                      link.href = url;
                      link.download = filename;
                      link.click();
                      
                      // Clean up
                      URL.revokeObjectURL(url);
                      
                      toast({
                        title: "HTML Downloaded",
                        description: `Your landing page HTML has been downloaded as "${filename}" with all design customizations.`,
                      });
                    }}
                  >
                    <Download className="h-4 w-4 mr-1" />
                    Download HTML
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => {
                      // Get all current form values for publishing
                      const values = form.getValues();
                      
                      // Show loading state
                      toast({
                        title: "Publishing...",
                        description: "Your landing page is being published.",
                      });
                      
                      // Simulate server processing
                      setTimeout(() => {
                        // Generate the public URL based on custom domain or a slug from the headline
                        const publicUrl = values.customDomain 
                          ? `https://${values.customDomain}` 
                          : `https://courses.example.com/l/${values.headline.toLowerCase().replace(/\s+/g, '-').slice(0, 30)}`;
                          
                        toast({
                          title: "Landing Page Published",
                          description: (
                            <div className="space-y-2">
                              <p>Your landing page has been published successfully with all design and SEO settings.</p>
                              <p className="text-xs font-medium">Available at: <a href="#" className="underline text-blue-500">{publicUrl}</a></p>
                              {values.googleAnalyticsId && <p className="text-xs text-slate-500">Google Analytics tracking is enabled.</p>}
                              {values.facebookPixelId && <p className="text-xs text-slate-500">Facebook Pixel tracking is enabled.</p>}
                            </div>
                          ),
                        });
                        
                        // Close the preview dialog after publishing
                        setShowFullPagePreview(false);
                      }, 1500);
                    }}
                  >
                    <Save className="h-4 w-4 mr-1" />
                    Publish
                  </Button>
                </div>
              </div>
              
              <div className="flex-1 overflow-auto p-4">
                <div 
                  ref={previewContainerRef}
                  className="landing-page-preview-container w-full h-full overflow-auto mx-auto"
                >
                  {editingSectionId ? (
                    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
                      <div className="bg-white p-6 rounded-lg shadow-lg max-w-md w-full">
                        <h3 className="text-lg font-medium mb-4">Edit Content</h3>
                        <Textarea 
                          defaultValue={
                            editingSectionId === 'headline' ? form.getValues('headline') :
                            editingSectionId === 'subHeadline' ? form.getValues('subHeadline') :
                            editingSectionId === 'description' ? form.getValues('description') :
                            form.getValues('callToAction')
                          }
                          className="mb-4"
                          rows={editingSectionId === 'description' ? 6 : 3}
                        />
                        <div className="flex justify-end gap-2">
                          <Button 
                            variant="outline" 
                            onClick={() => setEditingSectionId(null)}
                          >
                            Cancel
                          </Button>
                          <Button 
                            onClick={(e) => {
                              const textarea = e.currentTarget.parentElement?.previousElementSibling as HTMLTextAreaElement;
                              if (textarea) {
                                handleEditSection(editingSectionId, textarea.value);
                              }
                            }}
                          >
                            Save Changes
                          </Button>
                        </div>
                      </div>
                    </div>
                  ) : null}
                  <div className="scale-container">
                    <div 
                      className="preview-content w-full bg-white"
                      dangerouslySetInnerHTML={{ __html: generatedPreview }}
                    />
                  </div>
                </div>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    );
  };

export default LandingPageGenerator;