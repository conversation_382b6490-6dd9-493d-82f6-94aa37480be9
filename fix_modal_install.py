#!/usr/bin/env python3
"""
Fix Modal installation by resolving TTS dependency conflict
"""

import sys
import subprocess
import os

def uninstall_conflicting_packages():
    """Remove packages that conflict with Modal"""
    try:
        # First, uninstall the deprecated modal-client
        subprocess.run([sys.executable, "-m", "pip", "uninstall", "modal-client", "-y"], 
                      capture_output=True)
        
        # Check if TTS is causing conflicts and temporarily remove it
        result = subprocess.run([sys.executable, "-m", "pip", "show", "tts"], 
                               capture_output=True, text=True)
        if result.returncode == 0:
            print("Temporarily removing TTS to resolve conflicts...")
            subprocess.run([sys.executable, "-m", "pip", "uninstall", "tts", "-y"], 
                          capture_output=True)
            return True
        return False
    except Exception as e:
        print(f"Error during cleanup: {e}")
        return False

def install_modal_fresh():
    """Install Modal from scratch"""
    try:
        cmd = [sys.executable, "-m", "pip", "install", "modal", "--force-reinstall"]
        print(f"Installing Modal: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("Modal installed successfully")
            return True
        else:
            print(f"Installation failed: {result.stderr}")
            return False
    except Exception as e:
        print(f"Installation error: {e}")
        return False

def reinstall_tts_compatible():
    """Reinstall TTS with compatible version"""
    try:
        # Install a compatible version of TTS that works with Modal
        cmd = [sys.executable, "-m", "pip", "install", "TTS==0.21.3"]
        print(f"Reinstalling compatible TTS: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("TTS reinstalled successfully")
            return True
        else:
            print(f"TTS reinstallation warning: {result.stderr}")
            return False  # Not critical for Modal
    except Exception as e:
        print(f"TTS reinstallation error: {e}")
        return False

def test_modal_installation():
    """Test if Modal works properly"""
    try:
        import modal
        print(f"Modal imported successfully! Version: {getattr(modal, '__version__', 'unknown')}")
        
        # Test basic functionality
        app = modal.App("test-installation")
        print("Modal App creation successful")
        
        # Test image creation
        image = modal.Image.debian_slim()
        print("Modal Image creation successful")
        
        return True
    except Exception as e:
        print(f"Modal test failed: {e}")
        return False

def main():
    print("Fixing Modal installation...")
    print("=" * 40)
    
    # Step 1: Clean up conflicting packages
    had_tts = uninstall_conflicting_packages()
    
    # Step 2: Install Modal fresh
    if not install_modal_fresh():
        print("Failed to install Modal")
        return False
    
    # Step 3: Test Modal
    if not test_modal_installation():
        print("Modal installation test failed")
        return False
    
    # Step 4: Optionally reinstall TTS (not critical)
    if had_tts:
        print("Attempting to reinstall TTS...")
        reinstall_tts_compatible()
    
    print("\nModal installation completed successfully!")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)