import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ist,
  <PERSON><PERSON>Trigger,
} from '@/components/ui/tabs';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Separator } from '@/components/ui/separator';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { PaginationButton } from '@/components/ui/pagination-button';
import {
  ChevronLeft,
  ChevronRight,
  Download,
  FileText,
  Filter,
  MailCheck,
  Trash2,
  Upload,
  Users,
  MessageSquarePlus,
  Inbox,
  Settings as SettingsIcon,
  FileUp,
  Plus,
  Radio,
  FilePieChart,
  RefreshCw,
  Tag,
  Search,
  Eye,
  Edit,
  MoreVertical,
  ExternalLink
} from 'lucide-react';
import { Link } from 'wouter';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useForm } from 'react-hook-form';
import { useMediaQuery } from '@/hooks/use-media-query';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Switch } from '@/components/ui/switch';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useLocation, useRoute } from 'wouter';

// Tab components
const SubscriberManagement = () => {
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [activeFilter, setActiveFilter] = useState<string | null>(null);
  const [listFilter, setListFilter] = useState<string | null>(null);
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { isMobile } = useMediaQuery();

  // Fetch subscribers with pagination and filtering
  const { data: subscribersData, isLoading: subscribersLoading } = useQuery({
    queryKey: ['/api/subscribers', page, limit, activeFilter, listFilter],
    queryFn: async () => {
      let url = `/api/subscribers?page=${page}&limit=${limit}`;
      if (activeFilter) url += `&status=${activeFilter}`;
      if (listFilter) url += `&listId=${listFilter}`;
      const res = await apiRequest('GET', url);
      return res.json();
    },
  });

  // Fetch subscriber statistics
  const { data: statsData, isLoading: statsLoading } = useQuery({
    queryKey: ['/api/subscribers/stats'],
    queryFn: async () => {
      const res = await apiRequest('GET', `/api/subscribers/stats`);
      return res.json();
    },
  });

  // Fetch subscriber lists
  const { data: listsData, isLoading: listsLoading } = useQuery({
    queryKey: ['/api/subscribers/lists'],
    queryFn: async () => {
      const res = await apiRequest('GET', `/api/subscribers/lists`);
      return res.json();
    },
  });

  // Delete subscriber mutation
  const deleteMutation = useMutation({
    mutationFn: async (id: number) => {
      await apiRequest('DELETE', `/api/subscribers/${id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/subscribers'] });
      queryClient.invalidateQueries({ queryKey: ['/api/subscribers/stats'] });
      toast({
        title: 'Subscriber deleted',
        description: 'The subscriber has been deleted successfully.',
        variant: 'default',
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: `Failed to delete subscriber: ${error.message}`,
        variant: 'destructive',
      });
    },
  });

  const [importDialogOpen, setImportDialogOpen] = useState(false);
  const [addSubscriberDialogOpen, setAddSubscriberDialogOpen] = useState(false);
  const [newListDialogOpen, setNewListDialogOpen] = useState(false);

  // Add subscriber form schema
  const subscriberSchema = z.object({
    email: z.string().email({ message: "Please enter a valid email address" }),
    name: z.string().optional(),
    status: z.enum(['active', 'inactive', 'unsubscribed']).default('active'),
    listId: z.number().optional(),
  });

  // Create subscriber form
  const subscriberForm = useForm<z.infer<typeof subscriberSchema>>({
    resolver: zodResolver(subscriberSchema),
    defaultValues: {
      email: '',
      name: '',
      status: 'active',
    },
  });

  // Add subscriber mutation
  const addSubscriberMutation = useMutation({
    mutationFn: async (values: z.infer<typeof subscriberSchema>) => {
      const res = await apiRequest('POST', '/api/subscribers', values);
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/subscribers'] });
      queryClient.invalidateQueries({ queryKey: ['/api/subscribers/stats'] });
      setAddSubscriberDialogOpen(false);
      subscriberForm.reset();
      toast({
        title: 'Subscriber added',
        description: 'New subscriber has been added successfully.',
        variant: 'default',
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: `Failed to add subscriber: ${error.message}`,
        variant: 'destructive',
      });
    },
  });

  // List form schema
  const listSchema = z.object({
    name: z.string().min(1, { message: "List name is required" }),
    description: z.string().optional(),
  });

  // Create list form
  const listForm = useForm<z.infer<typeof listSchema>>({
    resolver: zodResolver(listSchema),
    defaultValues: {
      name: '',
      description: '',
    },
  });

  // Add list mutation
  const addListMutation = useMutation({
    mutationFn: async (values: z.infer<typeof listSchema>) => {
      const res = await apiRequest('POST', '/api/subscribers/lists', values);
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/subscribers/lists'] });
      setNewListDialogOpen(false);
      listForm.reset();
      toast({
        title: 'List created',
        description: 'New subscriber list has been created successfully.',
        variant: 'default',
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: `Failed to create list: ${error.message}`,
        variant: 'destructive',
      });
    },
  });

  // Handle file import
  const [importFile, setImportFile] = useState<File | null>(null);
  const [importListId, setImportListId] = useState<number | null>(null);

  const importMutation = useMutation({
    mutationFn: async () => {
      if (!importFile) return null;
      
      const formData = new FormData();
      formData.append('file', importFile);
      if (importListId) formData.append('listId', String(importListId));
      
      // Pass formData directly as the third parameter
      const res = await apiRequest('POST', '/api/subscribers/import', formData);
      return res.json();
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['/api/subscribers'] });
      queryClient.invalidateQueries({ queryKey: ['/api/subscribers/stats'] });
      setImportDialogOpen(false);
      setImportFile(null);
      setImportListId(null);
      toast({
        title: 'Import complete',
        description: `${data.imported} subscribers imported successfully.`,
        variant: 'default',
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'Import failed',
        description: `Failed to import subscribers: ${error.message}`,
        variant: 'destructive',
      });
    },
  });

  // Export subscribers
  const handleExport = async () => {
    try {
      let url = '/api/subscribers/export';
      const params = new URLSearchParams();
      if (activeFilter) params.append('status', activeFilter);
      if (listFilter) params.append('listId', listFilter);
      
      if (params.toString()) {
        url += `?${params.toString()}`;
      }
      
      // Trigger file download
      window.location.href = url;
      
      toast({
        title: 'Export started',
        description: 'Your subscriber export has started downloading.',
        variant: 'default',
      });
    } catch (error) {
      toast({
        title: 'Export failed',
        description: 'Failed to export subscribers.',
        variant: 'destructive',
      });
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-col md:flex-row justify-between md:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold">Subscriber Management</h2>
          <p className="text-muted-foreground">Manage your email subscribers and lists</p>
        </div>

        <div className="flex flex-wrap gap-2">
          <Dialog open={addSubscriberDialogOpen} onOpenChange={setAddSubscriberDialogOpen}>
            <DialogTrigger asChild>
              <Button variant="outline">
                <Plus className="mr-2 h-4 w-4" />
                Add Subscriber
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Add New Subscriber</DialogTitle>
                <DialogDescription>
                  Enter the details of the new subscriber below.
                </DialogDescription>
              </DialogHeader>
              
              <Form {...subscriberForm}>
                <form onSubmit={subscriberForm.handleSubmit((values) => addSubscriberMutation.mutate(values))} className="space-y-4">
                  <FormField
                    control={subscriberForm.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Email</FormLabel>
                        <FormControl>
                          <Input placeholder="<EMAIL>" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={subscriberForm.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Name (Optional)</FormLabel>
                        <FormControl>
                          <Input placeholder="John Doe" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={subscriberForm.control}
                    name="status"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Status</FormLabel>
                        <Select 
                          value={field.value} 
                          onValueChange={(value) => field.onChange(value)}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select status" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="active">Active</SelectItem>
                            <SelectItem value="inactive">Inactive</SelectItem>
                            <SelectItem value="unsubscribed">Unsubscribed</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={subscriberForm.control}
                    name="listId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Subscriber List (Optional)</FormLabel>
                        <Select 
                          value={field.value?.toString() || ''} 
                          onValueChange={(value) => field.onChange(value ? parseInt(value) : undefined)}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select list" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="none">None</SelectItem>
                            {listsData?.map((list: any) => (
                              <SelectItem key={list.id} value={list.id.toString()}>
                                {list.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <DialogFooter>
                    <Button type="submit" disabled={addSubscriberMutation.isPending}>
                      {addSubscriberMutation.isPending ? 'Adding...' : 'Add Subscriber'}
                    </Button>
                  </DialogFooter>
                </form>
              </Form>
            </DialogContent>
          </Dialog>
          
          <Dialog open={importDialogOpen} onOpenChange={setImportDialogOpen}>
            <DialogTrigger asChild>
              <Button variant="outline">
                <Upload className="mr-2 h-4 w-4" />
                Import
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Import Subscribers</DialogTitle>
                <DialogDescription>
                  Upload a CSV file with subscriber information. The file should have columns for email (required), name, and status.
                </DialogDescription>
              </DialogHeader>
              
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="file">CSV File</Label>
                  <Input 
                    id="file" 
                    type="file" 
                    accept=".csv" 
                    onChange={(e) => setImportFile(e.target.files?.[0] || null)} 
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="list">Add to List (Optional)</Label>
                  <Select onValueChange={(value) => setImportListId(value ? parseInt(value) : null)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select list" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">None</SelectItem>
                      {listsData?.map((list: any) => (
                        <SelectItem key={list.id} value={list.id.toString()}>
                          {list.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <DialogFooter>
                  <Button 
                    onClick={() => importMutation.mutate()} 
                    disabled={!importFile || importMutation.isPending}
                  >
                    {importMutation.isPending ? 'Importing...' : 'Import Subscribers'}
                  </Button>
                </DialogFooter>
              </div>
            </DialogContent>
          </Dialog>
          
          <Button variant="outline" onClick={handleExport}>
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
          
          <Dialog open={newListDialogOpen} onOpenChange={setNewListDialogOpen}>
            <DialogTrigger asChild>
              <Button variant="outline">
                <Tag className="mr-2 h-4 w-4" />
                New List
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create New Subscriber List</DialogTitle>
                <DialogDescription>
                  Create a new list to organize your subscribers.
                </DialogDescription>
              </DialogHeader>
              
              <Form {...listForm}>
                <form onSubmit={listForm.handleSubmit((values) => addListMutation.mutate(values))} className="space-y-4">
                  <FormField
                    control={listForm.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>List Name</FormLabel>
                        <FormControl>
                          <Input placeholder="Newsletter Subscribers" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={listForm.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Description (Optional)</FormLabel>
                        <FormControl>
                          <Textarea placeholder="Subscribers to the weekly newsletter" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <DialogFooter>
                    <Button type="submit" disabled={addListMutation.isPending}>
                      {addListMutation.isPending ? 'Creating...' : 'Create List'}
                    </Button>
                  </DialogFooter>
                </form>
              </Form>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total Subscribers</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {statsLoading ? (
                <Skeleton className="h-8 w-16" />
              ) : (
                statsData?.total || 0
              )}
            </div>
            <p className="text-xs text-muted-foreground">All subscribers in your database</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Active Subscribers</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {statsLoading ? (
                <Skeleton className="h-8 w-16" />
              ) : (
                statsData?.active || 0
              )}
            </div>
            <p className="text-xs text-muted-foreground">Subscribers who can receive emails</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Unsubscribed</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {statsLoading ? (
                <Skeleton className="h-8 w-16" />
              ) : (
                statsData?.unsubscribed || 0
              )}
            </div>
            <p className="text-xs text-muted-foreground">People who opted out</p>
          </CardContent>
        </Card>
      </div>

      <div className="flex flex-col md:flex-row justify-between md:items-center gap-4">
        <div className="flex flex-wrap gap-2">
          <Select value={activeFilter || 'all-statuses'} onValueChange={(value) => setActiveFilter(value === 'all-statuses' ? null : value)}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all-statuses">All statuses</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="inactive">Inactive</SelectItem>
              <SelectItem value="unsubscribed">Unsubscribed</SelectItem>
            </SelectContent>
          </Select>
          
          <Select value={listFilter || 'all-lists'} onValueChange={(value) => setListFilter(value === 'all-lists' ? null : value)}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Filter by list" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all-lists">All lists</SelectItem>
              {listsData?.map((list: any) => (
                <SelectItem key={list.id} value={list.id.toString()}>
                  {list.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={() => {
              setActiveFilter(null); 
              setListFilter(null);
            }}
            disabled={!activeFilter && !listFilter}
          >
            <RefreshCw className="h-4 w-4 mr-1" />
            Reset
          </Button>
        </div>
        
        <div className="flex items-center gap-2">
          <Label>Show</Label>
          <Select value={limit.toString()} onValueChange={(value) => setLimit(parseInt(value))}>
            <SelectTrigger className="w-[70px]">
              <SelectValue placeholder="10" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="10">10</SelectItem>
              <SelectItem value="20">20</SelectItem>
              <SelectItem value="50">50</SelectItem>
              <SelectItem value="100">100</SelectItem>
            </SelectContent>
          </Select>
          <Label>per page</Label>
        </div>
      </div>

      <div className="border rounded-md">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Email</TableHead>
              <TableHead>Name</TableHead>
              {!isMobile && <TableHead>Status</TableHead>}
              {!isMobile && <TableHead>List</TableHead>}
              {!isMobile && <TableHead>Joined</TableHead>}
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {subscribersLoading ? (
              Array.from({ length: 5 }).map((_, i) => (
                <TableRow key={i}>
                  <TableCell colSpan={isMobile ? 3 : 6}>
                    <Skeleton className="h-6 w-full" />
                  </TableCell>
                </TableRow>
              ))
            ) : subscribersData?.data?.length ? (
              subscribersData.data.map((subscriber: any) => (
                <TableRow key={subscriber.id}>
                  <TableCell className="font-medium">{subscriber.email}</TableCell>
                  <TableCell>{subscriber.name || '-'}</TableCell>
                  {!isMobile && (
                    <TableCell>
                      <Badge variant={
                        subscriber.status === 'active' ? 'default' :
                        subscriber.status === 'inactive' ? 'secondary' : 'destructive'
                      }>
                        {subscriber.status}
                      </Badge>
                    </TableCell>
                  )}
                  {!isMobile && (
                    <TableCell>
                      {subscriber.listId ? (
                        listsData?.find((list: any) => list.id === subscriber.listId)?.name || '-'
                      ) : '-'}
                    </TableCell>
                  )}
                  {!isMobile && (
                    <TableCell>
                      {new Date(subscriber.createdAt).toLocaleDateString()}
                    </TableCell>
                  )}
                  <TableCell className="text-right">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        if (confirm('Are you sure you want to delete this subscriber?')) {
                          deleteMutation.mutate(subscriber.id);
                        }
                      }}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={isMobile ? 3 : 6} className="text-center py-4 text-muted-foreground">
                  No subscribers found
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {subscribersData?.pagination && subscribersData.pagination.pages > 1 && (
        <div className="flex justify-center mt-4">
          <div className="flex items-center gap-1">
            <PaginationButton
              onClick={() => setPage(Math.max(1, page - 1))}
              disabled={page === 1}
            >
              <ChevronLeft className="h-4 w-4" />
            </PaginationButton>
            
            <div className="flex items-center">
              <span className="text-sm font-medium">
                Page {page} of {subscribersData.pagination.pages}
              </span>
            </div>
            
            <PaginationButton
              onClick={() => setPage(Math.min(subscribersData.pagination.pages, page + 1))}
              disabled={page === subscribersData.pagination.pages}
            >
              <ChevronRight className="h-4 w-4" />
            </PaginationButton>
          </div>
        </div>
      )}
    </div>
  );
};

const EmailSettings = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  
  // Fetch current email settings
  const { data: settings, isLoading: settingsLoading } = useQuery({
    queryKey: ['/api/email-settings'],
    queryFn: async () => {
      const res = await apiRequest('GET', '/api/email-settings');
      return res.json();
    },
  });

  // Email settings form schema
  const emailSettingsSchema = z.object({
    provider: z.enum(['resend', 'sendgrid']),
    fromName: z.string().min(1, { message: "From name is required" }),
    fromEmail: z.string().email({ message: "Please enter a valid email" }),
    replyToEmail: z.string().email({ message: "Please enter a valid reply-to email" }),
    resendApiKey: z.string().optional(),
    sendgridApiKey: z.string().optional(),
  });

  // Settings form
  const settingsForm = useForm<z.infer<typeof emailSettingsSchema>>({
    resolver: zodResolver(emailSettingsSchema),
    defaultValues: {
      provider: 'resend',
      fromName: '',
      fromEmail: '',
      replyToEmail: '',
    },
  });

  // Update settings on form load
  React.useEffect(() => {
    if (settings) {
      settingsForm.reset({
        provider: settings.provider,
        fromName: settings.fromName,
        fromEmail: settings.fromEmail,
        replyToEmail: settings.replyToEmail,
      });
    }
  }, [settings, settingsForm]);

  // Save settings mutation
  const saveSettingsMutation = useMutation({
    mutationFn: async (values: z.infer<typeof emailSettingsSchema>) => {
      const res = await apiRequest('POST', '/api/email-settings', values);
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/email-settings'] });
      toast({
        title: 'Settings saved',
        description: 'Your email settings have been updated successfully.',
        variant: 'default',
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: `Failed to save settings: ${error.message}`,
        variant: 'destructive',
      });
    },
  });

  if (settingsLoading) {
    return (
      <div className="space-y-4">
        <h2 className="text-2xl font-bold">Email Settings</h2>
        <Card>
          <CardContent className="p-6 space-y-4">
            <Skeleton className="h-8 w-48" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-1/3 mt-4" />
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div>
        <h2 className="text-2xl font-bold">Email Settings</h2>
        <p className="text-muted-foreground">Configure your email service providers and settings</p>
      </div>

      <Card>
        <CardContent className="pt-6">
          <Form {...settingsForm}>
            <form onSubmit={settingsForm.handleSubmit((values) => saveSettingsMutation.mutate(values))} className="space-y-6">
              <FormField
                control={settingsForm.control}
                name="provider"
                render={({ field }) => (
                  <FormItem className="space-y-3">
                    <FormLabel>Email Service Provider</FormLabel>
                    <FormControl>
                      <div className="flex flex-col space-y-3">
                        <div className="flex items-center space-x-2">
                          <input
                            type="radio"
                            id="resend"
                            value="resend"
                            checked={field.value === 'resend'}
                            onChange={() => field.onChange('resend')}
                            className="h-4 w-4 text-primary"
                          />
                          <Label htmlFor="resend" className="font-normal">
                            Resend
                          </Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <input
                            type="radio"
                            id="sendgrid"
                            value="sendgrid"
                            checked={field.value === 'sendgrid'}
                            onChange={() => field.onChange('sendgrid')}
                            className="h-4 w-4 text-primary"
                          />
                          <Label htmlFor="sendgrid" className="font-normal">
                            SendGrid
                          </Label>
                        </div>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid gap-6 md:grid-cols-2">
                <FormField
                  control={settingsForm.control}
                  name="fromName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>From Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Your Company Name" {...field} />
                      </FormControl>
                      <FormDescription>
                        The name that will appear in the sender field
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={settingsForm.control}
                  name="fromEmail"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>From Email</FormLabel>
                      <FormControl>
                        <Input placeholder="<EMAIL>" {...field} />
                      </FormControl>
                      <FormDescription>
                        The email address that will be used to send emails
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={settingsForm.control}
                name="replyToEmail"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Reply-To Email</FormLabel>
                    <FormControl>
                      <Input placeholder="<EMAIL>" {...field} />
                    </FormControl>
                    <FormDescription>
                      The email address recipients will reply to
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {settingsForm.watch('provider') === 'resend' && (
                <FormField
                  control={settingsForm.control}
                  name="resendApiKey"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Resend API Key</FormLabel>
                      <FormControl>
                        <Input
                          type="password"
                          placeholder={settings?.resendApiKey ? "••••••••••••••••" : "Enter your Resend API key"}
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Your Resend API key. Leave blank to keep existing key.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}

              {settingsForm.watch('provider') === 'sendgrid' && (
                <FormField
                  control={settingsForm.control}
                  name="sendgridApiKey"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>SendGrid API Key</FormLabel>
                      <FormControl>
                        <Input
                          type="password"
                          placeholder={settings?.sendgridApiKey ? "••••••••••••••••" : "Enter your SendGrid API key"}
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Your SendGrid API key. Leave blank to keep existing key.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}

              <Button type="submit" disabled={saveSettingsMutation.isPending}>
                {saveSettingsMutation.isPending ? 'Saving...' : 'Save Settings'}
              </Button>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
};

const EmailTemplates = () => {
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [categoryFilter, setCategoryFilter] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [templateDialogOpen, setTemplateDialogOpen] = useState(false);
  const [currentTemplate, setCurrentTemplate] = useState<any>(null);
  const [previewTemplate, setPreviewTemplate] = useState<any>(null);
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const isDesktop = useMediaQuery("(min-width: 768px)");

  // Form schema for creating/editing templates
  const templateSchema = z.object({
    name: z.string().min(1, { message: "Template name is required" }),
    description: z.string().optional(),
    subject: z.string().min(1, { message: "Email subject is required" }),
    content: z.string().min(1, { message: "Email content is required" }),
    htmlContent: z.string().min(1, { message: "HTML content is required" }),
    category: z.string().default("general"),
    tags: z.array(z.string()).optional(),
    isDefault: z.boolean().default(false),
  });

  // Create form
  const templateForm = useForm<z.infer<typeof templateSchema>>({
    resolver: zodResolver(templateSchema),
    defaultValues: {
      name: "",
      description: "",
      subject: "",
      content: "",
      htmlContent: "",
      category: "general",
      tags: [],
      isDefault: false,
    },
  });

  // Fetch templates with pagination and filtering
  const { data: templatesData, isLoading: templatesLoading } = useQuery({
    queryKey: ['/api/email-templates', page, limit, categoryFilter, searchQuery],
    queryFn: async () => {
      let url = `/api/email-templates?page=${page}&limit=${limit}`;
      if (categoryFilter) url += `&category=${categoryFilter}`;
      if (searchQuery) url += `&search=${searchQuery}`;
      const res = await apiRequest('GET', url);
      return res.json();
    },
  });

  // Create template mutation
  const createTemplateMutation = useMutation({
    mutationFn: async (values: z.infer<typeof templateSchema>) => {
      const res = await apiRequest('POST', '/api/email-templates', values);
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/email-templates'] });
      setTemplateDialogOpen(false);
      templateForm.reset();
      toast({
        title: 'Template created',
        description: 'Email template has been created successfully.',
        variant: 'default',
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: `Failed to create template: ${error.message}`,
        variant: 'destructive',
      });
    },
  });

  // Update template mutation
  const updateTemplateMutation = useMutation({
    mutationFn: async (values: z.infer<typeof templateSchema> & { id: number }) => {
      const { id, ...data } = values;
      const res = await apiRequest('PUT', `/api/email-templates/${id}`, data);
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/email-templates'] });
      setTemplateDialogOpen(false);
      setCurrentTemplate(null);
      templateForm.reset();
      toast({
        title: 'Template updated',
        description: 'Email template has been updated successfully.',
        variant: 'default',
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: `Failed to update template: ${error.message}`,
        variant: 'destructive',
      });
    },
  });

  // Delete template mutation
  const deleteTemplateMutation = useMutation({
    mutationFn: async (id: number) => {
      await apiRequest('DELETE', `/api/email-templates/${id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/email-templates'] });
      toast({
        title: 'Template deleted',
        description: 'Email template has been deleted successfully.',
        variant: 'default',
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: `Failed to delete template: ${error.message}`,
        variant: 'destructive',
      });
    },
  });

  // Handle opening the dialog for editing
  const handleEditTemplate = (template: any) => {
    setCurrentTemplate(template);
    templateForm.reset({
      name: template.name,
      description: template.description || "",
      subject: template.subject,
      content: template.content,
      htmlContent: template.htmlContent,
      category: template.category || "general",
      tags: template.tags || [],
      isDefault: template.isDefault || false,
    });
    setTemplateDialogOpen(true);
  };

  // Handle opening the dialog for creating
  const handleCreateTemplate = () => {
    setCurrentTemplate(null);
    templateForm.reset({
      name: "",
      description: "",
      subject: "",
      content: "",
      htmlContent: "",
      category: "general",
      tags: [],
      isDefault: false,
    });
    setTemplateDialogOpen(true);
  };

  // Handle form submission
  const onSubmit = (values: z.infer<typeof templateSchema>) => {
    if (currentTemplate) {
      updateTemplateMutation.mutate({ ...values, id: currentTemplate.id });
    } else {
      createTemplateMutation.mutate(values);
    }
  };

  // Render template cards or empty state
  const renderTemplates = () => {
    if (templatesLoading) {
      return (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {Array.from({ length: 6 }).map((_, index) => (
            <Card key={index} className="flex flex-col h-64">
              <CardHeader className="pb-2">
                <Skeleton className="h-6 w-3/4" />
                <Skeleton className="h-4 w-1/2 mt-2" />
              </CardHeader>
              <CardContent className="flex-grow">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-full mt-2" />
                <Skeleton className="h-4 w-2/3 mt-2" />
              </CardContent>
              <CardFooter>
                <Skeleton className="h-9 w-full" />
              </CardFooter>
            </Card>
          ))}
        </div>
      );
    }

    if (!templatesData?.data?.length) {
      return (
        <Card className="flex flex-col items-center justify-center py-12">
          <FileText className="h-16 w-16 text-muted-foreground mb-4" />
          <h3 className="text-lg font-medium">No Email Templates Yet</h3>
          <p className="text-muted-foreground text-center max-w-md mt-2">
            Create your first email template to get started with your email marketing campaigns.
          </p>
          <Button className="mt-4" onClick={handleCreateTemplate}>
            <Plus className="h-4 w-4 mr-2" />
            Create Template
          </Button>
        </Card>
      );
    }

    return (
      <>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {templatesData.data.map((template: any) => (
            <Card key={template.id} className="flex flex-col h-64">
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="text-lg">{template.name}</CardTitle>
                    {template.category && (
                      <Badge variant="outline" className="mt-1">
                        {template.category}
                      </Badge>
                    )}
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                        <span className="sr-only">Open menu</span>
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => setPreviewTemplate(template)}>
                        <Eye className="mr-2 h-4 w-4" />
                        Preview
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleEditTemplate(template)}>
                        <Edit className="mr-2 h-4 w-4" />
                        Edit
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        className="text-destructive focus:text-destructive"
                        onClick={() => {
                          if (window.confirm("Are you sure you want to delete this template?")) {
                            deleteTemplateMutation.mutate(template.id);
                          }
                        }}
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
                {template.description && (
                  <CardDescription className="line-clamp-2">{template.description}</CardDescription>
                )}
              </CardHeader>
              <CardContent className="flex-grow overflow-hidden">
                <div className="text-sm text-muted-foreground mb-2">
                  <strong>Subject:</strong> {template.subject}
                </div>
                <div className="text-sm text-muted-foreground line-clamp-3">
                  {template.content}
                </div>
              </CardContent>
              <CardFooter className="pt-0">
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full"
                  onClick={() => handleEditTemplate(template)}
                >
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Template
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>

        {templatesData.pagination.totalPages > 1 && (
          <div className="flex justify-center mt-8">
            <PaginationButton
              currentPage={page}
              totalPages={templatesData.pagination.totalPages}
              onPageChange={setPage}
            />
          </div>
        )}
      </>
    );
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-col md:flex-row justify-between md:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold">Email Templates</h2>
          <p className="text-muted-foreground">Create and manage your email templates</p>
        </div>

        <div className="flex flex-wrap gap-2">
          <Button onClick={handleCreateTemplate}>
            <Plus className="h-4 w-4 mr-2" />
            New Template
          </Button>
        </div>
      </div>

      {/* Search and filter */}
      <div className="flex flex-col md:flex-row gap-4">
        <div className="relative flex-grow">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search templates..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <Select
          value={categoryFilter || "all"}
          onValueChange={(value) => setCategoryFilter(value === "all" ? null : value)}
        >
          <SelectTrigger className="w-full md:w-[180px]">
            <SelectValue placeholder="Filter by category" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Categories</SelectItem>
            {templatesData?.categories?.map((category: string) => (
              <SelectItem key={category} value={category}>
                {category}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Templates Grid */}
      {renderTemplates()}

      {/* Template Create/Edit Dialog */}
      <Dialog open={templateDialogOpen} onOpenChange={setTemplateDialogOpen}>
        <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>{currentTemplate ? "Edit Template" : "Create Template"}</DialogTitle>
            <DialogDescription>
              {currentTemplate
                ? "Update your email template details"
                : "Fill in the details to create a new email template"}
            </DialogDescription>
          </DialogHeader>

          <Form {...templateForm}>
            <form onSubmit={templateForm.handleSubmit(onSubmit)} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={templateForm.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Template Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Welcome Email" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={templateForm.control}
                  name="category"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Category</FormLabel>
                      <Select value={field.value} onValueChange={field.onChange}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select category" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="general">General</SelectItem>
                          <SelectItem value="newsletter">Newsletter</SelectItem>
                          <SelectItem value="promotional">Promotional</SelectItem>
                          <SelectItem value="transactional">Transactional</SelectItem>
                          <SelectItem value="announcement">Announcement</SelectItem>
                          <SelectItem value="welcome">Welcome</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={templateForm.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description (Optional)</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="A brief description of the template"
                        className="resize-none"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={templateForm.control}
                name="subject"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email Subject</FormLabel>
                    <FormControl>
                      <Input placeholder="Welcome to our platform!" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={templateForm.control}
                  name="content"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Plain Text Content</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Enter the plain text version of your email content..."
                          className="h-40 resize-none"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Plain text version for email clients that don't support HTML.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={templateForm.control}
                  name="htmlContent"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>HTML Content</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="<h1>Welcome!</h1><p>Thank you for subscribing...</p>"
                          className="h-40 resize-none font-mono text-sm"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        HTML version for rich email content. Use HTML tags to format your email.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={templateForm.control}
                name="isDefault"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center space-x-3 space-y-0 rounded-md border p-4">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel className="cursor-pointer">Set as Default Template</FormLabel>
                      <FormDescription>
                        Use this as the default template for new campaigns
                      </FormDescription>
                    </div>
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button 
                  type="submit" 
                  disabled={
                    createTemplateMutation.isPending || 
                    updateTemplateMutation.isPending
                  }
                >
                  {createTemplateMutation.isPending || updateTemplateMutation.isPending
                    ? "Saving..."
                    : currentTemplate
                    ? "Update Template"
                    : "Create Template"}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Template Preview Dialog */}
      <Dialog open={!!previewTemplate} onOpenChange={() => setPreviewTemplate(null)}>
        <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Template Preview: {previewTemplate?.name}</DialogTitle>
          </DialogHeader>
          
          <div className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Email Subject</CardTitle>
              </CardHeader>
              <CardContent>
                <p>{previewTemplate?.subject}</p>
              </CardContent>
            </Card>
            
            <Tabs defaultValue="html">
              <TabsList className="mb-4">
                <TabsTrigger value="html">HTML Content</TabsTrigger>
                <TabsTrigger value="text">Plain Text</TabsTrigger>
                <TabsTrigger value="rendered">Rendered Email</TabsTrigger>
              </TabsList>
              
              <TabsContent value="html">
                <Card>
                  <CardHeader>
                    <CardTitle>HTML Content</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <pre className="bg-secondary p-4 rounded-md overflow-x-auto">
                      <code>{previewTemplate?.htmlContent}</code>
                    </pre>
                  </CardContent>
                </Card>
              </TabsContent>
              
              <TabsContent value="text">
                <Card>
                  <CardHeader>
                    <CardTitle>Plain Text Content</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <pre className="bg-secondary p-4 rounded-md whitespace-pre-wrap">
                      {previewTemplate?.content}
                    </pre>
                  </CardContent>
                </Card>
              </TabsContent>
              
              <TabsContent value="rendered">
                <Card>
                  <CardHeader>
                    <CardTitle>Rendered Email Preview</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="border rounded-md p-4">
                      {/* Security note: Using dangerouslySetInnerHTML only for preview purposes */}
                      <div 
                        className="email-preview" 
                        dangerouslySetInnerHTML={{ __html: previewTemplate?.htmlContent || "" }} 
                      />
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
          
          <DialogFooter>
            <Button 
              onClick={() => handleEditTemplate(previewTemplate)}
              variant="outline"
            >
              <Edit className="mr-2 h-4 w-4" />
              Edit
            </Button>
            <Button onClick={() => setPreviewTemplate(null)}>Close</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

const Campaigns = () => {
  return (
    <div className="space-y-4">
      <div>
        <h2 className="text-2xl font-bold">Email Campaigns</h2>
        <p className="text-muted-foreground">Create and manage your email marketing campaigns</p>
      </div>
      
      <Card className="flex flex-col items-center justify-center py-12">
        <MailCheck className="h-16 w-16 text-muted-foreground mb-4" />
        <h3 className="text-lg font-medium">No Campaigns Yet</h3>
        <p className="text-muted-foreground text-center max-w-md mt-2">
          Start creating email campaigns to engage with your subscribers.
        </p>
        <Button className="mt-4">
          <Plus className="h-4 w-4 mr-2" />
          Create Campaign
        </Button>
      </Card>
    </div>
  );
};

const Reports = () => {
  return (
    <div className="space-y-4">
      <div>
        <h2 className="text-2xl font-bold">Email Reports</h2>
        <p className="text-muted-foreground">View analytics and reports for your email campaigns</p>
      </div>
      
      <Card className="flex flex-col items-center justify-center py-12">
        <FilePieChart className="h-16 w-16 text-muted-foreground mb-4" />
        <h3 className="text-lg font-medium">No Reports Available</h3>
        <p className="text-muted-foreground text-center max-w-md mt-2">
          Run your first email campaign to start generating reports and analytics.
        </p>
        <Button className="mt-4" variant="outline">
          <Plus className="h-4 w-4 mr-2" />
          Create Campaign
        </Button>
      </Card>
    </div>
  );
};

// Main Email Marketing component
const EmailMarketing = () => {
  const [location, setLocation] = useLocation();
  const [match, params] = useRoute('/marketing/email/:tab');
  const currentTab = match ? params.tab : 'subscribers';

  const handleTabChange = (tab: string) => {
    setLocation(`/marketing/email/${tab}`);
  };

  return (
    <div className="container mx-auto py-6 max-w-7xl">
      <div className="mb-6">
        <h1 className="text-3xl font-bold">Email Marketing</h1>
        <p className="text-muted-foreground">Manage your email marketing campaigns, subscribers, and analytics</p>
      </div>

      <Tabs value={currentTab} onValueChange={handleTabChange} className="space-y-4">
        <TabsList className="grid grid-cols-2 md:grid-cols-5 gap-2">
          <TabsTrigger value="subscribers" className="flex gap-2 items-center">
            <Users className="h-4 w-4" />
            <span>Subscribers</span>
          </TabsTrigger>
          <TabsTrigger value="templates" className="flex gap-2 items-center">
            <FileText className="h-4 w-4" />
            <span>Templates</span>
          </TabsTrigger>
          <TabsTrigger value="campaigns" className="flex gap-2 items-center">
            <MessageSquarePlus className="h-4 w-4" />
            <span>Campaigns</span>
          </TabsTrigger>
          <TabsTrigger value="reports" className="flex gap-2 items-center">
            <FilePieChart className="h-4 w-4" />
            <span>Reports</span>
          </TabsTrigger>
          <TabsTrigger value="settings" className="flex gap-2 items-center">
            <SettingsIcon className="h-4 w-4" />
            <span>Settings</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="subscribers">
          <SubscriberManagement />
        </TabsContent>
        
        <TabsContent value="templates">
          <div className="space-y-4">
            <div className="flex justify-between items-center mb-4">
              <div>
                <h2 className="text-2xl font-bold">Email Templates</h2>
                <p className="text-muted-foreground">Create and manage your email templates</p>
              </div>
              <Button asChild>
                <Link href="/marketing/email/templates">
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Manage Templates
                </Link>
              </Button>
            </div>
            
            <Card>
              <CardContent className="p-6">
                <div className="text-center space-y-2">
                  <FileText className="h-12 w-12 mx-auto text-muted-foreground" />
                  <h3 className="text-lg font-medium">Manage Your Email Templates</h3>
                  <p className="text-muted-foreground">
                    Create, edit, and organize your email templates in the dedicated templates section
                  </p>
                  <Button asChild className="mt-4">
                    <Link href="/marketing/email/templates">
                      Open Templates Manager
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        <TabsContent value="campaigns">
          <Campaigns />
        </TabsContent>
        
        <TabsContent value="reports">
          <Reports />
        </TabsContent>
        
        <TabsContent value="settings">
          <EmailSettings />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default EmailMarketing;