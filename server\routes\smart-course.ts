import express from 'express';
import { Request, Response } from 'express';
import OpenAI from 'openai';
import { GoogleGenerativeAI } from '@google/generative-ai';

const router = express.Router();

// Initialize AI services
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

const genAI = new GoogleGenerativeAI(process.env.GOOGLE_GEMINI_API_KEY || '');

// Generate comprehensive course structure from just a title
router.post('/generate-smart-course-structure', async (req: Request, res: Response) => {
  try {
    const { title } = req.body;

    if (!title) {
      return res.status(400).json({ success: false, error: 'Course title is required' });
    }

    const prompt = `You are an expert course creator. Generate a comprehensive course structure for: "${title}"

Create a detailed course with 3-4 modules, each containing 2-3 lessons. Make it educational, engaging, and practical.

Respond with JSON in this exact format:
{
  "title": "Enhanced course title",
  "description": "Comprehensive course description (2-3 sentences)",
  "modules": [
    {
      "id": "module-1",
      "title": "Module title",
      "description": "Module description",
      "lessons": [
        {
          "id": "lesson-1-1",
          "title": "Lesson title",
          "description": "Lesson description focusing on specific learning outcomes"
        }
      ]
    }
  ]
}

Make sure each lesson builds upon previous ones and has clear educational value. Focus on practical, actionable content that viewers will want to learn.`;

    let courseStructure;

    try {
      // Try OpenAI first
      const response = await openai.chat.completions.create({
        model: "gpt-4o", // the newest OpenAI model is "gpt-4o" which was released May 13, 2024. do not change this unless explicitly requested by the user
        messages: [
          { role: "system", content: "You are an expert educational content creator. Always respond with valid JSON." },
          { role: "user", content: prompt }
        ],
        response_format: { type: "json_object" },
        temperature: 0.7,
        max_tokens: 2000
      });

      courseStructure = JSON.parse(response.choices[0].message.content || '{}');
    } catch (openaiError) {
      console.log('OpenAI failed, falling back to Gemini:', openaiError);
      
      // Fallback to Gemini
      const model = genAI.getGenerativeModel({ model: 'gemini-pro' });
      const result = await model.generateContent(prompt);
      const text = result.response.text();
      
      // Extract JSON from response
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('Failed to get valid JSON from Gemini');
      }
      
      courseStructure = JSON.parse(jsonMatch[0]);
    }

    // Add status and IDs to all elements
    courseStructure.status = 'draft';
    courseStructure.modules = courseStructure.modules.map((module: any, moduleIndex: number) => ({
      ...module,
      id: module.id || `module-${moduleIndex + 1}`,
      status: 'pending',
      lessons: module.lessons.map((lesson: any, lessonIndex: number) => ({
        ...lesson,
        id: lesson.id || `lesson-${moduleIndex + 1}-${lessonIndex + 1}`,
        status: 'pending',
        scenes: []
      }))
    }));

    res.json({
      success: true,
      courseStructure
    });

  } catch (error: any) {
    console.error('Course structure generation error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to generate course structure'
    });
  }
});

// Generate 4 engaging scenes for a lesson
router.post('/generate-lesson-scenes', async (req: Request, res: Response) => {
  try {
    const { courseTitle, moduleTitle, lessonTitle, lessonDescription, isLastLesson, isLastModule } = req.body;

    const hookPrompt = isLastLesson && isLastModule 
      ? "End with a compelling conclusion that motivates further learning"
      : isLastLesson 
        ? "End with a hook that creates excitement for the next module"
        : "End with a hook that makes viewers eager for the next lesson";

    const prompt = `Create 4 engaging video scenes for this lesson:
Course: ${courseTitle}
Module: ${moduleTitle}
Lesson: ${lessonTitle}
Description: ${lessonDescription}

Create 4 scenes that are educational, attention-grabbing, and make viewers say "I need to know more!"

${hookPrompt}

For each scene, provide:
1. Compelling educational text (30-60 words) that will be converted to speech
2. 5-6 one-word search terms for finding background videos from Pexels

Respond with JSON in this exact format:
{
  "scenes": [
    {
      "id": "scene-1",
      "text": "Engaging educational text for TTS",
      "searchTerms": ["word1", "word2", "word3", "word4", "word5", "word6"]
    }
  ]
}

Make each scene feel unique and valuable. Keep the tone educational but exciting.`;

    let scenes;

    try {
      // Try OpenAI first
      const response = await openai.chat.completions.create({
        model: "gpt-4o", // the newest OpenAI model is "gpt-4o" which was released May 13, 2024. do not change this unless explicitly requested by the user
        messages: [
          { role: "system", content: "You are an expert video content creator. Always respond with valid JSON." },
          { role: "user", content: prompt }
        ],
        response_format: { type: "json_object" },
        temperature: 0.8,
        max_tokens: 1000
      });

      scenes = JSON.parse(response.choices[0].message.content || '{}');
    } catch (openaiError) {
      console.log('OpenAI failed, falling back to Gemini:', openaiError);
      
      // Fallback to Gemini
      const model = genAI.getGenerativeModel({ model: 'gemini-pro' });
      const result = await model.generateContent(prompt);
      const text = result.response.text();
      
      // Extract JSON from response
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('Failed to get valid JSON from Gemini');
      }
      
      scenes = JSON.parse(jsonMatch[0]);
    }

    res.json({
      success: true,
      scenes: scenes.scenes || []
    });

  } catch (error: any) {
    console.error('Scene generation error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to generate lesson scenes'
    });
  }
});

// Kokoro TTS endpoint (using OpenAI TTS)
router.post('/kokoro-tts', async (req: Request, res: Response) => {
  try {
    const { text, voice = 'alloy', speed = 1.0 } = req.body;

    if (!text) {
      return res.status(400).json({ success: false, error: 'Text is required' });
    }

    if (!process.env.OPENAI_API_KEY) {
      return res.status(500).json({ 
        success: false, 
        error: 'OpenAI API key not configured' 
      });
    }

    // Generate TTS using OpenAI
    const mp3 = await openai.audio.speech.create({
      model: "tts-1-hd",
      voice: voice as any,
      input: text,
      speed: Math.max(0.25, Math.min(4.0, speed))
    });

    const buffer = Buffer.from(await mp3.arrayBuffer());
    const fileName = `tts_${Date.now()}.mp3`;
    
    // Save to uploads directory
    const fs = await import('fs');
    const path = await import('path');
    
    const uploadsDir = path.join(process.cwd(), 'uploads', 'audio');
    if (!fs.existsSync(uploadsDir)) {
      fs.mkdirSync(uploadsDir, { recursive: true });
    }
    
    const filePath = path.join(uploadsDir, fileName);
    fs.writeFileSync(filePath, buffer);

    res.json({
      success: true,
      audioUrl: `/uploads/audio/${fileName}`,
      duration: Math.ceil(text.length / 12) // Estimate: 12 chars per second
    });

  } catch (error: any) {
    console.error('TTS generation error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to generate TTS audio'
    });
  }
});

// Whisper captions endpoint
router.post('/whisper-captions', async (req: Request, res: Response) => {
  try {
    const { audioUrl } = req.body;

    if (!audioUrl) {
      return res.status(400).json({ success: false, error: 'Audio URL is required' });
    }

    // In a real implementation, this would use Whisper to generate captions
    // For now, return a mock captions URL
    const captionsUrl = audioUrl.replace('.mp3', '.vtt');

    res.json({
      success: true,
      captionsUrl
    });

  } catch (error: any) {
    console.error('Caption generation error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to generate captions'
    });
  }
});

// Pexels media endpoint
router.post('/pexels-media', async (req: Request, res: Response) => {
  try {
    const { searchTerms, mediaType = 'video', duration = 10 } = req.body;

    if (!searchTerms || !Array.isArray(searchTerms)) {
      return res.status(400).json({ success: false, error: 'Search terms array is required' });
    }

    const primaryTerm = searchTerms[0];
    const pexelsApiKey = process.env.PEXELS_API_KEY;

    if (!pexelsApiKey) {
      return res.status(500).json({ 
        success: false, 
        error: 'Pexels API key not configured' 
      });
    }

    // Call Pexels API for videos
    const pexelsResponse = await fetch(
      `https://api.pexels.com/videos/search?query=${encodeURIComponent(primaryTerm)}&per_page=1&orientation=landscape`,
      {
        headers: {
          'Authorization': pexelsApiKey
        }
      }
    );

    if (!pexelsResponse.ok) {
      throw new Error(`Pexels API error: ${pexelsResponse.status}`);
    }

    const pexelsData = await pexelsResponse.json();

    if (pexelsData.videos && pexelsData.videos.length > 0) {
      const video = pexelsData.videos[0];
      // Get the highest quality video file
      const videoFile = video.video_files.find((file: any) => file.quality === 'hd') || 
                       video.video_files[0];

      res.json({
        success: true,
        videoUrl: videoFile.link,
        searchTerm: primaryTerm,
        duration: video.duration || duration,
        attribution: {
          photographer: video.user.name,
          url: video.url
        }
      });
    } else {
      // Fallback to placeholder if no videos found
      res.json({
        success: true,
        videoUrl: `/uploads/videos/placeholder_${primaryTerm}_${Date.now()}.mp4`,
        searchTerm: primaryTerm,
        duration
      });
    }

  } catch (error: any) {
    console.error('Pexels media error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to get stock media'
    });
  }
});

// Assemble lesson video endpoint
router.post('/assemble-lesson-video', async (req: Request, res: Response) => {
  try {
    const { lessonId, scenes } = req.body;

    if (!lessonId || !scenes) {
      return res.status(400).json({ success: false, error: 'Lesson ID and scenes are required' });
    }

    // In a real implementation, this would use FFmpeg to combine scenes
    // For now, return a mock assembled video URL
    const videoUrl = `/uploads/videos/lesson_${lessonId}_${Date.now()}.mp4`;

    res.json({
      success: true,
      videoUrl,
      duration: scenes.length * 15 // Estimate 15 seconds per scene
    });

  } catch (error: any) {
    console.error('Video assembly error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to assemble lesson video'
    });
  }
});

export default router;