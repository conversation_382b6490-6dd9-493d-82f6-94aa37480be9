import { apiRequest } from "@/lib/queryClient";

// Course content type definitions
export interface CourseContentItem {
  title: string;
  description?: string;
  content?: string;
  type?: string;
  duration?: number;
  imageUrl?: string;
}

export interface CourseModule {
  id: number;
  title: string;
  description?: string;
  lessons: CourseLesson[];
}

export interface CourseLesson {
  id: number;
  title: string;
  description?: string;
  content?: string;
  duration?: number;
  videoUrl?: string;
  script?: string;
  imageUrl?: string;
}

export interface Course {
  id: number;
  title: string;
  description: string;
  thumbnail?: string;
  modules: CourseModule[];
  author?: string;
  authorId?: number;
  createdAt?: string;
  updatedAt?: string;
}

/**
 * Fetches course data from the API
 * @param courseId The ID of the course to fetch
 * @returns Promise resolving to the course data
 */
export async function fetchCourseData(courseId: number): Promise<Course> {
  try {
    const response = await apiRequest('GET', `/api/courses/${courseId}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch course: ${response.statusText}`);
    }
    const course = await response.json();
    
    // Fetch detailed module data with lessons
    const modulesWithLessons = await Promise.all(
      course.modules.map(async (module: any) => {
        const lessonsResponse = await apiRequest('GET', `/api/courses/${courseId}/modules/${module.id}/lessons`);
        if (!lessonsResponse.ok) {
          throw new Error(`Failed to fetch lessons: ${lessonsResponse.statusText}`);
        }
        const lessons = await lessonsResponse.json();
        return {
          ...module,
          lessons
        };
      })
    );
    
    return {
      ...course,
      modules: modulesWithLessons
    };
  } catch (error) {
    console.error('Error fetching course data:', error);
    throw error;
  }
}

/**
 * Generate a formatted course PDF as a Blob
 * This function uses client-side JavaScript to generate a PDF
 * 
 * @param course The course data to generate a PDF from
 * @returns Promise resolving to a Blob containing the PDF
 */
export async function generateCoursePdf(course: Course): Promise<Blob> {
  // Return a promise that resolves when the PDF is generated
  return new Promise(async (resolve, reject) => {
    try {
      // Dynamically load the jsPDF script
      const jsPDFScript = document.createElement('script');
      jsPDFScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js';
      jsPDFScript.async = true;
      
      // Load the autoTable plugin for better content formatting
      const autoTableScript = document.createElement('script');
      autoTableScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.8.0/jspdf.plugin.autotable.min.js';
      autoTableScript.async = true;
      
      // Wait for both scripts to load
      jsPDFScript.onload = () => {
        autoTableScript.onload = () => {
          generatePDF(course, resolve, reject);
        };
        document.head.appendChild(autoTableScript);
      };
      
      document.head.appendChild(jsPDFScript);
    } catch (error) {
      console.error('Error generating PDF:', error);
      reject(error);
    }
  });
}

/**
 * Helper function to actually generate the PDF once libraries are loaded
 */
function generatePDF(course: Course, resolve: (blob: Blob) => void, reject: (reason: any) => void) {
  try {
    // @ts-ignore - jsPDF is loaded dynamically
    const { jsPDF } = window.jspdf;
    
    // Create a new PDF document
    const doc = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4'
    });
    
    // Set document properties
    doc.setProperties({
      title: `${course.title} - Course Content`,
      subject: course.description,
      author: course.author || 'Course Creator Platform',
      keywords: 'course, education, learning',
      creator: 'Course Creator Platform'
    });
    
    // Add course title
    doc.setFontSize(24);
    doc.setTextColor(0, 0, 128); // Dark blue
    doc.text(course.title, 105, 20, { align: 'center' });
    
    // Add course description
    doc.setFontSize(12);
    doc.setTextColor(60, 60, 60); // Dark gray
    
    // Split long description into multiple lines
    const descLines = doc.splitTextToSize(course.description, 180);
    doc.text(descLines, 15, 30);
    
    // Add creation date
    if (course.createdAt) {
      doc.setFontSize(10);
      doc.setTextColor(100, 100, 100); // Light gray
      doc.text(`Created: ${new Date(course.createdAt).toLocaleDateString()}`, 15, 50);
    }
    
    let yPos = 60; // Start position for modules
    
    // Add modules and lessons
    course.modules.forEach((module, moduleIndex) => {
      // Add some spacing between modules
      if (moduleIndex > 0) {
        yPos += 10;
      }
      
      // Check if we need a new page
      if (yPos > 270) {
        doc.addPage();
        yPos = 20;
      }
      
      // Add module title
      doc.setFontSize(16);
      doc.setTextColor(70, 102, 255); // Bright blue
      doc.text(`Module ${moduleIndex + 1}: ${module.title}`, 15, yPos);
      yPos += 8;
      
      // Add module description if available
      if (module.description) {
        doc.setFontSize(11);
        doc.setTextColor(80, 80, 80);
        const moduleDescLines = doc.splitTextToSize(module.description, 180);
        doc.text(moduleDescLines, 15, yPos);
        yPos += moduleDescLines.length * 6;
      }
      
      // Add lessons
      module.lessons.forEach((lesson, lessonIndex) => {
        // Check if we need a new page
        if (yPos > 260) {
          doc.addPage();
          yPos = 20;
        }
        
        // Add lesson title
        doc.setFontSize(14);
        doc.setTextColor(0, 0, 0); // Black
        doc.text(`Lesson ${lessonIndex + 1}: ${lesson.title}`, 20, yPos);
        yPos += 6;
        
        // Add lesson description if available
        if (lesson.description) {
          doc.setFontSize(11);
          doc.setTextColor(80, 80, 80);
          const lessonDescLines = doc.splitTextToSize(lesson.description, 170);
          doc.text(lessonDescLines, 25, yPos);
          yPos += lessonDescLines.length * 5 + 2;
        }
        
        // Add lesson duration if available
        if (lesson.duration) {
          doc.setFontSize(10);
          doc.setTextColor(120, 120, 120);
          doc.text(`Duration: ${lesson.duration} minutes`, 25, yPos);
          yPos += 5;
        }
        
        // Add lesson script/content if available (truncated for PDF readability)
        if (lesson.script) {
          // Check if we need a new page
          if (yPos > 240) {
            doc.addPage();
            yPos = 20;
          }
          
          doc.setFontSize(10);
          doc.setTextColor(100, 100, 100);
          doc.text('Lesson Script:', 25, yPos);
          yPos += 5;
          
          doc.setFontSize(9);
          // Truncate very long scripts to prevent huge PDFs
          let scriptText = lesson.script;
          if (scriptText.length > 500) {
            scriptText = scriptText.substring(0, 500) + '... (full script available in the course)';
          }
          
          const scriptLines = doc.splitTextToSize(scriptText, 165);
          doc.text(scriptLines, 30, yPos);
          yPos += scriptLines.length * 4 + 5;
        }
      });
    });
    
    // Add page numbers
    const pageCount = doc.getNumberOfPages();
    for (let i = 1; i <= pageCount; i++) {
      doc.setPage(i);
      doc.setFontSize(10);
      doc.setTextColor(150, 150, 150);
      doc.text(`Page ${i} of ${pageCount}`, 105, 290, { align: 'center' });
    }
    
    // Generate PDF blob
    const pdfBlob = doc.output('blob');
    resolve(pdfBlob);
    
  } catch (error) {
    console.error('Error in PDF generation:', error);
    reject(error);
  }
}

/**
 * Download a course as a PDF file
 * @param courseId The ID of the course to download
 * @returns Promise that resolves when the download starts
 */
export async function downloadCoursePdf(courseId: number): Promise<void> {
  try {
    // Show loading indicator
    const loadingElement = document.createElement('div');
    loadingElement.style.position = 'fixed';
    loadingElement.style.top = '50%';
    loadingElement.style.left = '50%';
    loadingElement.style.transform = 'translate(-50%, -50%)';
    loadingElement.style.background = 'rgba(0, 0, 0, 0.7)';
    loadingElement.style.color = 'white';
    loadingElement.style.padding = '20px';
    loadingElement.style.borderRadius = '5px';
    loadingElement.style.zIndex = '9999';
    loadingElement.textContent = 'Generating PDF...';
    document.body.appendChild(loadingElement);
    
    // Fetch course data
    const course = await fetchCourseData(courseId);
    
    // Generate the PDF
    const pdfBlob = await generateCoursePdf(course);
    
    // Create download link
    const downloadUrl = URL.createObjectURL(pdfBlob);
    const downloadLink = document.createElement('a');
    downloadLink.href = downloadUrl;
    downloadLink.download = `${course.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_course.pdf`;
    
    // Trigger download
    document.body.appendChild(downloadLink);
    downloadLink.click();
    
    // Clean up
    document.body.removeChild(downloadLink);
    URL.revokeObjectURL(downloadUrl);
    document.body.removeChild(loadingElement);
    
  } catch (error) {
    console.error('Error downloading course PDF:', error);
    // Remove loading indicator if there was an error
    const loadingElement = document.querySelector('[data-pdf-loading]');
    if (loadingElement && loadingElement.parentNode) {
      loadingElement.parentNode.removeChild(loadingElement);
    }
    
    // Show error message to user
    alert('Failed to generate course PDF. Please try again later.');
  }
}