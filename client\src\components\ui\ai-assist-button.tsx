import React from 'react';
import { <PERSON>rk<PERSON>, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface AIAssistButtonProps {
  onClick: () => void;
  isLoading?: boolean;
  tooltipText?: string;
  variant?: "default" | "outline" | "ghost" | "secondary" | "link" | "destructive";
  size?: "default" | "sm" | "lg" | "icon";
  className?: string;
}

export function AIAssistButton({
  onClick,
  isLoading = false,
  tooltipText = "Generate with AI",
  variant = "ghost",
  size = "icon",
  className,
}: AIAssistButtonProps) {
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant={variant}
            size={size}
            onClick={onClick}
            disabled={isLoading}
            className={`text-purple-500 hover:text-purple-600 hover:bg-purple-50 ${className || ''}`}
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Sparkles className="h-4 w-4" />
            )}
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>{tooltipText}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
