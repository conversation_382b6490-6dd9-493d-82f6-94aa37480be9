import React, { useState } from 'react';
import { useQuery, useMutation } from '@tanstack/react-query';
import { useToast } from '@/hooks/use-toast';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Ta<PERSON>,
  Ta<PERSON><PERSON>ontent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Pie<PERSON>hart,
  Pie,
  Cell,
  Legend,
} from 'recharts';
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { apiRequest, queryClient } from '@/lib/queryClient';
import { Loader2, CreditCard, ArrowUpCircle, <PERSON><PERSON><PERSON> as PieChartIcon, Bar<PERSON>hart as BarChartIcon, History, Filter } from 'lucide-react';
import { format } from 'date-fns';
import { useRoute, useLocation } from 'wouter';
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Input } from '@/components/ui/input';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";

interface CreditData {
  id: number;
  userId: number;
  totalCredits: number;
  usedCredits: number;
  plan: string;
  nextRefill: string;
  stripeCustomerId?: string;
  stripeSubscriptionId?: string;
  createdAt: string;
  updatedAt: string;
}

interface UsageHistoryItem {
  id: number;
  userId: number;
  feature: 'text' | 'speech' | 'image' | 'video';
  credits: number;
  model: string;
  courseId?: number;
  courseName?: string;
  tokenCount?: number;
  characterCount?: number;
  imageCount?: number;
  prompt?: string;
  createdAt: string;
}

interface UsageHistoryResponse {
  data: UsageHistoryItem[];
  count: number;
  limit: number;
  offset: number;
}

const purchaseFormSchema = z.object({
  amount: z
    .number()
    .min(100, "Minimum purchase is 100 credits")
    .max(10000, "Maximum purchase is 10,000 credits"),
});

const planUpgradeSchema = z.object({
  plan: z.enum(["free", "starter", "pro", "business", "enterprise"]),
});

// Define valid plan types for type safety
type PlanType = "free" | "starter" | "pro" | "business" | "enterprise";

const creditPlans = [
  { name: "Starter", value: "starter" as PlanType, credits: 500, price: 49 },
  { name: "Pro", value: "pro" as PlanType, credits: 2000, price: 129 },
  { name: "Business", value: "business" as PlanType, credits: 5000, price: 299 },
  { name: "Enterprise", value: "enterprise" as PlanType, credits: 10000, price: 999 }
];

function getFeatureIcon(feature: string) {
  switch (feature) {
    case 'text':
      return <span className="text-blue-500">📝</span>;
    case 'speech':
      return <span className="text-green-500">🎤</span>;
    case 'image':
      return <span className="text-purple-500">🖼️</span>;
    case 'video':
      return <span className="text-red-500">🎬</span>;
    default:
      return <span className="text-gray-500">❓</span>;
  }
}

function formatDate(dateString: string) {
  return format(new Date(dateString), 'MMM d, yyyy h:mm a');
}

function getPlanColor(plan: string) {
  switch (plan) {
    case 'free':
      return 'bg-gray-100 text-gray-800';
    case 'starter':
      return 'bg-blue-100 text-blue-800';
    case 'pro':
      return 'bg-green-100 text-green-800';
    case 'business':
      return 'bg-purple-100 text-purple-800';
    case 'enterprise':
      return 'bg-pink-100 text-pink-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
}

function getPlanName(plan: string) {
  return plan.charAt(0).toUpperCase() + plan.slice(1);
}

function formatCreditPrice(amount: number) {
  // This pricing is approximate - adjust based on actual pricing structure
  const price = (amount / 100) * 4.99;
  return `$${price.toFixed(2)}`;
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8'];

// Helper function to safely calculate usage totals
function calculateUsageTotal(data: UsageHistoryItem[] | undefined): number {
  if (!data || data.length === 0) return 0;
  return data.reduce((sum, item) => sum + item.credits, 0);
}

const AICreditsPage = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [period, setPeriod] = useState('month');
  const [feature, setFeature] = useState('all');
  const [isPurchaseDialogOpen, setIsPurchaseDialogOpen] = useState(false);
  const [isUpgradeDialogOpen, setIsUpgradeDialogOpen] = useState(false);
  const { toast } = useToast();
  
  // Fetch credits data
  const { data: credits, isLoading: isLoadingCredits, error: creditsError } = useQuery<CreditData>({
    queryKey: ['/api/ai-credits'],
    refetchOnWindowFocus: false,
  });

  // Fetch usage history
  const { data: usageHistory, isLoading: isLoadingHistory } = useQuery<UsageHistoryResponse>({
    queryKey: ['/api/ai-credits/history', { period, feature: feature !== 'all' ? feature : undefined }],
    refetchOnWindowFocus: false,
  });

  // Purchase credits mutation
  const purchaseForm = useForm<z.infer<typeof purchaseFormSchema>>({
    resolver: zodResolver(purchaseFormSchema),
    defaultValues: {
      amount: 500,
    },
  });

  const purchaseCreditsMutation = useMutation({
    mutationFn: async (values: z.infer<typeof purchaseFormSchema>) => {
      const response = await apiRequest('POST', '/api/ai-credits/purchase', values);
      return await response.json();
    },
    onSuccess: () => {
      toast({
        title: "Purchase Successful",
        description: "Your AI credits have been added to your account.",
        variant: "default",
      });
      queryClient.invalidateQueries({ queryKey: ['/api/ai-credits'] });
      setIsPurchaseDialogOpen(false);
      purchaseForm.reset();
    },
    onError: (error: any) => {
      toast({
        title: "Purchase Failed",
        description: error.message || "There was an error processing your purchase.",
        variant: "destructive",
      });
    },
  });

  // Upgrade plan mutation
  const upgradeForm = useForm<z.infer<typeof planUpgradeSchema>>({
    resolver: zodResolver(planUpgradeSchema),
    defaultValues: {
      plan: "starter",
    },
  });

  const upgradePlanMutation = useMutation({
    mutationFn: async (values: z.infer<typeof planUpgradeSchema>) => {
      const response = await apiRequest('POST', '/api/ai-credits/upgrade-plan', values);
      return await response.json();
    },
    onSuccess: () => {
      toast({
        title: "Plan Upgraded",
        description: "Your subscription plan has been upgraded successfully.",
        variant: "default",
      });
      queryClient.invalidateQueries({ queryKey: ['/api/ai-credits'] });
      setIsUpgradeDialogOpen(false);
    },
    onError: (error: any) => {
      toast({
        title: "Upgrade Failed",
        description: error.message || "There was an error upgrading your plan.",
        variant: "destructive",
      });
    },
  });

  // Handle purchase form submission
  const onPurchaseSubmit = (values: z.infer<typeof purchaseFormSchema>) => {
    purchaseCreditsMutation.mutate(values);
  };

  // Handle upgrade form submission
  const onUpgradeSubmit = (values: z.infer<typeof planUpgradeSchema>) => {
    upgradePlanMutation.mutate(values);
  };

  // Prepare chart data
  const prepareUsageByFeatureData = () => {
    if (!usageHistory?.data) return [];
    
    const featureCounts: Record<string, number> = {
      text: 0,
      speech: 0,
      image: 0,
      video: 0,
    };
    
    usageHistory.data.forEach(item => {
      featureCounts[item.feature] = (featureCounts[item.feature] || 0) + item.credits;
    });
    
    return Object.entries(featureCounts).map(([name, value]) => ({
      name,
      value: value || 0,
    }));
  };

  const prepareUsageByDateData = () => {
    if (!usageHistory?.data) return [];
    
    const dateMap: Record<string, number> = {};
    
    usageHistory.data.forEach(item => {
      const date = format(new Date(item.createdAt), 'MMM d');
      dateMap[date] = (dateMap[date] || 0) + item.credits;
    });
    
    return Object.entries(dateMap).map(([date, credits]) => ({
      date,
      credits,
    }));
  };

  const usageByFeatureData = prepareUsageByFeatureData();
  const usageByDateData = prepareUsageByDateData();

  if (isLoadingCredits) {
    return (
      <div className="flex items-center justify-center h-full">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (creditsError) {
    return (
      <div className="flex flex-col items-center justify-center h-full p-4">
        <h2 className="text-2xl font-bold text-red-500 mb-2">Error Loading Credits</h2>
        <p className="text-gray-600">There was an error loading your AI credits information.</p>
        <Button className="mt-4" onClick={() => queryClient.invalidateQueries({ queryKey: ['/api/ai-credits'] })}>
          Try Again
        </Button>
      </div>
    );
  }

  const totalCredits = credits?.totalCredits ?? 0;
  const usedCredits = credits?.usedCredits ?? 0;
  const remainingCredits = totalCredits - usedCredits;
  const usagePercentage = totalCredits > 0 ? Math.min(100, Math.round((usedCredits / totalCredits) * 100)) : 0;

  return (
    <div className="container mx-auto py-6 max-w-7xl">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">AI Credits</h1>
        <div className="space-x-2">
          <Button 
            variant="outline" 
            onClick={() => setIsUpgradeDialogOpen(true)}
            className="bg-gradient-to-r from-purple-500 to-pink-500 text-white border-0 hover:from-purple-600 hover:to-pink-600"
          >
            <ArrowUpCircle className="mr-2 h-4 w-4" />
            Upgrade Plan
          </Button>
          <Button 
            onClick={() => setIsPurchaseDialogOpen(true)}
            className="bg-primary text-white"
          >
            <CreditCard className="mr-2 h-4 w-4" />
            Purchase Credits
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        {/* Summary Card */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle>Available Credits</CardTitle>
            <CardDescription>Your current AI credits balance</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex justify-between items-center">
              <div className="text-4xl font-bold">{remainingCredits}</div>
              <Badge className={`${getPlanColor(credits?.plan || 'free')}`}>
                {getPlanName(credits?.plan || 'free')}
              </Badge>
            </div>
            <div className="mt-4">
              <Progress value={usagePercentage} className="h-2" />
              <div className="flex justify-between text-sm text-muted-foreground mt-1">
                <span>Used: {credits?.usedCredits || 0}</span>
                <span>Total: {credits?.totalCredits || 0}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Next Refill Card */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle>Next Refill</CardTitle>
            <CardDescription>When your credits will be replenished</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-semibold mb-2">
              {credits?.nextRefill ? formatDate(credits.nextRefill) : 'Not scheduled'}
            </div>
            <p className="text-muted-foreground">
              {credits?.plan === 'free' 
                ? 'Free plan has limited refills.' 
                : 'Your credits will be automatically refilled on this date.'}
            </p>
          </CardContent>
        </Card>

        {/* Credit Usage Card */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle>Credit Usage</CardTitle>
            <CardDescription>AI features by credit consumption</CardDescription>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="space-y-1">
              <div className="flex items-center">
                <span className="text-blue-500 mr-2">📝</span>
                <span className="font-medium">Text Generation</span>
                <span className="ml-auto">1 credit / request</span>
              </div>
              <div className="flex items-center">
                <span className="text-green-500 mr-2">🎤</span>
                <span className="font-medium">Speech Synthesis</span>
                <span className="ml-auto">1 credit / 100 words</span>
              </div>
              <div className="flex items-center">
                <span className="text-purple-500 mr-2">🖼️</span>
                <span className="font-medium">Image Generation</span>
                <span className="ml-auto">5 credits / image</span>
              </div>
              <div className="flex items-center">
                <span className="text-red-500 mr-2">🎬</span>
                <span className="font-medium">Video Creation</span>
                <span className="ml-auto">200 credits / video</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">
            <PieChartIcon className="h-4 w-4 mr-2" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="history">
            <History className="h-4 w-4 mr-2" />
            Usage History
          </TabsTrigger>
          <TabsTrigger value="analytics">
            <BarChartIcon className="h-4 w-4 mr-2" />
            Analytics
          </TabsTrigger>
        </TabsList>
        
        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Usage by Feature</CardTitle>
                <CardDescription>
                  Distribution of AI credits across different features
                </CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                {isLoadingHistory ? (
                  <div className="flex items-center justify-center h-full">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                  </div>
                ) : !usageHistory?.data?.length ? (
                  <div className="flex flex-col items-center justify-center h-full">
                    <PieChartIcon className="h-16 w-16 text-muted-foreground/30 mb-4" />
                    <p className="text-muted-foreground">No usage data available yet</p>
                    <p className="text-muted-foreground text-sm mt-2">Start using AI features to see data</p>
                  </div>
                ) : usageByFeatureData.length > 0 ? (
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={usageByFeatureData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                        label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      >
                        {usageByFeatureData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip />
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                ) : (
                  <div className="flex items-center justify-center h-full text-muted-foreground">
                    No usage data available
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Recent Usage</CardTitle>
                <CardDescription>
                  AI credits used over time
                </CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                {isLoadingHistory ? (
                  <div className="flex items-center justify-center h-full">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                  </div>
                ) : !usageHistory?.data?.length ? (
                  <div className="flex flex-col items-center justify-center h-full">
                    <BarChartIcon className="h-16 w-16 text-muted-foreground/30 mb-4" />
                    <p className="text-muted-foreground">No usage data available</p>
                    <p className="text-muted-foreground text-sm mt-2">Use AI features to see your usage trends</p>
                  </div>
                ) : usageByDateData.length > 0 ? (
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={usageByDateData}
                      margin={{
                        top: 20,
                        right: 30,
                        left: 20,
                        bottom: 5,
                      }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" />
                      <YAxis />
                      <Tooltip />
                      <Bar dataKey="credits" fill="#8884d8" name="Credits Used" />
                    </BarChart>
                  </ResponsiveContainer>
                ) : (
                  <div className="flex items-center justify-center h-full text-muted-foreground">
                    No usage data available
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        {/* History Tab */}
        <TabsContent value="history">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>Usage History</CardTitle>
                <div className="flex gap-2">
                  <Select value={period} onValueChange={setPeriod}>
                    <SelectTrigger className="w-[140px]">
                      <SelectValue placeholder="Select period" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectGroup>
                        <SelectLabel>Time Period</SelectLabel>
                        <SelectItem value="day">Today</SelectItem>
                        <SelectItem value="week">This Week</SelectItem>
                        <SelectItem value="month">This Month</SelectItem>
                        <SelectItem value="year">This Year</SelectItem>
                        <SelectItem value="all">All Time</SelectItem>
                      </SelectGroup>
                    </SelectContent>
                  </Select>
                  
                  <Select value={feature} onValueChange={setFeature}>
                    <SelectTrigger className="w-[140px]">
                      <SelectValue placeholder="Select feature" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectGroup>
                        <SelectLabel>Feature</SelectLabel>
                        <SelectItem value="all">All Features</SelectItem>
                        <SelectItem value="text">Text Generation</SelectItem>
                        <SelectItem value="speech">Speech Synthesis</SelectItem>
                        <SelectItem value="image">Image Generation</SelectItem>
                        <SelectItem value="video">Video Creation</SelectItem>
                      </SelectGroup>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <CardDescription>
                Detailed history of your AI credit usage
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoadingHistory ? (
                <div className="flex items-center justify-center h-32">
                  <Loader2 className="h-8 w-8 animate-spin text-primary" />
                </div>
              ) : !usageHistory?.data?.length ? (
                <div className="flex flex-col items-center justify-center h-48 mt-6">
                  <History className="h-16 w-16 text-muted-foreground/30 mb-4" />
                  <p className="text-muted-foreground">No usage history found</p>
                  <Button variant="outline" size="sm" className="mt-4" onClick={() => setIsPurchaseDialogOpen(true)}>
                    <CreditCard className="mr-2 h-4 w-4" />
                    Purchase Credits
                  </Button>
                </div>
              ) : (
                <Table>
                  <TableCaption>
                    {usageHistory?.data?.length 
                      ? `Showing ${usageHistory.data.length} of ${usageHistory.count} entries` 
                      : 'No usage history found for the selected filters'}
                  </TableCaption>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Date</TableHead>
                      <TableHead>Feature</TableHead>
                      <TableHead>Model</TableHead>
                      <TableHead>Course</TableHead>
                      <TableHead>Details</TableHead>
                      <TableHead className="text-right">Credits</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {usageHistory?.data?.length ? (
                      usageHistory.data.map((item, index) => (
                        <TableRow key={item.id || index}>
                          <TableCell className="font-medium">
                            {formatDate(item.createdAt)}
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              {getFeatureIcon(item.feature)}
                              <span className="ml-2 capitalize">{item.feature}</span>
                            </div>
                          </TableCell>
                          <TableCell>{item.model}</TableCell>
                          <TableCell>{item.courseName || '-'}</TableCell>
                          <TableCell>
                            {item.feature === 'text' && item.tokenCount && (
                              <span>{item.tokenCount} tokens</span>
                            )}
                            {item.feature === 'speech' && item.characterCount && (
                              <span>{item.characterCount} characters</span>
                            )}
                            {item.feature === 'image' && item.imageCount && (
                              <span>{item.imageCount} images</span>
                            )}
                            {item.feature === 'video' && (
                              <span>1 video</span>
                            )}
                          </TableCell>
                          <TableCell className="text-right font-semibold">
                            {item.credits}
                          </TableCell>
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell colSpan={6} className="text-center h-24 text-muted-foreground">
                          No usage history found
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Analytics Tab */}
        <TabsContent value="analytics">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Feature Efficiency</CardTitle>
                <CardDescription>
                  Credits spent on each AI feature
                </CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                {isLoadingHistory ? (
                  <div className="flex items-center justify-center h-full">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                  </div>
                ) : !usageHistory?.data?.length ? (
                  <div className="flex flex-col items-center justify-center h-full">
                    <PieChartIcon className="h-16 w-16 text-muted-foreground/30 mb-4" />
                    <p className="text-muted-foreground">No usage data available yet</p>
                    <Button variant="outline" size="sm" className="mt-4" onClick={() => setIsPurchaseDialogOpen(true)}>
                      <CreditCard className="mr-2 h-4 w-4" />
                      Get Credits
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-8 pt-4">
                    {['text', 'speech', 'image', 'video'].map(feat => {
                      // Use our safe calculation function for consistent null handling
                      const totalCredits = calculateUsageTotal(
                        usageHistory?.data?.filter(item => item.feature === feat)
                      );
                      
                      // Safely calculate percentage using our utility function
                      const totalUsage = calculateUsageTotal(usageHistory?.data);
                      const percent = totalUsage > 0 ? (totalCredits / totalUsage) * 100 : 0;
                      
                      return (
                        <div key={feat} className="space-y-2">
                          <div className="flex items-center">
                            <span className="capitalize flex items-center">
                              {getFeatureIcon(feat)}
                              <span className="ml-2">{feat}</span>
                            </span>
                            <span className="ml-auto">{totalCredits} credits</span>
                          </div>
                          <Progress value={percent} className="h-2" />
                          <div className="text-xs text-right text-muted-foreground">
                            {percent.toFixed(1)}% of total
                          </div>
                        </div>
                      );
                    })}
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Usage Predictions</CardTitle>
                <CardDescription>
                  Estimated usage based on current patterns
                </CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                {isLoadingHistory || !credits ? (
                  <div className="flex items-center justify-center h-full">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                  </div>
                ) : !usageHistory?.data?.length ? (
                  <div className="flex flex-col items-center justify-center h-full">
                    <BarChartIcon className="h-16 w-16 text-muted-foreground/30 mb-4" />
                    <p className="text-muted-foreground">No usage data to make predictions</p>
                    <p className="text-muted-foreground text-sm mt-2">Start using AI features to see usage patterns</p>
                  </div>
                ) : (
                  <div className="space-y-6 pt-4">
                    <div>
                      <h3 className="text-lg font-medium mb-2">Current Usage</h3>
                      <div className="grid grid-cols-2 gap-4">
                        <div className="bg-muted rounded-lg p-4">
                          <div className="text-sm text-muted-foreground">Used</div>
                          <div className="text-2xl font-bold">{credits.usedCredits}</div>
                        </div>
                        <div className="bg-muted rounded-lg p-4">
                          <div className="text-sm text-muted-foreground">Remaining</div>
                          <div className="text-2xl font-bold">{remainingCredits}</div>
                        </div>
                      </div>
                    </div>
                    
                    <div>
                      <h3 className="text-lg font-medium mb-2">Usage Projections</h3>
                      <div className="space-y-3">
                        <div className="flex justify-between items-center">
                          <span>Daily average:</span>
                          <span className="font-semibold">
                            {Math.round(calculateUsageTotal(usageHistory?.data) / 30)} credits
                          </span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span>Estimated monthly:</span>
                          <span className="font-semibold">
                            {Math.round((calculateUsageTotal(usageHistory?.data) / 30) * 30)} credits
                          </span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span>Credits until exhaustion:</span>
                          <span className="font-semibold">
                            {remainingCredits > 0 && usageHistory?.data?.length 
                              ? `~${Math.round(remainingCredits / ((calculateUsageTotal(usageHistory?.data) / 30) || 1))} days` 
                              : 'N/A'}
                          </span>
                        </div>
                      </div>
                    </div>
                    
                    <div>
                      <h3 className="text-lg font-medium mb-2">Recommended Actions</h3>
                      <div className="space-y-2 text-sm">
                        {remainingCredits < 100 && (
                          <div className="flex items-center text-amber-600">
                            <span>⚠️ Low credits - consider purchasing more soon</span>
                          </div>
                        )}
                        {usageHistory?.data?.find(item => item.feature === 'video') && (
                          <div className="flex items-center">
                            <span>Video generation uses the most credits (200/video)</span>
                          </div>
                        )}
                        {credits.plan === 'free' && (
                          <div className="flex items-center">
                            <span>Upgrade to a paid plan for more monthly credits</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* Purchase Credits Dialog */}
      <AlertDialog open={isPurchaseDialogOpen} onOpenChange={setIsPurchaseDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Purchase AI Credits</AlertDialogTitle>
            <AlertDialogDescription>
              Add more AI credits to your account. Credits can be used for text generation, speech synthesis, 
              image generation, and video creation.
            </AlertDialogDescription>
          </AlertDialogHeader>
          
          <Form {...purchaseForm}>
            <form onSubmit={purchaseForm.handleSubmit(onPurchaseSubmit)} className="space-y-6">
              <FormField
                control={purchaseForm.control}
                name="amount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Credit Amount</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="Enter amount"
                        {...field}
                        onChange={(e) => field.onChange(parseInt(e.target.value))}
                      />
                    </FormControl>
                    <FormDescription>
                      {field.value ? `Estimated cost: ${formatCreditPrice(field.value)}` : 'Enter an amount to see price'}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction asChild>
                  <Button 
                    type="submit" 
                    disabled={purchaseCreditsMutation.isPending}
                    className="bg-primary text-white"
                  >
                    {purchaseCreditsMutation.isPending && (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    )}
                    Purchase
                  </Button>
                </AlertDialogAction>
              </AlertDialogFooter>
            </form>
          </Form>
        </AlertDialogContent>
      </AlertDialog>

      {/* Upgrade Plan Dialog */}
      <AlertDialog open={isUpgradeDialogOpen} onOpenChange={setIsUpgradeDialogOpen}>
        <AlertDialogContent className="max-w-xl">
          <AlertDialogHeader>
            <AlertDialogTitle>Upgrade Your Plan</AlertDialogTitle>
            <AlertDialogDescription>
              Upgrade to a higher tier plan to get more AI credits and features.
            </AlertDialogDescription>
          </AlertDialogHeader>
          
          <div className="grid gap-4">
            {creditPlans.map((plan) => (
              <div 
                key={plan.value}
                className={`border rounded-lg p-4 cursor-pointer transition-all
                  ${upgradeForm.watch('plan') === plan.value ? 'border-primary bg-primary/10' : 'hover:border-muted-foreground'}
                `}
                onClick={() => upgradeForm.setValue('plan', plan.value as PlanType)}
              >
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="font-semibold text-lg">{plan.name}</h3>
                    <p className="text-muted-foreground text-sm">{plan.credits} credits/month</p>
                  </div>
                  <div>
                    <span className="text-xl font-bold">${plan.price}</span>
                    <span className="text-muted-foreground text-sm">/mo</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
          
          <AlertDialogFooter className="mt-4">
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction asChild>
              <Button 
                onClick={() => onUpgradeSubmit(upgradeForm.getValues())}
                disabled={upgradePlanMutation.isPending}
                className="bg-gradient-to-r from-purple-500 to-pink-500 text-white border-0 hover:from-purple-600 hover:to-pink-600"
              >
                {upgradePlanMutation.isPending && (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                )}
                Upgrade to {creditPlans.find(p => p.value === upgradeForm.watch('plan'))?.name}
              </Button>
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default AICreditsPage;