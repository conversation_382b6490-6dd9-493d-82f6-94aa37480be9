import React, { useState, useRef } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import {
  Upload,
  User,
  Image,
  Library,
  Search,
  Check,
  X,
  Loader2
} from "lucide-react";
import { useQuery } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { cn } from "@/lib/utils";

export interface AvatarSelectorProps {
  onSelect: (data: { id: string; type: 'stock' | 'upload' | 'library'; url: string }) => void;
  defaultAvatarUrl?: string;
  defaultAvatarType?: 'stock' | 'upload' | 'library';
}

export function AvatarSelector({
  onSelect,
  defaultAvatarUrl = "",
  defaultAvatarType = "stock"
}: AvatarSelectorProps) {
  const [selectedTab, setSelectedTab] = useState<string>(defaultAvatarType || "stock");
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedAvatar, setSelectedAvatar] = useState<{
    id: string;
    type: 'stock' | 'upload' | 'library';
    url: string;
  } | null>(defaultAvatarUrl ? {
    id: "default",
    type: defaultAvatarType,
    url: defaultAvatarUrl
  } : null);
  
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  // Fetch stock avatars
  const { data: stockAvatars, isLoading: isLoadingStock } = useQuery({
    queryKey: ['/api/media', { type: 'avatars', source: 'stock', q: searchQuery }],
    queryFn: async () => {
      const res = await apiRequest('GET', `/api/media?type=avatars&source=stock&q=${encodeURIComponent(searchQuery)}`);
      if (!res.ok) throw new Error('Failed to fetch stock avatars');
      return res.json();
    }
  });

  // Fetch library (user's uploaded) avatars
  const { data: libraryAvatars, isLoading: isLoadingLibrary } = useQuery({
    queryKey: ['/api/media', { type: 'avatars', source: 'library', q: searchQuery }],
    queryFn: async () => {
      const res = await apiRequest('GET', `/api/media?type=avatars&source=library&q=${encodeURIComponent(searchQuery)}`);
      if (!res.ok) throw new Error('Failed to fetch library avatars');
      return res.json();
    }
  });

  const handleUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file
    if (!file.type.startsWith('image/')) {
      toast({
        title: "Invalid file type",
        description: "Please upload an image file (JPEG, PNG, etc.)",
        variant: "destructive"
      });
      return;
    }

    // Max file size: 5MB
    if (file.size > 5 * 1024 * 1024) {
      toast({
        title: "File too large",
        description: "Please upload an image smaller than 5MB",
        variant: "destructive"
      });
      return;
    }

    try {
      setIsUploading(true);
      
      const formData = new FormData();
      formData.append('file', file);
      formData.append('type', 'avatar');
      
      // Using FormData requires us to let the browser set the content type with boundary
      const res = await fetch('/api/media/upload', {
        method: 'POST',
        body: formData,
        credentials: 'include'
      });
      
      if (!res.ok) {
        throw new Error('Failed to upload avatar');
      }
      
      const data = await res.json();
      
      toast({
        title: "Avatar uploaded",
        description: "Your avatar has been uploaded successfully",
      });
      
      setSelectedAvatar({
        id: data.id,
        type: 'upload',
        url: data.url
      });
      
    } catch (error: any) {
      toast({
        title: "Upload failed",
        description: error.message,
        variant: "destructive"
      });
    } finally {
      setIsUploading(false);
    }
  };

  const handleAvatarSelect = (avatar: any, type: 'stock' | 'upload' | 'library') => {
    setSelectedAvatar({
      id: avatar.id,
      type,
      url: avatar.url
    });
  };

  const handleContinue = () => {
    if (selectedAvatar) {
      onSelect(selectedAvatar);
    } else {
      toast({
        title: "No avatar selected",
        description: "Please select an avatar to continue",
        variant: "destructive"
      });
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold mb-2">Select an Avatar</h2>
        <p className="text-muted-foreground mb-6">
          Choose an avatar that will be the face of your course. You can select a stock avatar,
          upload your own, or choose from your library.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-[2fr,1fr] gap-6">
        <div>
          <Tabs value={selectedTab} onValueChange={setSelectedTab} className="w-full">
            <TabsList className="grid grid-cols-3 mb-6">
              <TabsTrigger value="stock" className="flex items-center gap-2">
                <User className="h-4 w-4" />
                <span>Stock Avatars</span>
              </TabsTrigger>
              <TabsTrigger value="upload" className="flex items-center gap-2">
                <Upload className="h-4 w-4" />
                <span>Upload</span>
              </TabsTrigger>
              <TabsTrigger value="library" className="flex items-center gap-2">
                <Library className="h-4 w-4" />
                <span>My Library</span>
              </TabsTrigger>
            </TabsList>
            
            {/* Search Bar */}
            {(selectedTab === "stock" || selectedTab === "library") && (
              <div className="relative mb-4">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  className="pl-9"
                  placeholder={`Search ${selectedTab === "stock" ? "stock avatars" : "your avatar library"}...`}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            )}

            <TabsContent value="stock" className="space-y-4">
              {isLoadingStock ? (
                <div className="flex items-center justify-center h-[300px]">
                  <Loader2 className="h-8 w-8 animate-spin text-primary" />
                </div>
              ) : stockAvatars?.length ? (
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {stockAvatars.map((avatar: any) => (
                    <AvatarCard
                      key={avatar.id}
                      avatar={avatar}
                      isSelected={selectedAvatar?.id === avatar.id && selectedAvatar?.type === 'stock'}
                      onClick={() => handleAvatarSelect(avatar, 'stock')}
                    />
                  ))}
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center h-[300px] text-center">
                  <Image className="h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium">No stock avatars found</h3>
                  <p className="text-muted-foreground mt-1">
                    {searchQuery
                      ? `No results for "${searchQuery}"`
                      : "There are no stock avatars available at the moment"}
                  </p>
                </div>
              )}
            </TabsContent>

            <TabsContent value="upload" className="space-y-6">
              <div 
                className="border-2 border-dashed rounded-md p-8 text-center cursor-pointer hover:border-primary/50 transition-colors"
                onClick={() => fileInputRef.current?.click()}
              >
                <input
                  type="file"
                  ref={fileInputRef}
                  className="hidden"
                  accept="image/*"
                  onChange={handleUpload}
                  disabled={isUploading}
                />
                <Upload className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-1">Upload an avatar image</h3>
                <p className="text-muted-foreground mb-4 max-w-md mx-auto">
                  Drag and drop your image here, or click to browse. We recommend using a close-up image of a face with a clear background.
                </p>
                <div className="text-xs text-muted-foreground">
                  Supported formats: JPEG, PNG, WEBP • Max size: 5MB
                </div>
                
                {isUploading && (
                  <div className="mt-4 flex items-center justify-center">
                    <Loader2 className="h-5 w-5 animate-spin text-primary mr-2" />
                    <span>Uploading...</span>
                  </div>
                )}
              </div>
              
              {selectedAvatar?.type === 'upload' && (
                <div className="mt-4">
                  <Label>Selected Avatar</Label>
                  <div className="mt-2">
                    <AvatarCard
                      avatar={{ id: selectedAvatar.id, url: selectedAvatar.url }}
                      isSelected={true}
                      onClick={() => {}}
                    />
                  </div>
                </div>
              )}
            </TabsContent>

            <TabsContent value="library" className="space-y-4">
              {isLoadingLibrary ? (
                <div className="flex items-center justify-center h-[300px]">
                  <Loader2 className="h-8 w-8 animate-spin text-primary" />
                </div>
              ) : libraryAvatars?.length ? (
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {libraryAvatars.map((avatar: any) => (
                    <AvatarCard
                      key={avatar.id}
                      avatar={avatar}
                      isSelected={selectedAvatar?.id === avatar.id && selectedAvatar?.type === 'library'}
                      onClick={() => handleAvatarSelect(avatar, 'library')}
                    />
                  ))}
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center h-[300px] text-center">
                  <Library className="h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium">No avatars in your library</h3>
                  <p className="text-muted-foreground mt-1 max-w-md">
                    {searchQuery
                      ? `No results for "${searchQuery}"`
                      : "You haven't uploaded any avatars yet. Upload an avatar or use a stock avatar."}
                  </p>
                </div>
              )}
            </TabsContent>
          </Tabs>
        </div>

        <div>
          <Card>
            <CardContent className="p-6 space-y-6">
              <div>
                <h3 className="text-lg font-medium mb-1">Preview</h3>
                <p className="text-sm text-muted-foreground">
                  This is how your avatar will appear in your course
                </p>
              </div>

              <Separator />

              <div className="flex flex-col items-center justify-center">
                {selectedAvatar ? (
                  <div className="text-center">
                    <div className="w-40 h-40 mx-auto mb-3 relative overflow-hidden rounded-full border-4 border-primary/10">
                      <img
                        src={selectedAvatar.url}
                        alt="Selected Avatar"
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <p className="text-sm font-medium mb-1">
                      {selectedAvatar.type === 'stock' 
                        ? 'Stock Avatar' 
                        : selectedAvatar.type === 'upload' 
                          ? 'Uploaded Avatar'
                          : 'Library Avatar'
                      }
                    </p>
                    <Button
                      variant="outline"
                      size="sm"
                      className="mt-2"
                      onClick={() => setSelectedAvatar(null)}
                    >
                      <X className="h-4 w-4 mr-1" /> Remove
                    </Button>
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <User className="h-16 w-16 mx-auto text-muted-foreground mb-3" />
                    <p className="text-muted-foreground">
                      No avatar selected
                    </p>
                    <p className="text-xs text-muted-foreground mt-1">
                      Select an avatar to see a preview
                    </p>
                  </div>
                )}
              </div>

              <Button 
                className="w-full"
                disabled={!selectedAvatar}
                onClick={handleContinue}
              >
                <Check className="h-4 w-4 mr-2" /> Continue
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}

interface AvatarCardProps {
  avatar: {
    id: string;
    url: string;
    name?: string;
  };
  isSelected: boolean;
  onClick: () => void;
}

function AvatarCard({ avatar, isSelected, onClick }: AvatarCardProps) {
  return (
    <div
      className={cn(
        "relative rounded-md overflow-hidden cursor-pointer transition-all",
        "border-2 hover:shadow-md",
        isSelected 
          ? "border-primary ring-2 ring-primary/20" 
          : "border-border hover:border-primary/50"
      )}
      onClick={onClick}
    >
      <div className="aspect-square relative">
        <img
          src={avatar.url}
          alt={avatar.name || "Avatar"}
          className="w-full h-full object-cover"
        />
        {isSelected && (
          <div className="absolute top-2 right-2 bg-primary text-primary-foreground rounded-full p-1 shadow-md">
            <Check className="h-3 w-3" />
          </div>
        )}
      </div>
      {avatar.name && (
        <div className="p-2 text-xs font-medium truncate">{avatar.name}</div>
      )}
    </div>
  );
}