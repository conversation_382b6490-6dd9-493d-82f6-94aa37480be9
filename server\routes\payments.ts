import express, { Request, Response } from 'express';
import { storage } from '../storage';
import { stripeService } from '../services/stripe';

const router = express.Router();

// Create payment intent for embedded checkout
router.post('/create-payment-intent', async (req: Request, res: Response) => {
  try {
    if (!req.session?.userId) {
      return res.status(401).json({ message: "Not authenticated" });
    }
    
    const { planId, billingInterval } = req.body;
    
    if (!planId || !billingInterval) {
      return res.status(400).json({ message: "Plan ID and billing interval are required" });
    }
    
    // Get user data
    const user = await storage.getUser(req.session.userId);
    
    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }
    
    // Create payment intent for embedded checkout
    const paymentIntent = await stripeService.createPaymentIntent({
      planId,
      billingInterval,
      user
    });
    
    return res.status(200).json(paymentIntent);
  } catch (error: any) {
    console.error("Create payment intent error:", error);
    return res.status(500).json({ message: error.message || "Server error" });
  }
});

// Update user subscription after successful payment
router.post('/update-subscription', async (req: Request, res: Response) => {
  try {
    if (!req.session?.userId) {
      return res.status(401).json({ message: "Not authenticated" });
    }
    
    const { paymentIntentId, planId } = req.body;
    
    if (!paymentIntentId || !planId) {
      return res.status(400).json({ message: "Payment intent ID and plan ID are required" });
    }
    
    // Get user data
    const user = await storage.getUser(req.session.userId);
    
    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }
    
    // Update user subscription
    await storage.updateUser(user.id, {
      plan: planId
    });
    
    // Record analytics event with the format expected by the storage system
    const analyticsEvent: any = {
      eventType: 'subscription_updated',
      userId: user.id,
      eventData: null // Set to null to avoid type issues
    };
    
    await storage.recordAnalyticsEvent(analyticsEvent);
    
    return res.status(200).json({ success: true });
  } catch (error: any) {
    console.error("Update subscription error:", error);
    return res.status(500).json({ message: error.message || "Server error" });
  }
});

// Get session status
router.get('/session-status', async (req: Request, res: Response) => {
  try {
    const { session_id } = req.query;
    
    if (!session_id) {
      return res.status(400).json({ message: "Session ID is required" });
    }
    
    const session = await stripeService.getSessionInfo(session_id as string);
    
    return res.status(200).json(session);
  } catch (error: any) {
    console.error("Get session status error:", error);
    return res.status(500).json({ message: error.message || "Server error" });
  }
});

export default router;