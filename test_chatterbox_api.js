/**
 * Test Chatterbox TTS API endpoints
 */

import axios from 'axios';

const BASE_URL = 'http://localhost:5000/api/chatterbox-tts';

async function testChatterboxAPI() {
  console.log('Testing Chatterbox TTS API Integration...');
  console.log('='.repeat(50));

  try {
    // Test 1: Health check
    console.log('1. Testing health check...');
    try {
      const healthResponse = await axios.get(`${BASE_URL}/health`);
      console.log(`   Status: ${healthResponse.data.status}`);
      console.log(`   Available voices: ${healthResponse.data.availableVoices}`);
    } catch (error) {
      console.log(`   Health check failed: ${error.response?.data?.message || error.message}`);
    }

    // Test 2: Get available voices
    console.log('\n2. Testing voice list...');
    try {
      const voicesResponse = await axios.get(`${BASE_URL}/voices`);
      const voices = voicesResponse.data.voices;
      console.log(`   Found ${voices.length} voices:`);
      voices.slice(0, 3).forEach(voice => {
        console.log(`   - ${voice.name} (${voice.gender})`);
      });
    } catch (error) {
      console.log(`   Voice list failed: ${error.response?.data?.message || error.message}`);
    }

    // Test 3: Generate simple speech
    console.log('\n3. Testing speech generation...');
    try {
      const speechData = {
        text: "Welcome to the Course AI Platform with Chatterbox TTS powered by Modal A100 GPU.",
        voice: "v2/en_speaker_6",
        temperature: 0.7
      };

      const speechResponse = await axios.post(`${BASE_URL}/generate`, speechData, {
        responseType: 'arraybuffer',
        timeout: 60000
      });

      console.log(`   Generated ${speechResponse.data.byteLength} bytes of audio`);
      console.log(`   Content type: ${speechResponse.headers['content-type']}`);
    } catch (error) {
      console.log(`   Speech generation failed: ${error.response?.data?.message || error.message}`);
    }

    // Test 4: Batch narration (smaller test)
    console.log('\n4. Testing batch narration...');
    try {
      const batchData = {
        lessons: [
          {
            title: "Introduction to AI",
            text: "Artificial Intelligence is revolutionizing education.",
            moduleId: "module-1",
            lessonId: "lesson-1"
          },
          {
            title: "Getting Started",
            text: "Let's begin our journey into AI-powered learning.",
            moduleId: "module-1", 
            lessonId: "lesson-2"
          }
        ],
        voicePreset: "v2/en_speaker_6"
      };

      const batchResponse = await axios.post(`${BASE_URL}/batch-narration`, batchData, {
        timeout: 120000
      });

      console.log(`   Generated ${batchResponse.data.totalLessons} narrations`);
      console.log(`   Total duration: ${batchResponse.data.totalDuration.toFixed(1)}s`);
    } catch (error) {
      console.log(`   Batch narration failed: ${error.response?.data?.message || error.message}`);
    }

    console.log('\nChatterbox TTS API test completed!');

  } catch (error) {
    console.error('Test suite failed:', error.message);
  }
}

// Run the test
testChatterboxAPI().catch(console.error);