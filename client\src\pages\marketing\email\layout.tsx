import React from 'react';
import { Link, useLocation } from 'wouter';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';

interface EmailLayoutProps {
  children: React.ReactNode;
}

const EmailLayout: React.FC<EmailLayoutProps> = ({ children }) => {
  const [location, navigate] = useLocation();
  
  // Determine active tab based on URL
  const getActiveTab = () => {
    if (location === '/marketing/email') return 'subscribers';
    if (location.includes('/marketing/email/templates')) return 'templates';
    if (location.includes('/marketing/email/campaigns')) return 'campaigns';
    if (location.includes('/marketing/email/reports')) return 'reports';
    if (location.includes('/marketing/email/settings')) return 'settings';
    return 'subscribers';
  };

  const handleTabChange = (value: string) => {
    switch (value) {
      case 'subscribers':
        navigate('/marketing/email');
        break;
      case 'templates':
        navigate('/marketing/email/templates');
        break;
      case 'campaigns':
        navigate('/marketing/email/campaigns');
        break;
      case 'reports':
        navigate('/marketing/email/reports');
        break;
      case 'settings':
        navigate('/marketing/email/settings');
        break;
      default:
        navigate('/marketing/email');
    }
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex flex-col space-y-2">
        <h1 className="text-3xl font-bold">Email Marketing</h1>
        <div className="text-muted-foreground">
          Manage your email subscribers, templates, campaigns, and more
        </div>
      </div>
      
      <Tabs 
        value={getActiveTab()}
        onValueChange={handleTabChange}
        className="w-full"
      >
        <TabsList className="grid grid-cols-5 w-full">
          <TabsTrigger value="subscribers">Subscribers</TabsTrigger>
          <TabsTrigger value="templates">Templates</TabsTrigger>
          <TabsTrigger value="campaigns">Campaigns</TabsTrigger>
          <TabsTrigger value="reports">Reports</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>
      </Tabs>
      
      <div>
        {children}
      </div>
    </div>
  );
};

export default EmailLayout;