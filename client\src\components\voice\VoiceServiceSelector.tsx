import React, { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Play, Pause, Volume2, Zap, Crown, Cpu, Upload, Mic, <PERSON>, Settings, AlertCircle } from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';

interface VoiceService {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  tier: 'standard' | 'premium' | 'enterprise';
  features: string[];
}

interface Voice {
  id: string;
  name: string;
  gender?: string;
  language?: string;
  accent?: string;
  description?: string;
}

interface VoiceSettings {
  service: string;
  voiceId: string;
  speed: number;
  pitch: number;
  temperature?: number;
  stability?: number;
  isClonedVoice?: boolean;
  clonedVoiceName?: string;
}

interface VoiceServiceSelectorProps {
  onVoiceSelect: (settings: VoiceSettings) => void;
  sampleText?: string;
  defaultService?: string;
  defaultVoice?: string;
  showPreview?: boolean;
}

const voiceServices: VoiceService[] = [
  {
    id: 'chatterbox',
    name: 'Enterprise Voice Synthesis',
    description: 'GPU-powered high-quality voice synthesis (Coming Soon)',
    icon: <Cpu className="h-5 w-5" />,
    tier: 'enterprise',
    features: ['GPU Acceleration', 'Batch Processing', 'Voice Cloning', '10 Premium Voices']
  },
  {
    id: 'openai',
    name: 'Professional Voice Synthesis',
    description: 'Professional-grade text-to-speech with natural intonation',
    icon: <Zap className="h-5 w-5" />,
    tier: 'premium',
    features: ['Natural Speech', 'Multiple Languages', 'Fast Generation', 'High Quality']
  },
  {
    id: 'elevenlabs',
    name: 'Premium Voice Synthesis',
    description: 'Ultra-realistic voice synthesis (Requires API Key)',
    icon: <Crown className="h-5 w-5" />,
    tier: 'premium',
    features: ['Emotional Range', 'Voice Cloning', 'Multilingual', 'Ultra Realistic']
  }
];

export function VoiceServiceSelector({ 
  onVoiceSelect, 
  sampleText = "Welcome to this course. This is a preview of how your voice will sound.",
  defaultService = 'openai',
  defaultVoice = '',
  showPreview = true
}: VoiceServiceSelectorProps) {
  const { toast } = useToast();
  const [selectedService, setSelectedService] = useState(defaultService);
  const [selectedVoice, setSelectedVoice] = useState(defaultVoice);
  const [speed, setSpeed] = useState(1.0);
  const [pitch, setPitch] = useState(1.0);
  const [temperature, setTemperature] = useState(0.7);
  const [stability, setStability] = useState(0.5);
  const [isPlaying, setIsPlaying] = useState(false);
  const [playingVoice, setPlayingVoice] = useState<string | null>(null);
  
  // Voice cloning state
  const [showCloneDialog, setShowCloneDialog] = useState(false);
  const [cloneName, setCloneName] = useState('');
  const [cloneDescription, setCloneDescription] = useState('');
  const [cloneFile, setCloneFile] = useState<File | null>(null);
  const [isCloning, setIsCloning] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Fetch voices for selected service
  const { data: voices, isLoading: voicesLoading } = useQuery({
    queryKey: ['/api/voices', selectedService],
    queryFn: async () => {
      let endpoint = '';
      
      if (selectedService === 'chatterbox') {
        endpoint = '/api/chatterbox-tts/voices';
      } else if (selectedService === 'openai') {
        endpoint = '/api/ai/openai-voices';
      } else if (selectedService === 'elevenlabs') {
        endpoint = '/api/ai/elevenlabs-voices';
      }
      
      if (!endpoint) return [];
      
      const response = await fetch(endpoint);
      if (!response.ok) throw new Error('Failed to fetch voices');
      const data = await response.json();
      return data.voices || [];
    },
    enabled: !!selectedService
  });

  // Set default voice when voices load
  useEffect(() => {
    if (voices && voices.length > 0 && !selectedVoice) {
      setSelectedVoice(voices[0].id);
    }
  }, [voices, selectedVoice]);

  // Update parent when settings change
  useEffect(() => {
    if (selectedService && selectedVoice) {
      onVoiceSelect({
        service: selectedService,
        voiceId: selectedVoice,
        speed,
        pitch,
        temperature,
        stability
      });
    }
  }, [selectedService, selectedVoice, speed, pitch, temperature, stability, onVoiceSelect]);

  const handlePreview = async (voiceId?: string) => {
    const targetVoice = voiceId || selectedVoice;
    if (!targetVoice) return;
    
    setPlayingVoice(targetVoice);
    
    try {
      let endpoint = '';
      let requestBody = {};
      
      if (selectedService === 'chatterbox') {
        endpoint = '/api/chatterbox-tts/generate';
        requestBody = {
          text: sampleText,
          voice_preset: targetVoice,
          temperature,
          silence_duration: 0.25
        };
      } else if (selectedService === 'openai') {
        endpoint = '/api/ai/text-to-speech';
        requestBody = {
          text: sampleText,
          voice: targetVoice,
          model: 'tts-1',
          speed
        };
      } else if (selectedService === 'elevenlabs') {
        endpoint = '/api/ai/elevenlabs-tts';
        requestBody = {
          text: sampleText,
          voice_id: targetVoice,
          model_id: 'eleven_monolingual_v1',
          voice_settings: {
            stability,
            similarity_boost: 0.5,
            style: 0.0,
            use_speaker_boost: true
          }
        };
      }

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody)
      });
      
      if (!response.ok) {
        throw new Error('Failed to generate preview');
      }

      // Handle different response types
      if (selectedService === 'openai') {
        const data = await response.json();
        if (data.success && data.audioUrl) {
          const audio = new Audio(data.audioUrl);
          audio.play();
          audio.onended = () => setPlayingVoice(null);
        } else {
          throw new Error('Invalid response format');
        }
      } else {
        const blob = await response.blob();
        const url = URL.createObjectURL(blob);
        const audio = new Audio(url);
        audio.play();
        audio.onended = () => setPlayingVoice(null);
      }
      
    } catch (error) {
      console.error('Preview failed:', error);
      toast({
        title: "Preview Failed",
        description: "Unable to generate voice preview. Please try again.",
        variant: "destructive"
      });
      setPlayingVoice(null);
    }
  };

  const handleCloneVoice = async () => {
    if (!cloneFile || !cloneName) {
      toast({
        title: "Missing Information",
        description: "Please provide a voice sample file and name for cloning",
        variant: "destructive"
      });
      return;
    }

    setIsCloning(true);

    try {
      const formData = new FormData();
      formData.append('sampleAudio', cloneFile);
      formData.append('name', cloneName);
      formData.append('description', cloneDescription);
      formData.append('targetText', sampleText);

      const response = await fetch('/api/chatterbox-tts/clone-voice', {
        method: 'POST',
        body: formData
      });

      if (!response.ok) {
        throw new Error('Voice cloning failed');
      }

      const blob = await response.blob();
      const url = URL.createObjectURL(blob);
      const audio = new Audio(url);
      audio.play();

      toast({
        title: "Voice Cloned Successfully",
        description: `Created custom voice: ${cloneName}`,
      });

      // Reset form
      setCloneName('');
      setCloneDescription('');
      setCloneFile(null);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
      setShowCloneDialog(false);

    } catch (error) {
      console.error('Voice cloning failed:', error);
      toast({
        title: "Cloning Failed",
        description: "Unable to clone voice. Please try again with a different audio sample.",
        variant: "destructive"
      });
    } finally {
      setIsCloning(false);
    }
  };

  const getTierBadge = (tier: string) => {
    const colors = {
      standard: 'bg-gray-100 text-gray-800',
      premium: 'bg-blue-100 text-blue-800',
      enterprise: 'bg-purple-100 text-purple-800'
    };
    return <Badge className={colors[tier as keyof typeof colors]}>{tier}</Badge>;
  };

  const selectedServiceData = voiceServices.find(s => s.id === selectedService)!;

  return (
    <div className="space-y-6">
      {/* Service Selection */}
      <div>
        <h3 className="text-lg font-semibold">Choose Voice Service</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
          {voiceServices.map((service) => (
            <Card 
              key={service.id}
              className={`cursor-pointer transition-all hover:shadow-md ${
                selectedService === service.id 
                  ? 'ring-2 ring-blue-500 bg-blue-50' 
                  : ''
              }`}
              onClick={() => setSelectedService(service.id)}
            >
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    {service.icon}
                    <div>
                      <CardTitle className="text-base">{service.name}</CardTitle>
                    </div>
                  </div>
                  <div className="flex flex-col items-end gap-1">
                    {service.id === 'chatterbox' && (
                      <Badge variant="secondary" className="text-xs">
                        Coming Soon
                      </Badge>
                    )}
                    {getTierBadge(service.tier)}
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <CardDescription className="text-sm">
                  {service.description}
                </CardDescription>
                <div className="mt-3">
                  <div className="flex flex-wrap gap-1">
                    {service.features.slice(0, 2).map((feature) => (
                      <Badge key={feature} variant="outline" className="text-xs">
                        {feature}
                      </Badge>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Voice Selection */}
      {selectedService && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">{selectedServiceData.name} Settings</h3>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium">Voice Selection</label>
                <div className="flex items-center gap-2 mt-2">
                  <div className="flex-1">
                    {selectedService === 'chatterbox' && (
                      <Badge variant="secondary" className="mb-2">
                        GPU-Accelerated
                      </Badge>
                    )}
                    <Button
                      variant="outline"
                      onClick={() => setShowCloneDialog(true)}
                      className="mb-2"
                      disabled={selectedService !== 'chatterbox'}
                    >
                      <Mic className="h-4 w-4" />
                      Clone Voice
                    </Button>
                  </div>
                </div>
                
                {selectedService === 'chatterbox' && (
                  <Badge variant="outline" className="mb-2">
                    Enterprise Feature
                  </Badge>
                )}
              </div>

              {selectedService === 'chatterbox' && (
                <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
                  <div className="flex items-center gap-2 mb-2">
                    <AlertCircle className="h-4 w-4 text-yellow-600" />
                    <span className="text-sm font-medium text-yellow-800">
                      GPU Service Configuration Required
                    </span>
                  </div>
                  <p className="text-sm text-yellow-700">
                    This service requires Modal A100 GPU configuration. Please ensure your Modal credentials are properly configured to access enterprise voice synthesis features.
                  </p>
                </div>
              )}

              {/* Voice Cloning Dialog */}
              <Dialog open={showCloneDialog} onOpenChange={setShowCloneDialog}>
                <DialogTrigger asChild>
                  <Button variant="outline" disabled={selectedService !== 'chatterbox'}>
                    <Mic className="h-4 w-4" />
                    Clone Voice
                  </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-[425px]">
                  <DialogHeader>
                    <DialogTitle>Clone Your Voice</DialogTitle>
                    <DialogDescription>
                      Upload an audio sample to create a custom voice clone.
                    </DialogDescription>
                  </DialogHeader>
                  <div className="grid gap-4 py-4">
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="clone-name" className="text-right">
                        Voice Name
                      </Label>
                      <Input
                        id="clone-name"
                        value={cloneName}
                        onChange={(e) => setCloneName(e.target.value)}
                        className="col-span-3"
                      />
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="clone-description" className="text-right">
                        Description (optional)
                      </Label>
                      <Textarea
                        id="clone-description"
                        value={cloneDescription}
                        onChange={(e) => setCloneDescription(e.target.value)}
                        className="col-span-3"
                      />
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="clone-file" className="text-right">
                        Audio Sample
                      </Label>
                      <div className="col-span-3">
                        <Input
                          id="clone-file"
                          type="file"
                          ref={fileInputRef}
                          accept="audio/*"
                          onChange={(e) => setCloneFile(e.target.files?.[0] || null)}
                        />
                      </div>
                      <div className="col-span-4 text-center">
                        <p className="text-xs text-gray-500">
                          Upload a clear audio sample (10-30 seconds recommended)
                        </p>
                      </div>
                    </div>
                  </div>
                  <DialogFooter>
                    <Button variant="outline" onClick={() => setShowCloneDialog(false)}>
                      Cancel
                    </Button>
                    <Button onClick={handleCloneVoice} disabled={isCloning || !cloneFile || !cloneName}>
                      {isCloning ? (
                        <>
                          <Settings className="h-4 w-4 mr-2 animate-spin" />
                          Cloning...
                        </>
                      ) : (
                        <>
                          <Mic className="h-4 w-4 mr-2" />
                          Clone Voice
                        </>
                      )}
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>

              <div>
                {voicesLoading ? (
                  <div className="text-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                    <p className="text-sm text-gray-500 mt-2">Loading voices...</p>
                  </div>
                ) : (
                  <div className="grid gap-3">
                    {voices?.map((voice: Voice) => (
                      <Card 
                        key={voice.id} 
                        className={`cursor-pointer transition-all hover:shadow-md ${
                          selectedVoice === voice.id 
                            ? 'ring-2 ring-blue-500 bg-blue-50' 
                            : ''
                        }`}
                        onClick={() => setSelectedVoice(voice.id)}
                      >
                        <CardContent className="p-4">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <div className="flex items-center gap-2">
                                <Users className="h-4 w-4 text-gray-500" />
                                <div>
                                  <h4 className="font-medium">{voice.name}</h4>
                                  <div className="flex items-center gap-2 mt-1">
                                    {voice.gender && (
                                      <Badge variant="outline" className="text-xs">
                                        {voice.gender}
                                      </Badge>
                                    )}
                                    {voice.accent && (
                                      <Badge variant="secondary" className="text-xs">
                                        {voice.accent}
                                      </Badge>
                                    )}
                                  </div>
                                  {voice.description && (
                                    <p className="text-xs text-gray-500 mt-1">{voice.description}</p>
                                  )}
                                </div>
                              </div>
                            </div>
                            <Button
                              size="sm"
                              variant={playingVoice === voice.id ? "default" : "outline"}
                              onClick={(e) => {
                                e.stopPropagation();
                                void handlePreview(voice.id);
                              }}
                              disabled={
                                (playingVoice !== null && playingVoice !== voice.id) ||
                                selectedService === 'chatterbox' ||
                                selectedService === 'elevenlabs'
                              }
                              className="ml-4"
                            >
                              {playingVoice === voice.id ? (
                                <>
                                  <Pause className="h-4 w-4 mr-1" />
                                  Playing
                                </>
                              ) : (
                                <>
                                  <Play className="h-4 w-4 mr-1" />
                                  Preview
                                </>
                              )}
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">
                  Speed: {speed.toFixed(1)}x
                </label>
                <Slider
                  value={[speed]}
                  onValueChange={(value) => setSpeed(value[0])}
                  min={0.5}
                  max={2.0}
                  step={0.1}
                  className="w-full"
                />
              </div>

              {selectedService === 'chatterbox' && (
                <div>
                  <label className="block text-sm font-medium mb-2">
                    Temperature: {temperature.toFixed(1)}
                  </label>
                  <Slider
                    value={[temperature]}
                    onValueChange={(value) => setTemperature(value[0])}
                    min={0.1}
                    max={1.0}
                    step={0.1}
                    className="w-full"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Higher values create more expressive speech
                  </p>
                </div>
              )}

              {selectedService === 'elevenlabs' && (
                <>
                  <div>
                    <label className="block text-sm font-medium mb-2">
                      Stability: {stability.toFixed(1)}
                    </label>
                    <Slider
                      value={[stability]}
                      onValueChange={(value) => setStability(value[0])}
                      min={0.0}
                      max={1.0}
                      step={0.1}
                      className="w-full"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2">
                      Pitch: {pitch.toFixed(1)}
                    </label>
                    <Slider
                      value={[pitch]}
                      onValueChange={(value) => setPitch(value[0])}
                      min={0.5}
                      max={2.0}
                      step={0.1}
                      className="w-full"
                    />
                  </div>
                </>
              )}
            </div>

            {/* Enhanced Preview Section */}
            {showPreview && (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Global Voice Preview</label>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <p className="text-sm text-gray-600 mb-3">
                      "{sampleText}"
                    </p>
                    <Button 
                      onClick={() => void handlePreview()}
                      disabled={playingVoice !== null || !selectedVoice}
                      className="w-full"
                    >
                      {playingVoice === selectedVoice ? (
                        <>
                          <Pause className="h-4 w-4 mr-2" />
                          Playing Selected Voice...
                        </>
                      ) : (
                        <>
                          <Play className="h-4 w-4 mr-2" />
                          Preview Selected Voice
                        </>
                      )}
                    </Button>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <Volume2 className="h-4 w-4 text-blue-600" />
                      <span className="text-sm font-medium text-blue-800">
                        {selectedServiceData.name} Features
                      </span>
                    </div>
                    <ul className="text-sm text-blue-700 space-y-1">
                      {selectedServiceData.features.map((feature) => (
                        <li key={feature}>• {feature}</li>
                      ))}
                    </ul>
                  </div>

                  {selectedService === 'openai' && (
                    <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                      <div className="flex items-center gap-2 mb-2">
                        <Zap className="h-4 w-4 text-green-600" />
                        <span className="text-sm font-medium text-green-800">
                          Professional Voice Synthesis
                        </span>
                      </div>
                      <p className="text-sm text-green-700">
                        Professional-grade text-to-speech with natural intonation and fast processing for high-quality course creation.
                      </p>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}