import { useQuery, useMutation } from "@tanstack/react-query";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";

// Types for user credits
export interface UserCredits {
  balance: number;
  lifetimeUsage: number;
  lastPurchaseDate: string | null;
  totalPurchases: number;
}

// Use credits payload
export interface UseCreditsPayload {
  amount: number;
  description?: string;
  resourceId?: number;
  resourceType?: string;
}

// Purchase credits payload
export interface PurchaseCreditsPayload {
  amount: number;
  paymentMethodId: string;
}

export function useUserCredits() {
  const { toast } = useToast();

  // Fetch user credits
  const {
    data: credits,
    isLoading: isLoadingCredits,
    error: creditsError,
    refetch: refetchCredits,
  } = useQuery<UserCredits>({
    queryKey: ['/api/user/credits'],
    queryFn: async () => {
      const res = await apiRequest('GET', '/user/credits');
      if (!res.ok) {
        const errorData = await res.json();
        throw new Error(errorData.error || 'Failed to fetch credits');
      }
      return res.json();
    },
    refetchOnWindowFocus: false,
  });

  // Use credits mutation
  const useCredits = useMutation({
    mutationFn: async (payload: UseCreditsPayload) => {
      const res = await apiRequest('POST', '/user/credits/use', payload);
      if (!res.ok) {
        const errorData = await res.json();
        throw new Error(errorData.error || 'Failed to use credits');
      }
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/user/credits'] });
      toast({
        title: "Credits Used",
        description: "Your credits have been deducted successfully.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to Use Credits",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Purchase credits mutation
  const purchaseCredits = useMutation({
    mutationFn: async (payload: PurchaseCreditsPayload) => {
      const res = await apiRequest('POST', '/user/credits/purchase', payload);
      if (!res.ok) {
        const errorData = await res.json();
        throw new Error(errorData.error || 'Failed to purchase credits');
      }
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/user/credits'] });
      toast({
        title: "Credits Purchased",
        description: "Your credits have been added to your account.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Purchase Failed",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Helper function to check if the user has enough credits
  const hasEnoughCredits = (amount: number) => {
    return !!credits && credits.balance >= amount;
  };

  return {
    credits,
    isLoadingCredits,
    creditsError,
    refetchCredits,
    useCredits,
    purchaseCredits,
    hasEnoughCredits,
  };
}