import React, { useState } from "react";
import { <PERSON>, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { 
  Pencil, 
  Trash2, 
  Plus, 
  Lightbulb, 
  CheckCircle, 
  AlertCircle, 
  FileText, 
  Sparkles, 
  BrainCircuit, 
  Layers, 
  Info, 
  RefreshCw, 
  Shuffle, 
  Coffee,
  ArrowDown,
  ArrowUp,
  Settings
} from "lucide-react";
import { apiRequest } from "@/lib/queryClient";
import { useMutation } from "@tanstack/react-query";
import { toast } from "@/hooks/use-toast";

// Types for quiz generation
interface QuizGenerationParams {
  courseId: number;
  moduleId?: number;
  lessonId?: number;
  courseTitle: string;
  courseDescription: string;
  lessonScript?: string;
  questionTypes: string[];
  numQuestions: number;
  difficulty: string;
  includeFlashcards: boolean;
  includeSummary: boolean;
}

interface QuizQuestion {
  id?: number;
  questionType: string;
  questionText: string;
  explanation?: string;
  points: number;
  order: number;
  imageUrl?: string;
  answers: QuizAnswer[];
}

interface QuizAnswer {
  id?: number;
  answerText: string;
  isCorrect: boolean;
  explanation?: string;
  order: number;
}

interface QuizFlashcard {
  id?: number;
  front: string;
  back: string;
  order: number;
  category?: string;
  imageUrl?: string;
}

interface QuizData {
  title: string;
  description: string;
  instructions?: string;
  questions: QuizQuestion[];
  flashcards: QuizFlashcard[];
  summary?: string;
  passingScore: number;
  timeLimit?: number; // in seconds
  randomizeQuestions: boolean;
  showCorrectAnswers: boolean;
}

interface QuizBuilderProps {
  courseId: number;
  moduleId?: number;
  lessonId?: number;
  courseTitle: string;
  courseDescription: string;
  lessonScript?: string;
  onSave: (quizData: QuizData) => void;
}

export function QuizBuilder({
  courseId,
  moduleId,
  lessonId,
  courseTitle,
  courseDescription,
  lessonScript,
  onSave
}: QuizBuilderProps) {
  const [activeTab, setActiveTab] = useState<string>("generate");
  const [isGenerating, setIsGenerating] = useState<boolean>(false);
  const [quizData, setQuizData] = useState<QuizData>({
    title: `${courseTitle} - Assessment Quiz`,
    description: `A comprehensive assessment quiz for ${courseTitle}`,
    questions: [],
    flashcards: [],
    passingScore: 70,
    randomizeQuestions: false,
    showCorrectAnswers: true
  });
  
  // Generation parameters
  const [generationParams, setGenerationParams] = useState<QuizGenerationParams>({
    courseId: typeof courseId === 'number' ? courseId : 0,
    moduleId,
    lessonId,
    courseTitle: courseTitle || "",
    courseDescription: courseDescription || "",
    lessonScript,
    questionTypes: ["multiple-choice", "true-false"], // Always have at least one question type selected by default
    numQuestions: 10,
    difficulty: "medium",
    includeFlashcards: true,
    includeSummary: true,
  });

  // Mutation for generating quizzes
  const generateQuizMutation = useMutation({
    mutationFn: async (params: QuizGenerationParams) => {
      const response = await apiRequest("POST", "/api/ai/generate-quiz", params);
      return response.json();
    },
    onSuccess: (data) => {
      setQuizData({
        ...quizData,
        ...data,
        questions: data.questions.map((q: any, i: number) => ({
          ...q,
          order: i + 1,
          answers: q.answers.map((a: any, j: number) => ({
            ...a,
            order: j + 1,
          })),
        })),
        flashcards: data.flashcards?.map((f: any, i: number) => ({
          ...f,
          order: i + 1,
        })) || []
      });

      toast({
        title: "Quiz generated successfully",
        description: `Created ${data.questions.length} questions and ${data.flashcards?.length || 0} flashcards`,
      });
      
      setActiveTab("questions");
    },
    onError: (error) => {
      toast({
        title: "Failed to generate quiz",
        description: error instanceof Error ? error.message : "An unknown error occurred",
        variant: "destructive"
      });
    },
    onSettled: () => {
      setIsGenerating(false);
    }
  });

  const handleGenerateQuiz = () => {
    // Validate required fields
    if ((courseId === undefined || courseId === null) || 
        !courseTitle || 
        !courseDescription || 
        generationParams.questionTypes.length === 0) {
      toast({
        title: "Missing required fields",
        description: "Course ID, title, description, and at least one question type are required.",
        variant: "destructive"
      });
      return;
    }
    
    setIsGenerating(true);
    
    // Ensure all required fields are properly set
    const validParams: QuizGenerationParams = {
      ...generationParams,
      courseId: typeof courseId === 'number' ? courseId : 0,
      courseTitle: courseTitle || "Course Title",
      courseDescription: courseDescription || "Course Description",
      questionTypes: generationParams.questionTypes.length > 0 
        ? generationParams.questionTypes 
        : ["multiple-choice", "true-false"]
    };
    
    generateQuizMutation.mutate(validParams);
  };

  const handleAddQuestion = () => {
    const newQuestion: QuizQuestion = {
      questionType: "multiple-choice",
      questionText: "",
      explanation: "",
      points: 1,
      order: quizData.questions.length + 1,
      answers: [
        { answerText: "", isCorrect: true, order: 1 },
        { answerText: "", isCorrect: false, order: 2 },
        { answerText: "", isCorrect: false, order: 3 },
        { answerText: "", isCorrect: false, order: 4 },
      ]
    };
    
    setQuizData({
      ...quizData,
      questions: [...quizData.questions, newQuestion]
    });
  };

  const handleUpdateQuestion = (updatedQuestion: QuizQuestion, index: number) => {
    const updatedQuestions = [...quizData.questions];
    updatedQuestions[index] = updatedQuestion;
    
    setQuizData({
      ...quizData,
      questions: updatedQuestions
    });
  };
  
  const handleRemoveQuestion = (index: number) => {
    const updatedQuestions = quizData.questions.filter((_, i) => i !== index);
    // Re-order questions
    const reorderedQuestions = updatedQuestions.map((q, i) => ({
      ...q,
      order: i + 1
    }));
    
    setQuizData({
      ...quizData,
      questions: reorderedQuestions
    });
  };

  const handleMoveQuestion = (index: number, direction: 'up' | 'down') => {
    if (
      (direction === 'up' && index === 0) || 
      (direction === 'down' && index === quizData.questions.length - 1)
    ) {
      return;
    }
    
    const updatedQuestions = [...quizData.questions];
    const newIndex = direction === 'up' ? index - 1 : index + 1;
    
    // Swap the questions
    [updatedQuestions[index], updatedQuestions[newIndex]] = 
      [updatedQuestions[newIndex], updatedQuestions[index]];
    
    // Update the order property
    const reorderedQuestions = updatedQuestions.map((q, i) => ({
      ...q,
      order: i + 1
    }));
    
    setQuizData({
      ...quizData,
      questions: reorderedQuestions
    });
  };
  
  const handleAddFlashcard = () => {
    const newFlashcard: QuizFlashcard = {
      front: "",
      back: "",
      order: quizData.flashcards.length + 1
    };
    
    setQuizData({
      ...quizData,
      flashcards: [...quizData.flashcards, newFlashcard]
    });
  };
  
  const handleUpdateFlashcard = (updatedFlashcard: QuizFlashcard, index: number) => {
    const updatedFlashcards = [...quizData.flashcards];
    updatedFlashcards[index] = updatedFlashcard;
    
    setQuizData({
      ...quizData,
      flashcards: updatedFlashcards
    });
  };
  
  const handleRemoveFlashcard = (index: number) => {
    const updatedFlashcards = quizData.flashcards.filter((_, i) => i !== index);
    // Re-order flashcards
    const reorderedFlashcards = updatedFlashcards.map((f, i) => ({
      ...f,
      order: i + 1
    }));
    
    setQuizData({
      ...quizData,
      flashcards: reorderedFlashcards
    });
  };

  const handleFinish = () => {
    onSave(quizData);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-xl font-semibold">Quiz Builder</h3>
      </div>
      
      <Tabs defaultValue={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="mb-4 grid w-full grid-cols-3">
          <TabsTrigger value="generate">Generate Quiz</TabsTrigger>
          <TabsTrigger value="questions">Questions ({quizData.questions.length})</TabsTrigger>
          <TabsTrigger value="flashcards">Flashcards ({quizData.flashcards.length})</TabsTrigger>
        </TabsList>
        
        <TabsContent value="generate" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Quiz Generation</CardTitle>
              <CardDescription>
                Use AI to automatically create a quiz based on your course content. 
                Customize the generation parameters to fit your needs.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <Label htmlFor="quiz-title">Quiz Title</Label>
                  <Input 
                    id="quiz-title" 
                    value={quizData.title}
                    onChange={(e) => setQuizData({...quizData, title: e.target.value})}
                    placeholder="Enter quiz title" 
                  />
                </div>
                
                <div className="space-y-3">
                  <Label htmlFor="num-questions">Number of Questions</Label>
                  <Select 
                    value={String(generationParams.numQuestions)}
                    onValueChange={(value) => setGenerationParams({
                      ...generationParams, 
                      numQuestions: parseInt(value)
                    })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select number of questions" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="5">5 questions</SelectItem>
                      <SelectItem value="10">10 questions</SelectItem>
                      <SelectItem value="15">15 questions</SelectItem>
                      <SelectItem value="20">20 questions</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-3">
                  <Label htmlFor="difficulty">Difficulty Level</Label>
                  <Select 
                    value={generationParams.difficulty}
                    onValueChange={(value) => setGenerationParams({
                      ...generationParams, 
                      difficulty: value
                    })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select difficulty" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="easy">Easy</SelectItem>
                      <SelectItem value="medium">Medium</SelectItem>
                      <SelectItem value="hard">Hard</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-3">
                  <Label>Question Types</Label>
                  <div className="flex flex-wrap gap-2">
                    <Badge 
                      variant={generationParams.questionTypes.includes("multiple-choice") ? "default" : "outline"}
                      className="cursor-pointer"
                      onClick={() => {
                        // Prevent removing if it's the only type selected
                        if (generationParams.questionTypes.includes("multiple-choice") && 
                            generationParams.questionTypes.length === 1) {
                          toast({
                            title: "At least one question type required",
                            description: "You must keep at least one question type selected.",
                            variant: "destructive"
                          });
                          return;
                        }
                        
                        const types = generationParams.questionTypes.includes("multiple-choice")
                          ? generationParams.questionTypes.filter(t => t !== "multiple-choice")
                          : [...generationParams.questionTypes, "multiple-choice"];
                        setGenerationParams({...generationParams, questionTypes: types});
                      }}
                    >
                      Multiple Choice
                    </Badge>
                    <Badge 
                      variant={generationParams.questionTypes.includes("true-false") ? "default" : "outline"}
                      className="cursor-pointer"
                      onClick={() => {
                        // Prevent removing if it's the only type selected
                        if (generationParams.questionTypes.includes("true-false") && 
                            generationParams.questionTypes.length === 1) {
                          toast({
                            title: "At least one question type required",
                            description: "You must keep at least one question type selected.",
                            variant: "destructive"
                          });
                          return;
                        }
                        
                        const types = generationParams.questionTypes.includes("true-false")
                          ? generationParams.questionTypes.filter(t => t !== "true-false")
                          : [...generationParams.questionTypes, "true-false"];
                        setGenerationParams({...generationParams, questionTypes: types});
                      }}
                    >
                      True/False
                    </Badge>
                    <Badge 
                      variant={generationParams.questionTypes.includes("short-answer") ? "default" : "outline"}
                      className="cursor-pointer"
                      onClick={() => {
                        // Prevent removing if it's the only type selected
                        if (generationParams.questionTypes.includes("short-answer") && 
                            generationParams.questionTypes.length === 1) {
                          toast({
                            title: "At least one question type required",
                            description: "You must keep at least one question type selected.",
                            variant: "destructive"
                          });
                          return;
                        }
                        
                        const types = generationParams.questionTypes.includes("short-answer")
                          ? generationParams.questionTypes.filter(t => t !== "short-answer")
                          : [...generationParams.questionTypes, "short-answer"];
                        setGenerationParams({...generationParams, questionTypes: types});
                      }}
                    >
                      Short Answer
                    </Badge>
                  </div>
                  {generationParams.questionTypes.length === 0 && (
                    <p className="text-xs text-red-500">Please select at least one question type.</p>
                  )}
                </div>
              </div>
              
              <div className="space-y-3">
                <Label htmlFor="quiz-description">Quiz Description</Label>
                <Textarea 
                  id="quiz-description" 
                  value={quizData.description}
                  onChange={(e) => setQuizData({...quizData, description: e.target.value})}
                  placeholder="Enter a detailed description of the quiz" 
                  rows={3}
                />
              </div>
              
              <div className="grid md:grid-cols-2 gap-6">
                <div className="flex items-center space-x-2">
                  <Switch 
                    id="include-flashcards"
                    checked={generationParams.includeFlashcards}
                    onCheckedChange={(checked) => setGenerationParams({...generationParams, includeFlashcards: checked})}
                  />
                  <Label htmlFor="include-flashcards">Include flashcards</Label>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Switch 
                    id="include-summary"
                    checked={generationParams.includeSummary}
                    onCheckedChange={(checked) => setGenerationParams({...generationParams, includeSummary: checked})}
                  />
                  <Label htmlFor="include-summary">Include summary</Label>
                </div>
              </div>
              
              <div className="bg-blue-50 p-4 rounded-md border border-blue-200">
                <div className="flex items-start">
                  <Info className="h-5 w-5 text-blue-600 mt-0.5 mr-2" />
                  <div>
                    <h4 className="text-sm font-medium text-blue-800 mb-1">AI-Generation Information</h4>
                    <p className="text-xs text-blue-700">
                      The quiz generator will analyze your course content and create questions that test understanding
                      of key concepts. All questions, answers and flashcards can be edited after generation. 
                      This process will use approximately 25 AI credits.
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-end space-x-2">
              <Button 
                onClick={handleGenerateQuiz}
                disabled={isGenerating || generationParams.questionTypes.length === 0}
                className="space-x-2"
              >
                {isGenerating ? (
                  <>
                    <span className="animate-spin">⟳</span>
                    <span>Generating Quiz...</span>
                  </>
                ) : (
                  <>
                    <Sparkles className="h-4 w-4 mr-1" />
                    <span>Generate Quiz</span>
                  </>
                )}
              </Button>
            </CardFooter>
          </Card>
          
          {quizData.summary && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <BrainCircuit className="h-5 w-5 mr-2" />
                  <span>Knowledge Summary</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="prose prose-sm max-w-none">
                  <div dangerouslySetInnerHTML={{ __html: quizData.summary.replace(/\n/g, '<br/>') }} />
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>
        
        <TabsContent value="questions" className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>Quiz Questions</CardTitle>
                <Button size="sm" onClick={handleAddQuestion}>
                  <Plus className="h-4 w-4 mr-1" />
                  Add Question
                </Button>
              </div>
              <CardDescription>
                Create, edit and organize questions for your quiz. Different question types support different answer formats.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-2 gap-4 mb-4">
                <div className="flex items-center space-x-2">
                  <Switch 
                    id="randomize-questions"
                    checked={quizData.randomizeQuestions}
                    onCheckedChange={(checked) => setQuizData({...quizData, randomizeQuestions: checked})}
                  />
                  <Label htmlFor="randomize-questions">Randomize question order</Label>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Switch 
                    id="show-correct-answers"
                    checked={quizData.showCorrectAnswers}
                    onCheckedChange={(checked) => setQuizData({...quizData, showCorrectAnswers: checked})}
                  />
                  <Label htmlFor="show-correct-answers">Show correct answers after submission</Label>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Label htmlFor="passing-score" className="whitespace-nowrap">Passing Score (%): </Label>
                  <Input 
                    id="passing-score" 
                    type="number"
                    min="1"
                    max="100"
                    value={quizData.passingScore}
                    onChange={(e) => setQuizData({...quizData, passingScore: parseInt(e.target.value) || 70})}
                    className="max-w-[100px]"
                  />
                </div>
                
                <div className="flex items-center space-x-2">
                  <Label htmlFor="time-limit" className="whitespace-nowrap">Time Limit (minutes): </Label>
                  <Input 
                    id="time-limit" 
                    type="number"
                    min="0"
                    value={quizData.timeLimit ? Math.round(quizData.timeLimit / 60) : ''}
                    onChange={(e) => {
                      const minutes = parseInt(e.target.value);
                      setQuizData({
                        ...quizData, 
                        timeLimit: isNaN(minutes) ? undefined : minutes * 60
                      });
                    }}
                    placeholder="No limit"
                    className="max-w-[100px]"
                  />
                </div>
              </div>
              
              <ScrollArea className="h-[500px] pr-4">
                {quizData.questions.length === 0 ? (
                  <div className="text-center py-8 text-slate-500">
                    <Layers className="h-12 w-12 mx-auto mb-3 text-slate-300" />
                    <h4 className="text-lg font-medium mb-1">No Questions Yet</h4>
                    <p className="text-sm mb-4">Generate questions with AI or add them manually</p>
                    <Button size="sm" onClick={handleAddQuestion}>
                      <Plus className="h-4 w-4 mr-1" />
                      Add Question
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {quizData.questions.map((question, index) => (
                      <Card key={index} className="mb-4 overflow-hidden">
                        <CardHeader className="bg-slate-50 py-3">
                          <div className="flex justify-between items-center">
                            <div className="flex items-center">
                              <Badge className="mr-2">{question.order}</Badge>
                              <Badge variant="outline">
                                {question.questionType === "multiple-choice" && "Multiple Choice"}
                                {question.questionType === "true-false" && "True/False"}
                                {question.questionType === "short-answer" && "Short Answer"}
                              </Badge>
                            </div>
                            <div className="flex space-x-1">
                              <Button 
                                size="icon" 
                                variant="ghost"
                                disabled={index === 0}
                                onClick={() => handleMoveQuestion(index, 'up')}
                              >
                                <ArrowUp className="h-4 w-4" />
                              </Button>
                              <Button 
                                size="icon" 
                                variant="ghost"
                                disabled={index === quizData.questions.length - 1}
                                onClick={() => handleMoveQuestion(index, 'down')}
                              >
                                <ArrowDown className="h-4 w-4" />
                              </Button>
                              <Button 
                                size="icon" 
                                variant="ghost"
                                onClick={() => handleRemoveQuestion(index)}
                              >
                                <Trash2 className="h-4 w-4 text-red-500" />
                              </Button>
                            </div>
                          </div>
                        </CardHeader>
                        <CardContent className="py-4">
                          <div className="space-y-4">
                            <div>
                              <Label htmlFor={`question-${index}`}>Question</Label>
                              <Textarea 
                                id={`question-${index}`}
                                value={question.questionText}
                                onChange={(e) => handleUpdateQuestion(
                                  {...question, questionText: e.target.value}, 
                                  index
                                )}
                                className="mt-1"
                                rows={2}
                              />
                            </div>
                            
                            <div>
                              <Label>Question Type</Label>
                              <Select 
                                value={question.questionType}
                                onValueChange={(value) => {
                                  // Prepare appropriate answers based on question type
                                  let answers = [...question.answers];
                                  
                                  if (value === "true-false" && question.questionType !== "true-false") {
                                    // Set up true/false answers
                                    answers = [
                                      { answerText: "True", isCorrect: true, order: 1 },
                                      { answerText: "False", isCorrect: false, order: 2 },
                                    ];
                                  } else if (value === "multiple-choice" && question.questionType === "true-false") {
                                    // Set up multiple choice answers
                                    answers = [
                                      { answerText: "", isCorrect: true, order: 1 },
                                      { answerText: "", isCorrect: false, order: 2 },
                                      { answerText: "", isCorrect: false, order: 3 },
                                      { answerText: "", isCorrect: false, order: 4 },
                                    ];
                                  } else if (value === "short-answer") {
                                    // Set up short answer
                                    answers = [
                                      { answerText: "", isCorrect: true, order: 1 },
                                    ];
                                  }
                                  
                                  handleUpdateQuestion(
                                    {...question, questionType: value, answers}, 
                                    index
                                  );
                                }}
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder="Select question type" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="multiple-choice">Multiple Choice</SelectItem>
                                  <SelectItem value="true-false">True/False</SelectItem>
                                  <SelectItem value="short-answer">Short Answer</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                            
                            <div>
                              <Label className="mb-2 inline-block">
                                {question.questionType === "short-answer" ? "Answer Key" : "Answer Options"}
                              </Label>
                              
                              <div className="space-y-3">
                                {question.answers.map((answer, answerIndex) => (
                                  <div key={answerIndex} className="flex items-center space-x-2">
                                    {question.questionType !== "short-answer" && (
                                      <div className="mr-1">
                                        <input 
                                          type={question.questionType === "multiple-choice" ? "radio" : "hidden"}
                                          checked={answer.isCorrect}
                                          onChange={() => {
                                            // For multiple choice, only one correct answer
                                            if (question.questionType === "multiple-choice") {
                                              const updatedAnswers = question.answers.map((a, i) => ({
                                                ...a,
                                                isCorrect: i === answerIndex
                                              }));
                                              handleUpdateQuestion(
                                                {...question, answers: updatedAnswers},
                                                index
                                              );
                                            }
                                          }}
                                          className="h-4 w-4"
                                          id={`question-${index}-answer-${answerIndex}`}
                                        />
                                      </div>
                                    )}
                                    
                                    <Input 
                                      value={answer.answerText}
                                      onChange={(e) => {
                                        const updatedAnswers = [...question.answers];
                                        updatedAnswers[answerIndex] = {
                                          ...answer,
                                          answerText: e.target.value
                                        };
                                        handleUpdateQuestion(
                                          {...question, answers: updatedAnswers},
                                          index
                                        );
                                      }}
                                      placeholder={
                                        question.questionType === "short-answer" 
                                          ? "Correct answer (key)" 
                                          : `Answer option ${answerIndex + 1}`
                                      }
                                      className="flex-1"
                                    />
                                    
                                    {question.questionType === "multiple-choice" && (
                                      <Button 
                                        size="icon" 
                                        variant="ghost"
                                        onClick={() => {
                                          if (question.answers.length <= 2) {
                                            toast({
                                              title: "Cannot remove answer",
                                              description: "Multiple choice questions must have at least 2 answers",
                                              variant: "destructive"
                                            });
                                            return;
                                          }
                                          
                                          if (answer.isCorrect) {
                                            toast({
                                              title: "Cannot remove answer",
                                              description: "Cannot remove the correct answer. Select another answer as correct first.",
                                              variant: "destructive"
                                            });
                                            return;
                                          }
                                          
                                          const updatedAnswers = question.answers.filter((_, i) => i !== answerIndex);
                                          handleUpdateQuestion(
                                            {...question, answers: updatedAnswers},
                                            index
                                          );
                                        }}
                                      >
                                        <Trash2 className="h-4 w-4 text-red-500" />
                                      </Button>
                                    )}
                                  </div>
                                ))}
                              </div>
                              
                              {question.questionType === "multiple-choice" && (
                                <Button 
                                  size="sm"
                                  variant="outline"
                                  className="mt-2"
                                  onClick={() => {
                                    const newAnswer = {
                                      answerText: "",
                                      isCorrect: false,
                                      order: question.answers.length + 1
                                    };
                                    handleUpdateQuestion(
                                      {...question, answers: [...question.answers, newAnswer]},
                                      index
                                    );
                                  }}
                                >
                                  <Plus className="h-3 w-3 mr-1" />
                                  Add Answer Option
                                </Button>
                              )}
                            </div>
                            
                            <div>
                              <Label htmlFor={`explanation-${index}`}>Explanation (Optional)</Label>
                              <Textarea 
                                id={`explanation-${index}`}
                                value={question.explanation || ""}
                                onChange={(e) => handleUpdateQuestion(
                                  {...question, explanation: e.target.value}, 
                                  index
                                )}
                                placeholder="Explain why the correct answer is right and/or why other options are wrong"
                                className="mt-1"
                                rows={2}
                              />
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                )}
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="flashcards" className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>Flashcards</CardTitle>
                <Button size="sm" onClick={handleAddFlashcard}>
                  <Plus className="h-4 w-4 mr-1" />
                  Add Flashcard
                </Button>
              </div>
              <CardDescription>
                Create study flashcards to help learners memorize key concepts
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-[500px] pr-4">
                {quizData.flashcards.length === 0 ? (
                  <div className="text-center py-8 text-slate-500">
                    <Coffee className="h-12 w-12 mx-auto mb-3 text-slate-300" />
                    <h4 className="text-lg font-medium mb-1">No Flashcards Yet</h4>
                    <p className="text-sm mb-4">Create flashcards to help with memorization</p>
                    <Button size="sm" onClick={handleAddFlashcard}>
                      <Plus className="h-4 w-4 mr-1" />
                      Add Flashcard
                    </Button>
                  </div>
                ) : (
                  <div className="grid md:grid-cols-2 gap-4">
                    {quizData.flashcards.map((flashcard, index) => (
                      <Card key={index} className="overflow-hidden">
                        <CardHeader className="py-3 bg-slate-50">
                          <div className="flex justify-between items-center">
                            <Badge>Card {index + 1}</Badge>
                            <Button 
                              size="icon" 
                              variant="ghost"
                              onClick={() => handleRemoveFlashcard(index)}
                            >
                              <Trash2 className="h-4 w-4 text-red-500" />
                            </Button>
                          </div>
                        </CardHeader>
                        <CardContent className="py-4 space-y-4">
                          <div>
                            <Label htmlFor={`flashcard-front-${index}`}>Front (Question)</Label>
                            <Textarea 
                              id={`flashcard-front-${index}`}
                              value={flashcard.front}
                              onChange={(e) => handleUpdateFlashcard(
                                {...flashcard, front: e.target.value}, 
                                index
                              )}
                              className="mt-1"
                              rows={2}
                            />
                          </div>
                          
                          <div>
                            <Label htmlFor={`flashcard-back-${index}`}>Back (Answer)</Label>
                            <Textarea 
                              id={`flashcard-back-${index}`}
                              value={flashcard.back}
                              onChange={(e) => handleUpdateFlashcard(
                                {...flashcard, back: e.target.value}, 
                                index
                              )}
                              className="mt-1"
                              rows={2}
                            />
                          </div>
                          
                          <div>
                            <Label htmlFor={`flashcard-category-${index}`}>Category (Optional)</Label>
                            <Input 
                              id={`flashcard-category-${index}`}
                              value={flashcard.category || ""}
                              onChange={(e) => handleUpdateFlashcard(
                                {...flashcard, category: e.target.value}, 
                                index
                              )}
                              placeholder="E.g., Definitions, Formulas, Key Concepts"
                              className="mt-1"
                            />
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                )}
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
      
      <div className="flex justify-end">
        <Button 
          onClick={handleFinish}
          disabled={!quizData.title || quizData.questions.length === 0}
          className="bg-green-600 hover:bg-green-700 text-white"
        >
          Save Quiz
        </Button>
      </div>
    </div>
  );
}