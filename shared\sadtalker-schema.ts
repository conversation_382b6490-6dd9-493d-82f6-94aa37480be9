// Defines types for the SadTalker video generation service

export interface VideoGenerationJob {
  id: string;
  userId: number;
  status: 'processing' | 'completed' | 'error';
  script: string;
  createdAt: Date;
  estimatedCompletionTime: number; // in seconds
  avatarSource: 'upload' | 'url';
  avatarPath: string;
  videoUrl: string | null;
  error: string | null;
  mediaId?: number; // ID in the media library once completed
}

export interface SadTalkerGenerationRequest {
  script: string;
  image?: File;
  imageUrl?: string;
  voice: string;
  enhanceAudio?: boolean;
  title?: string;
  courseId?: number;
  lessonId?: number;
}

export interface SadTalkerJobStatus {
  id: string;
  status: 'processing' | 'completed' | 'error';
  createdAt: Date;
  estimatedTime: number;
  videoUrl: string | null;
  error: string | null;
}

export interface Voice {
  voice_id: string;
  name: string;
  gender: 'male' | 'female';
  preview_url?: string;
  source?: string;
}