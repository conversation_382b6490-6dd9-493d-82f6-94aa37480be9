import express from 'express';
import { VoiceServiceManager } from '../services/voice-service-manager';

const router = express.Router();
const voiceManager = new VoiceServiceManager();

// Get all available TTS services and their voices
router.get('/services', async (req, res) => {
  try {
    const services = await voiceManager.getAllServices();
    res.json({ services });
  } catch (error) {
    console.error('Error fetching TTS services:', error);
    res.status(500).json({ message: 'Failed to fetch TTS services' });
  }
});

// Generate voice preview
router.post('/preview', async (req, res) => {
  try {
    const { service, voiceId, text, tone = 'professional', speed = 1.0, pitch = 1.0 } = req.body;

    if (!service || !voiceId || !text) {
      return res.status(400).json({ message: 'Missing required parameters' });
    }

    const result = await voiceManager.generateVoice({
      service,
      voiceId,
      text: text.substring(0, 200), // Limit preview text
      settings: { tone, speed, pitch }
    });

    res.json({ audioUrl: result.audioUrl });
  } catch (error) {
    console.error('Error generating voice preview:', error);
    res.status(500).json({ message: 'Failed to generate voice preview' });
  }
});

// Generate batch voice content
router.post('/generate-batch', async (req, res) => {
  try {
    const { service, voiceId, scripts, settings } = req.body;

    if (!service || !voiceId || !scripts || !Array.isArray(scripts)) {
      return res.status(400).json({ message: 'Invalid request parameters' });
    }

    const results = [];
    
    for (const script of scripts) {
      try {
        const result = await voiceManager.generateVoice({
          service,
          voiceId,
          text: script.text,
          settings: {
            tone: settings.tone || 'professional',
            speed: settings.speed || 1.0,
            pitch: settings.pitch || 1.0
          }
        });

        results.push({
          moduleId: script.moduleId,
          lessonId: script.lessonId,
          moduleTitle: script.moduleTitle,
          lessonTitle: script.lessonTitle,
          audioUrl: result.audioUrl,
          duration: result.duration,
          service: result.service
        });
      } catch (scriptError) {
        console.error(`Error generating voice for script ${script.lessonId}:`, scriptError);
        // Continue with next script even if one fails
        results.push({
          moduleId: script.moduleId,
          lessonId: script.lessonId,
          moduleTitle: script.moduleTitle,
          lessonTitle: script.lessonTitle,
          error: 'Failed to generate audio',
          service
        });
      }
    }

    res.json({ results });
  } catch (error) {
    console.error('Error in batch voice generation:', error);
    res.status(500).json({ message: 'Failed to generate batch voice content' });
  }
});

// Get available voices for a specific service
router.get('/voices/:service', async (req, res) => {
  try {
    const { service } = req.params;
    const voices = await voiceManager.getVoicesForService(service);
    res.json({ voices });
  } catch (error) {
    console.error(`Error fetching voices for ${req.params.service}:`, error);
    res.status(500).json({ message: 'Failed to fetch voices' });
  }
});

export default router;