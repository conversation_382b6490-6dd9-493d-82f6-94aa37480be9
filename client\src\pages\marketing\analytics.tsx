import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON>Content, 
  <PERSON><PERSON><PERSON>ist, 
  TabsTrigger
} from '@/components/ui/tabs';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';
import { 
  Calendar as CalendarIcon, 
  BarChart4, 
  LineChart, 
  Activity, 
  Users, 
  MousePointerClick,
  Newspaper,
  ArrowUpRight,
  ArrowDownRight,
  Mail
} from 'lucide-react';
import { apiRequest } from '@/lib/queryClient';
import { <PERSON><PERSON><PERSON>, LineChart as RechartsLine<PERSON><PERSON>, <PERSON>, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, Line } from 'recharts';

// Example data - in a real app, this would come from the API
const generateDummyPageViewData = (days: number) => {
  const data = [];
  const now = new Date();
  
  for (let i = days - 1; i >= 0; i--) {
    const date = new Date(now);
    date.setDate(date.getDate() - i);
    
    data.push({
      date: format(date, 'MMM dd'),
      'Landing Pages': Math.floor(Math.random() * 300) + 100,
      'Email Campaigns': Math.floor(Math.random() * 200) + 50,
    });
  }
  
  return data;
};

const generateDummyConversionData = (days: number) => {
  const data = [];
  const now = new Date();
  
  for (let i = days - 1; i >= 0; i--) {
    const date = new Date(now);
    date.setDate(date.getDate() - i);
    
    const visitors = Math.floor(Math.random() * 500) + 200;
    const conversions = Math.floor(visitors * (Math.random() * 0.15 + 0.05));
    
    data.push({
      date: format(date, 'MMM dd'),
      visitors,
      conversions,
      rate: Number((conversions / visitors * 100).toFixed(1))
    });
  }
  
  return data;
};

const generateEmailStats = () => {
  return {
    sent: Math.floor(Math.random() * 5000) + 1000,
    opened: Math.floor(Math.random() * 3000) + 500,
    clicked: Math.floor(Math.random() * 1000) + 100,
    bounced: Math.floor(Math.random() * 100) + 10,
    unsubscribed: Math.floor(Math.random() * 50) + 5,
    openRate: Math.random() * 0.4 + 0.2,
    clickRate: Math.random() * 0.2 + 0.05
  };
};

const generateLandingPageStats = () => {
  return [
    {
      id: 1,
      title: "Master the Art of Photography",
      visitors: Math.floor(Math.random() * 1000) + 200,
      conversions: Math.floor(Math.random() * 100) + 10,
      conversionRate: Math.random() * 0.1 + 0.02
    },
    {
      id: 2,
      title: "Advanced Digital Marketing",
      visitors: Math.floor(Math.random() * 1000) + 200,
      conversions: Math.floor(Math.random() * 100) + 10,
      conversionRate: Math.random() * 0.1 + 0.02
    },
    {
      id: 3,
      title: "Complete Web Development",
      visitors: Math.floor(Math.random() * 1000) + 200,
      conversions: Math.floor(Math.random() * 100) + 10,
      conversionRate: Math.random() * 0.1 + 0.02
    }
  ];
};

export default function MarketingAnalytics() {
  const [dateRange, setDateRange] = useState<{
    from: Date | undefined;
    to: Date | undefined;
  }>({
    from: new Date(new Date().setDate(new Date().getDate() - 30)),
    to: new Date()
  });
  
  const [activeTab, setActiveTab] = useState("overview");
  
  // Example queries - in a real app, you would fetch actual data from your backend
  const { data: viewsData, isLoading: isLoadingViews } = useQuery({
    queryKey: ['marketing-analytics-views', dateRange],
    queryFn: async () => {
      const response = await apiRequest(
        'GET', 
        `/api/marketing/analytics/views?from=${dateRange.from?.toISOString()}&to=${dateRange.to?.toISOString()}`
      );
      return await response.json();
    }
  });
  
  const { data: conversionData, isLoading: isLoadingConversions } = useQuery({
    queryKey: ['marketing-analytics-conversions', dateRange],
    queryFn: async () => {
      const response = await apiRequest(
        'GET',
        `/api/marketing/analytics/conversions?from=${dateRange.from?.toISOString()}&to=${dateRange.to?.toISOString()}`
      );
      return await response.json();
    }
  });
  
  const { data: emailStats, isLoading: isLoadingEmailStats } = useQuery({
    queryKey: ['marketing-analytics-email'],
    queryFn: async () => {
      const response = await apiRequest('GET', '/api/marketing/analytics/email-stats');
      return await response.json();
    }
  });
  
  const { data: landingPageStats, isLoading: isLoadingLandingPageStats } = useQuery({
    queryKey: ['marketing-analytics-landing-pages'],
    queryFn: async () => {
      const response = await apiRequest('GET', '/api/marketing/analytics/landing-pages');
      return await response.json();
    }
  });

  return (
    <div className="container mx-auto p-6">
      <div className="flex flex-col md:flex-row justify-between items-start mb-6">
        <div>
          <h1 className="text-3xl font-bold mb-2 gradient-heading">Marketing Analytics</h1>
          <p className="text-slate-600">Track the performance of your marketing campaigns and landing pages</p>
        </div>
        
        <div className="flex mt-4 md:mt-0">
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className="w-[260px] justify-start text-left font-normal flex items-center"
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {dateRange?.from ? (
                  dateRange.to ? (
                    <>
                      {format(dateRange.from, "LLL dd, y")} - {format(dateRange.to, "LLL dd, y")}
                    </>
                  ) : (
                    format(dateRange.from, "LLL dd, y")
                  )
                ) : (
                  <span>Pick a date range</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="end">
              <Calendar
                initialFocus
                mode="range"
                defaultMonth={dateRange?.from}
                selected={dateRange}
                onSelect={setDateRange}
                numberOfMonths={2}
              />
            </PopoverContent>
          </Popover>
          
          <Select defaultValue="30">
            <SelectTrigger className="w-[120px] ml-2">
              <SelectValue placeholder="Period" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7">Last 7 days</SelectItem>
              <SelectItem value="30">Last 30 days</SelectItem>
              <SelectItem value="90">Last 90 days</SelectItem>
              <SelectItem value="365">Last year</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
      
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3 mb-8">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="email">Email Campaigns</TabsTrigger>
          <TabsTrigger value="landing-pages">Landing Pages</TabsTrigger>
        </TabsList>
        
        <TabsContent value="overview">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Total Views</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div className="text-2xl font-bold">12,546</div>
                  <div className="flex items-center text-green-600 font-medium text-sm">
                    <ArrowUpRight className="h-4 w-4 mr-1" />
                    12.5%
                  </div>
                </div>
                <p className="text-xs text-slate-500 mt-1">Compared to last period</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Conversion Rate</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div className="text-2xl font-bold">5.8%</div>
                  <div className="flex items-center text-green-600 font-medium text-sm">
                    <ArrowUpRight className="h-4 w-4 mr-1" />
                    2.1%
                  </div>
                </div>
                <p className="text-xs text-slate-500 mt-1">Compared to last period</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Email Open Rate</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div className="text-2xl font-bold">22.4%</div>
                  <div className="flex items-center text-red-600 font-medium text-sm">
                    <ArrowDownRight className="h-4 w-4 mr-1" />
                    1.3%
                  </div>
                </div>
                <p className="text-xs text-slate-500 mt-1">Compared to last period</p>
              </CardContent>
            </Card>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Page Views</CardTitle>
                <CardDescription>Daily page views over the selected period</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  {viewsData && (
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart data={viewsData}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="date" />
                        <YAxis />
                        <Tooltip />
                        <Legend />
                        <Bar dataKey="Landing Pages" fill="#8884d8" />
                        <Bar dataKey="Email Campaigns" fill="#82ca9d" />
                      </BarChart>
                    </ResponsiveContainer>
                  )}
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>Conversion Rate</CardTitle>
                <CardDescription>Visitors vs. conversions over time</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  {conversionData && (
                    <ResponsiveContainer width="100%" height="100%">
                      <RechartsLineChart data={conversionData}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="date" />
                        <YAxis yAxisId="left" />
                        <YAxis yAxisId="right" orientation="right" />
                        <Tooltip />
                        <Legend />
                        <Line yAxisId="left" type="monotone" dataKey="visitors" stroke="#8884d8" activeDot={{ r: 8 }} />
                        <Line yAxisId="right" type="monotone" dataKey="conversions" stroke="#82ca9d" />
                      </RechartsLineChart>
                    </ResponsiveContainer>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        <TabsContent value="email">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-slate-500">Emails Sent</CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="flex items-center">
                  <Mail className="h-5 w-5 text-primary mr-2" />
                  <div className="text-2xl font-bold">{emailStats?.sent.toLocaleString()}</div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-slate-500">Open Rate</CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="flex items-center">
                  <Activity className="h-5 w-5 text-blue-500 mr-2" />
                  <div className="text-2xl font-bold">{emailStats ? `${(emailStats.openRate * 100).toFixed(1)}%` : '0%'}</div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-slate-500">Click Rate</CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="flex items-center">
                  <MousePointerClick className="h-5 w-5 text-green-500 mr-2" />
                  <div className="text-2xl font-bold">{emailStats ? `${(emailStats.clickRate * 100).toFixed(1)}%` : '0%'}</div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-slate-500">Unsubscribe Rate</CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="flex items-center">
                  <Users className="h-5 w-5 text-red-500 mr-2" />
                  <div className="text-2xl font-bold">{emailStats ? `${((emailStats.unsubscribed / emailStats.sent) * 100).toFixed(2)}%` : '0%'}</div>
                </div>
              </CardContent>
            </Card>
          </div>
          
          <Card>
            <CardHeader>
              <CardTitle>Email Campaign Performance</CardTitle>
              <CardDescription>Recent campaign metrics</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="relative w-full overflow-auto">
                <table className="w-full caption-bottom text-sm">
                  <thead>
                    <tr className="border-b">
                      <th className="h-12 px-4 text-left align-middle font-medium">Campaign</th>
                      <th className="h-12 px-4 text-left align-middle font-medium">Sent</th>
                      <th className="h-12 px-4 text-left align-middle font-medium">Opened</th>
                      <th className="h-12 px-4 text-left align-middle font-medium">Clicked</th>
                      <th className="h-12 px-4 text-left align-middle font-medium">Open Rate</th>
                      <th className="h-12 px-4 text-left align-middle font-medium">Click Rate</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr className="border-b">
                      <td className="p-4 align-middle">Spring Course Promotion</td>
                      <td className="p-4 align-middle">3,245</td>
                      <td className="p-4 align-middle">1,892</td>
                      <td className="p-4 align-middle">632</td>
                      <td className="p-4 align-middle">58.3%</td>
                      <td className="p-4 align-middle">19.5%</td>
                    </tr>
                    <tr className="border-b">
                      <td className="p-4 align-middle">Monthly Newsletter</td>
                      <td className="p-4 align-middle">5,128</td>
                      <td className="p-4 align-middle">2,456</td>
                      <td className="p-4 align-middle">842</td>
                      <td className="p-4 align-middle">47.9%</td>
                      <td className="p-4 align-middle">16.4%</td>
                    </tr>
                    <tr className="border-b">
                      <td className="p-4 align-middle">Special Discount</td>
                      <td className="p-4 align-middle">2,564</td>
                      <td className="p-4 align-middle">1,845</td>
                      <td className="p-4 align-middle">972</td>
                      <td className="p-4 align-middle">72.0%</td>
                      <td className="p-4 align-middle">37.9%</td>
                    </tr>
                    <tr>
                      <td className="p-4 align-middle">Course Launch</td>
                      <td className="p-4 align-middle">1,825</td>
                      <td className="p-4 align-middle">1,248</td>
                      <td className="p-4 align-middle">546</td>
                      <td className="p-4 align-middle">68.4%</td>
                      <td className="p-4 align-middle">29.9%</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="landing-pages">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-slate-500">Total Visitors</CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="flex items-center">
                  <Users className="h-5 w-5 text-primary mr-2" />
                  <div className="text-2xl font-bold">
                    {landingPageStats ? landingPageStats.reduce((sum, page) => sum + page.visitors, 0).toLocaleString() : '0'}
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-slate-500">Total Conversions</CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="flex items-center">
                  <Activity className="h-5 w-5 text-green-500 mr-2" />
                  <div className="text-2xl font-bold">
                    {landingPageStats ? landingPageStats.reduce((sum, page) => sum + page.conversions, 0).toLocaleString() : '0'}
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-slate-500">Avg. Conversion Rate</CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="flex items-center">
                  <Newspaper className="h-5 w-5 text-blue-500 mr-2" />
                  <div className="text-2xl font-bold">
                    {landingPageStats ? 
                      `${(landingPageStats.reduce((sum, page) => sum + page.conversionRate, 0) / landingPageStats.length * 100).toFixed(1)}%` 
                      : '0%'}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
          
          <Card>
            <CardHeader>
              <CardTitle>Landing Page Performance</CardTitle>
              <CardDescription>Performance metrics for each landing page</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="relative w-full overflow-auto">
                <table className="w-full caption-bottom text-sm">
                  <thead>
                    <tr className="border-b">
                      <th className="h-12 px-4 text-left align-middle font-medium">Page Title</th>
                      <th className="h-12 px-4 text-left align-middle font-medium">Visitors</th>
                      <th className="h-12 px-4 text-left align-middle font-medium">Conversions</th>
                      <th className="h-12 px-4 text-left align-middle font-medium">Conversion Rate</th>
                      <th className="h-12 px-4 text-left align-middle font-medium">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {landingPageStats?.map((page) => (
                      <tr key={page.id} className="border-b">
                        <td className="p-4 align-middle font-medium">{page.title}</td>
                        <td className="p-4 align-middle">{page.visitors.toLocaleString()}</td>
                        <td className="p-4 align-middle">{page.conversions.toLocaleString()}</td>
                        <td className="p-4 align-middle">{(page.conversionRate * 100).toFixed(1)}%</td>
                        <td className="p-4 align-middle">
                          <Button variant="outline" size="sm">View Details</Button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}