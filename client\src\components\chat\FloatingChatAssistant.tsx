import React, { useState, useEffect, useRef, useCallback } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON>, CardContent, CardHeader, Card<PERSON><PERSON>le, CardFooter } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Loader2, X, Minimize2, MessageSquare, Send, Bot, RefreshCw } from 'lucide-react';
import { cn } from '@/lib/utils';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';

interface Message {
  id: string;
  content: string;
  sender: 'user' | 'assistant';
  timestamp: Date;
}

export function FloatingChatAssistant() {
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [position, setPosition] = useState<{ x: number; y: number }>({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState<boolean>(false);
  const [dragOffset, setDragOffset] = useState<{ x: number; y: number }>({ x: 0, y: 0 });
  const chatBubbleRef = useRef<HTMLDivElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const { toast } = useToast();

  // Load persisted messages from localStorage
  useEffect(() => {
    try {
      const savedMessages = localStorage.getItem('chatAssistantMessages');
      if (savedMessages) {
        const parsedMessages = JSON.parse(savedMessages).map((msg: any) => ({
          ...msg,
          timestamp: new Date(msg.timestamp),
        }));
        setMessages(parsedMessages);
      } else {
        // Initial welcome message if no saved messages
        const welcomeMessage: Message = {
          id: Date.now().toString(),
          content: "👋 Hi there! I'm your course creation assistant. How can I help you today?",
          sender: 'assistant',
          timestamp: new Date(),
        };
        setMessages([welcomeMessage]);
      }

      // Retrieve saved position from localStorage
      const savedPosition = localStorage.getItem('chatAssistantPosition');
      if (savedPosition) {
        setPosition(JSON.parse(savedPosition));
      }
    } catch (error) {
      console.error('Error loading chat history:', error);
    }
  }, []);

  // Save messages to localStorage when they change
  useEffect(() => {
    if (messages.length > 0) {
      localStorage.setItem('chatAssistantMessages', JSON.stringify(messages));
    }
  }, [messages]);

  // Save position to localStorage when it changes
  useEffect(() => {
    localStorage.setItem('chatAssistantPosition', JSON.stringify(position));
  }, [position]);

  // Scroll to bottom of messages when new message is added
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if (chatBubbleRef.current && !isOpen) {
      setIsDragging(true);
      const rect = chatBubbleRef.current.getBoundingClientRect();
      setDragOffset({
        x: e.clientX - rect.left,
        y: e.clientY - rect.top,
      });
    }
  }, [isOpen]);

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (isDragging && !isOpen) {
      const newX = e.clientX - dragOffset.x;
      const newY = e.clientY - dragOffset.y;
      
      // Keep within viewport bounds
      const maxX = window.innerWidth - (chatBubbleRef.current?.offsetWidth || 60);
      const maxY = window.innerHeight - (chatBubbleRef.current?.offsetHeight || 60);
      
      setPosition({
        x: Math.max(0, Math.min(newX, maxX)),
        y: Math.max(0, Math.min(newY, maxY)),
      });
    }
  }, [isDragging, dragOffset, isOpen]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  // Set up global mouse event listeners for dragging
  useEffect(() => {
    window.addEventListener('mousemove', handleMouseMove);
    window.addEventListener('mouseup', handleMouseUp);
    
    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseup', handleMouseUp);
    };
  }, [handleMouseMove, handleMouseUp]);

  const handleSendMessage = async () => {
    if (!inputValue.trim()) return;

    // Add user message to chat
    const userMessage: Message = {
      id: Date.now().toString(),
      content: inputValue,
      sender: 'user',
      timestamp: new Date(),
    };
    
    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsLoading(true);

    try {
      // Call the assistant API
      const response = await apiRequest('POST', '/api/ai/assistant-chat', {
        message: inputValue,
        history: messages.map(msg => ({
          role: msg.sender === 'user' ? 'user' : 'assistant',
          content: msg.content
        }))
      });

      const data = await response.json();

      if (response.ok && data.response) {
        // Add assistant response to chat
        const assistantMessage: Message = {
          id: (Date.now() + 1).toString(),
          content: data.response,
          sender: 'assistant',
          timestamp: new Date(),
        };
        
        setMessages(prev => [...prev, assistantMessage]);
      } else {
        throw new Error(data.message || 'Failed to get a response');
      }
    } catch (error) {
      console.error('Error getting assistant response:', error);
      toast({
        title: 'Error',
        description: 'Unable to process your request. Please try again.',
        variant: 'destructive',
      });
      
      // Add error message to chat
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: "I'm sorry, I'm having trouble processing your request. Please try again later.",
        sender: 'assistant',
        timestamp: new Date(),
      };
      
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleClearChat = () => {
    const welcomeMessage: Message = {
      id: Date.now().toString(),
      content: "Chat history cleared. How can I help you today?",
      sender: 'assistant',
      timestamp: new Date(),
    };
    
    setMessages([welcomeMessage]);
    localStorage.removeItem('chatAssistantMessages');
  };

  return (
    <>
      {/* Floating chat button */}
      <motion.div
        ref={chatBubbleRef}
        style={{ 
          position: 'fixed',
          bottom: position.y === 0 ? '20px' : undefined,
          right: position.x === 0 ? '20px' : undefined,
          top: position.y !== 0 ? position.y + 'px' : undefined,
          left: position.x !== 0 ? position.x + 'px' : undefined,
          zIndex: 50,
          cursor: isDragging ? 'grabbing' : (isOpen ? 'default' : 'grab'),
        }}
        whileHover={{ scale: isOpen ? 1 : 1.1 }}
        onMouseDown={handleMouseDown}
        className="select-none"
      >
        {!isOpen ? (
          <Button
            size="lg"
            onClick={() => setIsOpen(true)}
            className="h-14 w-14 rounded-full shadow-lg"
          >
            <Bot className="h-6 w-6" />
          </Button>
        ) : (
          <AnimatePresence>
            <motion.div
              initial={{ opacity: 0, scale: 0.8, y: 10 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.8, y: 10 }}
              className="w-80 sm:w-96 shadow-xl rounded-xl overflow-hidden"
            >
              <Card className="border-0">
                <CardHeader className="bg-primary text-primary-foreground py-3 px-4">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-base font-medium flex items-center">
                      <Bot className="h-5 w-5 mr-2" />
                      AI Learning Assistant
                    </CardTitle>
                    <div className="flex items-center gap-1">
                      <Button 
                        onClick={handleClearChat} 
                        variant="ghost" 
                        size="icon" 
                        className="h-7 w-7 text-primary-foreground/80 hover:text-primary-foreground hover:bg-primary-foreground/10"
                      >
                        <RefreshCw className="h-4 w-4" />
                      </Button>
                      <Button 
                        onClick={() => setIsOpen(false)} 
                        variant="ghost" 
                        size="icon" 
                        className="h-7 w-7 text-primary-foreground/80 hover:text-primary-foreground hover:bg-primary-foreground/10"
                      >
                        <Minimize2 className="h-4 w-4" />
                      </Button>
                      <Button 
                        onClick={() => setIsOpen(false)} 
                        variant="ghost" 
                        size="icon" 
                        className="h-7 w-7 text-primary-foreground/80 hover:text-primary-foreground hover:bg-primary-foreground/10"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="px-0 pt-0 pb-0">
                  <ScrollArea className="h-64 px-4 py-3">
                    <div className="space-y-4">
                      {messages.map((message) => (
                        <div
                          key={message.id}
                          className={cn(
                            "flex items-start gap-2",
                            message.sender === 'user' ? "flex-row-reverse" : "flex-row"
                          )}
                        >
                          <Avatar className={cn("h-8 w-8", message.sender === 'user' ? "bg-muted" : "bg-primary")}>
                            {message.sender === 'user' ? (
                              <AvatarFallback>U</AvatarFallback>
                            ) : (
                              <AvatarFallback className="text-primary-foreground">AI</AvatarFallback>
                            )}
                          </Avatar>
                          <div
                            className={cn(
                              "rounded-lg px-3 py-2 max-w-[75%] text-sm",
                              message.sender === 'user'
                                ? "bg-primary text-primary-foreground"
                                : "bg-muted text-muted-foreground"
                            )}
                          >
                            <p className="whitespace-pre-wrap">{message.content}</p>
                          </div>
                        </div>
                      ))}
                      {isLoading && (
                        <div className="flex items-center gap-2">
                          <Avatar className="h-8 w-8 bg-primary">
                            <AvatarFallback className="text-primary-foreground">AI</AvatarFallback>
                          </Avatar>
                          <div className="rounded-lg px-3 py-2 max-w-[75%] bg-muted text-muted-foreground">
                            <Loader2 className="h-4 w-4 animate-spin" />
                          </div>
                        </div>
                      )}
                      <div ref={messagesEndRef} />
                    </div>
                  </ScrollArea>
                </CardContent>
                <CardFooter className="p-3 border-t">
                  <div className="relative w-full flex items-center">
                    <Textarea
                      placeholder="Type your message..."
                      value={inputValue}
                      onChange={(e) => setInputValue(e.target.value)}
                      onKeyDown={handleInputKeyDown}
                      className="min-h-9 max-h-32 pr-10 py-2 resize-none"
                      disabled={isLoading}
                    />
                    <Button
                      size="icon"
                      className="absolute right-1 top-1/2 -translate-y-1/2 h-7 w-7"
                      onClick={handleSendMessage}
                      disabled={!inputValue.trim() || isLoading}
                    >
                      <Send className="h-4 w-4" />
                    </Button>
                  </div>
                </CardFooter>
              </Card>
            </motion.div>
          </AnimatePresence>
        )}
      </motion.div>
    </>
  );
}