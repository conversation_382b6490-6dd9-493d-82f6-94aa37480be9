import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useUser } from '@/hooks/use-user';
// Remove MainLayout import since it's already provided in app-content.tsx

import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter,
} from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
// MainLayout is provided by app-content.tsx
import { ColorfulIcon } from '@/components/ui/colorful-icon';
import { Search, BookOpen, Layers, GraduationCap, BookMarked, LifeBuoy, School, Shield, Briefcase, CheckCircle2, ArrowRight } from 'lucide-react';

// Define the form schema
const connectFormSchema = z.object({
  apiKey: z.string().min(1, { message: 'API Key is required' }),
  apiSecret: z.string().optional(),
  subdomain: z.string().optional(),
  instanceUrl: z.string().url({ message: 'Must be a valid URL' }).optional(),
});

type ConnectFormValues = z.infer<typeof connectFormSchema>;

// Mock LMS platforms data
const mockLmsPlatforms = [
  {
    id: 1,
    name: 'Moodle',
    slug: 'moodle',
    description: 'Open-source learning platform designed to provide educators, administrators and learners with a robust and secure system.',
    category: 'lms',
    iconUrl: 'moodle',
    website: 'https://moodle.org/',
    apiDocsUrl: 'https://docs.moodle.org/dev/Web_service_API_functions',
    features: ['Open Source', 'Customizable', 'Mobile-Friendly', 'SCORM Compliant'],
  },
  {
    id: 2,
    name: 'Canvas',
    slug: 'canvas',
    description: 'A modern, easy-to-use LMS that connects all the digital tools and resources teachers use into one place.',
    category: 'lms',
    iconUrl: 'canvas',
    website: 'https://www.instructure.com/canvas/',
    apiDocsUrl: 'https://canvas.instructure.com/doc/api/',
    features: ['Cloud-Based', 'Mobile Apps', 'Analytics', 'Integrated Tools'],
  },
  {
    id: 3,
    name: 'Blackboard',
    slug: 'blackboard',
    description: 'A virtual learning environment and learning management system developed by Blackboard Inc.',
    category: 'lms',
    iconUrl: 'blackboard',
    website: 'https://www.blackboard.com/',
    apiDocsUrl: 'https://developer.blackboard.com/',
    features: ['Assessment Tools', 'Content Management', 'Collaboration Tools'],
  },
  {
    id: 4,
    name: 'Teachable',
    slug: 'teachable',
    description: 'An online learning platform that allows instructors to create and sell courses on their own branded websites.',
    category: 'lms',
    iconUrl: 'teachable',
    website: 'https://teachable.com/',
    apiDocsUrl: 'https://teachable.com/blog/teachable-api',
    features: ['Course Creator', 'Sales Pages', 'Payment Processing'],
  },
  {
    id: 5,
    name: 'Thinkific',
    slug: 'thinkific',
    description: 'A software platform that enables entrepreneurs to create, market, sell, and deliver their own online courses.',
    category: 'lms',
    iconUrl: 'thinkific',
    website: 'https://www.thinkific.com/',
    apiDocsUrl: 'https://developers.thinkific.com/api/api-documentation/',
    features: ['Course Builder', 'Marketing Tools', 'Student Progress Tracking'],
  },
];

// Define Integration type
interface Integration {
  id: number;
  userId: number;
  platformId: number;
  apiKey: string;
  apiSecret?: string;
  settings: {
    subdomain?: string;
    instanceUrl?: string;
  };
  connectedAt: string;
  updatedAt: string;
}

// Mock integrations data
const mockIntegrations: Integration[] = [
  // Empty initially
];

export default function LMSPlatformsPage() {
  const { user } = useUser();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState('all');
  const [connectingPlatform, setConnectingPlatform] = useState<any>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [integrations, setIntegrations] = useState(mockIntegrations);

  const form = useForm<ConnectFormValues>({
    resolver: zodResolver(connectFormSchema),
    defaultValues: {
      apiKey: '',
      apiSecret: '',
      subdomain: '',
      instanceUrl: '',
    },
  });

  // Filter platforms based on search and active tab
  const filteredPlatforms = mockLmsPlatforms.filter(platform => {
    const matchesSearch = 
      platform.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      platform.description.toLowerCase().includes(searchTerm.toLowerCase());
    
    const isConnected = integrations.some(i => i.platformId === platform.id);
    
    if (activeTab === 'all') return matchesSearch;
    if (activeTab === 'connected') return matchesSearch && isConnected;
    return matchesSearch;
  });

  // Handle connect platform
  const handleConnectPlatform = (platform: any, data: ConnectFormValues) => {
    // In a real app, this would be an API call
    const newIntegration = {
      id: Date.now(),
      userId: user?.id || 0,
      platformId: platform.id,
      apiKey: data.apiKey,
      apiSecret: data.apiSecret,
      settings: {
        subdomain: data.subdomain,
        instanceUrl: data.instanceUrl,
      },
      connectedAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    
    setIntegrations([...integrations, newIntegration]);
    setIsDialogOpen(false);
    form.reset();
    
    toast({
      title: 'Platform Connected',
      description: `Successfully connected to ${platform.name}.`,
    });
  };
  
  // Handle disconnect platform
  const handleDisconnectPlatform = (platformId: number) => {
    setIntegrations(integrations.filter(i => i.platformId !== platformId));
    
    toast({
      title: 'Platform Disconnected',
      description: 'Successfully disconnected from the platform.',
    });
  };

  const openConnectDialog = (platform: any) => {
    setConnectingPlatform(platform);
    setIsDialogOpen(true);
    form.reset();
  };

  const getIconForPlatform = (slug: string) => {
    switch (slug) {
      case 'moodle': return BookOpen;
      case 'canvas': return Layers;
      case 'blackboard': return GraduationCap;
      case 'teachable': return BookMarked;
      case 'thinkific': return School;
      default: return BookOpen;
    }
  };

  return (
    <div className="container mx-auto py-6 max-w-6xl">
      <div className="flex flex-col space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">LMS Platforms</h1>
            <p className="text-gray-500 mt-1">
              Connect your learning management systems to distribute your courses
            </p>
          </div>
        </div>
          
          {/* Search and filter controls */}
          <div className="flex flex-col space-y-4 sm:flex-row sm:space-y-0 sm:space-x-4 sm:items-center justify-between">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                type="search"
                placeholder="Search LMS platforms..."
                className="pl-10 w-full sm:w-[300px]"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full sm:w-auto">
              <TabsList>
                <TabsTrigger value="all">All Platforms</TabsTrigger>
                <TabsTrigger value="connected">Connected</TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
          
          {/* Platforms grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredPlatforms.map((platform) => {
              const isConnected = integrations.some(i => i.platformId === platform.id);
              const Icon = getIconForPlatform(platform.slug);
              
              return (
                <Card key={platform.id} className={isConnected ? "border-primary shadow-md" : ""}>
                  <CardHeader className="pb-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="rounded-md bg-primary/10 p-2">
                          <ColorfulIcon icon={Icon} category="media" size={20} />
                        </div>
                        <div>
                          <CardTitle className="text-lg font-semibold">{platform.name}</CardTitle>
                          {isConnected && (
                            <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                              Connected
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="pb-4">
                    <p className="text-sm text-gray-600 mb-4">{platform.description}</p>
                    <div className="flex flex-wrap gap-2 mb-4">
                      {platform.features.map((feature, index) => (
                        <Badge key={index} variant="secondary" className="bg-slate-100 text-slate-800">
                          {feature}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                  <CardFooter className="bg-slate-50 pt-3 pb-3 flex justify-between">
                    <a
                      href={platform.website}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-xs text-blue-600 hover:underline"
                    >
                      Visit Website
                    </a>
                    <a
                      href={platform.apiDocsUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-xs text-blue-600 hover:underline"
                    >
                      API Documentation
                    </a>
                    
                    {isConnected ? (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDisconnectPlatform(platform.id)}
                        className="ml-auto border-red-300 hover:bg-red-50 hover:text-red-600 text-red-500"
                      >
                        Disconnect
                      </Button>
                    ) : (
                      <Button
                        size="sm"
                        className="ml-auto"
                        onClick={() => openConnectDialog(platform)}
                      >
                        Connect
                      </Button>
                    )}
                  </CardFooter>
                </Card>
              );
            })}
          </div>
          
          {filteredPlatforms.length === 0 && (
            <div className="rounded-lg bg-slate-50 p-12 text-center">
              <Search className="mx-auto h-12 w-12 text-slate-400" />
              <h3 className="mt-4 text-lg font-semibold text-slate-900">No matching platforms</h3>
              <p className="mt-2 text-slate-600">
                No LMS platforms match your search criteria. Try a different search term or filter.
              </p>
              <Button
                variant="outline"
                className="mt-4"
                onClick={() => {
                  setSearchTerm('');
                  setActiveTab('all');
                }}
              >
                Clear Filters
              </Button>
            </div>
          )}
          
          {/* Connected platforms section */}
          {activeTab === 'connected' && integrations.length > 0 && (
            <div className="mt-8">
              <h2 className="text-xl font-semibold mb-4">How to Connect Your Course</h2>
              
              <Card>
                <CardHeader>
                  <CardTitle>Publish Your Course to LMS</CardTitle>
                  <CardDescription>
                    Follow these steps to integrate your courses with connected learning management systems.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    <div className="flex">
                      <div className="mt-1 mr-4 flex h-8 w-8 items-center justify-center rounded-full border border-primary bg-primary/10 text-primary">1</div>
                      <div>
                        <h3 className="text-base font-medium">Create and Finalize Your Course</h3>
                        <p className="text-sm text-slate-600 mt-1">
                          Ensure your course is complete with all lessons, quizzes, and resources ready before publishing.
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex">
                      <div className="mt-1 mr-4 flex h-8 w-8 items-center justify-center rounded-full border border-primary bg-primary/10 text-primary">2</div>
                      <div>
                        <h3 className="text-base font-medium">Configure Export Settings</h3>
                        <p className="text-sm text-slate-600 mt-1">
                          From your course dashboard, select "Export" and choose the connected LMS platform.
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex">
                      <div className="mt-1 mr-4 flex h-8 w-8 items-center justify-center rounded-full border border-primary bg-primary/10 text-primary">3</div>
                      <div>
                        <h3 className="text-base font-medium">Review and Publish</h3>
                        <p className="text-sm text-slate-600 mt-1">
                          Review your course details and click "Publish" to send your course to the connected LMS platform.
                        </p>
                      </div>
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="bg-slate-50 p-4">
                  <Button className="w-full sm:w-auto" onClick={() => window.location.href = '/my-courses'}>
                    Go to My Courses <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </CardFooter>
              </Card>
            </div>
          )}
        </div>
        
        {/* Connect Dialog */}
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                Connect to {connectingPlatform?.name}
                {connectingPlatform && (
                  <ColorfulIcon
                    icon={getIconForPlatform(connectingPlatform.slug)}
                    category="media"
                    size={18}
                  />
                )}
              </DialogTitle>
              <DialogDescription>
                Enter your {connectingPlatform?.name} API credentials to connect your account.
              </DialogDescription>
            </DialogHeader>
            <Form {...form}>
              <form 
                onSubmit={form.handleSubmit(data => {
                  if (connectingPlatform) {
                    handleConnectPlatform(connectingPlatform, data);
                  }
                })} 
                className="space-y-4"
              >
                <FormField
                  control={form.control}
                  name="apiKey"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>API Key</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter your API key" {...field} />
                      </FormControl>
                      <FormDescription>
                        This is your unique identifier for the API.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="apiSecret"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>API Secret (Optional)</FormLabel>
                      <FormControl>
                        <Input
                          type="password"
                          placeholder="Enter your API secret"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Some platforms require an API secret for authentication.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                {connectingPlatform?.slug === 'teachable' && (
                  <FormField
                    control={form.control}
                    name="subdomain"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Subdomain</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="your-school"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          Your Teachable subdomain (e.g., your-school.teachable.com)
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}
                
                {['moodle', 'canvas', 'blackboard'].includes(connectingPlatform?.slug || '') && (
                  <FormField
                    control={form.control}
                    name="instanceUrl"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Instance URL</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="https://your-school.com/lms"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          The URL of your {connectingPlatform?.name} instance
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}
                
                <DialogFooter className="flex justify-end gap-2 pt-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setIsDialogOpen(false)}
                  >
                    Cancel
                  </Button>
                  <Button type="submit">Connect Platform</Button>
                </DialogFooter>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      </div>
  );
}