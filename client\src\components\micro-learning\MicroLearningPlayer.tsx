import React, { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Slider } from "@/components/ui/slider";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, <PERSON>bs<PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { useToast } from "@/hooks/use-toast";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  PlayCircle,
  PauseCircle,
  SkipForward,
  RotateCcw,
  Volume2,
  VolumeX,
  Settings,
  Check,
  X,
  HelpCircle,
  BookOpen,
  SplitSquareVertical,
  FileQuestion,
  Hourglass,
  Brain,
  Play,
  Pause,
  SkipBack
} from "lucide-react";
import { apiRequest } from "@/lib/queryClient";

interface MicroLearningSegment {
  id: string;
  title: string;
  content: string;
  startTime: number;
  endTime: number;
  completed: boolean;
}

interface KnowledgeCheck {
  id: string;
  question: string;
  options: string[];
  correctAnswer: number;
  completed: boolean;
  userAnswer?: number;
}

interface MicroLearningPlayerProps {
  lessonId: number;
  videoUrl: string;
  transcript?: string;
  segments?: MicroLearningSegment[];
  knowledgeChecks?: KnowledgeCheck[];
  segmentCount?: number;
  breakInterval?: number;
  breakDuration?: number;
  onComplete?: () => void;
}

export function MicroLearningPlayer({
  lessonId,
  videoUrl,
  transcript = "",
  segments = [],
  knowledgeChecks = [],
  segmentCount = 4,
  breakInterval = 300,
  breakDuration = 30,
  onComplete
}: MicroLearningPlayerProps) {
  const videoRef = useRef<HTMLVideoElement>(null);
  const { toast } = useToast();
  
  // State for the player
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(1);
  const [muted, setMuted] = useState(false);
  const [progress, setProgress] = useState(0);
  const [currentSegment, setCurrentSegment] = useState<MicroLearningSegment | null>(null);
  const [showBreak, setShowBreak] = useState(false);
  const [showKnowledgeCheck, setShowKnowledgeCheck] = useState<KnowledgeCheck | null>(null);
  const [breakTimeRemaining, setBreakTimeRemaining] = useState(breakDuration);
  const [breakTimer, setBreakTimer] = useState<NodeJS.Timeout | null>(null);
  const [autoGeneratedSegments, setAutoGeneratedSegments] = useState<MicroLearningSegment[]>(segments);
  const [generatedKnowledgeChecks, setGeneratedKnowledgeChecks] = useState<KnowledgeCheck[]>(knowledgeChecks);
  const [showTranscript, setShowTranscript] = useState(false);
  const [userNotes, setUserNotes] = useState("");
  const [completedSegmentsCount, setCompletedSegmentsCount] = useState(0);
  const [lastPlaybackPosition, setLastPlaybackPosition] = useState(0);
  const [isSavingProgress, setIsSavingProgress] = useState(false);
  const [userProgress, setUserProgress] = useState<{
    id?: number;
    progress: number;
    completed: boolean;
    lastPosition: number;
    completedSegments: string[];
  } | null>(null);
  
  // Fetch user progress when component loads
  useEffect(() => {
    if (lessonId) {
      fetchUserProgress();
    }
  }, [lessonId]);

  // Generate segments if none are provided
  useEffect(() => {
    if (segments.length === 0 && duration > 0) {
      generateSegments();
    }
  }, [duration, segments]);
  
  // Save progress periodically
  useEffect(() => {
    const saveInterval = setInterval(() => {
      if (currentTime > 0 && !showBreak && !showKnowledgeCheck) {
        saveUserProgress();
      }
    }, 10000); // Save every 10 seconds
    
    return () => clearInterval(saveInterval);
  }, [currentTime, autoGeneratedSegments]);
  
  // Load or generate knowledge checks
  useEffect(() => {
    if (lessonId && autoGeneratedSegments.length > 0) {
      fetchKnowledgeChecks();
    }
  }, [lessonId, autoGeneratedSegments]);
  
  // Update current segment based on video playback time
  useEffect(() => {
    if (autoGeneratedSegments.length > 0 && currentTime > 0) {
      const segment = autoGeneratedSegments.find(
        seg => currentTime >= seg.startTime && currentTime < seg.endTime
      );
      
      if (segment && segment.id !== currentSegment?.id) {
        setCurrentSegment(segment);
      }
    }
  }, [currentTime, autoGeneratedSegments]);
  
  // Check if we need to show a break
  useEffect(() => {
    if (isPlaying && currentTime > 0 && Math.floor(currentTime) % breakInterval === 0) {
      pauseVideo();
      setShowBreak(true);
      setBreakTimeRemaining(breakDuration);
      
      const timer = setInterval(() => {
        setBreakTimeRemaining(prev => {
          if (prev <= 1) {
            clearInterval(timer);
            setShowBreak(false);
            
            // Show knowledge check if available and not completed
            const availableChecks = generatedKnowledgeChecks.filter(check => !check.completed);
            if (availableChecks.length > 0) {
              setShowKnowledgeCheck(availableChecks[0]);
            } else {
              playVideo();
            }
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
      
      setBreakTimer(timer);
    }
  }, [currentTime, isPlaying]);
  
  // Cleanup
  useEffect(() => {
    return () => {
      if (breakTimer) {
        clearInterval(breakTimer);
      }
    };
  }, [breakTimer]);
  
  // Video event handlers
  const handleTimeUpdate = () => {
    if (videoRef.current) {
      const time = videoRef.current.currentTime;
      setCurrentTime(time);
      setProgress((time / duration) * 100);
    }
  };
  
  const handleLoadedMetadata = () => {
    if (videoRef.current) {
      setDuration(videoRef.current.duration);
    }
  };
  
  const handleEnded = () => {
    setIsPlaying(false);
    setCurrentTime(0);
    setProgress(0);
    if (videoRef.current) {
      videoRef.current.currentTime = 0;
    }
  };
  
  const playVideo = () => {
    if (videoRef.current) {
      videoRef.current.play()
        .then(() => setIsPlaying(true))
        .catch(error => {
          console.error('Error playing video:', error);
          toast({
            title: "Playback error",
            description: "There was an error playing the video.",
            variant: "destructive"
          });
        });
    }
  };
  
  const pauseVideo = () => {
    if (videoRef.current) {
      videoRef.current.pause();
      setIsPlaying(false);
    }
  };
  
  const togglePlay = () => {
    if (isPlaying) {
      pauseVideo();
    } else {
      playVideo();
    }
  };
  
  const handleVolumeChange = (values: number[]) => {
    const newVolume = values[0];
    setVolume(newVolume);
    if (videoRef.current) {
      videoRef.current.volume = newVolume;
    }
    setMuted(newVolume === 0);
  };
  
  const toggleMute = () => {
    if (videoRef.current) {
      const newMutedState = !muted;
      videoRef.current.muted = newMutedState;
      setMuted(newMutedState);
    }
  };
  
  const handleProgressClick = (e: React.MouseEvent<HTMLDivElement>) => {
    const progressBar = e.currentTarget;
    const clickPosition = (e.clientX - progressBar.getBoundingClientRect().left) / progressBar.offsetWidth;
    const newTime = duration * clickPosition;
    
    if (videoRef.current) {
      videoRef.current.currentTime = newTime;
      setCurrentTime(newTime);
      setProgress((newTime / duration) * 100);
    }
  };
  
  const skipToNextSegment = () => {
    if (currentSegment && videoRef.current) {
      const currentIndex = autoGeneratedSegments.findIndex(seg => seg.id === currentSegment.id);
      if (currentIndex < autoGeneratedSegments.length - 1) {
        const nextSegment = autoGeneratedSegments[currentIndex + 1];
        videoRef.current.currentTime = nextSegment.startTime;
        setCurrentTime(nextSegment.startTime);
        playVideo();
      }
    }
  };
  
  const restartSegment = () => {
    if (currentSegment && videoRef.current) {
      videoRef.current.currentTime = currentSegment.startTime;
      setCurrentTime(currentSegment.startTime);
      playVideo();
    }
  };
  
  // Fetch user progress from the API
  const fetchUserProgress = async () => {
    try {
      const response = await apiRequest(
        "GET",
        `/api/micro-learning/progress/lesson/${lessonId}`
      );
      
      if (response.status === 200) {
        const data = await response.json();
        setUserProgress(data);
        
        // Update UI based on existing progress
        if (data.completedSegments && data.completedSegments.length > 0) {
          setCompletedSegmentsCount(data.completedSegments.length);
          
          // Mark segments as completed
          setAutoGeneratedSegments(prev => 
            prev.map(segment => ({
              ...segment,
              completed: data.completedSegments.includes(segment.id)
            }))
          );
        }
        
        // Resume from last position if available
        if (data.lastPosition && videoRef.current) {
          videoRef.current.currentTime = data.lastPosition;
          setCurrentTime(data.lastPosition);
        }
      }
    } catch (error) {
      console.error("Error fetching progress:", error);
      // New user or no progress yet, that's okay
    }
  };
  
  // Save user progress to the API
  const saveUserProgress = async () => {
    if (isSavingProgress || !lessonId) return;
    
    try {
      setIsSavingProgress(true);
      
      // Get completed segment IDs
      const completedSegmentIds = autoGeneratedSegments
        .filter(segment => segment.completed)
        .map(segment => segment.id);
      
      // Calculate overall progress percentage
      const progressPercentage = Math.min(
        (completedSegmentIds.length / autoGeneratedSegments.length) * 100,
        100
      );
      
      const completed = progressPercentage === 100;
      
      const progressData = {
        lessonId,
        progress: progressPercentage,
        completed,
        lastPosition: currentTime,
        completedSegments: completedSegmentIds,
        ...(completed ? { completedAt: new Date().toISOString() } : {})
      };
      
      const response = await apiRequest(
        "POST",
        `/api/micro-learning/progress`,
        progressData
      );
      
      if (response.status === 200) {
        const data = await response.json();
        setUserProgress(data);
      }
    } catch (error) {
      console.error("Error saving progress:", error);
    } finally {
      setIsSavingProgress(false);
    }
  };
  
  // Fetch knowledge checks from the API
  const fetchKnowledgeChecks = async () => {
    try {
      if (!lessonId) return;
      
      const response = await apiRequest(
        "GET",
        `/api/micro-learning/knowledge-checks/lesson/${lessonId}`
      );
      
      if (response.status === 200) {
        const data = await response.json();
        if (data && data.length > 0) {
          setGeneratedKnowledgeChecks(data);
        } else if (knowledgeChecks && knowledgeChecks.length > 0) {
          // Use provided knowledge checks if no saved ones found
          setGeneratedKnowledgeChecks(knowledgeChecks);
        } else {
          // Auto-generate knowledge checks if none exist
          generateKnowledgeChecks();
        }
      } else {
        // Use provided knowledge checks or auto-generate if API fails
        if (knowledgeChecks && knowledgeChecks.length > 0) {
          setGeneratedKnowledgeChecks(knowledgeChecks);
        } else {
          generateKnowledgeChecks();
        }
      }
    } catch (error) {
      console.error("Error fetching knowledge checks:", error);
      // Fallback to provided knowledge checks or auto-generate
      if (knowledgeChecks && knowledgeChecks.length > 0) {
        setGeneratedKnowledgeChecks(knowledgeChecks);
      } else {
        generateKnowledgeChecks();
      }
    }
  };

  const markSegmentComplete = (segmentId: string) => {
    setAutoGeneratedSegments(prev => 
      prev.map(seg => 
        seg.id === segmentId ? { ...seg, completed: true } : seg
      )
    );
    
    const updatedCount = autoGeneratedSegments.filter(seg => seg.completed).length + 1;
    setCompletedSegmentsCount(updatedCount);
    
    // Save progress when a segment is completed
    saveUserProgress();
    
    // Check if all segments are completed
    if (updatedCount === autoGeneratedSegments.length) {
      toast({
        title: "All segments completed",
        description: "You've completed all segments of this lesson!",
      });
      if (onComplete) {
        onComplete();
      }
    }
  };
  
  // State for knowledge check feedback
  const [isAnswering, setIsAnswering] = useState(true);
  const [showFeedback, setShowFeedback] = useState(false);
  const [selectedAnswer, setSelectedAnswer] = useState<number | null>(null);
  const [answerIsCorrect, setAnswerIsCorrect] = useState(false);

  const handleKnowledgeCheckAnswer = async (answer: number) => {
    if (showKnowledgeCheck) {
      const isCorrect = answer === showKnowledgeCheck.correctAnswer;
      
      // Set states for feedback display
      setSelectedAnswer(answer);
      setAnswerIsCorrect(isCorrect);
      setIsAnswering(false);
      setShowFeedback(true);
      
      // Update knowledge check in state
      setGeneratedKnowledgeChecks(prev => 
        prev.map(check => 
          check.id === showKnowledgeCheck.id 
            ? { ...check, completed: true, userAnswer: answer }
            : check
        )
      );
      
      // Save the answer to the database
      try {
        if (lessonId) {
          // Send both field names for backward compatibility
          await apiRequest(
            "POST",
            `/api/micro-learning/knowledge-check/response`,
            {
              lessonId, 
              knowledgeCheckId: showKnowledgeCheck.id,
              userAnswer: answer,
              correct: isCorrect,
              isCorrect: isCorrect // Include both field names for compatibility
            }
          );
        }
      } catch (error) {
        console.error("Error saving knowledge check response:", error);
      }
      
      // Show a toast notification with the result
      toast({
        title: isCorrect ? "Correct! 🎉" : "Not quite right 🤔",
        description: isCorrect 
          ? "Great job! You're mastering this content." 
          : "That's okay! Learning is a journey - review this section again later.",
        variant: isCorrect ? "default" : "destructive"
      });
      
      // Don't automatically continue - let the user click the Continue button
      // The user can now see the detailed feedback in the dialog and
      // can continue learning at their own pace by clicking the button
      if (!showFeedback) {
        setShowFeedback(true); // Make sure feedback is showing
      }
      
      // Mark the segment as completed in the database
      if (currentSegment) {
        markSegmentComplete(currentSegment.id);
      }
    }
  };
  
  const skipBreak = () => {
    if (breakTimer) {
      clearInterval(breakTimer);
    }
    setShowBreak(false);
    
    // Show knowledge check if available
    const availableChecks = generatedKnowledgeChecks.filter(check => !check.completed);
    if (availableChecks.length > 0) {
      setShowKnowledgeCheck(availableChecks[0]);
    } else {
      playVideo();
    }
  };
  
  // Generate segments automatically based on video duration
  const generateSegments = () => {
    const newSegments: MicroLearningSegment[] = [];
    const segmentDuration = duration / segmentCount;
    
    for (let i = 0; i < segmentCount; i++) {
      const startTime = i * segmentDuration;
      const endTime = (i + 1) * segmentDuration;
      
      newSegments.push({
        id: `segment-${i}`,
        title: `Segment ${i + 1}`,
        content: transcript 
          ? transcript.substring(
              Math.floor(transcript.length * (startTime / duration)),
              Math.floor(transcript.length * (endTime / duration))
            )
          : `Content for segment ${i + 1}`,
        startTime,
        endTime,
        completed: false
      });
    }
    
    setAutoGeneratedSegments(newSegments);
  };
  
  // Generate knowledge checks based on transcript and segments
  const generateKnowledgeChecks = async () => {
    // For now, we'll use stub questions since we don't have AI integration in this component
    // In a real implementation, we would make an API call to generate questions
    try {
      const response = await apiRequest(
        "POST", 
        `/api/ai/generate-quiz`, 
        {
          lessonId,
          count: Math.min(segmentCount, 3), // Generate up to 3 questions
          transcript
        }
      );
      
      const quizData = await response.json();
      
      if (quizData && Array.isArray(quizData.questions)) {
        setGeneratedKnowledgeChecks(quizData.questions.map((q: any, index: number) => ({
          id: `quiz-${index}`,
          question: q.question,
          options: q.options,
          correctAnswer: q.correctAnswerIndex,
          completed: false
        })));
      } else {
        // Fallback with generic questions
        createGenericKnowledgeChecks();
      }
    } catch (error) {
      console.error("Error generating quiz questions:", error);
      // Fallback with generic questions
      createGenericKnowledgeChecks();
    }
  };
  
  const createGenericKnowledgeChecks = () => {
    const genericChecks: KnowledgeCheck[] = [
      {
        id: "quiz-1",
        question: "What is the main topic of this lesson segment?",
        options: [
          "Understanding key concepts",
          "Applying the knowledge",
          "Evaluating the outcomes",
          "All of the above"
        ],
        correctAnswer: 3,
        completed: false
      },
      {
        id: "quiz-2",
        question: "Which of the following best summarizes what you just learned?",
        options: [
          "Theoretical frameworks",
          "Practical applications",
          "Historical context",
          "Future implications"
        ],
        correctAnswer: 1,
        completed: false
      }
    ];
    
    setGeneratedKnowledgeChecks(genericChecks);
  };
  
  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };
  
  return (
    <div className="flex flex-col w-full h-full bg-background">
      <div className="relative w-full h-auto bg-background rounded-md shadow-md">
        {/* Video player */}
        <div className="aspect-video w-full relative">
          <video
            ref={videoRef}
            src={videoUrl}
            className="w-full h-full object-contain bg-black rounded-t-md"
            onTimeUpdate={handleTimeUpdate}
            onLoadedMetadata={handleLoadedMetadata}
            onEnded={handleEnded}
          />
          
          {/* Controls overlay */}
          <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4 text-white">
            {/* Progress bar */}
            <div 
              className="w-full h-2 bg-gray-700 rounded-full mb-3 cursor-pointer"
              onClick={handleProgressClick}
            >
              <div 
                className="h-full bg-primary rounded-full"
                style={{ width: `${progress}%` }}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <button onClick={togglePlay} className="text-white hover:text-primary focus:outline-none">
                  {isPlaying ? <PauseCircle size={24} /> : <PlayCircle size={24} />}
                </button>
                
                <button onClick={restartSegment} className="text-white hover:text-primary focus:outline-none">
                  <RotateCcw size={20} />
                </button>
                
                <button onClick={skipToNextSegment} className="text-white hover:text-primary focus:outline-none">
                  <SkipForward size={20} />
                </button>
                
                <div className="text-xs">
                  {formatTime(currentTime)} / {formatTime(duration)}
                </div>
              </div>
              
              <div className="flex items-center space-x-3">
                <button onClick={toggleMute} className="text-white hover:text-primary focus:outline-none">
                  {muted ? <VolumeX size={20} /> : <Volume2 size={20} />}
                </button>
                
                <div className="w-24">
                  <Slider
                    value={[volume]}
                    min={0}
                    max={1}
                    step={0.1}
                    onValueChange={handleVolumeChange}
                    className="w-full"
                  />
                </div>
                
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-white"
                  onClick={() => setShowTranscript(!showTranscript)}
                >
                  <BookOpen size={18} className="mr-1" />
                  {showTranscript ? "Hide Transcript" : "Show Transcript"}
                </Button>
              </div>
            </div>
          </div>
        </div>
        
        {/* Learning progress bar */}
        <div className="w-full bg-muted p-3">
          <div className="flex justify-between items-center mb-2">
            <h3 className="text-sm font-medium">Learning Progress</h3>
            <Badge variant="outline" className="ml-2">
              <SplitSquareVertical size={14} className="mr-1" />
              {completedSegmentsCount}/{autoGeneratedSegments.length} Segments
            </Badge>
          </div>
          <Progress value={(completedSegmentsCount / autoGeneratedSegments.length) * 100} />
        </div>
        
        {showTranscript && (
          <div className="p-4 border-t border-border mt-3">
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-base font-medium">Transcript</h3>
              <Button variant="ghost" size="sm" onClick={() => setShowTranscript(false)}>
                <X size={18} />
              </Button>
            </div>
            <ScrollArea className="h-40">
              <div className="space-y-4">
                {autoGeneratedSegments.map((segment) => (
                  <div 
                    key={segment.id}
                    className={`p-3 rounded-md ${currentSegment?.id === segment.id ? 'bg-primary/10 border border-primary/30' : 'bg-card'}`}
                  >
                    <div className="flex justify-between">
                      <h4 className="text-sm font-medium mb-1">{segment.title}</h4>
                      <span className="text-xs text-muted-foreground">
                        {formatTime(segment.startTime)} - {formatTime(segment.endTime)}
                      </span>
                    </div>
                    <p className="text-sm">{segment.content}</p>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </div>
        )}
      </div>
      
      {/* Break timer dialog */}
      <Dialog open={showBreak} onOpenChange={(open) => !open && skipBreak()}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader className="pb-2">
            <DialogTitle className="flex items-center text-lg">
              <Hourglass className="h-5 w-5 mr-2 text-primary animate-pulse" />
              Learning Break
            </DialogTitle>
            <DialogDescription>
              Take a short break to absorb what you've learned and refresh your mind.
            </DialogDescription>
          </DialogHeader>
          
          <div className="flex flex-col items-center py-6">
            <div className="relative mb-8">
              <div className="text-5xl font-bold text-center mb-2 font-mono">
                {Math.floor(breakTimeRemaining / 60)}:{String(breakTimeRemaining % 60).padStart(2, '0')}
              </div>
              
              <div className="w-full max-w-xs mx-auto">
                <Progress 
                  value={(breakTimeRemaining / breakDuration) * 100} 
                  className="h-2 mb-2"
                />
                <p className="text-xs text-center text-muted-foreground">
                  Break time remaining
                </p>
              </div>
            </div>
            
            <div className="rounded-lg border bg-card p-4 mb-4 w-full max-w-sm">
              <h3 className="text-sm font-medium mb-3 flex items-center">
                <SplitSquareVertical className="h-4 w-4 mr-2 text-primary" />
                Learning Tip
              </h3>
              
              <div className="space-y-3 text-sm text-card-foreground">
                <div className="flex items-start">
                  <div className="h-5 w-5 rounded-full bg-primary/10 text-primary flex items-center justify-center mr-2 mt-0.5">
                    <span className="text-xs">1</span>
                  </div>
                  <p>Take a few deep breaths and reflect on what you've learned so far</p>
                </div>
                
                <div className="flex items-start">
                  <div className="h-5 w-5 rounded-full bg-primary/10 text-primary flex items-center justify-center mr-2 mt-0.5">
                    <span className="text-xs">2</span>
                  </div>
                  <p>Stand up and stretch to improve blood circulation and focus</p>
                </div>
                
                <div className="flex items-start">
                  <div className="h-5 w-5 rounded-full bg-primary/10 text-primary flex items-center justify-center mr-2 mt-0.5">
                    <span className="text-xs">3</span>
                  </div>
                  <p>Stay hydrated - take a sip of water before continuing</p>
                </div>
              </div>
            </div>
            
            {currentSegment && (
              <div className="w-full">
                <h4 className="text-sm font-medium mb-2">Current Progress</h4>
                <div className="flex items-center justify-between text-sm mb-1">
                  <span>Segment {autoGeneratedSegments.findIndex(s => s.id === currentSegment.id) + 1} of {autoGeneratedSegments.length}</span>
                  <span className="text-muted-foreground text-xs">{formatTime(currentSegment.startTime)} - {formatTime(currentSegment.endTime)}</span>
                </div>
                <Progress value={((completedSegmentsCount) / autoGeneratedSegments.length) * 100} className="h-2" />
              </div>
            )}
          </div>
          
          <DialogFooter className="flex justify-between items-center">
            <div className="text-xs text-muted-foreground">
              Break helps improve retention by up to 30%
            </div>
            <Button 
              onClick={skipBreak}
              className="flex items-center"
            >
              <SkipForward className="h-4 w-4 mr-2" />
              Continue Learning
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Knowledge check dialog */}
      <Dialog open={showKnowledgeCheck !== null} onOpenChange={(open) => !open && setShowKnowledgeCheck(null)}>
        {showKnowledgeCheck && (
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle className="flex items-center text-lg">
                <FileQuestion className="h-5 w-5 mr-2 text-primary" />
                Knowledge Check
              </DialogTitle>
              <DialogDescription className="text-base">
                Let's reinforce what you've learned in this segment.
              </DialogDescription>
            </DialogHeader>
            
            <div className="py-4">
              <div className="bg-muted/40 p-4 rounded-lg mb-5 border border-muted">
                <h3 className="text-base font-medium mb-2">{showKnowledgeCheck.question}</h3>
                <p className="text-xs text-muted-foreground">
                  Select the best answer to continue your learning journey
                </p>
              </div>
              
              <div className="space-y-3">
                {showKnowledgeCheck.options.map((option, index) => {
                  // Determine styling based on answer state
                  let optionStyles = "flex items-center p-3 rounded-md border border-input transition-all duration-200 ";
                  
                  if (showFeedback) {
                    // Show correct/incorrect styling
                    if (index === showKnowledgeCheck.correctAnswer) {
                      // Correct answer
                      optionStyles += "bg-green-50 border-green-200 dark:bg-green-950/20 dark:border-green-800";
                    } else if (index === selectedAnswer && selectedAnswer !== showKnowledgeCheck.correctAnswer) {
                      // Wrong answer selected by user
                      optionStyles += "bg-red-50 border-red-200 dark:bg-red-950/20 dark:border-red-800";
                    } else {
                      // Other options - faded
                      optionStyles += "opacity-50";
                    }
                  } else {
                    // Normal interactive state
                    optionStyles += "cursor-pointer hover:bg-accent/50 hover:border-accent";
                  }
                  
                  return (
                    <div 
                      key={index}
                      className={optionStyles}
                      onClick={() => !showFeedback && handleKnowledgeCheckAnswer(index)}
                    >
                      <div className="flex-shrink-0 mr-3">
                        <div className="h-5 w-5 rounded-full border border-primary flex items-center justify-center">
                          {(showKnowledgeCheck.userAnswer === index || (showFeedback && selectedAnswer === index)) && (
                            <div className="h-3 w-3 rounded-full bg-primary" />
                          )}
                        </div>
                      </div>
                      
                      <div className="flex-grow">
                        <span className="text-sm">{option}</span>
                      </div>
                      
                      {showFeedback && (
                        <div className="flex-shrink-0 ml-2">
                          {index === showKnowledgeCheck.correctAnswer ? (
                            <Check className="h-5 w-5 text-green-500" />
                          ) : (index === selectedAnswer && selectedAnswer !== showKnowledgeCheck.correctAnswer) ? (
                            <X className="h-5 w-5 text-red-500" />
                          ) : null}
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
              
              {showFeedback && (
                <div className={`mt-4 p-3 rounded-md ${answerIsCorrect ? 'bg-green-50 text-green-800 dark:bg-green-950/30 dark:text-green-400' : 'bg-amber-50 text-amber-800 dark:bg-amber-950/30 dark:text-amber-400'}`}>
                  <div className="flex items-start">
                    {answerIsCorrect ? (
                      <>
                        <div className="mr-3 mt-0.5">
                          <Check className="h-5 w-5" />
                        </div>
                        <div>
                          <p className="font-medium">Great job!</p>
                          <p className="text-sm mt-1">You're making good progress. Continue to build your knowledge.</p>
                        </div>
                      </>
                    ) : (
                      <>
                        <div className="mr-3 mt-0.5">
                          <HelpCircle className="h-5 w-5" />
                        </div>
                        <div>
                          <p className="font-medium">Learning opportunity!</p>
                          <p className="text-sm mt-1">The correct answer is: {showKnowledgeCheck.options[showKnowledgeCheck.correctAnswer]}. Consider reviewing this section later.</p>
                        </div>
                      </>
                    )}
                  </div>
                </div>
              )}
            </div>
            
            <DialogFooter className="gap-2 flex-col sm:flex-row sm:justify-between sm:gap-0">
              <div className="text-xs text-muted-foreground">
                {completedSegmentsCount}/{autoGeneratedSegments.length} segments completed
              </div>
              {showFeedback && (
                <Button 
                  size="sm"
                  onClick={() => {
                    setShowFeedback(false);
                    setShowKnowledgeCheck(null);
                    if (currentSegment) {
                      markSegmentComplete(currentSegment.id);
                    }
                    playVideo();
                  }}
                >
                  Continue Learning
                </Button>
              )}
            </DialogFooter>
          </DialogContent>
        )}
      </Dialog>
    </div>
  );
}