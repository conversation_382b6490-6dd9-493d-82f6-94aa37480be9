#!/usr/bin/env python3
"""
Deploy Modal A100 GPU Application and Get Public API URL
This script deploys the Modal app and returns the public endpoints for Replit integration
"""

import subprocess
import sys
import os
import time
import requests
import json

def check_modal_auth():
    """Check if <PERSON><PERSON> is authenticated"""
    try:
        result = subprocess.run(['modal', 'token', 'current'], 
                              capture_output=True, text=True, check=True)
        print("✓ Modal authentication verified")
        return True
    except subprocess.CalledProcessError:
        print("✗ Modal not authenticated")
        return False

def setup_modal_auth():
    """Setup Modal authentication using environment variables"""
    token_id = os.getenv('MODAL_TOKEN_ID')
    token_secret = os.getenv('MODAL_TOKEN_SECRET')
    
    if not token_id or not token_secret:
        print("✗ MODAL_TOKEN_ID or MODAL_TOKEN_SECRET not found in environment")
        return False
    
    try:
        # Set Modal token using environment variables
        result = subprocess.run([
            'modal', 'token', 'set',
            '--token-id', token_id,
            '--token-secret', token_secret
        ], capture_output=True, text=True, check=True)
        
        print("✓ Modal authentication configured")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Failed to configure Modal auth: {e.stderr}")
        return False

def deploy_modal_app():
    """Deploy the Modal A100 GPU application"""
    try:
        print("🚀 Deploying Modal A100 GPU application...")
        
        # Deploy the app
        result = subprocess.run([
            'modal', 'deploy', 'modal_a100_production_app.py'
        ], capture_output=True, text=True, check=True)
        
        print("✓ Modal app deployed successfully")
        print(f"Deployment output:\n{result.stdout}")
        
        # Get app info to find endpoints
        app_info = subprocess.run([
            'modal', 'app', 'list'
        ], capture_output=True, text=True, check=True)
        
        print(f"App info:\n{app_info.stdout}")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"✗ Deployment failed: {e.stderr}")
        return False

def get_app_urls():
    """Get the public API URLs for the deployed app"""
    try:
        # Get app details including URLs
        result = subprocess.run([
            'modal', 'app', 'show', 'courseai-a100-gpu'
        ], capture_output=True, text=True, check=True)
        
        output = result.stdout
        print(f"App details:\n{output}")
        
        # Extract URLs from output
        urls = {}
        lines = output.split('\n')
        
        for line in lines:
            if 'https://' in line and 'modal.run' in line:
                if 'health' in line:
                    urls['health'] = line.strip().split()[-1]
                elif 'api_echo' in line:
                    urls['echo'] = line.strip().split()[-1]
                elif 'api_generate_image' in line:
                    urls['generate_image'] = line.strip().split()[-1]
                elif 'api_analyze_text' in line:
                    urls['analyze_text'] = line.strip().split()[-1]
        
        return urls
        
    except subprocess.CalledProcessError as e:
        print(f"✗ Failed to get app URLs: {e.stderr}")
        return {}

def test_endpoints(urls):
    """Test the deployed endpoints"""
    if not urls:
        print("✗ No URLs found to test")
        return False
    
    print("\n🧪 Testing deployed endpoints...")
    
    # Test health endpoint
    if 'health' in urls:
        try:
            response = requests.get(urls['health'], timeout=30)
            if response.status_code == 200:
                print(f"✓ Health endpoint working: {urls['health']}")
                print(f"Response: {response.json()}")
            else:
                print(f"✗ Health endpoint failed: {response.status_code}")
        except Exception as e:
            print(f"✗ Health endpoint error: {e}")
    
    # Test echo endpoint
    if 'echo' in urls:
        try:
            test_data = {"message": "Hello from Replit!"}
            response = requests.post(urls['echo'], json=test_data, timeout=30)
            if response.status_code == 200:
                print(f"✓ Echo endpoint working: {urls['echo']}")
                print(f"Response: {response.json()}")
            else:
                print(f"✗ Echo endpoint failed: {response.status_code}")
        except Exception as e:
            print(f"✗ Echo endpoint error: {e}")
    
    return True

def main():
    """Main deployment function"""
    print("=== Modal A100 GPU Deployment ===\n")
    
    # Check and setup authentication
    if not check_modal_auth():
        if not setup_modal_auth():
            print("❌ Cannot proceed without Modal authentication")
            return False
    
    # Deploy the application
    if not deploy_modal_app():
        print("❌ Deployment failed")
        return False
    
    # Wait a moment for deployment to propagate
    print("⏳ Waiting for deployment to propagate...")
    time.sleep(10)
    
    # Get endpoint URLs
    urls = get_app_urls()
    if urls:
        print("\n📡 Public API Endpoints:")
        for name, url in urls.items():
            print(f"  {name}: {url}")
        
        # Save URLs to file for Replit integration
        with open('modal_endpoints.json', 'w') as f:
            json.dump(urls, f, indent=2)
        print("✓ Endpoints saved to modal_endpoints.json")
        
        # Test endpoints
        test_endpoints(urls)
        
        print("\n🎉 Deployment completed successfully!")
        print("Your Modal A100 GPU backend is ready for Replit integration.")
        return True
    else:
        print("❌ Failed to get endpoint URLs")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)