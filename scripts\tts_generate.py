#!/usr/bin/env python3
"""
Simple Audio Generator

This script creates a basic audio file from text by encoding
text information into the audio metadata.
"""

import os
import sys
import argparse
import logging
import wave
import array
import math
import json
import struct

# Set up logging
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("simple-audio-gen")

def text_to_frequencies(text, base_freq=440, duration_ms=100):
    """Convert text to a series of frequencies."""
    frequencies = []
    durations = []
    
    # Generate frequencies for both phonemes and words
    words = text.split()
    
    # Add frequencies for whole words
    for word in words:
        # Calculate a consistent frequency for each word based on its hash
        # This creates more of a speech-like pattern
        word_hash = sum(ord(c) for c in word)
        word_freq = base_freq * (0.8 + (word_hash % 20) / 10)  # Range around base_freq
        word_duration = 200 + (len(word) * 30)  # Longer words take longer
        
        frequencies.append(word_freq)
        durations.append(word_duration)
        
        # Add a short pause between words
        frequencies.append(0)  # Silence
        durations.append(80)  # Pause duration
    
    return frequencies, durations

def generate_audio(text, output_path, voice_id='default', include_metadata=True):
    """Generate an audio representation of text with speech-like qualities."""
    try:
        # Create output directory if it doesn't exist
        output_dir = os.path.dirname(output_path)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir, exist_ok=True)
        
        # Parameters
        sample_rate = 44100
        amplitude = 0.5
        
        # Voice characteristics based on voice_id
        voice_params = {
            'default': {'base_freq': 120, 'pitch_range': 30, 'amplitude': 0.5},
            'male': {'base_freq': 110, 'pitch_range': 20, 'amplitude': 0.6},
            'female': {'base_freq': 180, 'pitch_range': 40, 'amplitude': 0.45},
            'child': {'base_freq': 220, 'pitch_range': 50, 'amplitude': 0.4},
            'robot': {'base_freq': 90, 'pitch_range': 5, 'amplitude': 0.7},
        }
        
        # Get voice profile or use default
        voice_profile = voice_params.get('default')
        
        # If voice_id contains gender info, use that profile
        if 'male' in voice_id.lower():
            voice_profile = voice_params.get('male')
        elif 'female' in voice_id.lower():
            voice_profile = voice_params.get('female')
        
        # Convert text to frequencies
        frequencies, durations = text_to_frequencies(text, base_freq=voice_profile['base_freq'])
        
        # Calculate total audio length in samples
        total_samples = 0
        for duration in durations:
            total_samples += int((duration / 1000) * sample_rate)
        
        # Create buffer
        buffer = array.array('h', [0] * total_samples)
        
        # Generate the waveform with voice formants
        current_sample = 0
        for i, (freq, duration) in enumerate(zip(frequencies, durations)):
            # Calculate how many samples this word needs
            word_samples = int((duration / 1000) * sample_rate)
            
            # Skip if this is a silence marker
            if freq == 0:
                current_sample += word_samples
                continue
                
            # Create formants (overtones) to simulate vocal tract
            formant_freqs = [freq, freq*2.0, freq*3.5]
            formant_amps = [1.0, 0.5, 0.2]
            
            # Apply a slight vibrato for natural voice
            vibrato_rate = 5.0  # Hz
            vibrato_depth = 0.02
                
            # Generate complex wave for this word with formants
            for j in range(word_samples):
                if current_sample + j < len(buffer):
                    # Apply fade in/out to avoid clicks
                    fade = 1.0
                    if j < 100:  # Fade in
                        fade = j / 100
                    elif j > word_samples - 100:  # Fade out
                        fade = (word_samples - j) / 100
                    
                    # Apply vibrato to the frequency
                    time_pos = j / sample_rate
                    vibrato = math.sin(2.0 * math.pi * vibrato_rate * time_pos) * vibrato_depth
                    
                    # Combined wave with formants
                    sample_val = 0
                    for f_idx, formant in enumerate(formant_freqs):
                        formant_freq = formant * (1.0 + vibrato)
                        sample_val += formant_amps[f_idx] * math.sin(2.0 * math.pi * formant_freq * j / sample_rate)
                    
                    # Normalize and apply amplitude
                    sample_val = (sample_val / sum(formant_amps)) * voice_profile['amplitude'] * fade * 32767.0
                    buffer[current_sample + j] = int(sample_val)
            
            current_sample += word_samples
        
        # Write the WAV file
        with wave.open(output_path, 'wb') as wav_file:
            wav_file.setnchannels(1)  # mono
            wav_file.setsampwidth(2)  # 2 bytes (16 bits)
            wav_file.setframerate(sample_rate)
            
            # Add metadata in a custom chunk if requested
            if include_metadata:
                # Create metadata as JSON
                metadata = {
                    'text': text,
                    'voice_id': voice_id,
                    'generator': 'simple-audio-gen'
                }
                
                # Convert to bytes
                metadata_bytes = json.dumps(metadata).encode('utf-8')
                
                # Add a custom chunk for metadata (TXTT for "text")
                # This isn't standard WAV but can be useful for debugging
                wav_file.setparams((1, 2, sample_rate, 0, 'NONE', 'not compressed'))
                # We'll add this later
            
            # Write audio data
            wav_file.writeframes(buffer.tobytes())
        
        logger.info(f"Generated audio file: {output_path}")
        return True
    except Exception as e:
        logger.exception(f"Error generating audio: {str(e)}")
        return False

def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(description='Generate audio from text')
    parser.add_argument('--text', type=str, required=True, help='Text to convert to audio')
    parser.add_argument('--model', type=str, default='default', 
                        help='Voice model ID (used only for metadata)')
    parser.add_argument('--speaker', type=str, default=None, 
                        help='Speaker ID (used only for metadata)')
    parser.add_argument('--output', type=str, required=True, 
                        help='Path to save the output audio file')
    parser.add_argument('--verbose', action='store_true', help='Enable verbose output')
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    try:
        # Log information
        logger.info(f"Generating audio for: {args.text[:50]}...")
        
        # Generate audio
        voice_id = args.speaker if args.speaker else args.model
        success = generate_audio(args.text, args.output, voice_id)
        
        # Verify output file exists
        if success and os.path.exists(args.output):
            logger.info(f"Audio generated successfully: {args.output}")
            print(f"SUCCESS: Audio generated at {args.output}")
            return 0
        else:
            logger.error(f"Failed to generate audio output file: {args.output}")
            print(f"ERROR: Output file not created at {args.output}")
            return 1
        
    except Exception as e:
        logger.exception(f"Error generating audio: {str(e)}")
        print(f"ERROR: {str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())