import { Router } from 'express';

const router = Router();

// Placeholder image endpoint
router.get('/placeholder/:dimensions', (req, res) => {
  const { dimensions } = req.params;
  const [width, height] = dimensions.split('x').map(Number);
  
  if (!width || !height || width > 2000 || height > 2000) {
    return res.status(400).json({ error: 'Invalid dimensions' });
  }
  
  // Generate a simple SVG placeholder
  const svg = `
    <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
      <rect width="100%" height="100%" fill="#f3f4f6"/>
      <text x="50%" y="50%" text-anchor="middle" dy=".3em" font-family="Arial, sans-serif" font-size="16" fill="#6b7280">
        ${width} × ${height}
      </text>
    </svg>
  `;
  
  res.setHeader('Content-Type', 'image/svg+xml');
  res.setHeader('Cache-Control', 'public, max-age=3600');
  res.send(svg);
});

// Placeholder audio endpoint
router.get('/audio.mp3', (req, res) => {
  res.setHeader('Content-Type', 'audio/mpeg');
  res.setHeader('Content-Length', '1024');
  res.setHeader('Cache-Control', 'public, max-age=3600');
  res.status(200).send(Buffer.alloc(1024));
});

export default router;