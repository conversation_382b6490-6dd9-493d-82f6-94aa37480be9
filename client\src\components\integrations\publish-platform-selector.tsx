import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { 
  CheckCircle2, 
  Link2, 
  Unlink, 
  Loader2,
  AlertCircle
} from 'lucide-react';
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { PlatformIcon } from './platform-logos';
import { PlatformIntegration } from '@/types/platform-integration';
import { defaultPlatforms } from '@/lib/platform-utils';
import { Link } from 'wouter';

interface PublishPlatformSelectorProps {
  selectedPlatforms: number[];
  onSelectPlatform: (platformId: number, selected: boolean) => void;
  disabled?: boolean;
}

export default function PublishPlatformSelector({
  selectedPlatforms,
  onSelectPlatform,
  disabled = false
}: PublishPlatformSelectorProps) {
  const {
    data: platforms,
    isLoading,
    isError,
  } = useQuery({
    queryKey: ['/api/platform-integrations'],
    retry: 1,
  });

  // Use default platforms when the API returns nothing
  const availablePlatforms: PlatformIntegration[] = (
    platforms && Array.isArray(platforms) && platforms.length > 0
  ) ? platforms : defaultPlatforms;
  
  // Filter only connected platforms
  const connectedPlatforms = availablePlatforms.filter(
    platform => platform.isConnected
  );

  // Education platforms that directly appear on the publish screen
  const eduPlatforms = availablePlatforms.filter(
    platform => platform.category === 'education'
  );
  
  if (isLoading) {
    return (
      <div className="space-y-3">
        <Skeleton className="h-[60px] w-full" />
        <Skeleton className="h-[60px] w-full" />
        <Skeleton className="h-[60px] w-full" />
      </div>
    );
  }

  if (isError) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          Failed to load platforms. Please try again.
        </AlertDescription>
      </Alert>
    );
  }

  // If no connected platforms
  if (connectedPlatforms.length === 0) {
    return (
      <Alert className="bg-muted">
        <AlertDescription className="flex flex-col gap-3">
          <p>No connected platforms. Connect platforms to publish your course.</p>
          <Button 
            size="sm" 
            variant="outline" 
            className="self-start"
            asChild
          >
            <Link to="/platform-connections">
              <Link2 className="h-4 w-4 mr-2" /> Connect Platforms
            </Link>
          </Button>
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-3">
      {eduPlatforms.map(platform => (
        <Card 
          key={platform.id} 
          className={`hover:shadow-sm transition-shadow border ${
            selectedPlatforms.includes(platform.id) 
              ? 'border-primary/30 bg-primary/5' 
              : 'border-border'
          }`}
        >
          <CardContent className="p-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <Checkbox
                  id={`platform-${platform.id}`}
                  checked={selectedPlatforms.includes(platform.id)}
                  onCheckedChange={(checked) => {
                    onSelectPlatform(platform.id, checked as boolean);
                  }}
                  disabled={disabled || !platform.isConnected}
                />
                <div className="flex items-center gap-2">
                  <div className="flex items-center justify-center bg-muted w-8 h-8 rounded">
                    <PlatformIcon platform={platform.icon} size={18} />
                  </div>
                  <label 
                    htmlFor={`platform-${platform.id}`}
                    className="text-sm font-medium cursor-pointer"
                  >
                    {platform.name}
                  </label>
                </div>
              </div>
              
              {platform.isConnected ? (
                <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                  <CheckCircle2 className="h-3 w-3 mr-1" /> Connected
                </Badge>
              ) : (
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="text-xs" 
                  asChild
                >
                  <Link to="/platform-connections">
                    <Link2 className="h-3 w-3 mr-1" /> Connect
                  </Link>
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      ))}
      
      {/* If there are other connected platforms not shown above */}
      {connectedPlatforms.length > eduPlatforms.filter(p => p.isConnected).length && (
        <Button 
          variant="outline" 
          size="sm" 
          className="w-full mt-2" 
          asChild
        >
          <Link to="/platform-connections">
            View All Connected Platforms ({connectedPlatforms.length})
          </Link>
        </Button>
      )}
    </div>
  );
}