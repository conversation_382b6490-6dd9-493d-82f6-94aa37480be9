import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { useMutation, useQuery } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import { 
  Plus, 
  Trash2, 
  Edit, 
  Brain, 
  Play, 
  Save, 
  Copy,
  HelpCircle,
  CheckCircle,
  XCircle,
  Clock,
  Target,
  Shuffle,
  <PERSON>,
  RotateCcw
} from 'lucide-react';

interface Question {
  id: string;
  type: 'multiple-choice' | 'true-false' | 'short-answer' | 'essay';
  question: string;
  options?: string[];
  correctAnswer: string | number;
  explanation?: string;
  difficulty: 'easy' | 'medium' | 'hard';
  points: number;
  timeLimit?: number; // in seconds
}

interface Quiz {
  id: string;
  title: string;
  description: string;
  moduleId?: string;
  lessonId?: string;
  questions: Question[];
  settings: {
    timeLimit: number; // total time in minutes
    passingScore: number; // percentage
    allowRetakes: boolean;
    shuffleQuestions: boolean;
    showCorrectAnswers: boolean;
    showExplanations: boolean;
  };
}

interface QuizBuilderProps {
  moduleId?: string;
  lessonId?: string;
  courseId?: string;
  courseStructure?: any;
  courseScripts?: any;
  moduleLessons?: any[];
  onQuizCreated?: (quiz: Quiz) => void;
  onQuizUpdated?: (quiz: Quiz) => void;
}

export function QuizBuilder({ 
  moduleId, 
  lessonId,
  courseId,
  courseStructure, 
  courseScripts,
  moduleLessons,
  onQuizCreated,
  onQuizUpdated 
}: QuizBuilderProps) {
  const { toast } = useToast();
  const [currentQuiz, setCurrentQuiz] = useState<Quiz>({
    id: `quiz-${Date.now()}`,
    title: '',
    description: '',
    moduleId,
    lessonId,
    questions: [],
    settings: {
      timeLimit: 15,
      passingScore: 70,
      allowRetakes: true,
      shuffleQuestions: false,
      showCorrectAnswers: true,
      showExplanations: true,
    }
  });

  const [selectedQuestion, setSelectedQuestion] = useState<Question | null>(null);
  const [isPreviewMode, setIsPreviewMode] = useState(false);
  const [currentPreviewIndex, setCurrentPreviewIndex] = useState(0);
  const [previewAnswers, setPreviewAnswers] = useState<Record<string, any>>({});

  // AI Question Generation
  const generateQuestionsMutation = useMutation({
    mutationFn: async (data: {
      content: string;
      questionCount: number;
      difficulty: string;
      questionTypes: string[];
    }) => {
      const response = await apiRequest('POST', '/api/ai/generate-quiz-questions', data);
      if (!response.ok) {
        throw new Error('Failed to generate questions');
      }
      return response.json();
    },
    onSuccess: (data) => {
      const newQuestions: Question[] = data.questions.map((q: any, index: number) => ({
        id: `question-${Date.now()}-${index}`,
        type: q.type,
        question: q.question,
        options: q.options,
        correctAnswer: q.correctAnswer,
        explanation: q.explanation,
        difficulty: q.difficulty,
        points: getDifficultyPoints(q.difficulty),
        timeLimit: getDifficultyTimeLimit(q.difficulty),
      }));

      setCurrentQuiz(prev => ({
        ...prev,
        questions: [...prev.questions, ...newQuestions]
      }));

      toast({
        title: "Questions Generated Successfully",
        description: `Added ${newQuestions.length} new questions to your quiz`,
      });
    },
    onError: (error) => {
      console.error('Error generating questions:', error);
      toast({
        title: "Generation Failed",
        description: "Failed to generate questions. Please try again.",
        variant: "destructive",
      });
    },
  });

  // Save Quiz
  const saveQuizMutation = useMutation({
    mutationFn: async (quiz: Quiz) => {
      const response = await apiRequest('POST', '/api/quizzes', quiz);
      if (!response.ok) {
        throw new Error('Failed to save quiz');
      }
      return response.json();
    },
    onSuccess: (data) => {
      toast({
        title: "Quiz Saved",
        description: "Your quiz has been saved successfully",
      });
      onQuizCreated?.(data);
    },
    onError: (error) => {
      console.error('Error saving quiz:', error);
      toast({
        title: "Save Failed",
        description: "Failed to save quiz. Please try again.",
        variant: "destructive",
      });
    },
  });

  const getDifficultyPoints = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 1;
      case 'medium': return 2;
      case 'hard': return 3;
      default: return 1;
    }
  };

  const getDifficultyTimeLimit = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 30;
      case 'medium': return 60;
      case 'hard': return 90;
      default: return 30;
    }
  };

  const handleGenerateQuestions = () => {
    if (!courseScripts || !moduleId) {
      toast({
        title: "Content Required",
        description: "Please generate course content first to create relevant questions",
        variant: "destructive",
      });
      return;
    }

    const moduleContent = courseScripts[moduleId];
    if (!moduleContent || Object.keys(moduleContent).length === 0) {
      toast({
        title: "No Content Found",
        description: "No content found for this module. Please generate scripts first.",
        variant: "destructive",
      });
      return;
    }

    // Combine all lesson content for this module
    const combinedContent = Object.values(moduleContent).join('\n\n');

    generateQuestionsMutation.mutate({
      content: combinedContent,
      questionCount: 5,
      difficulty: 'mixed',
      questionTypes: ['multiple-choice', 'true-false', 'short-answer']
    });
  };

  const addNewQuestion = () => {
    const newQuestion: Question = {
      id: `question-${Date.now()}`,
      type: 'multiple-choice',
      question: '',
      options: ['', '', '', ''],
      correctAnswer: 0,
      explanation: '',
      difficulty: 'medium',
      points: 2,
      timeLimit: 60,
    };

    setCurrentQuiz(prev => ({
      ...prev,
      questions: [...prev.questions, newQuestion]
    }));
    setSelectedQuestion(newQuestion);
  };

  const updateQuestion = (questionId: string, updates: Partial<Question>) => {
    setCurrentQuiz(prev => ({
      ...prev,
      questions: prev.questions.map(q => 
        q.id === questionId ? { ...q, ...updates } : q
      )
    }));

    if (selectedQuestion?.id === questionId) {
      setSelectedQuestion(prev => prev ? { ...prev, ...updates } : null);
    }
  };

  const deleteQuestion = (questionId: string) => {
    setCurrentQuiz(prev => ({
      ...prev,
      questions: prev.questions.filter(q => q.id !== questionId)
    }));

    if (selectedQuestion?.id === questionId) {
      setSelectedQuestion(null);
    }
  };

  const duplicateQuestion = (question: Question) => {
    const duplicated: Question = {
      ...question,
      id: `question-${Date.now()}`,
      question: `${question.question} (Copy)`,
    };

    setCurrentQuiz(prev => ({
      ...prev,
      questions: [...prev.questions, duplicated]
    }));
  };

  const calculateQuizStats = () => {
    const totalPoints = currentQuiz.questions.reduce((sum, q) => sum + q.points, 0);
    const estimatedTime = currentQuiz.questions.reduce((sum, q) => sum + (q.timeLimit || 60), 0) / 60;
    const difficultyDistribution = currentQuiz.questions.reduce((dist, q) => {
      dist[q.difficulty] = (dist[q.difficulty] || 0) + 1;
      return dist;
    }, {} as Record<string, number>);

    return { totalPoints, estimatedTime, difficultyDistribution };
  };

  const stats = calculateQuizStats();

  const renderQuestionEditor = () => {
    if (!selectedQuestion) {
      return (
        <div className="flex items-center justify-center h-64 text-gray-500">
          <div className="text-center">
            <HelpCircle className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>Select a question to edit or create a new one</p>
          </div>
        </div>
      );
    }

    return (
      <div className="space-y-6">
        <div>
          <Label htmlFor="question-text">Question</Label>
          <Textarea
            id="question-text"
            value={selectedQuestion.question}
            onChange={(e) => updateQuestion(selectedQuestion.id, { question: e.target.value })}
            placeholder="Enter your question..."
            className="mt-1"
            rows={3}
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label>Question Type</Label>
            <Select
              value={selectedQuestion.type}
              onValueChange={(value: any) => updateQuestion(selectedQuestion.id, { type: value })}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="multiple-choice">Multiple Choice</SelectItem>
                <SelectItem value="true-false">True/False</SelectItem>
                <SelectItem value="short-answer">Short Answer</SelectItem>
                <SelectItem value="essay">Essay</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label>Difficulty</Label>
            <Select
              value={selectedQuestion.difficulty}
              onValueChange={(value: any) => updateQuestion(selectedQuestion.id, { 
                difficulty: value,
                points: getDifficultyPoints(value),
                timeLimit: getDifficultyTimeLimit(value)
              })}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="easy">Easy (1 pt)</SelectItem>
                <SelectItem value="medium">Medium (2 pts)</SelectItem>
                <SelectItem value="hard">Hard (3 pts)</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {selectedQuestion.type === 'multiple-choice' && (
          <div>
            <Label>Answer Options</Label>
            <div className="space-y-2 mt-2">
              {selectedQuestion.options?.map((option, index) => (
                <div key={index} className="flex items-center space-x-2">
                  <div className="flex items-center">
                    <input
                      type="radio"
                      name="correct-answer"
                      checked={selectedQuestion.correctAnswer === index}
                      onChange={() => updateQuestion(selectedQuestion.id, { correctAnswer: index })}
                      className="mr-2"
                    />
                  </div>
                  <Input
                    value={option}
                    onChange={(e) => {
                      const newOptions = [...(selectedQuestion.options || [])];
                      newOptions[index] = e.target.value;
                      updateQuestion(selectedQuestion.id, { options: newOptions });
                    }}
                    placeholder={`Option ${index + 1}`}
                    className="flex-1"
                  />
                </div>
              ))}
            </div>
          </div>
        )}

        {selectedQuestion.type === 'true-false' && (
          <div>
            <Label>Correct Answer</Label>
            <Select
              value={selectedQuestion.correctAnswer as string}
              onValueChange={(value) => updateQuestion(selectedQuestion.id, { correctAnswer: value })}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="true">True</SelectItem>
                <SelectItem value="false">False</SelectItem>
              </SelectContent>
            </Select>
          </div>
        )}

        {(selectedQuestion.type === 'short-answer' || selectedQuestion.type === 'essay') && (
          <div>
            <Label htmlFor="correct-answer">Sample Answer/Keywords</Label>
            <Textarea
              id="correct-answer"
              value={selectedQuestion.correctAnswer as string}
              onChange={(e) => updateQuestion(selectedQuestion.id, { correctAnswer: e.target.value })}
              placeholder="Enter sample answer or keywords for grading..."
              className="mt-1"
              rows={2}
            />
          </div>
        )}

        <div>
          <Label htmlFor="explanation">Explanation (Optional)</Label>
          <Textarea
            id="explanation"
            value={selectedQuestion.explanation || ''}
            onChange={(e) => updateQuestion(selectedQuestion.id, { explanation: e.target.value })}
            placeholder="Provide an explanation for the correct answer..."
            className="mt-1"
            rows={2}
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="points">Points</Label>
            <Input
              id="points"
              type="number"
              value={selectedQuestion.points}
              onChange={(e) => updateQuestion(selectedQuestion.id, { points: parseInt(e.target.value) || 1 })}
              min="1"
              max="10"
              className="mt-1"
            />
          </div>

          <div>
            <Label htmlFor="time-limit">Time Limit (seconds)</Label>
            <Input
              id="time-limit"
              type="number"
              value={selectedQuestion.timeLimit || 60}
              onChange={(e) => updateQuestion(selectedQuestion.id, { timeLimit: parseInt(e.target.value) || 60 })}
              min="10"
              max="300"
              className="mt-1"
            />
          </div>
        </div>
      </div>
    );
  };

  const renderQuestionPreview = (question: Question, index: number) => {
    return (
      <Card className="w-full max-w-2xl mx-auto">
        <CardHeader>
          <div className="flex justify-between items-start">
            <CardTitle className="text-lg">Question {index + 1}</CardTitle>
            <div className="flex items-center space-x-2">
              <Badge variant={question.difficulty === 'easy' ? 'secondary' : question.difficulty === 'medium' ? 'default' : 'destructive'}>
                {question.difficulty}
              </Badge>
              <Badge variant="outline">{question.points} pts</Badge>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <p className="text-lg font-medium">{question.question}</p>

            {question.type === 'multiple-choice' && (
              <div className="space-y-2">
                {question.options?.map((option, optionIndex) => (
                  <label key={optionIndex} className="flex items-center space-x-2 cursor-pointer">
                    <input
                      type="radio"
                      name={`preview-${question.id}`}
                      value={optionIndex}
                      checked={previewAnswers[question.id] === optionIndex}
                      onChange={(e) => setPreviewAnswers(prev => ({
                        ...prev,
                        [question.id]: parseInt(e.target.value)
                      }))}
                    />
                    <span>{option}</span>
                  </label>
                ))}
              </div>
            )}

            {question.type === 'true-false' && (
              <div className="space-y-2">
                <label className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="radio"
                    name={`preview-${question.id}`}
                    value="true"
                    checked={previewAnswers[question.id] === 'true'}
                    onChange={(e) => setPreviewAnswers(prev => ({
                      ...prev,
                      [question.id]: e.target.value
                    }))}
                  />
                  <span>True</span>
                </label>
                <label className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="radio"
                    name={`preview-${question.id}`}
                    value="false"
                    checked={previewAnswers[question.id] === 'false'}
                    onChange={(e) => setPreviewAnswers(prev => ({
                      ...prev,
                      [question.id]: e.target.value
                    }))}
                  />
                  <span>False</span>
                </label>
              </div>
            )}

            {(question.type === 'short-answer' || question.type === 'essay') && (
              <Textarea
                value={previewAnswers[question.id] || ''}
                onChange={(e) => setPreviewAnswers(prev => ({
                  ...prev,
                  [question.id]: e.target.value
                }))}
                placeholder="Enter your answer..."
                rows={question.type === 'essay' ? 4 : 2}
              />
            )}

            {question.timeLimit && (
              <div className="flex items-center text-sm text-gray-500">
                <Clock className="h-4 w-4 mr-1" />
                Time limit: {question.timeLimit} seconds
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="w-full space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <HelpCircle className="h-5 w-5" />
            <span>Quiz Builder</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="builder" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="builder">Builder</TabsTrigger>
              <TabsTrigger value="preview">Preview</TabsTrigger>
              <TabsTrigger value="settings">Settings</TabsTrigger>
            </TabsList>

            <TabsContent value="builder" className="space-y-6">
              {/* Quiz Basic Info */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="quiz-title">Quiz Title</Label>
                  <Input
                    id="quiz-title"
                    value={currentQuiz.title}
                    onChange={(e) => setCurrentQuiz(prev => ({ ...prev, title: e.target.value }))}
                    placeholder="Enter quiz title..."
                    className="mt-1"
                  />
                </div>
                <div>
                  <Label htmlFor="quiz-description">Description</Label>
                  <Input
                    id="quiz-description"
                    value={currentQuiz.description}
                    onChange={(e) => setCurrentQuiz(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="Brief description..."
                    className="mt-1"
                  />
                </div>
              </div>

              {/* AI Generation & Actions */}
              <div className="flex items-center justify-between p-4 bg-blue-50 rounded-lg border">
                <div>
                  <h3 className="font-medium text-blue-900">AI-Powered Question Generation</h3>
                  <p className="text-sm text-blue-700">Generate questions automatically from your course content</p>
                </div>
                <Button
                  onClick={handleGenerateQuestions}
                  disabled={generateQuestionsMutation.isPending}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  <Brain className="h-4 w-4 mr-2" />
                  {generateQuestionsMutation.isPending ? 'Generating...' : 'Generate Questions'}
                </Button>
              </div>

              {/* Quiz Stats */}
              <div className="grid grid-cols-4 gap-4">
                <Card>
                  <CardContent className="p-4 text-center">
                    <div className="text-2xl font-bold text-blue-600">{currentQuiz.questions.length}</div>
                    <div className="text-sm text-gray-500">Questions</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4 text-center">
                    <div className="text-2xl font-bold text-green-600">{stats.totalPoints}</div>
                    <div className="text-sm text-gray-500">Total Points</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4 text-center">
                    <div className="text-2xl font-bold text-purple-600">{Math.round(stats.estimatedTime)}</div>
                    <div className="text-sm text-gray-500">Est. Minutes</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4 text-center">
                    <div className="text-2xl font-bold text-orange-600">{currentQuiz.settings.passingScore}%</div>
                    <div className="text-sm text-gray-500">Passing Score</div>
                  </CardContent>
                </Card>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Questions List */}
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-medium">Questions</h3>
                    <Button onClick={addNewQuestion} size="sm">
                      <Plus className="h-4 w-4 mr-2" />
                      Add Question
                    </Button>
                  </div>

                  <div className="space-y-2 max-h-96 overflow-y-auto">
                    {currentQuiz.questions.map((question, index) => (
                      <Card 
                        key={question.id}
                        className={`cursor-pointer transition-colors ${
                          selectedQuestion?.id === question.id ? 'ring-2 ring-blue-500' : ''
                        }`}
                        onClick={() => setSelectedQuestion(question)}
                      >
                        <CardContent className="p-4">
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <div className="flex items-center space-x-2 mb-2">
                                <span className="text-sm font-medium">Q{index + 1}</span>
                                <Badge variant="outline" className="text-xs">
                                  {question.type.replace('-', ' ')}
                                </Badge>
                                <Badge variant={question.difficulty === 'easy' ? 'secondary' : question.difficulty === 'medium' ? 'default' : 'destructive'} className="text-xs">
                                  {question.difficulty}
                                </Badge>
                              </div>
                              <p className="text-sm text-gray-700 truncate">
                                {question.question || 'Untitled Question'}
                              </p>
                            </div>
                            <div className="flex items-center space-x-1 ml-2">
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  duplicateQuestion(question);
                                }}
                              >
                                <Copy className="h-3 w-3" />
                              </Button>
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  deleteQuestion(question.id);
                                }}
                              >
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}

                    {currentQuiz.questions.length === 0 && (
                      <div className="text-center py-8 text-gray-500">
                        <HelpCircle className="h-12 w-12 mx-auto mb-4 opacity-50" />
                        <p>No questions yet. Generate some with AI or add manually.</p>
                      </div>
                    )}
                  </div>
                </div>

                {/* Question Editor */}
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">
                    {selectedQuestion ? 'Edit Question' : 'Question Editor'}
                  </h3>
                  <Card>
                    <CardContent className="p-6">
                      {renderQuestionEditor()}
                    </CardContent>
                  </Card>
                </div>
              </div>

              <div className="flex justify-end space-x-4">
                <Button
                  variant="outline"
                  onClick={() => setIsPreviewMode(true)}
                  disabled={currentQuiz.questions.length === 0}
                >
                  <Eye className="h-4 w-4 mr-2" />
                  Preview Quiz
                </Button>
                <Button
                  onClick={() => saveQuizMutation.mutate(currentQuiz)}
                  disabled={!currentQuiz.title || currentQuiz.questions.length === 0 || saveQuizMutation.isPending}
                >
                  <Save className="h-4 w-4 mr-2" />
                  {saveQuizMutation.isPending ? 'Saving...' : 'Save Quiz'}
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="preview" className="space-y-6">
              {currentQuiz.questions.length > 0 ? (
                <div className="space-y-6">
                  <div className="flex items-center justify-between">
                    <h3 className="text-xl font-semibold">{currentQuiz.title || 'Untitled Quiz'}</h3>
                    <div className="flex items-center space-x-4">
                      <span className="text-sm text-gray-500">
                        Question {currentPreviewIndex + 1} of {currentQuiz.questions.length}
                      </span>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => {
                          setPreviewAnswers({});
                          setCurrentPreviewIndex(0);
                        }}
                      >
                        <RotateCcw className="h-4 w-4 mr-2" />
                        Reset
                      </Button>
                    </div>
                  </div>

                  {renderQuestionPreview(currentQuiz.questions[currentPreviewIndex], currentPreviewIndex)}

                  <div className="flex justify-between">
                    <Button
                      variant="outline"
                      onClick={() => setCurrentPreviewIndex(Math.max(0, currentPreviewIndex - 1))}
                      disabled={currentPreviewIndex === 0}
                    >
                      Previous
                    </Button>
                    <Button
                      onClick={() => setCurrentPreviewIndex(Math.min(currentQuiz.questions.length - 1, currentPreviewIndex + 1))}
                      disabled={currentPreviewIndex === currentQuiz.questions.length - 1}
                    >
                      Next
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="text-center py-12">
                  <HelpCircle className="h-16 w-16 mx-auto mb-4 opacity-50" />
                  <p className="text-gray-500">No questions to preview yet</p>
                </div>
              )}
            </TabsContent>

            <TabsContent value="settings" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Quiz Settings</CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="time-limit">Time Limit (minutes)</Label>
                      <Input
                        id="time-limit"
                        type="number"
                        value={currentQuiz.settings.timeLimit}
                        onChange={(e) => setCurrentQuiz(prev => ({
                          ...prev,
                          settings: { ...prev.settings, timeLimit: parseInt(e.target.value) || 15 }
                        }))}
                        min="1"
                        max="180"
                        className="mt-1"
                      />
                    </div>

                    <div>
                      <Label htmlFor="passing-score">Passing Score (%)</Label>
                      <Input
                        id="passing-score"
                        type="number"
                        value={currentQuiz.settings.passingScore}
                        onChange={(e) => setCurrentQuiz(prev => ({
                          ...prev,
                          settings: { ...prev.settings, passingScore: parseInt(e.target.value) || 70 }
                        }))}
                        min="1"
                        max="100"
                        className="mt-1"
                      />
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label>Allow Retakes</Label>
                        <p className="text-sm text-gray-500">Let students retake the quiz if they fail</p>
                      </div>
                      <Switch
                        checked={currentQuiz.settings.allowRetakes}
                        onCheckedChange={(checked) => setCurrentQuiz(prev => ({
                          ...prev,
                          settings: { ...prev.settings, allowRetakes: checked }
                        }))}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label>Shuffle Questions</Label>
                        <p className="text-sm text-gray-500">Randomize question order for each attempt</p>
                      </div>
                      <Switch
                        checked={currentQuiz.settings.shuffleQuestions}
                        onCheckedChange={(checked) => setCurrentQuiz(prev => ({
                          ...prev,
                          settings: { ...prev.settings, shuffleQuestions: checked }
                        }))}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label>Show Correct Answers</Label>
                        <p className="text-sm text-gray-500">Display correct answers after completion</p>
                      </div>
                      <Switch
                        checked={currentQuiz.settings.showCorrectAnswers}
                        onCheckedChange={(checked) => setCurrentQuiz(prev => ({
                          ...prev,
                          settings: { ...prev.settings, showCorrectAnswers: checked }
                        }))}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label>Show Explanations</Label>
                        <p className="text-sm text-gray-500">Display answer explanations after completion</p>
                      </div>
                      <Switch
                        checked={currentQuiz.settings.showExplanations}
                        onCheckedChange={(checked) => setCurrentQuiz(prev => ({
                          ...prev,
                          settings: { ...prev.settings, showExplanations: checked }
                        }))}
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}