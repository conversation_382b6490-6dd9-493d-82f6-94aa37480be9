import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { useMutation } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import {
  ArrowLeft,
  CheckCircle,
  Globe,
  Share,
  Copy,
  ExternalLink,
  ChevronRight
} from "lucide-react";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";

export interface AvatarPublishProps {
  courseId: number | null;
  courseTitle: string;
  videoUrl: string;
  onPrevious: () => void;
  onFinish: () => void;
}

export function AvatarPublish({
  courseId,
  courseTitle,
  videoUrl,
  onPrevious,
  onFinish
}: AvatarPublishProps) {
  const [isPublic, setIsPublic] = useState(false);
  const [shareableLink, setShareableLink] = useState("");
  const [linkCopied, setLinkCopied] = useState(false);
  const { toast } = useToast();
  
  // Publish course mutation
  const publishMutation = useMutation({
    mutationFn: async () => {
      if (!courseId) {
        throw new Error("Course ID is missing");
      }
      
      const data = {
        status: isPublic ? "published" : "draft",
        isPublic
      };
      
      const res = await apiRequest('PATCH', `/api/courses/${courseId}`, data);
      if (!res.ok) {
        const errorText = await res.text();
        throw new Error(errorText || 'Failed to publish course');
      }
      
      return await res.json();
    },
    onSuccess: (data) => {
      toast({
        title: isPublic ? "Course Published" : "Course Updated",
        description: isPublic 
          ? "Your course is now available to the public." 
          : "Your course has been updated successfully.",
        variant: "default",
      });
      
      // Set shareable link if published
      if (isPublic && data.shareLink) {
        setShareableLink(data.shareLink);
      }
      
      // Invalidate course queries to refresh the data
      queryClient.invalidateQueries({ queryKey: ['/api/courses'] });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: `Failed to update course: ${error.message}`,
        variant: "destructive",
      });
    }
  });
  
  const handlePublish = () => {
    publishMutation.mutate();
  };
  
  const handleCopyLink = () => {
    if (shareableLink) {
      navigator.clipboard.writeText(shareableLink);
      setLinkCopied(true);
      
      // Reset copied state after 2 seconds
      setTimeout(() => {
        setLinkCopied(false);
      }, 2000);
    }
  };
  
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <Card>
            <CardContent className="p-6">
              <h3 className="text-xl font-semibold mb-4">Course Preview</h3>
              
              <div className="aspect-video rounded-lg border overflow-hidden mb-4">
                {videoUrl ? (
                  <img 
                    src={videoUrl} 
                    alt="Video Preview"
                    className="w-full h-full object-contain"
                  />
                ) : (
                  <div className="w-full h-full bg-muted/30 flex items-center justify-center">
                    <p className="text-muted-foreground">No video available</p>
                  </div>
                )}
              </div>
              
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium">{courseTitle}</h4>
                </div>
                
                <div className="flex items-center justify-between">
                  <Button 
                    variant="outline" 
                    size="sm"
                    className="gap-1"
                    onClick={() => window.open(`/courses/${courseId}/preview`, '_blank')}
                  >
                    <ExternalLink className="h-4 w-4" />
                    Preview Course
                  </Button>
                  
                  <Button 
                    variant="outline" 
                    size="sm"
                    className="gap-1"
                    onClick={() => window.open(`/courses/${courseId}/edit/avatar`, '_blank')}
                  >
                    <ChevronRight className="h-4 w-4" />
                    Edit Course
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
        
        <div>
          <Card>
            <CardContent className="p-6 space-y-6">
              <div>
                <h3 className="text-xl font-semibold mb-4">Publish Settings</h3>
                
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="public-toggle">Make Course Public</Label>
                      <p className="text-sm text-muted-foreground">
                        Allow others to view and enroll in your course
                      </p>
                    </div>
                    <Switch 
                      id="public-toggle"
                      checked={isPublic}
                      onCheckedChange={setIsPublic}
                    />
                  </div>
                  
                  <Button 
                    onClick={handlePublish}
                    disabled={publishMutation.isPending}
                    className="w-full gap-2"
                  >
                    {publishMutation.isPending ? (
                      <>
                        <span className="animate-spin mr-2">&#10227;</span>
                        Updating...
                      </>
                    ) : (
                      <>
                        <Globe className="h-4 w-4" />
                        {isPublic ? "Publish Course" : "Save Settings"}
                      </>
                    )}
                  </Button>
                </div>
              </div>
              
              <div className="pt-4 border-t">
                <h3 className="font-semibold mb-4">Share Course</h3>
                
                {shareableLink ? (
                  <div className="space-y-4">
                    <div className="flex gap-2">
                      <Input 
                        value={shareableLink} 
                        readOnly 
                        className="flex-1"
                      />
                      <Button 
                        variant="outline" 
                        size="icon"
                        onClick={handleCopyLink}
                        className="shrink-0"
                      >
                        {linkCopied ? (
                          <CheckCircle className="h-4 w-4 text-green-500" />
                        ) : (
                          <Copy className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                    
                    <div className="flex gap-2">
                      <Button variant="outline" className="flex-1 gap-2">
                        <Share className="h-4 w-4" />
                        Share Link
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="bg-muted/30 p-4 rounded-lg text-center">
                    <p className="text-muted-foreground text-sm">
                      Publish your course to get a shareable link
                    </p>
                  </div>
                )}
              </div>
              
              <div className="pt-4 border-t">
                <Button 
                  variant="default"
                  onClick={onFinish}
                  className="w-full"
                >
                  Finish & Go to Dashboard
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
      
      <div className="flex justify-start pt-4">
        <Button 
          variant="outline" 
          onClick={onPrevious}
          className="gap-1"
        >
          <ArrowLeft className="h-4 w-4" />
          Previous
        </Button>
      </div>
    </div>
  );
}