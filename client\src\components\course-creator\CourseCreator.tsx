import React, { useState, useEffect } from "react";
import { StepIndicator } from "./StepIndicator";
import { CourseDetails } from "./CourseDetails";
import { ContentStructure } from "./ContentStructure";
import { ScriptGeneration } from "./ScriptGeneration";
import { MediaCreation } from "./MediaCreation";
import { CourseAssembly } from "./CourseAssembly";
import { PreviewPublish } from "./PreviewPublish";
import { X, Loader2 } from "lucide-react";
import { apiRequest } from "@/lib/queryClient";
import { useQuery } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { usePlayfulLoading } from "@/hooks/use-playful-loading";

interface CourseCreatorProps {
  onClose: () => void;
  mode?: "create" | "edit";
  initialFormat?: string;
  avatarData?: any;
  courseId?: number | null;
}

// Course details interface to share state between steps
interface CourseDetailsState {
  title: string;
  category: string;
  description: string;
  targetAudience?: string;
  useAI: boolean;
  format: 'avatar' | 'traditional';
  moduleCount?: number;
}

// Interface for module structure
interface Module {
  title: string;
  description?: string;
  lessons: {
    title: string;
    description: string;
  }[];
  expanded?: boolean;
}

// Interface for script content
interface CourseScripts {
  [moduleKey: string]: string;
}

export function CourseCreator({ onClose, mode = "create", initialFormat, avatarData, courseId: providedCourseId = null }: CourseCreatorProps) {
  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 6;
  const { toast } = useToast();
  const { showTaskLoading, hideLoading } = usePlayfulLoading();
  
  // Use provided courseId if available, otherwise try to extract from URL
  const courseId = providedCourseId || (mode === "edit" ? parseInt(window.location.pathname.split('/')[2]) : null);
  
  // State to store course details between steps
  const [courseDetails, setCourseDetails] = useState<CourseDetailsState>({
    title: "",
    category: "",
    description: "",
    targetAudience: "",
    useAI: true,
    format: (initialFormat as 'avatar' | 'traditional') || "traditional"
  });
  
  // State to store content structure between steps
  const [courseStructure, setCourseStructure] = useState<{
    modules: Module[]
  }>({
    modules: []
  });
  
  // State to store scripts between steps
  const [courseScripts, setCourseScripts] = useState<CourseScripts>({});
  
  // State to store export settings
  const [exportSettings, setExportSettings] = useState({
    format: 'video',
    quality: 'hd',
  });
  
  // State to store generated audio data between steps
  const [generatedAudio, setGeneratedAudio] = useState<Record<string, any[]>>({});
  
  // Fetch course data if in edit mode
  const { data: course, isLoading: isLoadingCourse } = useQuery({
    queryKey: ['/api/courses', courseId],
    queryFn: async () => {
      if (!courseId) return null;
      const res = await apiRequest('GET', `/api/courses/${courseId}`);
      if (!res.ok) throw new Error('Failed to fetch course');
      return res.json();
    },
    enabled: mode === "edit" && !!courseId
  });
  
  // Fetch modules data if in edit mode
  const { data: modules, isLoading: isLoadingModules } = useQuery({
    queryKey: ['/api/courses', courseId, 'modules'],
    queryFn: async () => {
      if (!courseId) return null;
      const res = await apiRequest('GET', `/api/courses/${courseId}/modules`);
      if (!res.ok) throw new Error('Failed to fetch modules');
      return res.json();
    },
    enabled: mode === "edit" && !!courseId && !!course
  });
  
  // Load course data into state when data is fetched
  useEffect(() => {
    if (mode === "edit" && course) {
      setCourseDetails({
        title: course.title || "",
        category: course.category || "",
        description: course.description || "",
        targetAudience: course.targetAudience || "",
        useAI: true, // Default since we don't store this
        format: course.format || "traditional" // Default to traditional if not specified
      });
      
      // If course has a structure, load it
      if (course.structure) {
        try {
          const parsedStructure = typeof course.structure === 'string' 
            ? JSON.parse(course.structure) 
            : course.structure;
          
          if (parsedStructure && parsedStructure.modules) {
            setCourseStructure(parsedStructure);
          }
        } catch (error) {
          console.error("Error parsing course structure:", error);
          toast({
            title: "Error",
            description: "Could not load course structure.",
            variant: "destructive"
          });
        }
      }
      
      // If we have modules and they have scripts, load the scripts
      if (modules && modules.length > 0) {
        const scripts: CourseScripts = {};
        
        modules.forEach((module: any, moduleIndex: number) => {
          // Store script directly at the module level
          scripts[`module-${moduleIndex}`] = module.script || "";
        });
        
        setCourseScripts(scripts);
      }
    }
  }, [course, modules, mode]);

  const steps = [
    { number: 1, label: "Course Details" },
    { number: 2, label: "Content Structure" },
    { number: 3, label: "Script Generation" },
    { number: 4, label: "Media Creation" },
    { number: 5, label: "Course Assembly" },
    { number: 6, label: "Preview & Publish" }
  ];

  const handleNextStep = () => {
    if (currentStep < totalSteps) {
      // Show appropriate loading screens based on the current step
      if (currentStep === 1) {
        showTaskLoading('building-structure', {
          expression: 'excited',
          estimatedTime: 2000,
        }, () => {
          setCurrentStep(currentStep + 1);
        });
      } else if (currentStep === 2) {
        showTaskLoading('generating-content', {
          expression: 'thinking',
          estimatedTime: 3000,
        }, () => {
          setCurrentStep(currentStep + 1);
        });
      } else if (currentStep === 3) {
        showTaskLoading('generating-speech', {
          expression: 'working',
          estimatedTime: 2500,
        }, () => {
          setCurrentStep(currentStep + 1);
        });
      } else if (currentStep === 4) {
        showTaskLoading('processing-video', {
          expression: 'working',
          estimatedTime: 3000,
        }, () => {
          setCurrentStep(currentStep + 1);
        });
      } else if (currentStep === 5) {
        showTaskLoading('generating-thumbnail', {
          expression: 'excited',
          estimatedTime: 2500,
        }, () => {
          setCurrentStep(currentStep + 1);
        });
      } else {
        setCurrentStep(currentStep + 1);
      }
    }
  };

  const handlePreviousStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };
  
  const handleCourseDetailsSubmit = (values: CourseDetailsState) => {
    setCourseDetails(values);
    // Show a custom loading animation when moving from course details to structure
    showTaskLoading('building-structure', {
      text: 'Preparing Your Course Builder',
      subText: values.useAI ? 'Setting up AI-powered tools for your course' : 'Setting up your course builder',
      expression: 'excited',
      estimatedTime: 2500,
    }, () => {
      setCurrentStep(currentStep + 1);
    });
  };

  // Use effect to show loading screen when course is loading
  useEffect(() => {
    if (mode === "edit" && (isLoadingCourse || isLoadingModules)) {
      // Show loading screen with playful mascot
      showTaskLoading('analyzing-content', {
        text: 'Loading Your Course',
        subText: 'Retrieving all your course content and materials',
        expression: 'thinking',
        estimatedTime: 4000,
      });
    } else if (mode === "edit" && course && modules) {
      // Hide loading screen once course and modules are loaded
      hideLoading();
    }
  }, [isLoadingCourse, isLoadingModules, mode, course, modules]);
  
  // Check if we're still loading course data in edit mode
  if (mode === "edit" && (isLoadingCourse || isLoadingModules)) {
    return (
      <div className="p-4 sm:p-6 space-y-6">
        <div className="bg-white rounded-xl shadow-sm border border-slate-200 overflow-hidden">
          <div className="border-b border-slate-200 px-6 py-4">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-semibold text-slate-900">Loading Course...</h2>
              <button 
                className="text-slate-500 hover:text-slate-700"
                onClick={onClose}
              >
                <X className="h-5 w-5" />
              </button>
            </div>
          </div>
          <div className="p-12 flex flex-col items-center justify-center">
            {/* This content is technically hidden behind the loading screen but is still here for fallback */}
            <Loader2 className="h-10 w-10 animate-spin text-primary mb-4" />
            <p className="text-slate-600">Loading course content...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4 sm:p-6 space-y-6">
      <div className="bg-white rounded-xl shadow-sm border border-slate-200 overflow-hidden">
        {/* Wizard Header */}
        <div className="border-b border-slate-200 px-6 py-4">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-semibold text-slate-900">
              {mode === "edit" ? "Edit Course" : "Create New Course"}
            </h2>
            <button 
              className="text-slate-500 hover:text-slate-700"
              onClick={onClose}
            >
              <X className="h-5 w-5" />
            </button>
          </div>
        </div>
        
        {/* Wizard Progress */}
        <div className="px-6 py-4 bg-slate-50 border-b border-slate-200">
          <StepIndicator steps={steps} currentStep={currentStep} />
        </div>
        
        {/* Wizard Content */}
        <div className="p-6">
          {currentStep === 1 && (
            <CourseDetails 
              onNext={handleCourseDetailsSubmit} 
              initialValues={courseDetails}
            />
          )}
          
          {currentStep === 2 && (
            <ContentStructure 
              onNext={handleNextStep} 
              onPrevious={handlePreviousStep}
              courseDetails={courseDetails}
              onUpdateStructure={setCourseStructure}
              initialStructure={courseStructure}
            />
          )}
          
          {currentStep === 3 && (
            <ScriptGeneration
              onNext={handleNextStep}
              onPrevious={handlePreviousStep}
              courseDetails={courseDetails}
              courseStructure={courseStructure}
              courseScripts={courseScripts}
              onUpdateScripts={setCourseScripts}
            />
          )}
          
          {currentStep === 4 && (
            <MediaCreation 
              onNext={handleNextStep}
              onPrevious={handlePreviousStep}
              courseDetails={courseDetails}
              courseStructure={courseStructure}
              courseScripts={courseScripts}
              onUpdateGeneratedAudio={setGeneratedAudio}
            />
          )}
          
          {currentStep === 5 && (
            <CourseAssembly
              onNext={() => {
                // Save export settings from CourseAssembly to state
                handleNextStep();
              }}
              onPrevious={handlePreviousStep}
              courseDetails={courseDetails}
              courseStructure={courseStructure}
              courseScripts={courseScripts}
              exportSettings={exportSettings}
              onUpdateExportSettings={setExportSettings}
            />
          )}
          
          {currentStep === 6 && (
            <PreviewPublish
              onPrevious={handlePreviousStep}
              onFinish={onClose}
              courseDetails={courseDetails}
              courseStructure={courseStructure}
              courseScripts={courseScripts}
              exportSettings={exportSettings}
              courseId={courseId}
              isEditing={mode === "edit"}
              generatedAudio={generatedAudio}
            />
          )}
        </div>
      </div>
    </div>
  );
}
