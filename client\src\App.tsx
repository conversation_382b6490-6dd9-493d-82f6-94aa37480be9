import { Toaster } from "@/components/ui/toaster";
import { AuthProvider } from "@/hooks/use-auth";
import { AppContent } from "./components/app-content";
import { HintProvider } from "@/components/hints/HintProvider";
import { LoadingProvider } from "@/components/providers/LoadingProvider";
import { NotificationsProvider } from "@/hooks/use-notifications";
import { OnboardingProvider } from "@/hooks/use-onboarding";

function App() {
  return (
    <AuthProvider>
      <NotificationsProvider>
        <HintProvider>
          <LoadingProvider>
            <OnboardingProvider>
              <AppContent />
              <Toaster />
            </OnboardingProvider>
          </LoadingProvider>
        </HintProvider>
      </NotificationsProvider>
    </AuthProvider>
  );
}

export default App;