import React from 'react';
import { User } from '@/types';
import { Link } from 'wouter';
import { 
  CheckCircle2, 
  AlertCircle, 
  ExternalLink 
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface SubscriptionBadgeProps {
  user: User;
}

export function SubscriptionBadge({ user }: SubscriptionBadgeProps) {
  // Determine if user has an active subscription
  const hasSubscription = Boolean(user?.stripeSubscriptionId);
  const planName = user?.plan || 'Free';
  
  if (!user) return null;
  
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Link to="/pricing" className="flex items-center">
            <Badge 
              variant={hasSubscription ? "default" : "outline"}
              className={`cursor-pointer ${hasSubscription 
                ? "bg-green-500 hover:bg-green-600" 
                : "border-amber-500 text-amber-500 hover:bg-amber-100"}`}
            >
              {hasSubscription ? (
                <><CheckCircle2 className="mr-1 h-3 w-3" /> {planName}</>
              ) : (
                <><AlertCircle className="mr-1 h-3 w-3" /> {planName}</>
              )}
              <ExternalLink className="ml-1 h-3 w-3" />
            </Badge>
          </Link>
        </TooltipTrigger>
        <TooltipContent>
          <div className="p-1 max-w-xs">
            <p className="font-medium mb-1">{hasSubscription ? 'Active Subscription' : 'Upgrade Your Plan'}</p>
            <p className="text-xs text-muted-foreground">
              {hasSubscription 
                ? 'You have full access to all premium features'
                : 'Upgrade to unlock premium features and create more courses'
              }
            </p>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}