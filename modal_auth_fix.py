#!/usr/bin/env python3
"""
Modal Authentication and A100 GPU Configuration Fix
Resolves authentication issues and ensures proper A100 GPU allocation
"""

import os
import sys
import subprocess
import json
from pathlib import Path

def check_modal_version():
    """Check Modal version and compatibility"""
    try:
        import modal
        print(f"Modal version: {modal.__version__}")
        return modal.__version__
    except ImportError:
        print("Modal not installed")
        return None

def setup_modal_auth():
    """Setup Modal authentication using environment variables"""
    token_id = os.getenv('MODAL_TOKEN_ID')
    token_secret = os.getenv('MODAL_TOKEN_SECRET')
    
    if not token_id or not token_secret:
        print("ERROR: Modal credentials not found in environment")
        return False
    
    print(f"Modal Token ID: {token_id[:8]}...")
    print(f"Modal Token Secret: {token_secret[:8]}...")
    
    # Create Modal config directory
    config_dir = Path.home() / '.modal'
    config_dir.mkdir(exist_ok=True)
    
    # Write credentials to config file
    config_file = config_dir / 'config.json'
    config_data = {
        "token_id": token_id,
        "token_secret": token_secret,
        "environment": "main"
    }
    
    with open(config_file, 'w') as f:
        json.dump(config_data, f, indent=2)
    
    print(f"Modal config written to: {config_file}")
    return True

def test_modal_connection():
    """Test Modal connection and authentication"""
    try:
        # Set environment variables for Modal CLI
        os.environ['MODAL_TOKEN_ID'] = os.getenv('MODAL_TOKEN_ID')
        os.environ['MODAL_TOKEN_SECRET'] = os.getenv('MODAL_TOKEN_SECRET')
        
        # Test using Modal CLI
        result = subprocess.run(['modal', 'token', 'current'], 
                              capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("Modal authentication successful via CLI")
            print(result.stdout)
            return True
        else:
            print(f"Modal CLI authentication failed: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("Modal authentication timeout")
        return False
    except FileNotFoundError:
        print("Modal CLI not found - trying Python client")
        return test_python_client()
    except Exception as e:
        print(f"Modal authentication error: {e}")
        return False

def test_python_client():
    """Test Modal Python client authentication"""
    try:
        import modal
        
        # Try different client initialization methods
        try:
            # Method 1: Default client
            client = modal.Client.from_env()
            print("Modal client created with from_env()")
        except:
            try:
                # Method 2: Explicit credentials
                token_id = os.getenv('MODAL_TOKEN_ID')
                token_secret = os.getenv('MODAL_TOKEN_SECRET')
                client = modal.Client(token_id=token_id, token_secret=token_secret)
                print("Modal client created with explicit credentials")
            except Exception as e:
                print(f"Failed to create Modal client: {e}")
                return False
        
        # Test client functionality
        try:
            apps = list(client.list_apps())
            print(f"Successfully authenticated - found {len(apps)} apps")
            return True
        except Exception as e:
            print(f"Client authentication test failed: {e}")
            return False
            
    except ImportError:
        print("Modal package not available")
        return False

def check_a100_availability():
    """Check A100 GPU availability and pricing"""
    try:
        import modal
        
        # Check GPU configurations
        gpu_configs = {
            "A100-40GB": modal.gpu.A100(count=1, memory=40),
            "A100-80GB": modal.gpu.A100(count=1, memory=80),
        }
        
        print("\nA100 GPU Configurations:")
        for name, config in gpu_configs.items():
            print(f"  {name}: {config}")
            
        return True
        
    except Exception as e:
        print(f"A100 availability check failed: {e}")
        return False

def create_test_a100_app():
    """Create a test A100 GPU application"""
    app_code = '''
import modal

# Create Modal app with A100 80GB GPU
app = modal.App("a100-test")

# Define A100 GPU image with required dependencies
image = modal.Image.debian_slim().pip_install([
    "torch",
    "transformers",
    "accelerate",
    "numpy"
])

@app.function(
    image=image,
    gpu=modal.gpu.A100(count=1, memory=80),
    timeout=300,
    keep_warm=0
)
def test_a100_gpu():
    """Test A100 GPU functionality"""
    import torch
    
    # Check GPU availability
    if torch.cuda.is_available():
        gpu_name = torch.cuda.get_device_name(0)
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        
        return {
            "gpu_available": True,
            "gpu_name": gpu_name,
            "gpu_memory_gb": round(gpu_memory, 2),
            "cuda_version": torch.version.cuda,
            "pytorch_version": torch.__version__
        }
    else:
        return {
            "gpu_available": False,
            "error": "CUDA not available"
        }

@app.function(
    image=image,
    gpu=modal.gpu.A100(count=1, memory=80),
    timeout=600
)
def test_a100_inference():
    """Test A100 GPU inference capabilities"""
    import torch
    import time
    
    if not torch.cuda.is_available():
        return {"error": "GPU not available"}
    
    # Simple GPU computation test
    device = torch.device("cuda")
    
    # Create large tensors to utilize GPU memory
    start_time = time.time()
    x = torch.randn(10000, 10000, device=device)
    y = torch.randn(10000, 10000, device=device)
    
    # Perform matrix multiplication
    result = torch.matmul(x, y)
    torch.cuda.synchronize()
    
    computation_time = time.time() - start_time
    
    return {
        "computation_time": round(computation_time, 4),
        "tensor_shape": list(result.shape),
        "gpu_utilization": "successful",
        "memory_allocated": torch.cuda.memory_allocated() / 1024**3
    }

if __name__ == "__main__":
    print("Testing A100 GPU functionality...")
    
    with app.run():
        # Test basic GPU info
        gpu_info = test_a100_gpu.remote()
        print("GPU Info:", gpu_info)
        
        # Test GPU inference
        if gpu_info.get("gpu_available"):
            inference_result = test_a100_inference.remote()
            print("Inference Test:", inference_result)
        else:
            print("Skipping inference test - GPU not available")
'''
    
    # Write test app to file
    with open('test_a100_app.py', 'w') as f:
        f.write(app_code)
    
    print("A100 test app created: test_a100_app.py")
    return True

def run_a100_test():
    """Run A100 GPU test"""
    try:
        print("\nRunning A100 GPU test...")
        result = subprocess.run(['modal', 'run', 'test_a100_app.py'], 
                              capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("A100 GPU test successful:")
            print(result.stdout)
            return True
        else:
            print("A100 GPU test failed:")
            print(result.stderr)
            
            # Check for billing/quota issues
            if "billing" in result.stderr.lower() or "quota" in result.stderr.lower():
                print("\n⚠️  BILLING/QUOTA ISSUE DETECTED:")
                print("- Verify Modal account has valid payment method")
                print("- Check account limits and spending caps") 
                print("- Ensure A100 GPU quota is available")
                
            return False
            
    except subprocess.TimeoutExpired:
        print("A100 GPU test timeout - may indicate resource allocation issues")
        return False
    except Exception as e:
        print(f"A100 GPU test error: {e}")
        return False

def main():
    """Main execution function"""
    print("🔧 Modal A100 GPU Configuration Diagnostic")
    print("=" * 50)
    
    # Step 1: Check Modal version
    version = check_modal_version()
    if not version:
        print("❌ Modal not installed")
        return False
    
    # Step 2: Setup authentication
    if not setup_modal_auth():
        print("❌ Modal authentication setup failed")
        return False
    
    # Step 3: Test connection
    if not test_modal_connection():
        print("❌ Modal connection test failed")
        return False
    
    # Step 4: Check A100 availability
    if not check_a100_availability():
        print("❌ A100 availability check failed")
        return False
    
    # Step 5: Create test app
    if not create_test_a100_app():
        print("❌ A100 test app creation failed")
        return False
    
    # Step 6: Run A100 test
    if not run_a100_test():
        print("❌ A100 GPU test failed")
        return False
    
    print("\n✅ Modal A100 GPU configuration successful!")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)