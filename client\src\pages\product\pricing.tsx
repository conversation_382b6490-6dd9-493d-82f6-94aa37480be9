import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { 
  Check, X, Star, Crown, Users, Shield, Rocket,
  Video, Mic, FileVideo, Brain, BarChart3, ArrowRight
} from "lucide-react";
import { <PERSON> } from "wouter";

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: { staggerChildren: 0.1 }
  }
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { 
    opacity: 1, 
    y: 0,
    transition: { duration: 0.5 }
  }
};

export default function PricingPage() {
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'yearly'>('monthly');

  const plans = [
    {
      id: "starter",
      name: "Starter",
      description: "Essential features for creating basic courses",
      priceMonthly: 19,
      priceYearly: 15,
      popular: false,
      color: "blue",
      features: {
        courses: "1",
        videos: "2",
        voiceovers: "5", 
        images: "10",
        templates: true,
        aiScript: true,
        microlearning: false,
        teamCollaboration: false,
        analytics: false,
        apiAccess: false,
        prioritySupport: false,
        successManager: false
      }
    },
    {
      id: "pro",
      name: "Pro", 
      description: "Advanced features for professional educators",
      priceMonthly: 49,
      priceYearly: 39,
      popular: true,
      color: "purple",
      features: {
        courses: "3",
        videos: "7",
        voiceovers: "15",
        images: "30", 
        templates: true,
        aiScript: true,
        microlearning: true,
        teamCollaboration: true,
        analytics: true,
        apiAccess: true,
        prioritySupport: true,
        successManager: false
      }
    },
    {
      id: "business",
      name: "Business",
      description: "Comprehensive solution for growing businesses",
      priceMonthly: 99,
      priceYearly: 79,
      popular: false,
      color: "emerald",
      features: {
        courses: "10",
        videos: "20", 
        voiceovers: "50",
        images: "100",
        templates: true,
        aiScript: true,
        microlearning: true,
        teamCollaboration: true,
        analytics: true,
        apiAccess: true,
        prioritySupport: true,
        successManager: true
      }
    },
    {
      id: "enterprise",
      name: "Enterprise",
      description: "Custom solutions for large organizations", 
      priceMonthly: 299,
      priceYearly: 249,
      popular: false,
      color: "slate",
      features: {
        courses: "Unlimited",
        videos: "Unlimited",
        voiceovers: "Unlimited", 
        images: "Unlimited",
        templates: true,
        aiScript: true,
        microlearning: true,
        teamCollaboration: true,
        analytics: true,
        apiAccess: true,
        prioritySupport: true,
        successManager: true
      }
    }
  ];

  const featureLabels = {
    courses: "Courses",
    videos: "AI Videos", 
    voiceovers: "Voiceovers",
    images: "AI Images",
    templates: "Course Templates",
    aiScript: "AI Script Generation",
    microlearning: "Micro-learning Mode",
    teamCollaboration: "Team Collaboration", 
    analytics: "Advanced Analytics",
    apiAccess: "API Access",
    prioritySupport: "Priority Support",
    successManager: "Dedicated Success Manager"
  };

  const getCurrentPrice = (plan: any) => {
    return billingCycle === 'monthly' ? plan.priceMonthly : plan.priceYearly;
  };

  const getSavings = (plan: any) => {
    if (plan.priceYearly >= plan.priceMonthly) return 0;
    const monthlyCost = plan.priceMonthly * 12;
    const yearlyCost = plan.priceYearly * 12;
    return Math.round(((monthlyCost - yearlyCost) / monthlyCost) * 100);
  };

  const getColorClasses = (color: string, popular: boolean = false) => {
    const colors = {
      blue: {
        gradient: "from-blue-50 to-indigo-50",
        border: popular ? "border-blue-500" : "border-blue-200",
        text: "text-blue-600",
        button: popular ? "bg-blue-600 hover:bg-blue-700 text-white" : "bg-white border border-blue-600 text-blue-600 hover:bg-blue-50"
      },
      purple: {
        gradient: "from-purple-50 to-violet-50", 
        border: popular ? "border-purple-500" : "border-purple-200",
        text: "text-purple-600",
        button: popular ? "bg-purple-600 hover:bg-purple-700 text-white" : "bg-white border border-purple-600 text-purple-600 hover:bg-purple-50"
      },
      emerald: {
        gradient: "from-emerald-50 to-teal-50",
        border: popular ? "border-emerald-500" : "border-emerald-200", 
        text: "text-emerald-600",
        button: popular ? "bg-emerald-600 hover:bg-emerald-700 text-white" : "bg-white border border-emerald-600 text-emerald-600 hover:bg-emerald-50"
      },
      slate: {
        gradient: "from-slate-50 to-gray-50",
        border: popular ? "border-slate-500" : "border-slate-200",
        text: "text-slate-600", 
        button: popular ? "bg-slate-600 hover:bg-slate-700 text-white" : "bg-white border border-slate-600 text-slate-600 hover:bg-slate-50"
      }
    };
    return colors[color as keyof typeof colors];
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Hero Section */}
      <motion.section 
        className="relative overflow-hidden pt-24 pb-16"
        initial="hidden"
        animate="visible"
        variants={containerVariants}
      >
        <div className="absolute inset-0">
          <div className="absolute top-0 left-1/4 w-96 h-96 bg-blue-400 rounded-full opacity-10 blur-3xl"></div>
          <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-purple-400 rounded-full opacity-10 blur-3xl"></div>
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div className="text-center" variants={itemVariants}>
            <div className="inline-flex items-center space-x-3 mb-6">
              <Crown className="h-10 w-10 text-blue-600" />
              <span className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Choose Your Plan
              </span>
            </div>

            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-6">
              Pricing that scales
              <span className="block bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                with your ambition
              </span>
            </h1>
            
            <p className="text-xl text-gray-600 mb-12 max-w-3xl mx-auto">
              Start free, upgrade when you need more. All plans include our core AI-powered 
              course creation tools and avatar video generation.
            </p>

            {/* Billing Toggle */}
            <motion.div 
              className="flex items-center justify-center space-x-6 mb-12"
              variants={itemVariants}
            >
              <span className={`text-lg font-medium transition-colors duration-300 ${
                billingCycle === 'monthly' ? 'text-gray-900' : 'text-gray-500'
              }`}>
                Monthly
              </span>
              <div className="relative">
                <Switch
                  checked={billingCycle === 'yearly'}
                  onCheckedChange={(checked) => setBillingCycle(checked ? 'yearly' : 'monthly')}
                  className="data-[state=checked]:bg-blue-600"
                />
              </div>
              <div className="flex items-center space-x-3">
                <span className={`text-lg font-medium transition-colors duration-300 ${
                  billingCycle === 'yearly' ? 'text-gray-900' : 'text-gray-500'
                }`}>
                  Yearly
                </span>
                <Badge variant="outline" className="bg-green-50 text-green-600 border-green-200 text-sm">
                  Save up to 20%
                </Badge>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </motion.section>

      {/* Pricing Cards */}
      <motion.section 
        className="py-16"
        variants={containerVariants}
        initial="hidden" 
        whileInView="visible"
        viewport={{ once: true }}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 lg:gap-8">
            {plans.map((plan, index) => {
              const colorClasses = getColorClasses(plan.color, plan.popular);
              return (
                <motion.div
                  key={plan.id}
                  variants={itemVariants}
                  className="relative"
                >
                  {/* Popular Badge */}
                  {plan.popular && (
                    <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 z-10">
                      <Badge className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-1 text-sm font-semibold shadow-lg">
                        Most Popular
                      </Badge>
                    </div>
                  )}

                  <Card className={`
                    h-full bg-gradient-to-br ${colorClasses.gradient} 
                    border-2 ${colorClasses.border} 
                    shadow-lg hover:shadow-xl transition-all duration-300 
                    ${plan.popular ? 'scale-105' : 'hover:scale-105'}
                  `}>
                    <CardHeader className="text-center pb-6">
                      <div className="mb-4">
                        <CardTitle className="text-2xl font-bold text-gray-900 mb-2">
                          {plan.name}
                        </CardTitle>
                        <CardDescription className="text-gray-600">
                          {plan.description}
                        </CardDescription>
                      </div>

                      {/* Price */}
                      <div className="space-y-2">
                        <div className="flex items-baseline justify-center space-x-1">
                          <span className="text-4xl font-bold text-gray-900">
                            ${getCurrentPrice(plan)}
                          </span>
                          <span className="text-gray-600">/month</span>
                        </div>
                        
                        <AnimatePresence mode="wait">
                          {billingCycle === 'yearly' && getSavings(plan) > 0 && (
                            <motion.div
                              initial={{ opacity: 0, y: -10 }}
                              animate={{ opacity: 1, y: 0 }}
                              exit={{ opacity: 0, y: -10 }}
                              className="flex items-center justify-center space-x-2"
                            >
                              <span className="text-sm text-gray-500 line-through">
                                ${plan.priceMonthly}/month
                              </span>
                              <Badge variant="outline" className="bg-green-50 text-green-600 border-green-200 text-xs">
                                Save {getSavings(plan)}%
                              </Badge>
                            </motion.div>
                          )}
                        </AnimatePresence>
                      </div>
                    </CardHeader>

                    <CardContent className="space-y-4 px-6">
                      {/* Quantified Features */}
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium text-gray-700">Courses</span>
                          <span className="text-sm font-bold text-gray-900">{plan.features.courses}</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium text-gray-700">AI Videos</span>
                          <span className="text-sm font-bold text-gray-900">{plan.features.videos}</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium text-gray-700">Voiceovers</span>
                          <span className="text-sm font-bold text-gray-900">{plan.features.voiceovers}</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium text-gray-700">AI Images</span>
                          <span className="text-sm font-bold text-gray-900">{plan.features.images}</span>
                        </div>
                      </div>

                      <div className="border-t border-gray-200 pt-4">
                        {/* Boolean Features */}
                        <div className="space-y-3">
                          {Object.entries(plan.features).map(([key, value]) => {
                            if (typeof value === 'boolean' && featureLabels[key as keyof typeof featureLabels]) {
                              return (
                                <div key={key} className="flex items-center space-x-3">
                                  {value ? (
                                    <Check className="h-4 w-4 text-green-500 flex-shrink-0" />
                                  ) : (
                                    <X className="h-4 w-4 text-gray-300 flex-shrink-0" />
                                  )}
                                  <span className={`text-sm ${value ? 'text-gray-700' : 'text-gray-400'}`}>
                                    {featureLabels[key as keyof typeof featureLabels]}
                                  </span>
                                </div>
                              );
                            }
                            return null;
                          })}
                        </div>
                      </div>
                    </CardContent>

                    <CardFooter className="pt-6 px-6">
                      <Link href="/auth" className="w-full">
                        <Button className={`w-full h-12 font-semibold transition-all duration-300 ${colorClasses.button}`}>
                          Subscribe
                          <ArrowRight className="ml-2 h-4 w-4" />
                        </Button>
                      </Link>
                    </CardFooter>
                  </Card>
                </motion.div>
              );
            })}
          </div>
        </div>
      </motion.section>

      {/* Feature Comparison Table */}
      <motion.section 
        className="py-20 bg-white/50 backdrop-blur-sm"
        variants={containerVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div className="text-center mb-16" variants={itemVariants}>
            <h2 className="text-4xl font-bold text-gray-900 mb-6">
              Compare all features
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              See everything that's included in each plan to choose the right fit for your needs.
            </p>
          </motion.div>

          <motion.div className="overflow-x-auto" variants={itemVariants}>
            <table className="w-full bg-white rounded-2xl shadow-lg overflow-hidden">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900">Features</th>
                  {plans.map((plan) => (
                    <th key={plan.id} className="px-6 py-4 text-center text-sm font-semibold text-gray-900">
                      {plan.name}
                      {plan.popular && (
                        <Badge className="ml-2 bg-blue-600 text-white text-xs">Popular</Badge>
                      )}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {Object.entries(featureLabels).map(([key, label]) => (
                  <tr key={key} className="hover:bg-gray-50">
                    <td className="px-6 py-4 text-sm font-medium text-gray-900">{label}</td>
                    {plans.map((plan) => (
                      <td key={plan.id} className="px-6 py-4 text-center">
                        {typeof plan.features[key as keyof typeof plan.features] === 'boolean' ? (
                          plan.features[key as keyof typeof plan.features] ? (
                            <Check className="h-5 w-5 text-green-500 mx-auto" />
                          ) : (
                            <X className="h-5 w-5 text-gray-300 mx-auto" />
                          )
                        ) : (
                          <span className="text-sm font-semibold text-gray-900">
                            {plan.features[key as keyof typeof plan.features]}
                          </span>
                        )}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </motion.div>
        </div>
      </motion.section>

      {/* FAQ Section */}
      <motion.section 
        className="py-20"
        variants={containerVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
      >
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div className="text-center mb-16" variants={itemVariants}>
            <h2 className="text-4xl font-bold text-gray-900 mb-6">
              Frequently asked questions
            </h2>
            <p className="text-xl text-gray-600">
              Everything you need to know about our pricing and plans
            </p>
          </motion.div>

          <motion.div className="space-y-8" variants={itemVariants}>
            {[
              {
                question: "Can I change my plan at any time?",
                answer: "Yes, you can upgrade or downgrade your plan at any time. Changes take effect immediately, and we'll prorate any billing differences."
              },
              {
                question: "What happens if I exceed my plan limits?",
                answer: "We'll notify you when you're approaching your limits. You can either upgrade your plan or purchase additional credits as needed."
              },
              {
                question: "Is there a free trial available?",
                answer: "Yes, all plans come with a 14-day free trial. No credit card required to start. You can explore all features during your trial period."
              },
              {
                question: "What payment methods do you accept?",
                answer: "We accept all major credit cards, PayPal, and bank transfers for Enterprise plans. All payments are processed securely through Stripe."
              }
            ].map((faq, index) => (
              <div
                key={index}
                className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100"
              >
                <h3 className="text-lg font-semibold text-gray-900 mb-3">
                  {faq.question}
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  {faq.answer}
                </p>
              </div>
            ))}
          </motion.div>
        </div>
      </motion.section>

      {/* CTA Section */}
      <motion.section 
        className="py-20 bg-gradient-to-r from-blue-600 to-purple-600 text-white"
        variants={containerVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div variants={itemVariants}>
            <h2 className="text-4xl lg:text-5xl font-bold mb-6">
              Ready to transform your course creation?
            </h2>
            <p className="text-xl mb-8 max-w-3xl mx-auto opacity-90">
              Join thousands of educators already using CourseAI to create 
              engaging, professional courses with AI-powered tools.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/auth">
                <Button 
                  size="lg" 
                  variant="secondary"
                  className="bg-white text-blue-600 hover:bg-gray-100 px-8 py-4 text-lg font-semibold"
                >
                  Start Free Trial <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
              <Link href="/product/features">
                <Button 
                  size="lg" 
                  variant="outline"
                  className="border-2 border-white text-white hover:bg-white hover:text-blue-600 px-8 py-4 text-lg font-semibold"
                >
                  View Features
                </Button>
              </Link>
            </div>
          </motion.div>
        </div>
      </motion.section>
    </div>
  );
}