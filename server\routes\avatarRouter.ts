import express, { Request, Response } from 'express';
import { storage } from '../storage';
import { db } from '../db';
import { mediaLibrary } from '@shared/schema';
import { eq } from 'drizzle-orm';

const router = express.Router();

// HeyGen avatar endpoints removed as requested

// Get all available TTS voices (combines from different providers)
router.get('/all-voices', async (req: Request, res: Response) => {
  try {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: 'Unauthorized' });
    }
    
    // Return empty array as HeyGen integration has been removed
    // Other voice providers will be integrated here when needed
    const allVoices: any[] = [];
    
    res.json(allVoices);
  } catch (error: any) {
    console.error('Error fetching all voices:', error);
    res.status(500).json({ 
      message: 'Error fetching voices', 
      error: error.message
    });
  }
});

// Get stock avatar images from the media library
router.get('/stock-avatars', async (req: Request, res: Response) => {
  try {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: 'Unauthorized' });
    }
    
    // Use Drizzle ORM to query media library items of type "image"
    const mediaItems = await db.select()
      .from(mediaLibrary)
      .where(eq(mediaLibrary.type, 'image'));
    
    // Filter items that look like avatars based on name
    const avatarItems = mediaItems.filter(item => 
      item.name.toLowerCase().includes('avatar') || 
      (item.source && item.source === 'avatar')
    );
    
    res.json(avatarItems);
  } catch (error: any) {
    console.error('Error fetching stock avatars:', error);
    res.status(500).json({ 
      message: 'Error fetching stock avatars', 
      error: error.message
    });
  }
});

export default router;