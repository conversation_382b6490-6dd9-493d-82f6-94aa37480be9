import { useState, useEffect } from 'react';
import { useQuery, useMutation } from '@tanstack/react-query';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
import { Trophy, Star, Target, Zap, Award, CheckCircle } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';
import { AchievementNotification } from './AchievementNotification';

interface CourseCreationStep {
  id: string;
  name: string;
  xpReward: number;
  completed: boolean;
}

interface GamificationProgress {
  level: number;
  totalXp: number;
  xpToNextLevel: number;
  totalBadges: number;
  streakDays: number;
}

interface Badge {
  id: number;
  name: string;
  description: string;
  icon: string;
  rarity: string;
  xpReward: number;
}

interface CourseCreationGamificationProps {
  currentStep: string;
  completedSteps: string[];
  courseType: 'traditional' | 'avatar';
  onStepComplete?: (stepId: string, xpEarned: number) => void;
}

const COURSE_CREATION_STEPS: Record<string, CourseCreationStep> = {
  'course-details': {
    id: 'course-details',
    name: 'Course Setup',
    xpReward: 50,
    completed: false
  },
  'course-structure': {
    id: 'course-structure',
    name: 'Structure Planning',
    xpReward: 75,
    completed: false
  },
  'avatar-selection': {
    id: 'avatar-selection',
    name: 'Avatar Creation',
    xpReward: 100,
    completed: false
  },
  'script-creation': {
    id: 'script-creation',
    name: 'Content Creation',
    xpReward: 125,
    completed: false
  },
  'voice-settings': {
    id: 'voice-settings',
    name: 'Voice Configuration',
    xpReward: 75,
    completed: false
  },
  'media-library': {
    id: 'media-library',
    name: 'Media Management',
    xpReward: 100,
    completed: false
  },
  'quiz-creation': {
    id: 'quiz-creation',
    name: 'Assessment Design',
    xpReward: 150,
    completed: false
  },
  'course-generation': {
    id: 'course-generation',
    name: 'Course Generation',
    xpReward: 200,
    completed: false
  },
  'course-publish': {
    id: 'course-publish',
    name: 'Course Publishing',
    xpReward: 250,
    completed: false
  }
};

export function CourseCreationGamification({ 
  currentStep, 
  completedSteps, 
  courseType,
  onStepComplete 
}: CourseCreationGamificationProps) {
  const { toast } = useToast();
  const [sessionXp, setSessionXp] = useState(0);
  const [earnedBadges, setEarnedBadges] = useState<Badge[]>([]);
  const [achievements, setAchievements] = useState<Array<{
    id: string;
    type: 'xp' | 'badge' | 'level' | 'milestone';
    title: string;
    description: string;
    value?: number;
    icon?: string;
    rarity?: string;
  }>>([]);

  // Get user's current gamification progress
  const { data: progress } = useQuery<GamificationProgress>({
    queryKey: ['/api/gamification/progress']
  });

  // Get available badges
  const { data: allBadges } = useQuery<Badge[]>({
    queryKey: ['/api/gamification/badges']
  });

  // Record activity mutation
  const recordActivityMutation = useMutation({
    mutationFn: async (activity: {
      activityType: string;
      xpEarned: number;
      metadata: Record<string, any>;
    }) => {
      const response = await apiRequest('POST', '/api/gamification/activity', activity);
      if (!response.ok) {
        throw new Error('Failed to record activity');
      }
      return response.json();
    },
    onSuccess: (data) => {
      // Update session XP
      setSessionXp(prev => prev + data.xpEarned);
      
      // Add XP achievement notification
      setAchievements(prev => [...prev, {
        id: `xp-${Date.now()}`,
        type: 'xp',
        title: 'XP Earned!',
        description: `Great progress on your course creation`,
        value: data.xpEarned
      }]);
      
      // Check for new badges
      if (data.newBadges && data.newBadges.length > 0) {
        setEarnedBadges(prev => [...prev, ...data.newBadges]);
        data.newBadges.forEach((badge: Badge) => {
          setAchievements(prev => [...prev, {
            id: `badge-${badge.id}-${Date.now()}`,
            type: 'badge',
            title: 'New Badge Earned!',
            description: badge.description || `You've earned the "${badge.name}" badge!`,
            icon: badge.icon,
            rarity: badge.rarity
          }]);
        });
      }
      
      // Check for level up
      if (data.levelUp) {
        setAchievements(prev => [...prev, {
          id: `level-${Date.now()}`,
          type: 'level',
          title: 'Level Up!',
          description: `You've reached level ${data.newLevel}!`
        }]);
      }
    }
  });

  // Track step completion
  useEffect(() => {
    const lastCompletedStep = completedSteps[completedSteps.length - 1];
    if (lastCompletedStep && COURSE_CREATION_STEPS[lastCompletedStep]) {
      const step = COURSE_CREATION_STEPS[lastCompletedStep];
      
      // Record the activity
      recordActivityMutation.mutate({
        activityType: `course_creation_step_${step.id}`,
        xpEarned: step.xpReward,
        metadata: {
          stepName: step.name,
          courseType,
          stepId: step.id
        }
      });

      // Notify parent component
      onStepComplete?.(step.id, step.xpReward);
    }
  }, [completedSteps, courseType]);

  // Calculate total possible XP for this course creation
  const totalPossibleXp = Object.values(COURSE_CREATION_STEPS).reduce(
    (sum, step) => sum + step.xpReward, 
    0
  );

  // Calculate current progress XP
  const currentProgressXp = completedSteps.reduce((sum, stepId) => {
    const step = COURSE_CREATION_STEPS[stepId];
    return sum + (step?.xpReward || 0);
  }, 0);

  const progressPercentage = (currentProgressXp / totalPossibleXp) * 100;

  return (
    <>
      <AchievementNotification 
        achievements={achievements}
        onClose={(achievementId) => {
          setAchievements(prev => prev.filter(a => a.id !== achievementId));
        }}
      />
      
      <Card className="mb-6 bg-gradient-to-r from-purple-50 to-blue-50 border-purple-200">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Trophy className="h-5 w-5 text-purple-600" />
            Course Creation Progress
          </CardTitle>
        </CardHeader>
      <CardContent className="space-y-4">
        {/* Current Level & Stats */}
        {progress && (
          <div className="grid grid-cols-3 gap-4 text-center">
            <div className="space-y-1">
              <div className="flex items-center justify-center">
                <Star className="h-4 w-4 text-yellow-500 mr-1" />
                <span className="font-semibold">Level {progress.level}</span>
              </div>
              <p className="text-xs text-muted-foreground">Current Level</p>
            </div>
            <div className="space-y-1">
              <div className="flex items-center justify-center">
                <Award className="h-4 w-4 text-purple-500 mr-1" />
                <span className="font-semibold">{progress.totalBadges}</span>
              </div>
              <p className="text-xs text-muted-foreground">Badges Earned</p>
            </div>
            <div className="space-y-1">
              <div className="flex items-center justify-center">
                <Target className="h-4 w-4 text-blue-500 mr-1" />
                <span className="font-semibold">{progress.streakDays}</span>
              </div>
              <p className="text-xs text-muted-foreground">Day Streak</p>
            </div>
          </div>
        )}

        {/* Step Progress */}
        <div className="space-y-2">
          <h4 className="font-medium text-sm">Creation Steps</h4>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
            {Object.values(COURSE_CREATION_STEPS).map((step) => {
              const isCompleted = completedSteps.includes(step.id);
              const isCurrent = currentStep === step.id;
              
              return (
                <div
                  key={step.id}
                  className={cn(
                    'flex items-center gap-2 p-2 rounded-md text-xs',
                    isCompleted 
                      ? 'bg-green-100 text-green-800' 
                      : isCurrent 
                      ? 'bg-blue-100 text-blue-800' 
                      : 'bg-gray-100 text-gray-600'
                  )}
                >
                  {isCompleted ? (
                    <CheckCircle className="h-3 w-3" />
                  ) : (
                    <div className={cn(
                      'h-3 w-3 rounded-full border-2',
                      isCurrent ? 'border-blue-500' : 'border-gray-300'
                    )} />
                  )}
                  <span className="flex-1">{step.name}</span>
                  <Badge variant="outline" className="text-xs px-1 py-0">
                    +{step.xpReward} XP
                  </Badge>
                </div>
              );
            })}
          </div>
        </div>

        {/* Recently Earned Badges */}
        {earnedBadges.length > 0 && (
          <div className="space-y-2">
            <h4 className="font-medium text-sm">New Badges Earned!</h4>
            <div className="flex flex-wrap gap-2">
              {earnedBadges.map((badge) => (
                <Badge
                  key={badge.id}
                  variant="secondary"
                  className="flex items-center gap-1"
                >
                  <span className="text-xs">{badge.icon}</span>
                  {badge.name}
                </Badge>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
    </>
  );
}