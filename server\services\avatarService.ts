
import runpodService from './runpodService';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';

export async function generateTalkingAvatar(
  text: string,
  imagePath: string,
  options = {
    voice: 'default',
    emotion: 'neutral',
    enhance: true,
    service: 'kokoro' as 'kokoro' | 'coqui'
  }
) {
  // Validate RunPod configuration
  if (!runpodService.isConfigured()) {
    throw new Error('RunPod service is not properly configured');
  }

  // Validate specific endpoints
  if (!runpodService.isEndpointConfigured('sadtalker')) {
    throw new Error('SadTalker endpoint not configured');
  }

  if (!runpodService.isEndpointConfigured(options.service)) {
    throw new Error(`${options.service} TTS endpoint not configured`);
  }
  try {
    // First generate speech using Kokoro
    const speech = await runpodService.generateKokoroSpeech({
      text,
      voice: options.voice
    });

    // Generate temporary paths
    const audioPath = path.join('temp', `${uuidv4()}_audio.mp3`);
    
    // Create talking video
    const video = await runpodService.generateTalkingVideo({
      audioPath: speech.audioUrl,
      imagePath,
      enhanceMode: options.enhance,
      emotion: options.emotion
    });

    return {
      videoUrl: video.url,
      audioUrl: speech.audioUrl
    };
  } catch (error) {
    console.error('Error generating talking avatar:', error);
    throw error;
  }
}

export default {
  generateTalkingAvatar
};
