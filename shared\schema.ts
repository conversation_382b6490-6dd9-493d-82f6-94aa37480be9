import { pgTable, text, serial, integer, boolean, timestamp, jsonb, primaryKey, numeric, uniqueIndex, index, varchar } from "drizzle-orm/pg-core";
import { relations } from "drizzle-orm";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";
import { sql } from "drizzle-orm";
// Import AI credits tables and schemas
import { aiCredits, aiUsageHistory, insertAiCreditsSchema, insertAiUsageHistorySchema } from "./ai-credits-schema";
// Re-export AI credits tables and schemas
export { aiCredits, aiUsageHistory, insertAiCreditsSchema, insertAiUsageHistorySchema };

export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  username: text("username").notNull().unique(),
  email: text("email").notNull().unique(),
  password: text("password").notNull(),
  name: text("name").notNull(),
  plan: text("plan").default("free"),
  role: text("role").default("user"),
  emailVerified: boolean("email_verified").default(false),
  verificationToken: text("verification_token"),
  resetPasswordToken: text("reset_password_token"),
  resetPasswordExpiry: timestamp("reset_password_expiry"),
  avatarUrl: text("avatar_url"),
  stripeCustomerId: text("stripe_customer_id"),
  stripeSubscriptionId: text("stripe_subscription_id"),
  preferences: jsonb("preferences").default({}), // User preferences including HeyGen settings
  createdAt: timestamp("created_at").defaultNow(),
});

export const courses = pgTable("courses", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").notNull(),
  title: text("title").notNull(),
  description: text("description").notNull(),
  targetAudience: text("target_audience"),
  category: text("category").notNull(),
  status: text("status").notNull().default("draft"),
  thumbnailUrl: text("thumbnail_url"),
  completion: integer("completion").default(0),
  lessonsCount: integer("lessons_count").default(0),
  structure: jsonb("structure"), // Store original AI-generated structure
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// Course drafts for real-time auto-save
export const courseDrafts = pgTable("course_drafts", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").notNull(),
  draftId: text("draft_id").notNull(), // Unique identifier for the draft session
  courseDetails: jsonb("course_details").notNull(), // Title, description, category, etc.
  courseStructure: jsonb("course_structure"), // Generated module/lesson structure
  courseScripts: jsonb("course_scripts"), // All scripts for modules and lessons
  mediaAttachments: jsonb("media_attachments"), // Media attached to lessons/modules
  quizData: jsonb("quiz_data"), // Quiz information
  publishData: jsonb("publish_data"), // Publishing settings
  stepProgress: integer("step_progress").default(0), // Current step in workflow
  completedSteps: jsonb("completed_steps").default('[]'), // Array of completed step indices
  generatedAudioFiles: jsonb("generated_audio_files"), // Voice generation results
  lastAutoSave: timestamp("last_auto_save").defaultNow(),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
}, (table) => ({
  userDraftIdx: uniqueIndex("user_draft_idx").on(table.userId, table.draftId),
}));

export const modules = pgTable("modules", {
  id: serial("id").primaryKey(),
  courseId: integer("course_id").notNull().references(() => courses.id, { onDelete: "cascade" }),
  title: text("title").notNull(),
  description: text("description"),
  script: text("script"), // Adding script directly to modules
  order: integer("order").notNull(),
  status: text("status").notNull().default("draft"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

export const lessons = pgTable("lessons", {
  id: serial("id").primaryKey(),
  courseId: integer("course_id").notNull().references(() => courses.id, { onDelete: "cascade" }),
  moduleId: integer("module_id").notNull().references(() => modules.id, { onDelete: "cascade" }),
  title: text("title").notNull(),
  description: text("description"),
  script: text("script"),
  videoUrl: text("video_url"),
  voiceoverId: text("voiceover_id"),
  duration: integer("duration").default(0),
  order: integer("order").notNull(),
  status: text("status").notNull().default("draft"),
  // Micro-learning fields
  microLearningEnabled: boolean("micro_learning_enabled").default(false),
  microLearningSegmentCount: integer("micro_learning_segment_count").default(1),
  microLearningBreakInterval: integer("micro_learning_break_interval").default(300), // seconds
  microLearningBreakDuration: integer("micro_learning_break_duration").default(60), // seconds
});

export const mediaLibrary = pgTable("media_library", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").notNull(),
  name: text("name").notNull(),
  type: text("type").notNull(), // image, document, spreadsheet, audio, text, formula, video
  mimeType: text("mime_type").notNull(),
  fileSize: integer("file_size").notNull(),
  url: text("url").notNull(),
  originalFilename: text("original_filename"),
  duration: integer("duration"), // For audio/video files (in seconds)
  courseId: integer("course_id"), // Optional: associate with a specific course
  lessonId: integer("lesson_id"), // Optional: associate with a specific lesson
  createdAt: timestamp("created_at").defaultNow(),
  source: text("source").default("upload"), // 'upload', 'stock_photo', 'stock_video', etc.
  sourceId: text("source_id"), // For external services, store the original ID
  sourceData: jsonb("source_data"), // Additional metadata from the source
  tags: text("tags").array(), // Search tags for better discovery
  category: text("category"), // Category classification
  isPublic: boolean("is_public").default(true), // Whether available to all users
  downloadCount: integer("download_count").default(0), // Usage tracking
});

// Stock media cache table to store search results and avoid repeated API calls
export const stockMediaCache = pgTable("stock_media_cache", {
  id: serial("id").primaryKey(),
  query: text("query").notNull(),
  type: text("type").notNull(), // 'photo' or 'video'
  provider: text("provider").notNull(), // API provider identifier
  results: jsonb("results").notNull(), // Cached search results
  totalResults: integer("total_results").default(0),
  page: integer("page").default(1),
  expiresAt: timestamp("expires_at").notNull(), // Cache expiration
  createdAt: timestamp("created_at").defaultNow(),
}, (table) => ({
  queryTypeIdx: index("query_type_idx").on(table.query, table.type, table.provider),
}));

export const templates = pgTable("templates", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  description: text("description").notNull(),
  icon: text("icon").notNull(),
  type: text("type").notNull(),
  category: text("category"),
  structure: jsonb("structure"),
});

// Template history for tracking user interactions with templates
export const templateHistory = pgTable("template_history", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  templateId: integer("template_id").notNull().references(() => templates.id),
  name: text("name").notNull(),
  prompt: text("prompt").notNull(),
  result: text("result").notNull(),
  aiCreditsUsed: integer("ai_credits_used").notNull().default(0),
  favorited: boolean("favorited").notNull().default(false),
  createdAt: timestamp("created_at").notNull().defaultNow(),
});

export const platformIntegrations = pgTable("platform_integrations", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  slug: text("slug").notNull().unique(),
  category: text("category").notNull(), // "social_media", "education", "video", "communication", etc.
  description: text("description").notNull(),
  iconUrl: text("icon_url").notNull(),
  authType: text("auth_type").notNull(), // "oauth", "api_key", "username_password", etc.
  authUrl: text("auth_url"), // For OAuth flows
  tokenUrl: text("token_url"), // For OAuth flows
  apiBaseUrl: text("api_base_url"),
  enabled: boolean("enabled").default(true),
  priority: integer("priority").default(0), // For ordering
  features: jsonb("features").default([]), // Array of supported features
  createdAt: timestamp("created_at").defaultNow()
});

export const integrations = pgTable("integrations", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").notNull(),
  platformId: integer("platform_id").notNull().references(() => platformIntegrations.id),
  platform: text("platform").notNull(), // Keeping this for backward compatibility
  platformUserId: text("platform_user_id"),
  accessToken: text("access_token"),
  refreshToken: text("refresh_token"),
  tokenExpiry: timestamp("token_expiry"),
  config: jsonb("config"),
  status: text("status").notNull().default("active"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// Tables for tracking micro-learning progress
export const microLearningSegments = pgTable("micro_learning_segments", {
  id: serial("id").primaryKey(),
  lessonId: integer("lesson_id").notNull().references(() => lessons.id, { onDelete: "cascade" }),
  title: text("title").notNull(),
  content: text("content"),
  startTime: numeric("start_time").notNull(),
  endTime: numeric("end_time").notNull(),
  order: integer("order").notNull(),
  createdAt: timestamp("created_at").defaultNow(),
});

export const microLearningKnowledgeChecks = pgTable("micro_learning_knowledge_checks", {
  id: serial("id").primaryKey(),
  lessonId: integer("lesson_id").notNull().references(() => lessons.id, { onDelete: "cascade" }),
  segmentId: integer("segment_id").references(() => microLearningSegments.id, { onDelete: "cascade" }),
  question: text("question").notNull(),
  options: jsonb("options").notNull(),
  correctAnswer: integer("correct_answer").notNull(),
  explanation: text("explanation"),
  createdAt: timestamp("created_at").defaultNow(),
});

export const microLearningUserProgress = pgTable("micro_learning_user_progress", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  lessonId: integer("lesson_id").notNull().references(() => lessons.id, { onDelete: "cascade" }),
  progress: numeric("progress").default("0").notNull(),
  completed: boolean("completed").default(false),
  lastPosition: numeric("last_position").default("0"),
  completedSegments: jsonb("completed_segments").default([]),
  completedAt: timestamp("completed_at"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

export const userKnowledgeCheckResponses = pgTable("user_knowledge_check_responses", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  knowledgeCheckId: integer("knowledge_check_id").notNull().references(() => microLearningKnowledgeChecks.id, { onDelete: "cascade" }),
  userAnswer: integer("user_answer").notNull(),
  correct: boolean("correct").notNull(),
  createdAt: timestamp("created_at").defaultNow(),
});

export const userStats = pgTable("user_stats", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").notNull().unique(),
  activeCourses: integer("active_courses").default(0),
  publishedCourses: integer("published_courses").default(0),
  aiCredits: integer("ai_credits").default(500),
  storageUsed: integer("storage_used").default(0),
  storageLimit: integer("storage_limit").default(1000),
  // Gamification stats
  totalBadges: integer("total_badges").default(0),
  totalXp: integer("total_xp").default(0),
  level: integer("level").default(1),
  streakDays: integer("streak_days").default(0),
  longestStreak: integer("longest_streak").default(0),
  lastActivityDate: timestamp("last_activity_date"),
});

// Badge definitions table
export const badges = pgTable("badges", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  description: text("description").notNull(),
  icon: text("icon").notNull(), // SVG or emoji
  category: text("category").notNull(), // learning, streak, achievement, milestone
  rarity: text("rarity").notNull().default("common"), // common, rare, epic, legendary
  xpReward: integer("xp_reward").default(0),
  condition: jsonb("condition").notNull(), // Criteria for earning the badge
  isActive: boolean("is_active").default(true),
  createdAt: timestamp("created_at").defaultNow(),
});

// User badges - tracks which badges users have earned
export const userBadges = pgTable("user_badges", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  badgeId: integer("badge_id").notNull().references(() => badges.id, { onDelete: "cascade" }),
  earnedAt: timestamp("earned_at").defaultNow(),
  progress: jsonb("progress").default({}), // Track progress towards badge completion
  isDisplayed: boolean("is_displayed").default(true), // Whether user displays this badge
}, (table) => {
  return {
    userBadgeUnique: uniqueIndex("user_badge_unique").on(table.userId, table.badgeId),
  };
});

// Learning activities tracking for gamification
export const learningActivities = pgTable("learning_activities", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  activityType: text("activity_type").notNull(), // lesson_completed, course_started, quiz_passed, etc.
  courseId: integer("course_id").references(() => courses.id, { onDelete: "cascade" }),
  lessonId: integer("lesson_id").references(() => lessons.id, { onDelete: "cascade" }),
  xpEarned: integer("xp_earned").default(0),
  metadata: jsonb("metadata").default({}), // Additional activity data
  createdAt: timestamp("created_at").defaultNow(),
}, (table) => {
  return {
    userActivityIdx: index("user_activity_idx").on(table.userId, table.createdAt),
  };
});

// Daily challenges for engagement
export const dailyChallenges = pgTable("daily_challenges", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  description: text("description").notNull(),
  icon: text("icon").notNull(),
  targetType: text("target_type").notNull(), // lessons_completed, time_spent, courses_started
  targetValue: integer("target_value").notNull(),
  xpReward: integer("xp_reward").default(50),
  isActive: boolean("is_active").default(true),
  validFrom: timestamp("valid_from").defaultNow(),
  validUntil: timestamp("valid_until"),
});

// User challenge progress
export const userChallengeProgress = pgTable("user_challenge_progress", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  challengeId: integer("challenge_id").notNull().references(() => dailyChallenges.id, { onDelete: "cascade" }),
  progress: integer("progress").default(0),
  completed: boolean("completed").default(false),
  completedAt: timestamp("completed_at"),
  date: timestamp("date").defaultNow(),
}, (table) => {
  return {
    userChallengeUnique: uniqueIndex("user_challenge_unique").on(table.userId, table.challengeId, table.date),
  };
});

export const socialAccounts = pgTable("social_accounts", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").notNull(),
  provider: text("provider").notNull(),
  providerId: text("provider_id").notNull(),
  accessToken: text("access_token"),
  refreshToken: text("refresh_token"),
  createdAt: timestamp("created_at").defaultNow(),
});

export const teams = pgTable("teams", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  description: text("description"),
  ownerId: integer("owner_id").notNull(), // User who created/owns the team
  avatarUrl: text("avatar_url"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

export const teamMembers = pgTable("team_members", {
  teamId: integer("team_id").notNull(),
  userId: integer("user_id").notNull(),
  role: text("role").notNull().default("member"), // owner, admin, member
  status: text("status").notNull().default("active"), // active, inactive, pending
  joinedAt: timestamp("joined_at").defaultNow(),
}, (table) => {
  return {
    pk: primaryKey({ columns: [table.teamId, table.userId] }),
  };
});

export const courseCollaborators = pgTable("course_collaborators", {
  courseId: integer("course_id").notNull(),
  userId: integer("user_id").notNull(),
  role: text("role").notNull().default("editor"), // owner, editor, viewer
  canEdit: boolean("can_edit").default(true),
  addedAt: timestamp("added_at").defaultNow(),
  addedById: integer("added_by_id").notNull(), // User who added this collaborator
}, (table) => {
  return {
    pk: primaryKey({ columns: [table.courseId, table.userId] }),
  };
});

export const teamCourses = pgTable("team_courses", {
  teamId: integer("team_id").notNull(),
  courseId: integer("course_id").notNull(),
  addedAt: timestamp("added_at").defaultNow(),
  addedById: integer("added_by_id").notNull(), // User who added this course to the team
}, (table) => {
  return {
    pk: primaryKey({ columns: [table.teamId, table.courseId] }),
  };
});

// Insert schemas
export const insertUserSchema = createInsertSchema(users).pick({
  username: true,
  email: true,
  password: true,
  name: true,
  plan: true,
  role: true,
  avatarUrl: true,
  stripeCustomerId: true,
  stripeSubscriptionId: true,
  preferences: true,
});

export const insertSocialAccountSchema = createInsertSchema(socialAccounts).pick({
  userId: true,
  provider: true,
  providerId: true,
  accessToken: true,
  refreshToken: true,
});

export const insertCourseSchema = createInsertSchema(courses).pick({
  userId: true,
  title: true,
  description: true,
  targetAudience: true,
  category: true,
  status: true,
  thumbnailUrl: true,
  structure: true,
});

export const insertModuleSchema = createInsertSchema(modules).pick({
  courseId: true,
  title: true,
  description: true,
  order: true,
  status: true,
});

export const insertLessonSchema = createInsertSchema(lessons).pick({
  courseId: true,
  moduleId: true, 
  title: true,
  description: true,
  script: true,
  videoUrl: true,
  voiceoverId: true,
  order: true,
  status: true,
  microLearningEnabled: true,
  microLearningSegmentCount: true,
  microLearningBreakInterval: true,
  microLearningBreakDuration: true,
});

export const insertMediaSchema = createInsertSchema(mediaLibrary).pick({
  userId: true,
  name: true,
  type: true,
  mimeType: true,
  fileSize: true,
  url: true,
  originalFilename: true,
  duration: true,
  courseId: true,
  lessonId: true,
  source: true,
  sourceId: true,
  sourceData: true,
});

// Course drafts schema and types
export const insertCourseDraftSchema = createInsertSchema(courseDrafts).pick({
  userId: true,
  draftId: true,
  courseDetails: true,
  courseStructure: true,
  courseScripts: true,
  mediaAttachments: true,
  quizData: true,
  publishData: true,
  stepProgress: true,
  completedSteps: true,
  generatedAudioFiles: true,
});

export type CourseDraft = typeof courseDrafts.$inferSelect;
export type InsertCourseDraft = z.infer<typeof insertCourseDraftSchema>;

// Platform integrations insert schema
export const insertPlatformIntegrationSchema = createInsertSchema(platformIntegrations).pick({
  name: true,
  slug: true,
  category: true,
  description: true,
  iconUrl: true,
  authType: true,
  authUrl: true,
  tokenUrl: true,
  apiBaseUrl: true,
  enabled: true,
  priority: true,
  features: true,
});

// User integration connection
export const insertIntegrationSchema = createInsertSchema(integrations);

export const insertTemplateHistorySchema = createInsertSchema(templateHistory).pick({
  userId: true,
  templateId: true,
  name: true,
  prompt: true,
  result: true,
  aiCreditsUsed: true,
  favorited: true,
});

export const insertUserStatsSchema = createInsertSchema(userStats).pick({
  userId: true,
  activeCourses: true,
  publishedCourses: true,
  aiCredits: true,
  storageUsed: true,
  storageLimit: true,
  totalBadges: true,
  totalXp: true,
  level: true,
  streakDays: true,
  longestStreak: true,
  lastActivityDate: true,
});

// Gamification insert schemas
export const insertBadgeSchema = createInsertSchema(badges).pick({
  name: true,
  description: true,
  icon: true,
  category: true,
  rarity: true,
  xpReward: true,
  condition: true,
  isActive: true,
});

export const insertUserBadgeSchema = createInsertSchema(userBadges).pick({
  userId: true,
  badgeId: true,
  progress: true,
  isDisplayed: true,
});

export const insertLearningActivitySchema = createInsertSchema(learningActivities).pick({
  userId: true,
  activityType: true,
  courseId: true,
  lessonId: true,
  xpEarned: true,
  metadata: true,
});

export const insertDailyChallengeSchema = createInsertSchema(dailyChallenges).pick({
  name: true,
  description: true,
  icon: true,
  targetType: true,
  targetValue: true,
  xpReward: true,
  isActive: true,
  validFrom: true,
  validUntil: true,
});

export const insertUserChallengeProgressSchema = createInsertSchema(userChallengeProgress).pick({
  userId: true,
  challengeId: true,
  progress: true,
  completed: true,
  completedAt: true,
  date: true,
});

export const insertTeamSchema = createInsertSchema(teams).pick({
  name: true,
  description: true,
  ownerId: true,
  avatarUrl: true,
});

export const insertTeamMemberSchema = createInsertSchema(teamMembers).pick({
  teamId: true,
  userId: true,
  role: true,
  status: true,
});

export const insertCourseCollaboratorSchema = createInsertSchema(courseCollaborators).pick({
  courseId: true,
  userId: true,
  role: true,
  canEdit: true,
  addedById: true,
});

export const insertTeamCourseSchema = createInsertSchema(teamCourses).pick({
  teamId: true,
  courseId: true,
  addedById: true,
});

// Types
export type InsertUser = z.infer<typeof insertUserSchema>;
export type InsertCourse = z.infer<typeof insertCourseSchema>;
export type InsertModule = z.infer<typeof insertModuleSchema>;
export type InsertLesson = z.infer<typeof insertLessonSchema>;
export type InsertMedia = z.infer<typeof insertMediaSchema>;
export type InsertPlatformIntegration = z.infer<typeof insertPlatformIntegrationSchema>;
export type InsertIntegration = z.infer<typeof insertIntegrationSchema>;
export type InsertUserStats = z.infer<typeof insertUserStatsSchema>;
export type InsertTemplateHistory = z.infer<typeof insertTemplateHistorySchema>;
export type InsertTeam = z.infer<typeof insertTeamSchema>;
export type InsertTeamMember = z.infer<typeof insertTeamMemberSchema>;
export type InsertCourseCollaborator = z.infer<typeof insertCourseCollaboratorSchema>;
export type InsertTeamCourse = z.infer<typeof insertTeamCourseSchema>;

// Gamification types
export type Badge = typeof badges.$inferSelect;
export type UserBadge = typeof userBadges.$inferSelect;
export type LearningActivity = typeof learningActivities.$inferSelect;
export type DailyChallenge = typeof dailyChallenges.$inferSelect;
export type UserChallengeProgress = typeof userChallengeProgress.$inferSelect;

export type InsertBadge = z.infer<typeof insertBadgeSchema>;
export type InsertUserBadge = z.infer<typeof insertUserBadgeSchema>;
export type InsertLearningActivity = z.infer<typeof insertLearningActivitySchema>;
export type InsertDailyChallenge = z.infer<typeof insertDailyChallengeSchema>;
export type InsertUserChallengeProgress = z.infer<typeof insertUserChallengeProgressSchema>;

// Chatbot tables
export const chatSessions = pgTable("chat_sessions", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  courseId: integer("course_id").references(() => courses.id, { onDelete: "cascade" }),
  lessonId: integer("lesson_id").references(() => lessons.id),
  title: text("title"),
  status: text("status").notNull().default("active"), // active, archived
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
});

export const chatMessages = pgTable("chat_messages", {
  id: serial("id").primaryKey(),
  sessionId: integer("session_id").notNull().references(() => chatSessions.id, { onDelete: "cascade" }),
  role: text("role").notNull(), // user, assistant, system
  content: text("content").notNull(),
  metadata: jsonb("metadata").default({}), // Store any additional data like references, citations, etc.
  aiCreditsUsed: integer("ai_credits_used").default(0),
  createdAt: timestamp("created_at").notNull().defaultNow(),
});

// Insert schemas for chat
export const insertChatSessionSchema = createInsertSchema(chatSessions).pick({
  userId: true,
  courseId: true,
  lessonId: true,
  title: true,
  status: true,
});

export const insertChatMessageSchema = createInsertSchema(chatMessages).pick({
  sessionId: true,
  role: true,
  content: true,
  metadata: true,
  aiCreditsUsed: true,
});

// Types for chat
export type InsertChatSession = z.infer<typeof insertChatSessionSchema>;
export type InsertChatMessage = z.infer<typeof insertChatMessageSchema>;

// Analytics tables
export const courseAnalytics = pgTable("course_analytics", {
  id: serial("id").primaryKey(),
  courseId: integer("course_id").notNull().references(() => courses.id, { onDelete: "cascade" }),
  totalViews: integer("total_views").notNull().default(0),
  totalCompletions: integer("total_completions").notNull().default(0),
  averageRating: numeric("average_rating", { precision: 3, scale: 2 }).default("0"),
  ratingsCount: integer("ratings_count").notNull().default(0),
  studentsEnrolled: integer("students_enrolled").notNull().default(0),
  averageCompletionTime: integer("average_completion_time").default(0), // in minutes
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow()
});

export const lessonAnalytics = pgTable("lesson_analytics", {
  id: serial("id").primaryKey(),
  lessonId: integer("lesson_id").notNull().references(() => lessons.id, { onDelete: "cascade" }),
  totalViews: integer("total_views").notNull().default(0),
  completionRate: numeric("completion_rate", { precision: 5, scale: 2 }).default("0"),
  averageTimeSpent: integer("average_time_spent").default(0), // in seconds
  engagementScore: numeric("engagement_score", { precision: 5, scale: 2 }).default("0"),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow()
});

export const userCourseProgress = pgTable("user_course_progress", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  courseId: integer("course_id").notNull().references(() => courses.id, { onDelete: "cascade" }),
  enrolledAt: timestamp("enrolled_at").defaultNow(),
  lastAccessedAt: timestamp("last_accessed_at").defaultNow(),
  completionPercentage: numeric("completion_percentage").default("0"),
  isCompleted: boolean("is_completed").default(false),
  timeSpent: integer("time_spent").default(0), // in seconds
  rating: integer("rating"),
  feedback: text("feedback")
});

export const userLessonProgressRecord = pgTable("user_lesson_progress", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  lessonId: integer("lesson_id").notNull().references(() => lessons.id, { onDelete: "cascade" }),
  courseId: integer("course_id").notNull().references(() => courses.id, { onDelete: "cascade" }),
  startedAt: timestamp("started_at").notNull().defaultNow(),
  lastAccessedAt: timestamp("last_accessed_at").notNull().defaultNow(),
  timeSpent: integer("time_spent").notNull().default(0), // in seconds
  isCompleted: boolean("is_completed").notNull().default(false),
  completedAt: timestamp("completed_at"),
  // Track video playback position
  videoPosition: integer("video_position").default(0) // in seconds
});

// Publishing schema for tracking course publications to external platforms
export const publishing = pgTable("publishing", {
  id: serial("id").primaryKey(),
  courseId: integer("course_id").notNull().references(() => courses.id, { onDelete: "cascade" }),
  platform: text("platform").notNull(),
  status: text("status").notNull().default("processing"),
  platformUrl: text("platform_url"),
  analyticsData: jsonb("analytics_data"),
  publishedAt: timestamp("published_at").defaultNow(),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// API Keys schema to store user's API keys for external services
export const userApiKeys = pgTable("user_api_keys", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  service: text("service").notNull(), // openai, elevenlabs, google, etc.
  apiKey: text("api_key").notNull(),
  isVerified: boolean("is_verified").default(false),
  isActive: boolean("is_active").default(true),
  lastVerified: timestamp("last_verified"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

export const analyticsEvents = pgTable("analytics_events", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id, { onDelete: "set null" }),
  courseId: integer("course_id").references(() => courses.id, { onDelete: "set null" }),
  lessonId: integer("lesson_id").references(() => lessons.id, { onDelete: "set null" }),
  eventType: text("event_type").notNull(), // e.g., "view", "complete", "enroll", "rate"
  eventData: jsonb("event_data").$type<Record<string, any>>().default({}),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  ipAddress: text("ip_address").default(""),
  userAgent: text("user_agent").default("")
});

export const aiGeneratedImages = pgTable("ai_generated_images", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  prompt: text("prompt").notNull(),
  negativePrompt: text("negative_prompt"),
  imageUrl: text("image_url").notNull(),
  fileName: text("file_name").notNull(),
  width: integer("width").notNull(),
  height: integer("height").notNull(),
  seed: integer("seed"),
  stylePreset: text("style_preset"),
  engineId: text("engine_id"),
  mediaId: integer("media_id").references(() => mediaLibrary.id, { onDelete: "set null" }),
  courseId: integer("course_id").references(() => courses.id, { onDelete: "set null" }),
  lessonId: integer("lesson_id").references(() => lessons.id, { onDelete: "set null" }),
  createdAt: timestamp("created_at").notNull().defaultNow(),
});

// Table for storing generated or imported videos
export const generatedVideos = pgTable("generated_videos", {
  id: text("id").primaryKey(), // Custom ID format (free-pexels-123, free-pixabay-456, or uuid for AI)
  userId: integer("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  prompt: text("prompt").notNull(), // Script for AI videos or description for free videos
  processingStatus: text("processing_status").notNull().default("processing"),
  duration: integer("duration").default(0), // in seconds
  estimatedCompletionTime: integer("estimated_completion_time").notNull(), // Unix timestamp
  videoUrl: text("video_url"),
  thumbnailUrl: text("thumbnail_url"),
  mediaId: integer("media_id").references(() => mediaLibrary.id, { onDelete: "set null" }),
  courseId: integer("course_id").references(() => courses.id, { onDelete: "set null" }),
  lessonId: integer("lesson_id").references(() => lessons.id, { onDelete: "set null" }),
  error: text("error"),
  animationKeyframes: jsonb("animation_keyframes"),
  createdAt: timestamp("created_at").notNull().defaultNow(),
});

// Quiz tables for assessment and learning
export const quizzes = pgTable("quizzes", {
  id: serial("id").primaryKey(),
  courseId: integer("course_id").notNull().references(() => courses.id, { onDelete: "cascade" }),
  moduleId: integer("module_id").references(() => modules.id, { onDelete: "set null" }),
  lessonId: integer("lesson_id").references(() => lessons.id, { onDelete: "set null" }),
  title: text("title").notNull(),
  description: text("description"),
  instructions: text("instructions"),
  passingScore: integer("passing_score").default(70), // percentage needed to pass
  timeLimit: integer("time_limit"), // in seconds, null means no time limit
  randomizeQuestions: boolean("randomize_questions").default(false),
  showCorrectAnswers: boolean("show_correct_answers").default(true),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
});

export const quizQuestions = pgTable("quiz_questions", {
  id: serial("id").primaryKey(),
  quizId: integer("quiz_id").notNull().references(() => quizzes.id, { onDelete: "cascade" }),
  questionType: text("question_type").notNull(), // multiple-choice, true-false, short-answer
  questionText: text("question_text").notNull(),
  explanation: text("explanation"),
  points: integer("points").default(1),
  order: integer("order").notNull(),
  imageUrl: text("image_url"),
  metadata: jsonb("metadata").default({}), // For additional type-specific settings
});

export const quizAnswers = pgTable("quiz_answers", {
  id: serial("id").primaryKey(),
  questionId: integer("question_id").notNull().references(() => quizQuestions.id, { onDelete: "cascade" }),
  answerText: text("answer_text").notNull(),
  isCorrect: boolean("is_correct").notNull().default(false),
  explanation: text("explanation"),
  order: integer("order").notNull(),
});

export const quizAttempts = pgTable("quiz_attempts", {
  id: serial("id").primaryKey(),
  quizId: integer("quiz_id").notNull().references(() => quizzes.id, { onDelete: "cascade" }),
  userId: integer("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  score: integer("score").notNull(),
  maxPossibleScore: integer("max_possible_score").notNull(),
  percentageScore: numeric("percentage_score", { precision: 5, scale: 2 }).notNull(),
  passed: boolean("passed").notNull(),
  startedAt: timestamp("started_at").notNull().defaultNow(),
  completedAt: timestamp("completed_at"),
  timeSpent: integer("time_spent"), // in seconds
});

export const quizFlashcards = pgTable("quiz_flashcards", {
  id: serial("id").primaryKey(),
  quizId: integer("quiz_id").notNull().references(() => quizzes.id, { onDelete: "cascade" }),
  front: text("front").notNull(),
  back: text("back").notNull(),
  order: integer("order").notNull(),
  category: text("category"),
  imageUrl: text("image_url"),
});

// Mini Courses schema
export const miniCourses = pgTable("mini_courses", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  title: text("title").notNull(),
  description: text("description").notNull(),
  duration: integer("duration"), // in seconds
  thumbnailUrl: text("thumbnail_url"),
  scriptId: integer("script_id").references(() => mediaLibrary.id),
  audioId: integer("audio_id").references(() => mediaLibrary.id),
  videoId: integer("video_id").references(() => mediaLibrary.id),
  quizId: integer("quiz_id").references(() => quizzes.id),
  status: text("status").default("draft").notNull(), // draft, published
  difficultyLevel: text("difficulty_level").default("beginner").notNull(),
  targetAudience: text("target_audience"),
  includesResources: boolean("includes_resources").default(false),
  contentSections: jsonb("content_sections"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
  publishedAt: timestamp("published_at"),
});

// Insert schemas for analytics tables
export const insertCourseAnalyticsSchema = createInsertSchema(courseAnalytics).pick({
  courseId: true,
  totalViews: true,
  totalCompletions: true,
  averageRating: true,
  ratingsCount: true,
  studentsEnrolled: true,
  averageCompletionTime: true
});

export const insertLessonAnalyticsSchema = createInsertSchema(lessonAnalytics).pick({
  lessonId: true,
  totalViews: true,
  completionRate: true,
  averageTimeSpent: true,
  engagementScore: true
});

export const insertUserCourseProgressSchema = createInsertSchema(userCourseProgress).pick({
  userId: true,
  courseId: true,
  enrolledAt: true,
  lastAccessedAt: true,
  completionPercentage: true,
  isCompleted: true,
  timeSpent: true,
  rating: true,
  feedback: true
});

export const insertUserLessonProgressSchema = createInsertSchema(userLessonProgressRecord).pick({
  userId: true,
  lessonId: true,
  courseId: true,
  startedAt: true,
  lastAccessedAt: true,
  timeSpent: true,
  isCompleted: true,
  completedAt: true,
  videoPosition: true
});

export const insertAnalyticsEventSchema = createInsertSchema(analyticsEvents).pick({
  userId: true,
  courseId: true,
  lessonId: true,
  eventType: true,
  eventData: true,
  ipAddress: true,
  userAgent: true
});

export const insertPublishingSchema = createInsertSchema(publishing).omit({
  id: true,
  createdAt: true,
  updatedAt: true
});

export const insertAiGeneratedImageSchema = createInsertSchema(aiGeneratedImages).pick({
  userId: true,
  prompt: true,
  negativePrompt: true,
  imageUrl: true,
  fileName: true,
  width: true,
  height: true,
  seed: true,
  stylePreset: true,
  engineId: true,
  mediaId: true,
  courseId: true,
  lessonId: true
});

export const insertGeneratedVideoSchema = createInsertSchema(generatedVideos).pick({
  id: true,
  userId: true,
  prompt: true,
  processingStatus: true,
  duration: true,
  estimatedCompletionTime: true,
  videoUrl: true,
  thumbnailUrl: true,
  mediaId: true,
  courseId: true,
  lessonId: true,
  error: true,
  animationKeyframes: true,
});

export type InsertCourseAnalytics = z.infer<typeof insertCourseAnalyticsSchema>;
export type InsertLessonAnalytics = z.infer<typeof insertLessonAnalyticsSchema>;
export type InsertUserCourseProgress = z.infer<typeof insertUserCourseProgressSchema>;
export type InsertUserLessonProgress = z.infer<typeof insertUserLessonProgressSchema>;
export type InsertAnalyticsEvent = z.infer<typeof insertAnalyticsEventSchema>;

// AI Jobs
export const aiJobs = pgTable("ai_jobs", {
  id: text("id").primaryKey(),
  userId: integer("user_id").notNull().references(() => users.id),
  prompt: text("prompt").notNull(),
  model: text("model"),
  type: text("type").notNull(), // text, speech, image, animation
  status: text("status").notNull().default("pending"), // pending, processing, completed, failed
  result: jsonb("result"),
  error: text("error"),
  courseId: integer("course_id").references(() => courses.id),
  lessonId: integer("lesson_id").references(() => lessons.id),
  mediaId: integer("media_id").references(() => mediaLibrary.id),
  estimatedCompletionTime: integer("estimated_completion_time"), // in seconds
  duration: integer("duration"), // in seconds, for speech/animation
  thumbnailUrl: text("thumbnail_url"),
  videoUrl: text("video_url"),
  audioUrl: text("audio_url"),
  imageUrl: text("image_url"),
  animationKeyframes: jsonb("animation_keyframes"),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
});

export const insertAiJobSchema = createInsertSchema(aiJobs);
export type InsertAiJob = z.infer<typeof insertAiJobSchema>;
export type AiJob = typeof aiJobs.$inferSelect;
export type InsertPublishing = z.infer<typeof insertPublishingSchema>;
export type InsertAiGeneratedImage = z.infer<typeof insertAiGeneratedImageSchema>;
export type InsertGeneratedVideo = z.infer<typeof insertGeneratedVideoSchema>;

// Quiz insert schemas
export const insertQuizSchema = createInsertSchema(quizzes).pick({
  courseId: true,
  moduleId: true,
  lessonId: true,
  title: true,
  description: true,
  instructions: true,
  passingScore: true,
  timeLimit: true,
  randomizeQuestions: true,
  showCorrectAnswers: true,
});

export const insertQuizQuestionSchema = createInsertSchema(quizQuestions).pick({
  quizId: true,
  questionType: true,
  questionText: true,
  explanation: true,
  points: true,
  order: true,
  imageUrl: true,
  metadata: true,
});

export const insertQuizAnswerSchema = createInsertSchema(quizAnswers).pick({
  questionId: true,
  answerText: true,
  isCorrect: true,
  explanation: true,
  order: true,
});

export const insertQuizAttemptSchema = createInsertSchema(quizAttempts).pick({
  quizId: true,
  userId: true,
  score: true,
  maxPossibleScore: true,
  percentageScore: true,
  passed: true,
  completedAt: true,
  timeSpent: true,
});

export const insertQuizFlashcardSchema = createInsertSchema(quizFlashcards).pick({
  quizId: true,
  front: true,
  back: true,
  order: true,
  category: true,
  imageUrl: true,
});

// Landing Pages
export const landingPages = pgTable("landing_pages", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").notNull().references(() => users.id),
  courseId: integer("course_id").references(() => courses.id),
  title: text("title").notNull(),
  slug: text("slug").notNull().unique(),
  headline: text("headline").notNull(),
  subheadline: text("subheadline").notNull(),
  description: text("description").notNull(),
  callToAction: text("call_to_action").notNull().default("Enroll Now"),
  benefits: jsonb("benefits").notNull().default([]),
  sections: jsonb("sections").notNull().default([]),
  testimonials: jsonb("testimonials").default([]),
  pricing: jsonb("pricing").default({}),
  status: text("status").notNull().default("draft"), // draft, published
  publishedAt: timestamp("published_at"),
  theme: text("theme").notNull().default("professional"),
  colorScheme: text("color_scheme").notNull().default("blue"),
  bannerImageUrl: text("banner_image_url"),
  customDomain: text("custom_domain"),
  // SEO fields
  seoTitle: text("seo_title"),
  seoDescription: text("seo_description"),
  seoKeywords: text("seo_keywords"),
  // Analytics fields
  googleAnalyticsId: text("google_analytics_id"),
  facebookPixelId: text("facebook_pixel_id"),
  // Stats fields
  viewCount: integer("view_count").default(0),
  uniqueVisitorCount: integer("unique_visitor_count").default(0),
  conversionCount: integer("conversion_count").default(0),
  conversionRate: numeric("conversion_rate").default("0"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const landingPageVisits = pgTable("landing_page_visits", {
  id: serial("id").primaryKey(),
  landingPageId: integer("landing_page_id").notNull().references(() => landingPages.id, { onDelete: "cascade" }),
  visitorIp: text("visitor_ip"),
  userAgent: text("user_agent"),
  referrer: text("referrer"),
  timestamp: timestamp("timestamp").defaultNow().notNull(),
  conversionTimestamp: timestamp("conversion_timestamp"),
  converted: boolean("converted").default(false),
  sessionDuration: integer("session_duration"), // in seconds
  utmSource: text("utm_source"),
  utmMedium: text("utm_medium"),
  utmCampaign: text("utm_campaign"),
  utmContent: text("utm_content"),
  utmTerm: text("utm_term"),
});

// Insert schemas for landing pages
export const insertLandingPageSchema = createInsertSchema(landingPages)
  .omit({ 
    id: true, 
    createdAt: true, 
    updatedAt: true, 
    viewCount: true, 
    uniqueVisitorCount: true,
    conversionCount: true,
    conversionRate: true,
    publishedAt: true
  });

export const insertLandingPageVisitSchema = createInsertSchema(landingPageVisits)
  .omit({ 
    id: true, 
    timestamp: true,
    conversionTimestamp: true
  });

export type InsertLandingPage = z.infer<typeof insertLandingPageSchema>;
export type LandingPage = typeof landingPages.$inferSelect;
export type InsertLandingPageVisit = z.infer<typeof insertLandingPageVisitSchema>;
export type LandingPageVisit = typeof landingPageVisits.$inferSelect;

// User API keys schema
export const insertUserApiKeySchema = createInsertSchema(userApiKeys).pick({
  userId: true,
  service: true,
  apiKey: true,
  isVerified: true,
  isActive: true,
  lastVerified: true,
});

// Quiz type exports
export type InsertQuiz = z.infer<typeof insertQuizSchema>;
export type InsertQuizQuestion = z.infer<typeof insertQuizQuestionSchema>;
export type InsertQuizAnswer = z.infer<typeof insertQuizAnswerSchema>;
export type InsertQuizAttempt = z.infer<typeof insertQuizAttemptSchema>;
export type InsertQuizFlashcard = z.infer<typeof insertQuizFlashcardSchema>;

// Mini Course insert schema
export const insertMiniCourseSchema = createInsertSchema(miniCourses).pick({
  userId: true,
  title: true,
  description: true,
  duration: true,
  thumbnailUrl: true,
  scriptId: true,
  audioId: true,
  videoId: true,
  quizId: true,
  status: true,
  difficultyLevel: true,
  targetAudience: true,
  includesResources: true,
  contentSections: true,
});

export type InsertMiniCourse = z.infer<typeof insertMiniCourseSchema>;

export type User = typeof users.$inferSelect;
export type Course = typeof courses.$inferSelect;
export type Module = typeof modules.$inferSelect;
export type Lesson = typeof lessons.$inferSelect;
export type Media = typeof mediaLibrary.$inferSelect;
export type PlatformIntegration = typeof platformIntegrations.$inferSelect;
export type Integration = typeof integrations.$inferSelect;
export type UserStats = typeof userStats.$inferSelect;
export type Template = typeof templates.$inferSelect;
export type TemplateHistory = typeof templateHistory.$inferSelect;
export type MicroLearningSegment = typeof microLearningSegments.$inferSelect;
export type MicroLearningKnowledgeCheck = typeof microLearningKnowledgeChecks.$inferSelect;
export type UserKnowledgeCheckResponse = typeof userKnowledgeCheckResponses.$inferSelect;
export type Team = typeof teams.$inferSelect;
export type TeamMember = typeof teamMembers.$inferSelect;
export type CourseCollaborator = typeof courseCollaborators.$inferSelect;
export type TeamCourse = typeof teamCourses.$inferSelect;
export type CourseAnalytics = typeof courseAnalytics.$inferSelect;
export type LessonAnalytics = typeof lessonAnalytics.$inferSelect;
export type UserCourseProgress = typeof userCourseProgress.$inferSelect;
export type MicroLearningUserProgress = typeof microLearningUserProgress.$inferSelect;
export type UserLessonProgress = typeof userLessonProgressRecord.$inferSelect;
export type AnalyticsEvent = typeof analyticsEvents.$inferSelect;
export type Publishing = typeof publishing.$inferSelect;
export type AiGeneratedImage = typeof aiGeneratedImages.$inferSelect;
export type GeneratedVideo = typeof generatedVideos.$inferSelect;
export type Quiz = typeof quizzes.$inferSelect;
export type QuizQuestion = typeof quizQuestions.$inferSelect;
export type QuizAnswer = typeof quizAnswers.$inferSelect;
export type QuizAttempt = typeof quizAttempts.$inferSelect;
export type QuizFlashcard = typeof quizFlashcards.$inferSelect;
export type MiniCourse = typeof miniCourses.$inferSelect;
export type UserApiKey = typeof userApiKeys.$inferSelect;
export type InsertUserApiKey = z.infer<typeof insertUserApiKeySchema>;

// Insert schemas for micro-learning
export const insertMicroLearningSegmentSchema = createInsertSchema(microLearningSegments).pick({
  lessonId: true,
  title: true,
  content: true,
  startTime: true,
  endTime: true,
  order: true,
});

export const insertMicroLearningKnowledgeCheckSchema = createInsertSchema(microLearningKnowledgeChecks).pick({
  lessonId: true,
  segmentId: true,
  question: true,
  options: true,
  correctAnswer: true,
  explanation: true,
});

export const insertMicroLearningUserProgressSchema = createInsertSchema(microLearningUserProgress).pick({
  userId: true,
  lessonId: true,
  progress: true,
  completed: true,
  lastPosition: true,
  completedSegments: true,
  completedAt: true,
});

export const insertUserKnowledgeCheckResponseSchema = createInsertSchema(userKnowledgeCheckResponses).pick({
  userId: true,
  knowledgeCheckId: true,
  userAnswer: true,
  correct: true,
});

export type InsertMicroLearningSegment = z.infer<typeof insertMicroLearningSegmentSchema>;
export type InsertMicroLearningKnowledgeCheck = z.infer<typeof insertMicroLearningKnowledgeCheckSchema>;
export type InsertMicroLearningUserProgress = z.infer<typeof insertMicroLearningUserProgressSchema>;
export type InsertUserKnowledgeCheckResponse = z.infer<typeof insertUserKnowledgeCheckResponseSchema>;

// Text-to-speech records
export const ttsRecords = pgTable("tts_records", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  text: text("text").notNull(),
  fileName: text("file_name").notNull(),
  filePath: text("file_path").notNull(),
  service: text("service").notNull(), // 'coqui', 'elevenlabs', 'google'
  wordCount: integer("word_count").notNull(),
  creditCost: integer("credit_cost").notNull(),
  createdAt: timestamp("created_at").defaultNow(),
  title: text("title"),
  metadata: jsonb("metadata").default({}), // Store voice ID, model, rate, etc.
});

// TTS insert schema and type
export const insertTTSRecordSchema = createInsertSchema(ttsRecords).pick({
  userId: true,
  text: true,
  fileName: true,
  filePath: true,
  service: true,
  wordCount: true,
  creditCost: true,
  title: true,
  metadata: true
});

export type InsertTTSRecord = z.infer<typeof insertTTSRecordSchema>;
export type TTSRecord = typeof ttsRecords.$inferSelect;

// Type for TTS API responses
export interface TTSResponse extends InsertTTSRecord {
  id?: number;
}

// Notifications system
export const notificationTypes = pgTable("notification_types", {
  id: serial("id").primaryKey(),
  type: text("type").notNull().unique(), // e.g., 'course_update', 'team_invite', 'system_alert'
  displayName: text("display_name").notNull(),
  description: text("description").notNull(),
  iconName: text("icon_name").notNull(), // Lucide icon name
  iconColor: text("icon_color").notNull(), // Tailwind color class
  importance: text("importance").notNull().default("medium"), // 'high', 'medium', 'low'
  category: text("category").notNull(), // 'system', 'social', 'course', 'team'
  isDefault: boolean("is_default").default(false), // If true, users will receive this by default
});

export const notifications = pgTable("notifications", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  typeId: integer("type_id").notNull().references(() => notificationTypes.id),
  title: text("title").notNull(),
  message: text("message").notNull(),
  isRead: boolean("is_read").default(false),
  linkUrl: text("link_url"), // Optional URL to navigate to when notification is clicked
  createdAt: timestamp("created_at").defaultNow(),
  expiresAt: timestamp("expires_at"), // Optional expiration date for temporary notifications
}, (table) => {
  return {
    userIdIdx: index("notifications_user_id_idx").on(table.userId),
    createdAtIdx: index("notifications_created_at_idx").on(table.createdAt)
  };
});

export const userNotificationPreferences = pgTable("user_notification_preferences", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  typeId: integer("type_id").notNull().references(() => notificationTypes.id),
  enabled: boolean("enabled").default(true),
}, (table) => {
  return {
    userTypeIdx: uniqueIndex("user_notification_prefs_user_type_idx").on(table.userId, table.typeId),
  };
});

// Define relations for notifications
export const notificationsRelations = relations(notifications, ({ one }) => ({
  user: one(users, { fields: [notifications.userId], references: [users.id] }),
  type: one(notificationTypes, { fields: [notifications.typeId], references: [notificationTypes.id] })
}));

// Define relations for user notification preferences
export const userNotificationPreferencesRelations = relations(userNotificationPreferences, ({ one }) => ({
  user: one(users, { fields: [userNotificationPreferences.userId], references: [users.id] }),
  type: one(notificationTypes, { fields: [userNotificationPreferences.typeId], references: [notificationTypes.id] })
}));

// Define relations for landing pages
export const landingPagesRelations = relations(landingPages, ({ one, many }) => ({
  user: one(users, { fields: [landingPages.userId], references: [users.id] }),
  course: one(courses, { fields: [landingPages.courseId], references: [courses.id] }),
  visits: many(landingPageVisits)
}));

export const landingPageVisitsRelations = relations(landingPageVisits, ({ one }) => ({
  landingPage: one(landingPages, { fields: [landingPageVisits.landingPageId], references: [landingPages.id] })
}));

// Insert schemas for notifications
export const insertNotificationTypeSchema = createInsertSchema(notificationTypes).pick({
  type: true,
  displayName: true,
  description: true,
  iconName: true,
  iconColor: true,
  importance: true,
  category: true,
  isDefault: true,
});

export const insertNotificationSchema = createInsertSchema(notifications).pick({
  userId: true,
  typeId: true,
  title: true,
  message: true,
  isRead: true,
  linkUrl: true,
  expiresAt: true,
});

export const insertUserNotificationPreferenceSchema = createInsertSchema(userNotificationPreferences).pick({
  userId: true,
  typeId: true,
  enabled: true,
});

// Types for notifications
export type NotificationType = typeof notificationTypes.$inferSelect;
export type Notification = typeof notifications.$inferSelect;
export type UserNotificationPreference = typeof userNotificationPreferences.$inferSelect;
export type InsertNotificationType = z.infer<typeof insertNotificationTypeSchema>;
export type InsertNotification = z.infer<typeof insertNotificationSchema>;
export type InsertUserNotificationPreference = z.infer<typeof insertUserNotificationPreferenceSchema>;

// Email marketing settings
export const emailSettings = pgTable("email_settings", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  emailProvider: text("email_provider").notNull().default("resend"),
  fromName: text("from_name").notNull(),
  fromEmail: text("from_email").notNull(),
  replyToEmail: text("reply_to_email"),
  footerText: text("footer_text"),
  enableUnsubscribeLink: boolean("enable_unsubscribe_link").default(true),
  enableOpenTracking: boolean("enable_open_tracking").default(true),
  enableClickTracking: boolean("enable_click_tracking").default(true),
  sendgridApiKey: text("sendgrid_api_key"),
  resendApiKey: text("resend_api_key"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

export const emailSettingsRelations = relations(emailSettings, ({ one }) => ({
  user: one(users, {
    fields: [emailSettings.userId],
    references: [users.id],
  }),
}));

export const subscribers = pgTable("subscribers", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  email: text("email").notNull(),
  name: text("name"),
  status: text("status").notNull().default("active"),
  source: text("source"),
  listId: integer("list_id").references(() => subscriberLists.id),
  metadata: jsonb("metadata"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

export const subscriberLists = pgTable("subscriber_lists", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  name: text("name").notNull(),
  description: text("description"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

export const subscribersRelations = relations(subscribers, ({ one }) => ({
  user: one(users, {
    fields: [subscribers.userId],
    references: [users.id],
  }),
  list: one(subscriberLists, {
    fields: [subscribers.listId],
    references: [subscriberLists.id],
  }),
}));

export const subscriberListsRelations = relations(subscriberLists, ({ one, many }) => ({
  user: one(users, {
    fields: [subscriberLists.userId],
    references: [users.id],
  }),
  subscribers: many(subscribers),
}));

// Email templates
export const emailTemplates = pgTable("email_templates", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  name: text("name").notNull(),
  description: text("description"),
  subject: text("subject").notNull(),
  content: text("content").notNull(),
  htmlContent: text("html_content").notNull(),
  thumbnail: text("thumbnail"),
  category: text("category").default("general"),
  tags: text("tags").array(),
  isDefault: boolean("is_default").default(false),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

export const emailTemplatesRelations = relations(emailTemplates, ({ one }) => ({
  user: one(users, {
    fields: [emailTemplates.userId],
    references: [users.id],
  }),
}));

export const insertEmailSettingsSchema = createInsertSchema(emailSettings).pick({
  userId: true,
  emailProvider: true,
  fromName: true,
  fromEmail: true,
  replyToEmail: true,
  footerText: true,
  enableUnsubscribeLink: true,
  enableOpenTracking: true,
  enableClickTracking: true,
  sendgridApiKey: true,
  resendApiKey: true,
});

export const insertSubscriberSchema = createInsertSchema(subscribers).pick({
  userId: true,
  email: true,
  name: true,
  status: true,
  source: true,
  listId: true,
  metadata: true,
});

export const insertSubscriberListSchema = createInsertSchema(subscriberLists).pick({
  userId: true,
  name: true,
  description: true,
});

export const insertEmailTemplateSchema = createInsertSchema(emailTemplates).pick({
  userId: true,
  name: true,
  description: true,
  subject: true,
  content: true,
  htmlContent: true,
  thumbnail: true,
  category: true,
  tags: true,
  isDefault: true,
});

// Email campaign table
export const emailCampaigns = pgTable("email_campaigns", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  name: text("name").notNull(),
  subject: text("subject").notNull(),
  fromName: text("from_name"),
  fromEmail: text("from_email"),
  replyToEmail: text("reply_to_email"),
  content: text("content").notNull(),
  contentType: text("content_type").notNull().default("html"), // html, text, template
  templateId: integer("template_id").references(() => emailTemplates.id),
  listId: integer("list_id").references(() => subscriberLists.id),
  status: text("status").notNull().default("draft"), // draft, scheduled, sent, cancelled
  scheduledFor: timestamp("scheduled_for"),
  sentAt: timestamp("sent_at"),
  totalSent: integer("total_sent").default(0),
  totalOpened: integer("total_opened").default(0),
  totalClicked: integer("total_clicked").default(0),
  trackOpens: boolean("track_opens").default(true),
  trackClicks: boolean("track_clicks").default(true),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

export const emailCampaignsRelations = relations(emailCampaigns, ({ one }) => ({
  user: one(users, {
    fields: [emailCampaigns.userId],
    references: [users.id],
  }),
  template: one(emailTemplates, {
    fields: [emailCampaigns.templateId],
    references: [emailTemplates.id],
  }),
  list: one(subscriberLists, {
    fields: [emailCampaigns.listId],
    references: [subscriberLists.id],
  }),
}));

export const insertEmailCampaignSchema = createInsertSchema(emailCampaigns).pick({
  userId: true,
  name: true,
  subject: true,
  fromName: true,
  fromEmail: true,
  replyToEmail: true,
  content: true,
  contentType: true,
  templateId: true,
  listId: true,
  status: true,
  scheduledFor: true,
  trackOpens: true,
  trackClicks: true,
});

// Types for email marketing
export type EmailSettings = typeof emailSettings.$inferSelect;
export type Subscriber = typeof subscribers.$inferSelect;
export type SubscriberList = typeof subscriberLists.$inferSelect;
export type EmailTemplate = typeof emailTemplates.$inferSelect;
export type EmailCampaign = typeof emailCampaigns.$inferSelect;
export type InsertEmailSettings = z.infer<typeof insertEmailSettingsSchema>;
export type InsertSubscriber = z.infer<typeof insertSubscriberSchema>;
export type InsertSubscriberList = z.infer<typeof insertSubscriberListSchema>;
export type InsertEmailTemplate = z.infer<typeof insertEmailTemplateSchema>;
export type InsertEmailCampaign = z.infer<typeof insertEmailCampaignSchema>;
