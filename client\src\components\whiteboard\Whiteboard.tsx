import React, { useEffect, useRef, useState } from 'react';
import {
  Pencil,
  Square,
  Circle,
  Type,
  Image as ImageIcon,
  Trash2,
  Download,
  Undo,
  Redo,
  Users,
  StickyNote,
  Link,
  Hand,
  ZoomIn,
  Save,
  FileText
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { cn } from "@/lib/utils";

// Define action types
type Tool = 'select' | 'draw' | 'rectangle' | 'circle' | 'text' | 'sticky' | 'image' | 'connect' | 'pan';
type History = { objects: fabric.Object[]; }[];

interface CollaboratorCursor {
  id: string;
  username: string;
  color: string;
  x: number;
  y: number;
  lastActivity: number;
}

interface WhiteboardProps {
  courseId?: number;
  readOnly?: boolean;
  initialData?: string;
  onSave?: (data: string) => void;
  collaborationEnabled?: boolean;
}

export const Whiteboard: React.FC<WhiteboardProps> = ({ 
  courseId, 
  readOnly = false, 
  initialData,
  onSave,
  collaborationEnabled = false
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const canvasContainerRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [canvas, setCanvas] = useState<fabric.Canvas | null>(null);
  const [activeTool, setActiveTool] = useState<Tool>('select');
  const [drawingColor, setDrawingColor] = useState('#000000');
  const [brushSize, setBrushSize] = useState(5);
  const [isDrawing, setIsDrawing] = useState(false);
  const [history, setHistory] = useState<History>([]);
  const [historyIndex, setHistoryIndex] = useState(-1);
  const [collaborators, setCollaborators] = useState<CollaboratorCursor[]>([]);
  const [stickyNoteText, setStickyNoteText] = useState('');
  const [textValue, setTextValue] = useState('');
  const [shapeType, setShapeType] = useState<'filled' | 'outlined'>('outlined');
  const [zoomLevel, setZoomLevel] = useState(1);
  const { toast } = useToast();
  
  // Initialize canvas
  useEffect(() => {
    if (canvasRef.current && !canvas) {
      const fabricCanvas = new fabric.Canvas(canvasRef.current, {
        isDrawingMode: false,
        backgroundColor: '#ffffff',
        width: canvasContainerRef.current?.clientWidth || 800,
        height: 600,
      });

      setCanvas(fabricCanvas);
      
      // Set up auto-save
      const autoSaveInterval = setInterval(() => {
        if (fabricCanvas && onSave) {
          const json = JSON.stringify(fabricCanvas.toJSON());
          onSave(json);
        }
      }, 60000); // Auto-save every minute
      
      // Clean up on unmount
      return () => {
        fabricCanvas.dispose();
        clearInterval(autoSaveInterval);
      };
    }
  }, [canvasRef, canvas, onSave]);

  // Load initial data
  useEffect(() => {
    if (canvas && initialData) {
      try {
        canvas.loadFromJSON(initialData, canvas.renderAll.bind(canvas));
        addToHistory();
      } catch (error) {
        console.error('Error loading whiteboard data:', error);
      }
    }
  }, [canvas, initialData]);

  // Handle tool change
  useEffect(() => {
    if (!canvas) return;
    
    canvas.isDrawingMode = activeTool === 'draw';
    
    if (activeTool === 'draw') {
      canvas.freeDrawingBrush.color = drawingColor;
      canvas.freeDrawingBrush.width = brushSize;
    }
    
    // Set cursor based on tool
    switch (activeTool) {
      case 'select':
        canvas.defaultCursor = 'default';
        break;
      case 'draw':
        canvas.defaultCursor = 'crosshair';
        break;
      case 'pan':
        canvas.defaultCursor = 'grab';
        break;
      default:
        canvas.defaultCursor = 'default';
    }
    
    canvas.renderAll();
  }, [canvas, activeTool, drawingColor, brushSize]);

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      if (canvas && canvasContainerRef.current) {
        canvas.setWidth(canvasContainerRef.current.clientWidth);
        canvas.renderAll();
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [canvas]);

  // Canvas event listeners
  useEffect(() => {
    if (!canvas) return;

    // Add object added event
    const handleObjectAdded = () => {
      if (!isDrawing) {
        addToHistory();
      }
    };
    
    // Add object modified event
    const handleObjectModified = () => {
      addToHistory();
    };
    
    // Add path created event
    const handlePathCreated = () => {
      setIsDrawing(false);
      addToHistory();
    };

    // Mouse down event
    const handleMouseDown = () => {
      if (activeTool === 'draw') {
        setIsDrawing(true);
      }
    };

    canvas.on('object:added', handleObjectAdded);
    canvas.on('object:modified', handleObjectModified);
    canvas.on('path:created', handlePathCreated);
    canvas.on('mouse:down', handleMouseDown);
    
    // Set readonly state
    canvas.selection = !readOnly;
    canvas.forEachObject((obj) => {
      obj.selectable = !readOnly;
      obj.evented = !readOnly;
    });
    
    return () => {
      canvas.off('object:added', handleObjectAdded);
      canvas.off('object:modified', handleObjectModified);
      canvas.off('path:created', handlePathCreated);
      canvas.off('mouse:down', handleMouseDown);
    };
  }, [canvas, isDrawing, readOnly, activeTool]);

  // Mouse events for tools
  const handleCanvasMouseDown = (e: React.MouseEvent) => {
    if (!canvas || readOnly) return;

    const pointer = canvas.getPointer(e as unknown as Event);
    
    if (activeTool === 'rectangle') {
      const rect = new fabric.Rect({
        left: pointer.x,
        top: pointer.y,
        width: 100,
        height: 100,
        fill: shapeType === 'filled' ? drawingColor : 'transparent',
        stroke: drawingColor,
        strokeWidth: 2,
      });
      canvas.add(rect);
      canvas.setActiveObject(rect);
    } else if (activeTool === 'circle') {
      const circle = new fabric.Circle({
        left: pointer.x,
        top: pointer.y,
        radius: 50,
        fill: shapeType === 'filled' ? drawingColor : 'transparent',
        stroke: drawingColor,
        strokeWidth: 2,
      });
      canvas.add(circle);
      canvas.setActiveObject(circle);
    } else if (activeTool === 'sticky') {
      // Create a sticky note
      const sticky = new fabric.Rect({
        left: pointer.x,
        top: pointer.y,
        width: 150,
        height: 150,
        fill: '#FFEB3B',
        rx: 10,
        ry: 10,
      });
      
      const text = new fabric.Textbox(stickyNoteText || 'Double-click to edit', {
        left: pointer.x + 10,
        top: pointer.y + 10,
        width: 130,
        fontSize: 14,
        fontFamily: 'Arial',
      });
      
      const group = new fabric.Group([sticky, text], {
        left: pointer.x,
        top: pointer.y,
      });
      
      canvas.add(group);
      canvas.setActiveObject(group);
      setStickyNoteText('');
    } else if (activeTool === 'text') {
      const text = new fabric.Textbox(textValue || 'Double-click to edit', {
        left: pointer.x,
        top: pointer.y,
        width: 200,
        fontSize: 20,
        fontFamily: 'Arial',
        fill: drawingColor,
      });
      
      canvas.add(text);
      canvas.setActiveObject(text);
      setTextValue('');
    } else if (activeTool === 'connect') {
      // This would be implemented with a line connection tool
      // For simplicity, we'll just add a line for now
      const line = new fabric.Line([pointer.x, pointer.y, pointer.x + 100, pointer.y + 100], {
        stroke: drawingColor,
        strokeWidth: 2,
        strokeDashArray: [5, 5],
      });
      
      canvas.add(line);
      canvas.setActiveObject(line);
    } else if (activeTool === 'pan') {
      // Panning logic would be implemented here
      // For simplicity, we'll use fabric's built-in panning
      canvas.defaultCursor = 'grabbing';
    }
    
    canvas.renderAll();
  };

  // File upload handling
  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!canvas || !e.target.files || e.target.files.length === 0) return;
    
    const file = e.target.files[0];
    const reader = new FileReader();
    
    reader.onload = (event) => {
      if (!event.target) return;
      
      fabric.Image.fromURL(event.target.result as string, (img) => {
        // Scale image to fit within the canvas
        const maxSize = 300;
        const scale = Math.min(
          maxSize / img.width!,
          maxSize / img.height!
        );
        
        img.scale(scale);
        
        // Center the image on canvas
        const center = canvas.getCenter();
        img.left = center.left;
        img.top = center.top;
        
        canvas.add(img);
        canvas.setActiveObject(img);
        canvas.renderAll();
        addToHistory();
      });
    };
    
    reader.readAsDataURL(file);
    
    // Reset file input
    e.target.value = '';
  };

  // Add to history
  const addToHistory = () => {
    if (!canvas) return;
    
    // Get current objects
    const currentObjects = canvas.getObjects();
    const objectsCopy = currentObjects.map(obj => fabric.util.object.clone(obj));
    
    // If we're in the middle of the history, truncate
    if (historyIndex < history.length - 1) {
      setHistory(prevHistory => prevHistory.slice(0, historyIndex + 1));
    }
    
    // Add to history
    setHistory(prevHistory => [...prevHistory, { objects: objectsCopy }]);
    setHistoryIndex(prevIndex => prevIndex + 1);
  };

  // Undo
  const handleUndo = () => {
    if (!canvas || historyIndex <= 0) return;
    
    const newIndex = historyIndex - 1;
    const prevState = history[newIndex];
    
    canvas.clear();
    prevState.objects.forEach(obj => {
      canvas.add(fabric.util.object.clone(obj));
    });
    
    canvas.renderAll();
    setHistoryIndex(newIndex);
  };

  // Redo
  const handleRedo = () => {
    if (!canvas || historyIndex >= history.length - 1) return;
    
    const newIndex = historyIndex + 1;
    const nextState = history[newIndex];
    
    canvas.clear();
    nextState.objects.forEach(obj => {
      canvas.add(fabric.util.object.clone(obj));
    });
    
    canvas.renderAll();
    setHistoryIndex(newIndex);
  };

  // Delete selected object
  const handleDelete = () => {
    if (!canvas) return;
    
    const activeObjects = canvas.getActiveObjects();
    
    if (activeObjects.length) {
      activeObjects.forEach(obj => canvas.remove(obj));
      canvas.discardActiveObject();
      canvas.renderAll();
      addToHistory();
    }
  };

  // Export as PNG
  const handleExportPNG = () => {
    if (!canvas) return;
    
    const dataURL = canvas.toDataURL({
      format: 'png',
      quality: 1,
    });
    
    const link = document.createElement('a');
    link.download = `whiteboard-${new Date().toISOString()}.png`;
    link.href = dataURL;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    toast({
      title: "Export successful",
      description: "Whiteboard exported as PNG",
    });
  };

  // Export as PDF
  const handleExportPDF = () => {
    if (!canvas) return;
    
    // In a real implementation, we'd use a library like jsPDF
    // For now, we'll just simulate it
    toast({
      title: "Export as PDF",
      description: "PDF export would be implemented with jsPDF library",
    });
  };

  // Save whiteboard
  const handleSave = () => {
    if (!canvas || !onSave) return;
    
    const json = JSON.stringify(canvas.toJSON());
    onSave(json);
    
    toast({
      title: "Whiteboard saved",
      description: "Your whiteboard has been saved successfully",
    });
  };

  // Handle zoom
  const handleZoom = (value: number[]) => {
    if (!canvas) return;
    
    const zoom = value[0];
    setZoomLevel(zoom);
    
    canvas.setZoom(zoom);
    canvas.renderAll();
  };

  return (
    <div className="flex flex-col h-full gap-4">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-2 pb-2 border-b">
        <div className="flex items-center gap-2 overflow-x-auto p-1 whiteboard-toolbar">
          <Button
            variant={activeTool === 'select' ? 'default' : 'outline'}
            size="icon"
            onClick={() => setActiveTool('select')}
            disabled={readOnly}
            title="Select (V)"
          >
            <Hand size={18} />
          </Button>
          
          <Button
            variant={activeTool === 'draw' ? 'default' : 'outline'}
            size="icon"
            onClick={() => setActiveTool('draw')}
            disabled={readOnly}
            title="Draw (P)"
          >
            <Pencil size={18} />
          </Button>
          
          <Button
            variant={activeTool === 'rectangle' ? 'default' : 'outline'}
            size="icon"
            onClick={() => setActiveTool('rectangle')}
            disabled={readOnly}
            title="Rectangle (R)"
          >
            <Square size={18} />
          </Button>
          
          <Button
            variant={activeTool === 'circle' ? 'default' : 'outline'}
            size="icon"
            onClick={() => setActiveTool('circle')}
            disabled={readOnly}
            title="Circle (C)"
          >
            <Circle size={18} />
          </Button>
          
          <Button
            variant={activeTool === 'text' ? 'default' : 'outline'}
            size="icon"
            onClick={() => setActiveTool('text')}
            disabled={readOnly}
            title="Text (T)"
          >
            <Type size={18} />
          </Button>
          
          <Button
            variant={activeTool === 'sticky' ? 'default' : 'outline'}
            size="icon"
            onClick={() => setActiveTool('sticky')}
            disabled={readOnly}
            title="Sticky Note (S)"
          >
            <StickyNote size={18} />
          </Button>
          
          <Button
            variant={activeTool === 'connect' ? 'default' : 'outline'}
            size="icon"
            onClick={() => setActiveTool('connect')}
            disabled={readOnly}
            title="Connect (L)"
          >
            <Link size={18} />
          </Button>
          
          <Button
            variant={activeTool === 'pan' ? 'default' : 'outline'}
            size="icon"
            onClick={() => setActiveTool('pan')}
            title="Pan (Space)"
          >
            <Hand size={18} />
          </Button>
          
          <input
            type="color"
            value={drawingColor}
            onChange={(e) => setDrawingColor(e.target.value)}
            className="h-9 w-9 rounded-md border border-input cursor-pointer"
            disabled={readOnly}
            title="Color"
          />
          
          <div className="flex items-center gap-2 min-w-[120px]">
            <Label className="text-xs w-14">Brush Size</Label>
            <Slider
              disabled={readOnly}
              min={1}
              max={30}
              step={1}
              value={[brushSize]}
              onValueChange={(value) => setBrushSize(value[0])}
              className="w-20"
            />
          </div>
          
          <div className="flex items-center">
            <Input
              type="file"
              ref={fileInputRef}
              onChange={handleFileUpload}
              className="hidden"
              accept="image/*"
              disabled={readOnly}
            />
            <Button
              variant="outline"
              size="icon"
              onClick={() => fileInputRef.current?.click()}
              disabled={readOnly}
              title="Upload Image"
            >
              <ImageIcon size={18} />
            </Button>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="icon"
            onClick={handleUndo}
            disabled={readOnly || historyIndex <= 0}
            title="Undo (Ctrl+Z)"
          >
            <Undo size={18} />
          </Button>
          
          <Button
            variant="outline"
            size="icon"
            onClick={handleRedo}
            disabled={readOnly || historyIndex >= history.length - 1}
            title="Redo (Ctrl+Y)"
          >
            <Redo size={18} />
          </Button>
          
          <Button
            variant="outline"
            size="icon"
            onClick={handleDelete}
            disabled={readOnly}
            title="Delete Selected"
          >
            <Trash2 size={18} />
          </Button>
          
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" size="icon" title="Export">
                <Download size={18} />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-48">
              <div className="flex flex-col gap-2">
                <Button variant="ghost" onClick={handleExportPNG} className="justify-start">
                  <ImageIcon size={16} className="mr-2" />
                  Export as PNG
                </Button>
                <Button variant="ghost" onClick={handleExportPDF} className="justify-start">
                  <FileText size={16} className="mr-2" />
                  Export as PDF
                </Button>
              </div>
            </PopoverContent>
          </Popover>
          
          {onSave && (
            <Button
              variant="outline"
              size="icon"
              onClick={handleSave}
              disabled={readOnly}
              title="Save"
            >
              <Save size={18} />
            </Button>
          )}
          
          {collaborationEnabled && (
            <Button
              variant="outline"
              size="icon"
              title="Collaborators"
            >
              <Users size={18} />
              {collaborators.length > 0 && (
                <span className="absolute top-0 right-0 h-4 w-4 rounded-full bg-primary text-[10px] flex items-center justify-center text-white">
                  {collaborators.length}
                </span>
              )}
            </Button>
          )}
        </div>
      </div>
      
      {activeTool === 'text' && (
        <div className="flex items-center gap-2 p-2 border rounded-md">
          <Label htmlFor="text-input">Text:</Label>
          <Input
            id="text-input"
            value={textValue}
            onChange={(e) => setTextValue(e.target.value)}
            placeholder="Enter text content"
            className="max-w-md"
          />
        </div>
      )}
      
      {activeTool === 'sticky' && (
        <div className="flex items-center gap-2 p-2 border rounded-md">
          <Label htmlFor="sticky-input">Sticky Note:</Label>
          <Textarea
            id="sticky-input"
            value={stickyNoteText}
            onChange={(e) => setStickyNoteText(e.target.value)}
            placeholder="Enter sticky note content"
            className="max-w-md h-20"
          />
        </div>
      )}
      
      {(activeTool === 'rectangle' || activeTool === 'circle') && (
        <div className="flex items-center gap-2 p-2 border rounded-md">
          <Label>Shape Style:</Label>
          <RadioGroup
            value={shapeType}
            onValueChange={(value) => setShapeType(value as 'filled' | 'outlined')}
            className="flex gap-4"
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="outlined" id="outlined" />
              <Label htmlFor="outlined">Outlined</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="filled" id="filled" />
              <Label htmlFor="filled">Filled</Label>
            </div>
          </RadioGroup>
        </div>
      )}
      
      <div className="relative flex-grow border rounded-md overflow-hidden" ref={canvasContainerRef}>
        <div
          className="w-full h-full"
          onMouseDown={handleCanvasMouseDown}
        >
          <canvas ref={canvasRef} className="whiteboard-canvas" />
        </div>
        
        {/* Collaborator cursors */}
        {collaborationEnabled && collaborators.map((cursor) => (
          <div
            key={cursor.id}
            className="absolute pointer-events-none"
            style={{
              left: cursor.x,
              top: cursor.y,
              transform: 'translate(-50%, -50%)',
              opacity: Date.now() - cursor.lastActivity < 5000 ? 1 : 0.3,
              transition: 'opacity 0.5s ease',
            }}
          >
            <div
              className="h-4 w-4 rounded-full"
              style={{ backgroundColor: cursor.color }}
            />
            <div
              className="absolute top-4 left-2 px-2 py-0.5 rounded text-xs text-white whitespace-nowrap"
              style={{ backgroundColor: cursor.color }}
            >
              {cursor.username}
            </div>
          </div>
        ))}
        
        {/* Zoom controls */}
        <div className="absolute bottom-4 right-4 flex items-center gap-2 bg-background p-2 rounded-md shadow-md">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => handleZoom([Math.max(0.5, zoomLevel - 0.1)])}
            className="h-8 w-8"
          >
            <span className="text-lg">-</span>
          </Button>
          
          <div className="w-16 text-center text-sm">
            {Math.round(zoomLevel * 100)}%
          </div>
          
          <Button
            variant="ghost"
            size="icon"
            onClick={() => handleZoom([Math.min(3, zoomLevel + 0.1)])}
            className="h-8 w-8"
          >
            <span className="text-lg">+</span>
          </Button>
        </div>
      </div>
      
      {/* Version history would be added here in a production implementation */}
    </div>
  );
};

export default Whiteboard;