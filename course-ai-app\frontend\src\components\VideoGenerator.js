import React, { useState, useRef } from 'react';
import { Card, CardContent, TextField, Button, Grid, Typography, CircularProgress, Slider, FormControl, InputLabel, Select, MenuItem } from '@mui/material';
import { CloudUpload, Download, VideoFile, AudioFile } from '@mui/icons-material';
import { useDropzone } from 'react-dropzone';
import apiService from '../services/apiService';

const VideoGenerator = () => {
  const [imageFile, setImageFile] = useState(null);
  const [audioFile, setAudioFile] = useState(null);
  const [imagePreview, setImagePreview] = useState('');
  const [audioPreview, setAudioPreview] = useState('');
  const [formData, setFormData] = useState({
    enhancer: 'gfpgan',
    size: 512,
    expressionScale: 1.0,
    stillMode: true
  });
  
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState(null);
  const [error, setError] = useState('');
  const videoRef = useRef(null);

  const enhancerOptions = [
    { value: 'gfpgan', label: 'GFPGAN (Face Enhancement)' },
    { value: 'realesrgan', label: 'Real-ESRGAN (Super Resolution)' },
    { value: 'none', label: 'No Enhancement' }
  ];

  const sizeOptions = [
    { value: 256, label: '256x256 (Fast)' },
    { value: 512, label: '512x512 (Balanced)' },
    { value: 1024, label: '1024x1024 (High Quality)' }
  ];

  const onImageDrop = (acceptedFiles) => {
    const file = acceptedFiles[0];
    if (file) {
      setImageFile(file);
      const reader = new FileReader();
      reader.onload = () => setImagePreview(reader.result);
      reader.readAsDataURL(file);
    }
  };

  const onAudioDrop = (acceptedFiles) => {
    const file = acceptedFiles[0];
    if (file) {
      setAudioFile(file);
      const reader = new FileReader();
      reader.onload = () => setAudioPreview(reader.result);
      reader.readAsDataURL(file);
    }
  };

  const { getRootProps: getImageRootProps, getInputProps: getImageInputProps, isDragActive: isImageDragActive } = useDropzone({
    onDrop: onImageDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.webp']
    },
    maxFiles: 1
  });

  const { getRootProps: getAudioRootProps, getInputProps: getAudioInputProps, isDragActive: isAudioDragActive } = useDropzone({
    onDrop: onAudioDrop,
    accept: {
      'audio/*': ['.mp3', '.wav', '.m4a', '.aac']
    },
    maxFiles: 1
  });

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleGenerate = async () => {
    if (!imageFile || !audioFile) {
      setError('Please upload both an image and audio file');
      return;
    }

    setLoading(true);
    setError('');
    
    try {
      const imageBase64 = await apiService.fileToBase64(imageFile);
      const audioBase64 = await apiService.fileToBase64(audioFile);

      const requestData = {
        imageBase64,
        audioBase64,
        ...formData
      };

      const cacheKey = apiService.generateCacheKey(requestData);
      const cached = apiService.getCachedResult(cacheKey);
      
      if (cached) {
        setResult({ ...cached, cached: true });
        setLoading(false);
        return;
      }

      const result = await apiService.generateVideo(requestData);
      
      if (result.status === 'success') {
        setResult(result);
        
        if (!result.cached) {
          apiService.setCachedResult(cacheKey, result);
        }
      } else {
        setError(result.error || 'Video generation failed');
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleDownload = () => {
    if (result?.video_base64) {
      apiService.downloadBase64File(
        result.video_base64,
        `talking-avatar-${Date.now()}.mp4`,
        'video/mp4'
      );
    }
  };

  const clearFiles = () => {
    setImageFile(null);
    setAudioFile(null);
    setImagePreview('');
    setAudioPreview('');
    setResult(null);
    setError('');
  };

  return (
    <div className="video-generator">
      <Typography variant="h4" gutterBottom>
        <VideoFile /> SadTalker Avatar Video Generator
      </Typography>
      
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>Upload Files</Typography>
              
              <div {...getImageRootProps()} style={{
                border: '2px dashed #ccc',
                borderRadius: '8px',
                padding: '20px',
                textAlign: 'center',
                cursor: 'pointer',
                marginBottom: '16px',
                backgroundColor: isImageDragActive ? '#f0f0f0' : 'white'
              }}>
                <input {...getImageInputProps()} />
                {imagePreview ? (
                  <img 
                    src={imagePreview} 
                    alt="Preview" 
                    style={{ maxWidth: '200px', maxHeight: '200px', objectFit: 'cover' }}
                  />
                ) : (
                  <div>
                    <CloudUpload sx={{ fontSize: 48, color: '#ccc', mb: 1 }} />
                    <Typography>
                      Drop an image here, or click to select
                    </Typography>
                    <Typography variant="caption" color="textSecondary">
                      Supported: JPG, PNG, WebP
                    </Typography>
                  </div>
                )}
              </div>

              <div {...getAudioRootProps()} style={{
                border: '2px dashed #ccc',
                borderRadius: '8px',
                padding: '20px',
                textAlign: 'center',
                cursor: 'pointer',
                marginBottom: '16px',
                backgroundColor: isAudioDragActive ? '#f0f0f0' : 'white'
              }}>
                <input {...getAudioInputProps()} />
                {audioPreview ? (
                  <div>
                    <AudioFile sx={{ fontSize: 48, color: '#4caf50', mb: 1 }} />
                    <Typography>{audioFile?.name}</Typography>
                    <audio controls style={{ marginTop: '8px', width: '100%' }}>
                      <source src={audioPreview} />
                    </audio>
                  </div>
                ) : (
                  <div>
                    <AudioFile sx={{ fontSize: 48, color: '#ccc', mb: 1 }} />
                    <Typography>
                      Drop an audio file here, or click to select
                    </Typography>
                    <Typography variant="caption" color="textSecondary">
                      Supported: MP3, WAV, M4A, AAC
                    </Typography>
                  </div>
                )}
              </div>

              <Button
                variant="outlined"
                onClick={clearFiles}
                fullWidth
                sx={{ mt: 1 }}
              >
                Clear All Files
              </Button>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>Generation Settings</Typography>
              
              <FormControl fullWidth margin="normal">
                <InputLabel>Enhancement Method</InputLabel>
                <Select
                  value={formData.enhancer}
                  onChange={(e) => handleInputChange('enhancer', e.target.value)}
                >
                  {enhancerOptions.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>

              <FormControl fullWidth margin="normal">
                <InputLabel>Output Size</InputLabel>
                <Select
                  value={formData.size}
                  onChange={(e) => handleInputChange('size', e.target.value)}
                >
                  {sizeOptions.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>

              <Typography gutterBottom sx={{ mt: 2 }}>
                Expression Scale: {formData.expressionScale}
              </Typography>
              <Slider
                value={formData.expressionScale}
                onChange={(e, value) => handleInputChange('expressionScale', value)}
                min={0.5}
                max={2.0}
                step={0.1}
                marks
                valueLabelDisplay="auto"
              />

              <FormControl fullWidth margin="normal">
                <InputLabel>Animation Mode</InputLabel>
                <Select
                  value={formData.stillMode}
                  onChange={(e) => handleInputChange('stillMode', e.target.value)}
                >
                  <MenuItem value={true}>Still Mode (More Stable)</MenuItem>
                  <MenuItem value={false}>Full Animation (More Dynamic)</MenuItem>
                </Select>
              </FormControl>

              <Button
                fullWidth
                variant="contained"
                size="large"
                onClick={handleGenerate}
                disabled={loading || !imageFile || !audioFile}
                sx={{ mt: 3 }}
              >
                {loading ? <CircularProgress size={24} /> : 'Generate Talking Avatar'}
              </Button>
            </CardContent>
          </Card>
        </Grid>

        {error && (
          <Grid item xs={12}>
            <Card sx={{ backgroundColor: '#ffebee' }}>
              <CardContent>
                <Typography color="error">{error}</Typography>
              </CardContent>
            </Card>
          </Grid>
        )}

        {result && (
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Generated Video {result.cached && '(Cached)'}
                </Typography>
                <video
                  ref={videoRef}
                  controls
                  style={{ width: '100%', maxHeight: '500px' }}
                  src={`data:video/mp4;base64,${result.video_base64}`}
                />
                <Button
                  fullWidth
                  variant="outlined"
                  startIcon={<Download />}
                  onClick={handleDownload}
                  sx={{ mt: 2 }}
                >
                  Download Video
                </Button>
                <Typography variant="caption" display="block" sx={{ mt: 1 }}>
                  Enhancement: {result.enhancer} | Size: {result.size}x{result.size} | GPU Used: {result.gpu_used ? 'Yes' : 'No'}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        )}
      </Grid>
    </div>
  );
};

export default VideoGenerator;