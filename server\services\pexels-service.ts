const PEXELS_API_KEY = process.env.PEXELS_API_KEY;
const PEXELS_BASE_URL = 'https://api.pexels.com/v1';

interface PexelsPhoto {
  id: number;
  url: string;
  photographer: string;
  photographer_url: string;
  photographer_id: number;
  avg_color: string;
  src: {
    original: string;
    large2x: string;
    large: string;
    medium: string;
    small: string;
    portrait: string;
    landscape: string;
    tiny: string;
  };
  liked: boolean;
  alt: string;
}

interface PexelsVideo {
  id: number;
  url: string;
  duration: number;
  user: {
    id: number;
    name: string;
    url: string;
  };
  video_files: Array<{
    id: number;
    quality: string;
    file_type: string;
    width: number;
    height: number;
    link: string;
  }>;
  video_pictures: Array<{
    id: number;
    picture: string;
    nr: number;
  }>;
}

export async function searchPexelsMedia(query: string, type: 'image' | 'video' = 'image', per_page: number = 15) {
  if (!PEXELS_API_KEY) {
    throw new Error('Pexels API key not configured');
  }

  try {
    const endpoint = type === 'video' ? 'videos/search' : 'search';
    const url = `${PEXELS_BASE_URL}/${endpoint}?query=${encodeURIComponent(query)}&per_page=${per_page}`;
    
    const response = await fetch(url, {
      headers: {
        'Authorization': PEXELS_API_KEY,
      },
    });

    if (!response.ok) {
      throw new Error(`Pexels API error: ${response.statusText}`);
    }

    const data = await response.json();
    
    if (type === 'video') {
      return {
        results: data.videos?.map((video: PexelsVideo) => ({
          id: video.id.toString(),
          type: 'video',
          url: video.video_files?.[0]?.link || '',
          thumbnailUrl: video.video_pictures?.[0]?.picture || '',
          title: `Video by ${video.user.name}`,
          author: video.user.name,
          source: 'pexels',
          duration: video.duration,
          description: `High-quality video content`
        })) || [],
        total: data.total_results || 0
      };
    } else {
      return {
        results: data.photos?.map((photo: PexelsPhoto) => ({
          id: photo.id.toString(),
          type: 'image',
          url: photo.src.large,
          thumbnailUrl: photo.src.medium,
          title: photo.alt || `Photo by ${photo.photographer}`,
          author: photo.photographer,
          source: 'pexels',
          description: photo.alt || 'High-quality stock photo'
        })) || [],
        total: data.total_results || 0
      };
    }
  } catch (error) {
    console.error('Pexels search error:', error);
    throw error;
  }
}