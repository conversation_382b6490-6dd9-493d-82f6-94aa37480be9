import fs from 'fs';
import fetch from 'cross-fetch';

// API constants
const ELEVENLABS_API_URL = 'https://api.elevenlabs.io/v1';
const ELEVENLABS_API_KEY = process.env.ELEVENLABS_API_KEY;

// Default voice if none is specified
const DEFAULT_VOICE_ID = '21m00Tcm4TlvDq8ikWAM'; // Rachel voice

// Types for API responses
interface ElevenLabsVoice {
  voice_id: string;
  name: string;
  samples: any[];
  category: string;
  fine_tuning: {
    language: string | null;
    is_allowed_to_fine_tune: boolean;
    fine_tuning_requested: boolean;
    finetuning_state: string;
    verification_attempts?: number;
    verification_failures?: number;
    verification_attempts_count?: number;
    slice_ids?: string[];
  };
  labels: Record<string, string>;
  description: string | null;
  preview_url: string;
  available_for_tiers: string[];
  settings: any | null;
}

/**
 * Get available voices from ElevenLabs API
 */
export async function getVoices(): Promise<ElevenLabsVoice[]> {
  if (!ELEVENLABS_API_KEY) {
    throw new Error('ElevenLabs API key not set');
  }

  try {
    const response = await fetch(`${ELEVENLABS_API_URL}/voices`, {
      method: 'GET',
      headers: {
        'xi-api-key': ELEVENLABS_API_KEY,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`ElevenLabs API error: ${errorData.detail || response.statusText}`);
    }

    const data = await response.json();
    return data.voices || [];
  } catch (error) {
    console.error('Error fetching ElevenLabs voices:', error);
    throw error;
  }
}

/**
 * Generate speech from text using ElevenLabs API
 * @param text Text to convert to speech
 * @param outputPath Path to save the audio file
 * @param voiceId Voice ID to use (optional)
 */
export async function generateSpeech(
  text: string,
  outputPath: string,
  voiceId?: string
): Promise<string> {
  if (!ELEVENLABS_API_KEY) {
    throw new Error('ElevenLabs API key not set');
  }

  const selectedVoiceId = voiceId || DEFAULT_VOICE_ID;

  try {
    const response = await fetch(
      `${ELEVENLABS_API_URL}/text-to-speech/${selectedVoiceId}`,
      {
        method: 'POST',
        headers: {
          'xi-api-key': ELEVENLABS_API_KEY,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text,
          model_id: 'eleven_monolingual_v1',
          voice_settings: {
            stability: 0.5,
            similarity_boost: 0.5,
          },
        }),
      }
    );

    if (!response.ok) {
      let errorMessage = `ElevenLabs API error: ${response.statusText}`;
      try {
        const errorData = await response.json();
        errorMessage = `ElevenLabs API error: ${errorData.detail || response.statusText}`;
      } catch (e) {
        // If error response is not JSON, use default error message
      }
      throw new Error(errorMessage);
    }

    // Get audio as array buffer
    const audioBuffer = await response.arrayBuffer();
    
    // Save to file
    fs.writeFileSync(outputPath, Buffer.from(audioBuffer));
    
    return outputPath;
  } catch (error) {
    console.error('Error generating speech with ElevenLabs:', error);
    throw error;
  }
}

/**
 * Check if ElevenLabs API is available and configured
 */
export async function isElevenLabsAvailable(): Promise<boolean> {
  if (!ELEVENLABS_API_KEY) {
    return false;
  }

  try {
    await getVoices();
    return true;
  } catch (error) {
    console.error('ElevenLabs API check failed:', error);
    return false;
  }
}