/**
 * Secure Secrets Management for AILearnMaster
 * Implements secure handling of API keys, credentials, and sensitive configuration
 */

import crypto from 'crypto';
import fs from 'fs';
import path from 'path';

interface SecretConfig {
  name: string;
  value: string;
  encrypted: boolean;
  lastRotated: Date;
  expiresAt?: Date;
  source: 'env' | 'file' | 'vault' | 'aws' | 'azure';
}

interface SecretsManagerConfig {
  encryptionKey: string;
  secretsPath: string;
  rotationIntervalDays: number;
  enableAutoRotation: boolean;
  enableAuditLogging: boolean;
}

class SecureSecretsManager {
  private secrets: Map<string, SecretConfig> = new Map();
  private config: SecretsManagerConfig;
  private encryptionAlgorithm = 'aes-256-gcm';
  private auditLog: Array<{ timestamp: Date; action: string; secret: string; source: string }> = [];

  constructor() {
    this.config = this.getSecretsConfig();
    this.initializeSecrets();
  }

  /**
   * Get secrets management configuration
   */
  private getSecretsConfig(): SecretsManagerConfig {
    const environment = process.env.NODE_ENV || 'development';
    
    return {
      encryptionKey: this.getOrGenerateEncryptionKey(),
      secretsPath: path.join(process.cwd(), '.secrets'),
      rotationIntervalDays: environment === 'production' ? 30 : 90,
      enableAutoRotation: environment === 'production',
      enableAuditLogging: true
    };
  }

  /**
   * Get or generate encryption key
   */
  private getOrGenerateEncryptionKey(): string {
    // In production, this should come from a secure key management service
    const keyEnvVar = process.env.SECRETS_ENCRYPTION_KEY;
    
    if (keyEnvVar) {
      return keyEnvVar;
    }

    // Generate a key for development (not recommended for production)
    if (process.env.NODE_ENV !== 'production') {
      console.warn('⚠️ WARNING: Using generated encryption key for development only');
      return crypto.randomBytes(32).toString('hex');
    }

    throw new Error('SECRETS_ENCRYPTION_KEY environment variable required in production');
  }

  /**
   * Initialize secrets from various sources
   */
  private initializeSecrets(): void {
    console.log('🔐 Initializing secure secrets management...');

    // Load secrets from environment variables
    this.loadEnvironmentSecrets();

    // Load secrets from encrypted file (if exists)
    this.loadFileSecrets();

    // Validate required secrets
    this.validateRequiredSecrets();

    console.log(`✅ Loaded ${this.secrets.size} secrets securely`);
  }

  /**
   * Load secrets from environment variables
   */
  private loadEnvironmentSecrets(): void {
    const secretEnvVars = [
      'DATABASE_URL',
      'SESSION_SECRET',
      'MODAL_TOKEN_ID',
      'MODAL_TOKEN_SECRET',
      'OPENAI_API_KEY',
      'ELEVENLABS_API_KEY',
      'AWS_ACCESS_KEY_ID',
      'AWS_SECRET_ACCESS_KEY',
      'STRIPE_SECRET_KEY',
      'GOOGLE_API_KEY',
      'PEXELS_API_KEY',
      'PIXABAY_API_KEY'
    ];

    secretEnvVars.forEach(envVar => {
      const value = process.env[envVar];
      if (value) {
        this.setSecret(envVar, value, 'env');
      }
    });
  }

  /**
   * Load secrets from encrypted file
   */
  private loadFileSecrets(): void {
    const secretsFilePath = path.join(this.config.secretsPath, 'encrypted-secrets.json');
    
    if (fs.existsSync(secretsFilePath)) {
      try {
        const encryptedData = fs.readFileSync(secretsFilePath, 'utf8');
        const decryptedSecrets = this.decryptData(encryptedData);
        const secrets = JSON.parse(decryptedSecrets);
        
        Object.entries(secrets).forEach(([name, config]) => {
          this.secrets.set(name, config as SecretConfig);
        });
        
        this.logAudit('LOAD_FILE_SECRETS', 'system', 'file');
      } catch (error) {
        console.error('❌ Failed to load encrypted secrets file:', error);
      }
    }
  }

  /**
   * Validate required secrets are present
   */
  private validateRequiredSecrets(): void {
    const requiredSecrets = [
      'DATABASE_URL',
      'SESSION_SECRET'
    ];

    const missingSecrets = requiredSecrets.filter(secret => !this.secrets.has(secret));
    
    if (missingSecrets.length > 0) {
      throw new Error(`Missing required secrets: ${missingSecrets.join(', ')}`);
    }

    // Validate secret strength
    this.validateSecretStrength();
  }

  /**
   * Validate secret strength
   */
  private validateSecretStrength(): void {
    const sessionSecret = this.getSecret('SESSION_SECRET');
    if (sessionSecret && sessionSecret.length < 32) {
      throw new Error('SESSION_SECRET must be at least 32 characters long');
    }

    // Check for weak or default secrets
    const weakSecrets = ['password', '123456', 'secret', 'default'];
    this.secrets.forEach((config, name) => {
      if (weakSecrets.some(weak => config.value.toLowerCase().includes(weak))) {
        console.warn(`⚠️ WARNING: Weak secret detected for ${name}`);
      }
    });
  }

  /**
   * Set a secret securely
   */
  public setSecret(name: string, value: string, source: SecretConfig['source'] = 'env'): void {
    const secretConfig: SecretConfig = {
      name,
      value: this.encryptValue(value),
      encrypted: true,
      lastRotated: new Date(),
      source
    };

    this.secrets.set(name, secretConfig);
    this.logAudit('SET_SECRET', name, source);
  }

  /**
   * Get a secret securely
   */
  public getSecret(name: string): string | null {
    const secretConfig = this.secrets.get(name);
    
    if (!secretConfig) {
      this.logAudit('GET_SECRET_FAILED', name, 'unknown');
      return null;
    }

    this.logAudit('GET_SECRET', name, secretConfig.source);
    
    return secretConfig.encrypted 
      ? this.decryptValue(secretConfig.value)
      : secretConfig.value;
  }

  /**
   * Get all secrets for a specific service
   */
  public getServiceSecrets(servicePrefix: string): Record<string, string> {
    const serviceSecrets: Record<string, string> = {};
    
    this.secrets.forEach((config, name) => {
      if (name.startsWith(servicePrefix)) {
        const value = config.encrypted 
          ? this.decryptValue(config.value)
          : config.value;
        serviceSecrets[name] = value;
      }
    });

    return serviceSecrets;
  }

  /**
   * Rotate a secret
   */
  public rotateSecret(name: string, newValue: string): void {
    const existingConfig = this.secrets.get(name);
    
    if (!existingConfig) {
      throw new Error(`Secret ${name} not found`);
    }

    const rotatedConfig: SecretConfig = {
      ...existingConfig,
      value: this.encryptValue(newValue),
      lastRotated: new Date()
    };

    this.secrets.set(name, rotatedConfig);
    this.logAudit('ROTATE_SECRET', name, existingConfig.source);
    
    console.log(`🔄 Secret ${name} rotated successfully`);
  }

  /**
   * Check if secrets need rotation
   */
  public checkRotationNeeded(): string[] {
    const needsRotation: string[] = [];
    const rotationThreshold = this.config.rotationIntervalDays * 24 * 60 * 60 * 1000;
    
    this.secrets.forEach((config, name) => {
      const timeSinceRotation = Date.now() - config.lastRotated.getTime();
      
      if (timeSinceRotation > rotationThreshold) {
        needsRotation.push(name);
      }
    });

    return needsRotation;
  }

  /**
   * Encrypt a value
   */
  private encryptValue(value: string): string {
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipher(this.encryptionAlgorithm, this.config.encryptionKey);
    
    let encrypted = cipher.update(value, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    const authTag = cipher.getAuthTag();
    
    return `${iv.toString('hex')}:${authTag.toString('hex')}:${encrypted}`;
  }

  /**
   * Decrypt a value
   */
  private decryptValue(encryptedValue: string): string {
    const [ivHex, authTagHex, encrypted] = encryptedValue.split(':');
    
    const iv = Buffer.from(ivHex, 'hex');
    const authTag = Buffer.from(authTagHex, 'hex');
    
    const decipher = crypto.createDecipher(this.encryptionAlgorithm, this.config.encryptionKey);
    decipher.setAuthTag(authTag);
    
    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
  }

  /**
   * Encrypt data for file storage
   */
  private encryptData(data: string): string {
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipherGCM(this.encryptionAlgorithm, Buffer.from(this.config.encryptionKey, 'hex'), iv);
    
    let encrypted = cipher.update(data, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    const authTag = cipher.getAuthTag();
    
    return JSON.stringify({
      iv: iv.toString('hex'),
      authTag: authTag.toString('hex'),
      data: encrypted
    });
  }

  /**
   * Decrypt data from file storage
   */
  private decryptData(encryptedData: string): string {
    const { iv, authTag, data } = JSON.parse(encryptedData);
    
    const decipher = crypto.createDecipherGCM(
      this.encryptionAlgorithm,
      Buffer.from(this.config.encryptionKey, 'hex'),
      Buffer.from(iv, 'hex')
    );
    
    decipher.setAuthTag(Buffer.from(authTag, 'hex'));
    
    let decrypted = decipher.update(data, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
  }

  /**
   * Save secrets to encrypted file
   */
  public saveSecretsToFile(): void {
    if (!fs.existsSync(this.config.secretsPath)) {
      fs.mkdirSync(this.config.secretsPath, { recursive: true });
    }

    const secretsData: Record<string, SecretConfig> = {};
    this.secrets.forEach((config, name) => {
      // Only save non-environment secrets to file
      if (config.source !== 'env') {
        secretsData[name] = config;
      }
    });

    const encryptedData = this.encryptData(JSON.stringify(secretsData));
    const filePath = path.join(this.config.secretsPath, 'encrypted-secrets.json');
    
    fs.writeFileSync(filePath, encryptedData, { mode: 0o600 }); // Restricted permissions
    
    this.logAudit('SAVE_FILE_SECRETS', 'system', 'file');
    console.log('💾 Secrets saved to encrypted file');
  }

  /**
   * Log audit events
   */
  private logAudit(action: string, secret: string, source: string): void {
    if (!this.config.enableAuditLogging) return;

    const auditEntry = {
      timestamp: new Date(),
      action,
      secret: secret.replace(/./g, '*'), // Mask secret name
      source
    };

    this.auditLog.push(auditEntry);
    
    // Keep only last 1000 audit entries
    if (this.auditLog.length > 1000) {
      this.auditLog = this.auditLog.slice(-1000);
    }

    // Log security-relevant events
    if (['SET_SECRET', 'ROTATE_SECRET', 'GET_SECRET_FAILED'].includes(action)) {
      console.log(`🔐 AUDIT: ${action} for ${auditEntry.secret} from ${source}`);
    }
  }

  /**
   * Get audit log
   */
  public getAuditLog(): typeof this.auditLog {
    return [...this.auditLog]; // Return copy
  }

  /**
   * Clear sensitive data from memory
   */
  public clearSecrets(): void {
    this.secrets.clear();
    this.auditLog.length = 0;
    console.log('🧹 Secrets cleared from memory');
  }

  /**
   * Get secrets status
   */
  public getSecretsStatus(): {
    totalSecrets: number;
    secretsBySource: Record<string, number>;
    needsRotation: string[];
    lastAuditEntries: number;
  } {
    const secretsBySource: Record<string, number> = {};
    
    this.secrets.forEach(config => {
      secretsBySource[config.source] = (secretsBySource[config.source] || 0) + 1;
    });

    return {
      totalSecrets: this.secrets.size,
      secretsBySource,
      needsRotation: this.checkRotationNeeded(),
      lastAuditEntries: this.auditLog.length
    };
  }

  /**
   * Validate environment for production
   */
  public validateProductionEnvironment(): { isValid: boolean; issues: string[] } {
    const issues: string[] = [];

    // Check encryption key
    if (!process.env.SECRETS_ENCRYPTION_KEY) {
      issues.push('SECRETS_ENCRYPTION_KEY not set');
    }

    // Check required secrets
    const requiredSecrets = ['DATABASE_URL', 'SESSION_SECRET'];
    requiredSecrets.forEach(secret => {
      if (!this.getSecret(secret)) {
        issues.push(`Required secret ${secret} not found`);
      }
    });

    // Check secret rotation
    const needsRotation = this.checkRotationNeeded();
    if (needsRotation.length > 0) {
      issues.push(`Secrets need rotation: ${needsRotation.join(', ')}`);
    }

    return {
      isValid: issues.length === 0,
      issues
    };
  }
}

// Create singleton secrets manager
const secretsManager = new SecureSecretsManager();

// Export secure functions
export const getSecret = (name: string): string | null => secretsManager.getSecret(name);
export const setSecret = (name: string, value: string, source?: SecretConfig['source']) => 
  secretsManager.setSecret(name, value, source);
export const getServiceSecrets = (servicePrefix: string) => secretsManager.getServiceSecrets(servicePrefix);
export const rotateSecret = (name: string, newValue: string) => secretsManager.rotateSecret(name, newValue);
export const getSecretsStatus = () => secretsManager.getSecretsStatus();
export const validateProductionSecrets = () => secretsManager.validateProductionEnvironment();

// Graceful shutdown
process.on('SIGTERM', () => {
  secretsManager.clearSecrets();
});

process.on('SIGINT', () => {
  secretsManager.clearSecrets();
});

export { secretsManager };
