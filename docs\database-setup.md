# AILearnMaster Database Setup Guide

## Overview

AILearnMaster uses **PostgreSQL** as its primary database with **Drizzle ORM** for type-safe database operations. The platform is already configured to use PostgreSQL via Neon Database (serverless PostgreSQL hosting).

## Current Database Architecture

### Database Provider
- **Primary**: Neon Database (Serverless PostgreSQL)
- **ORM**: Drizzle ORM with TypeScript
- **Connection**: Pooled connections with automatic scaling
- **Environment**: Production-ready with development support

### Key Features
- ✅ **Type-safe queries** with Drizzle ORM
- ✅ **Connection pooling** for optimal performance
- ✅ **Automatic migrations** with Drizzle Kit
- ✅ **Health monitoring** and diagnostics
- ✅ **Graceful error handling** and fallbacks
- ✅ **Session management** with PostgreSQL store

## Database Schema

### Core Tables
- **users**: User accounts, authentication, and profiles
- **courses**: Course metadata and configuration
- **modules**: Course modules and organization
- **lessons**: Individual lesson content and media
- **media_library**: File storage and media management
- **ai_credits**: AI service usage tracking
- **notifications**: User notification system
- **landing_pages**: Marketing and conversion pages

### Advanced Features
- **micro_learning_segments**: Bite-sized learning content
- **quiz_questions**: Assessment and evaluation
- **course_drafts**: Auto-save and draft management
- **stock_media_cache**: API response caching
- **platform_connections**: LMS integrations

## Environment Setup

### 1. Environment Variables

Copy `.env.example` to `.env` and configure:

```bash
# Primary Database Configuration
DATABASE_URL=postgresql://username:password@host:port/database?sslmode=require

# Optional: Connection Pool Settings
DB_MAX_CONNECTIONS=20
DB_IDLE_TIMEOUT_MS=60000
DB_CONNECTION_TIMEOUT_MS=10000
```

### 2. Local Development Setup

#### Option A: Neon Database (Recommended)
1. Create account at [neon.tech](https://neon.tech)
2. Create new project and database
3. Copy connection string to `DATABASE_URL`
4. Run setup script: `npm run db:setup`

#### Option B: Local PostgreSQL
1. Install PostgreSQL locally
2. Create database: `createdb ailearn_master`
3. Set `DATABASE_URL=postgresql://localhost:5432/ailearn_master`
4. Run setup script: `npm run db:setup`

### 3. Production Setup

#### Neon Database (Recommended)
- Automatic scaling and connection pooling
- Built-in backups and point-in-time recovery
- Global edge locations for low latency
- Serverless architecture with pay-per-use

#### Alternative Providers
- **Supabase**: PostgreSQL with additional features
- **AWS RDS**: Managed PostgreSQL service
- **Google Cloud SQL**: Fully managed database
- **Azure Database**: PostgreSQL as a service

## Database Operations

### Setup and Migration

```bash
# Install dependencies
npm install

# Run database setup (migrations + seeding)
npm run db:setup

# Run migrations only
npm run db:migrate

# Generate new migration
npm run db:generate

# Reset database (development only)
npm run db:reset
```

### Health Monitoring

```bash
# Check database health
curl http://localhost:3001/api/database/health

# Get detailed diagnostics
curl http://localhost:3001/api/database/diagnostics

# Performance metrics
curl http://localhost:3001/api/database/metrics

# Run database tests
curl -X POST http://localhost:3001/api/database/test
```

### Backup and Recovery

```bash
# Create backup (if configured)
npm run db:backup

# Restore from backup
npm run db:restore

# Export data
npm run db:export

# Import data
npm run db:import
```

## Performance Optimization

### Connection Pooling
- **Max Connections**: 20 (production), 10 (development)
- **Idle Timeout**: 60 seconds (production), 30 seconds (development)
- **Connection Timeout**: 10 seconds (production), 5 seconds (development)

### Query Optimization
- **Indexes**: Automatically created for foreign keys and common queries
- **Query Logging**: Enabled in development for debugging
- **Connection Reuse**: Pooled connections for optimal performance

### Caching Strategy
- **Stock Media Cache**: API responses cached for 24 hours
- **Session Store**: PostgreSQL-based session management
- **Query Results**: Application-level caching for frequent queries

## Monitoring and Diagnostics

### Health Check Endpoints

| Endpoint | Purpose | Response |
|----------|---------|----------|
| `/api/database/health` | Basic connectivity | Status + timing |
| `/api/database/diagnostics` | Detailed analysis | Tables + connections |
| `/api/database/metrics` | Performance data | Sizes + usage |
| `/api/database/test` | Operation testing | CRUD validation |

### Key Metrics
- **Connection Count**: Active vs. maximum connections
- **Query Performance**: Response times and slow queries
- **Table Sizes**: Storage usage by table
- **Index Usage**: Query optimization effectiveness

## Security Configuration

### Connection Security
- **SSL/TLS**: Required for all connections
- **Connection Pooling**: Prevents connection exhaustion
- **Timeout Settings**: Prevents hanging connections
- **Error Handling**: Graceful degradation on failures

### Data Protection
- **Password Hashing**: bcrypt with salt rounds
- **Session Security**: Secure session management
- **Input Validation**: Zod schema validation
- **SQL Injection Prevention**: Parameterized queries with Drizzle

## Troubleshooting

### Common Issues

#### Connection Errors
```bash
# Check environment variables
echo $DATABASE_URL

# Test connection
npm run db:test

# Check logs
npm run logs
```

#### Migration Issues
```bash
# Reset migrations (development only)
npm run db:reset

# Force migration
npm run db:migrate --force

# Check migration status
npm run db:status
```

#### Performance Issues
```bash
# Check slow queries
npm run db:slow-queries

# Analyze table sizes
npm run db:analyze

# Monitor connections
npm run db:connections
```

### Error Codes

| Code | Description | Solution |
|------|-------------|----------|
| `ECONNREFUSED` | Connection refused | Check DATABASE_URL and network |
| `ENOTFOUND` | Host not found | Verify hostname in connection string |
| `ETIMEDOUT` | Connection timeout | Check network and increase timeout |
| `28P01` | Invalid credentials | Verify username/password |
| `3D000` | Database not found | Create database or check name |

## Migration from Other Databases

### From SQLite
1. Export data: `sqlite3 database.db .dump > export.sql`
2. Convert to PostgreSQL format
3. Import: `psql -d ailearn_master -f converted.sql`

### From MySQL
1. Use `mysqldump` to export data
2. Convert schema and data types
3. Import using `psql`

### From MongoDB
1. Export collections to JSON
2. Transform data structure
3. Import using custom scripts

## Best Practices

### Development
- Use separate databases for development/staging/production
- Enable query logging for debugging
- Regular database backups
- Monitor connection usage

### Production
- Use connection pooling
- Enable SSL/TLS
- Monitor performance metrics
- Set up automated backups
- Configure alerts for issues

### Schema Changes
- Always use migrations for schema changes
- Test migrations on staging first
- Backup before major changes
- Document schema modifications

## Support and Resources

### Documentation
- [Drizzle ORM Docs](https://orm.drizzle.team/)
- [Neon Database Docs](https://neon.tech/docs)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)

### Community
- [Drizzle Discord](https://discord.gg/drizzle)
- [PostgreSQL Community](https://www.postgresql.org/community/)
- [Neon Community](https://neon.tech/community)

### Professional Support
- Database optimization consulting
- Migration assistance
- Performance tuning
- Custom development
