import OpenAI from "openai";
import * as geminiService from "./gemini";

// Initialize OpenAI client
const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });

// Shared interface for course structure
export interface CourseStructure {
  title: string;
  description: string;
  modules: {
    title: string;
    description: string;
    lessons: {
      title: string;
      description: string;
    }[];
  }[];
}

/**
 * Primary function to generate course structure using OpenAI with Gemini fallback
 * This tries OpenAI first, and if it fails, falls back to Gemini
 */
export async function generateCourseStructure(courseData: {
  title: string;
  description: string;
  category: string;
  targetAudience?: string;
  keyTopics?: string;
  contentNotes?: string;
  moduleCount?: number;
}): Promise<CourseStructure> {
  try {
    // First try using OpenAI
    console.log("Attempting to generate course structure with OpenAI");
    
    // The newest OpenAI model is "gpt-4o" which was released May 13, 2024. do not change this unless explicitly requested by the user
    const response = await openai.chat.completions.create({
      model: "gpt-4o",
      messages: [
        {
          role: "system",
          content: `You are an expert instructional designer specialized in creating pedagogically sound, cohesive educational content.
          Your task is to create a detailed course structure for "${courseData.title}" in the category "${courseData.category}".
          
          Important instructional design principles to follow:
          1. Create a logical progression where each module builds upon previous knowledge
          2. Ensure concepts flow naturally from fundamental to advanced topics
          3. Design a coherent learning journey with clear connections between modules
          4. Structure content to scaffold learning with proper knowledge prerequisites
          5. Balance theory and practical application throughout the learning sequence
          
          The course must include exactly ${courseData.moduleCount || 4} module${(courseData.moduleCount || 4) === 1 ? '' : 's'}, each with 3-6 lessons. Each module and lesson should have a clear title and description that demonstrates how it connects to the overall learning journey.
          Return your response as a JSON object.`
        },
        {
          role: "user",
          content: `I need a detailed course structure for my course titled "${courseData.title}" with the description "${courseData.description}" in the category "${courseData.category}". 
          ${courseData.targetAudience ? `Target Audience: ${courseData.targetAudience}` : ''}
          ${courseData.keyTopics ? `Key Topics: ${courseData.keyTopics}` : ''}
          ${courseData.contentNotes ? `Content Notes: ${courseData.contentNotes}` : ''}
          
          Please create a coherent educational structure with ${courseData.moduleCount || 4} module${(courseData.moduleCount || 4) === 1 ? '' : 's'} that follows a logical learning progression:
          
          1. Each module should build upon concepts from previous modules
          2. Begin with foundational concepts and progress to advanced applications
          3. Ensure there's a clear learning pathway from the first to the last module
          4. Create connections between modules to reinforce learning
          5. Balance theoretical understanding with practical application
          
          Return the response in JSON format with this structure:
          {
            "modules": [
              {
                "title": "Module Title",
                "description": "Module description text that explains how this module fits into the overall learning progression",
                "lessons": [
                  {
                    "title": "Lesson Title",
                    "description": "Lesson description text that shows how this lesson builds on previous concepts"
                  }
                ]
              }
            ]
          }`
        }
      ],
      response_format: { type: "json_object" },
      temperature: 0.7,
    });

    // Parse the JSON response
    const content = response.choices[0].message.content || '{"modules":[]}';
    const structureData = JSON.parse(content);
    
    // Format as CourseStructure
    const result = {
      title: courseData.title,
      description: courseData.description,
      modules: structureData.modules || []
    };

    console.log("Successfully generated course structure with OpenAI");
    return result;
    
  } catch (openAiError) {
    // If OpenAI fails, log the error and try Gemini as a fallback
    console.error("OpenAI API error:", openAiError);
    console.log("Falling back to Gemini for course structure generation");
    
    try {
      // Use Gemini as fallback
      const fallbackStructure = await geminiService.generateCourseStructure(courseData);
      console.log("Successfully generated course structure with Gemini fallback");
      return fallbackStructure;
    } catch (geminiError) {
      console.error("Gemini fallback API error:", geminiError);
      
      // Return a basic structure if both APIs fail
      return {
        title: courseData.title,
        description: courseData.description,
        modules: [
          {
            title: "Introduction to " + courseData.title,
            description: "Get started with the basics of " + courseData.title,
            lessons: [
              {
                title: "Course Overview",
                description: "A brief introduction to the course content"
              },
              {
                title: "Key Concepts",
                description: "Essential concepts you need to understand for this course"
              }
            ]
          }
        ]
      };
    }
  }
}

/**
 * Generate script for a lesson using OpenAI with Gemini fallback
 */
export async function generateLessonScript(
  courseTitle: string, 
  courseDescription: string,
  moduleTitle: string,
  moduleDescription: string,
  lessonTitle: string,
  lessonDescription: string,
  targetAudience: string
): Promise<string> {
  try {
    // First try using OpenAI
    console.log("Attempting to generate lesson script with OpenAI");
    
    // The newest OpenAI model is "gpt-4o" which was released May 13, 2024. do not change this unless explicitly requested by the user
    const response = await openai.chat.completions.create({
      model: "gpt-4o",
      messages: [
        {
          role: "system",
          content: `You are an expert course content creator specialized in writing educational scripts.
          Your task is to create a detailed script for a lesson titled "${lessonTitle}" which is part of the module "${moduleTitle}" 
          in the course "${courseTitle}". The script should be comprehensive, engaging, and educational, 
          targeted at ${targetAudience}.`
        },
        {
          role: "user",
          content: `Please write a detailed script for the lesson "${lessonTitle}" with description "${lessonDescription}".
          This lesson is part of the module "${moduleTitle}" (${moduleDescription}) in my course "${courseTitle}" (${courseDescription}).
          The target audience is: ${targetAudience}.
          The script should be educational, engaging, and cover all necessary aspects of the topic.
          Format it as a complete lesson script with introduction, main content points, and conclusion.`
        }
      ],
      temperature: 0.7,
    });

    const script = response.choices[0].message.content || "Script generation failed. Please try again.";
    console.log("Successfully generated lesson script with OpenAI");
    return script;
    
  } catch (openAiError) {
    // If OpenAI fails, log the error and try Gemini as a fallback
    console.error("OpenAI API error:", openAiError);
    console.log("Falling back to Gemini for lesson script generation");
    
    try {
      // Format the data for Gemini service
      const scriptData = {
        title: lessonTitle,
        description: lessonDescription,
        courseTitle: courseTitle,
        courseDescription: courseDescription,
        targetAudience: targetAudience,
        lessonTitle: lessonTitle,
        lessonDescription: lessonDescription
      };
      
      // Use Gemini as fallback
      const fallbackScript = await geminiService.generateScript(scriptData);
      console.log("Successfully generated lesson script with Gemini fallback");
      return fallbackScript;
    } catch (geminiError) {
      console.error("Gemini fallback API error:", geminiError);
      
      // Return a basic script if both APIs fail
      return `# ${lessonTitle}\n\n## Introduction\n\nWelcome to this lesson on ${lessonTitle}. In this lesson, we will explore ${lessonDescription}.\n\n## Content\n\n[Due to technical issues, the content generation service is temporarily unavailable. Please try again later or edit this script manually.]\n\n## Conclusion\n\nThank you for completing this lesson.`;
    }
  }
}

/**
 * Generate a generic script using OpenAI with Gemini fallback
 */
export async function generateScript(scriptData: {
  title: string;
  description: string;
  targetAudience?: string;
}): Promise<string> {
  try {
    // First try using OpenAI
    console.log("Attempting to generate script with OpenAI");
    
    // The newest OpenAI model is "gpt-4o" which was released May 13, 2024. do not change this unless explicitly requested by the user
    const response = await openai.chat.completions.create({
      model: "gpt-4o",
      messages: [
        {
          role: "system",
          content: `You are an expert content creator specialized in writing educational scripts.
          Your task is to create a detailed script for a topic titled "${scriptData.title}".
          The script should be comprehensive, engaging, and educational.`
        },
        {
          role: "user",
          content: `Please write a detailed script for the topic "${scriptData.title}" with description "${scriptData.description}".
          ${scriptData.targetAudience ? `The target audience is: ${scriptData.targetAudience}.` : ''}
          The script should be educational, engaging, and cover all necessary aspects of the topic.
          Format it as a complete script with introduction, main content points, and conclusion.`
        }
      ],
      temperature: 0.7,
    });

    const script = response.choices[0].message.content || "Script generation failed. Please try again.";
    console.log("Successfully generated script with OpenAI");
    return script;
    
  } catch (openAiError) {
    // If OpenAI fails, log the error and try Gemini as a fallback
    console.error("OpenAI API error:", openAiError);
    console.log("Falling back to Gemini for script generation");
    
    try {
      // Use Gemini as fallback
      const fallbackScript = await geminiService.generateScript(scriptData);
      console.log("Successfully generated script with Gemini fallback");
      return fallbackScript;
    } catch (geminiError) {
      console.error("Gemini fallback API error:", geminiError);
      
      // Return a basic script if both APIs fail
      return `# ${scriptData.title}\n\n## Introduction\n\nWelcome to this content on ${scriptData.title}. In this material, we will explore ${scriptData.description}.\n\n## Content\n\n[Due to technical issues, the content generation service is temporarily unavailable. Please try again later or edit this script manually.]\n\n## Conclusion\n\nThank you for completing this material.`;
    }
  }
}