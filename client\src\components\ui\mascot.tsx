import React from 'react';
import { cn } from '@/lib/utils';

interface MascotProps {
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  expression?: 'happy' | 'thinking' | 'surprised' | 'excited' | 'working';
  animated?: boolean;
}

export function Mascot({
  className,
  size = 'md',
  expression = 'happy',
  animated = true,
}: MascotProps) {
  const sizes = {
    sm: 'w-12 h-12',
    md: 'w-24 h-24',
    lg: 'w-32 h-32',
  };

  const getEyeExpression = () => {
    switch (expression) {
      case 'happy':
        return (
          <>
            <path 
              d="M18 20C19.6569 20 21 18.2091 21 16C21 13.7909 19.6569 12 18 12C16.3431 12 15 13.7909 15 16C15 18.2091 16.3431 20 18 20Z" 
              fill="#2563EB" 
              className="fill-primary"
            />
            <path 
              d="M42 20C43.6569 20 45 18.2091 45 16C45 13.7909 43.6569 12 42 12C40.3431 12 39 13.7909 39 16C39 18.2091 40.3431 20 42 20Z" 
              fill="#2563EB" 
              className="fill-primary"
            />
            <path 
              d="M18 18C20 18 21 16.5 21 15" 
              stroke="white" 
              strokeWidth="1.5" 
              strokeLinecap="round" 
              className={animated ? 'animate-blink' : ''}
            />
            <path 
              d="M42 18C44 18 45 16.5 45 15" 
              stroke="white" 
              strokeWidth="1.5" 
              strokeLinecap="round" 
              className={animated ? 'animate-blink' : ''}
            />
          </>
        );
      case 'thinking':
        return (
          <>
            <path 
              d="M18 20C19.6569 20 21 18.2091 21 16C21 13.7909 19.6569 12 18 12C16.3431 12 15 13.7909 15 16C15 18.2091 16.3431 20 18 20Z" 
              fill="#2563EB" 
              className="fill-primary"
            />
            <path 
              d="M42 20C43.6569 20 45 18.2091 45 16C45 13.7909 43.6569 12 42 12C40.3431 12 39 13.7909 39 16C39 18.2091 40.3431 20 42 20Z" 
              fill="#2563EB" 
              className="fill-primary"
            />
            <path 
              d="M17 16H19" 
              stroke="white" 
              strokeWidth="1.5" 
              strokeLinecap="round" 
              className={animated ? 'animate-blink' : ''}
            />
            <path 
              d="M41 16H43" 
              stroke="white" 
              strokeWidth="1.5" 
              strokeLinecap="round" 
              className={animated ? 'animate-blink' : ''}
            />
          </>
        );
      case 'surprised':
        return (
          <>
            <circle 
              cx="18" 
              cy="16" 
              r="4" 
              fill="#2563EB" 
              className="fill-primary"
            />
            <circle 
              cx="42" 
              cy="16" 
              r="4" 
              fill="#2563EB" 
              className="fill-primary"
            />
            <circle 
              cx="18" 
              cy="16" 
              r="1.5" 
              fill="white" 
              className={animated ? 'animate-pulse' : ''}
            />
            <circle 
              cx="42" 
              cy="16" 
              r="1.5" 
              fill="white" 
              className={animated ? 'animate-pulse' : ''}
            />
          </>
        );
      case 'excited':
        return (
          <>
            <path 
              d="M18 20C19.6569 20 21 18.2091 21 16C21 13.7909 19.6569 12 18 12C16.3431 12 15 13.7909 15 16C15 18.2091 16.3431 20 18 20Z" 
              fill="#2563EB" 
              className={`fill-primary ${animated ? 'animate-bounce' : ''}`}
            />
            <path 
              d="M42 20C43.6569 20 45 18.2091 45 16C45 13.7909 43.6569 12 42 12C40.3431 12 39 13.7909 39 16C39 18.2091 40.3431 20 42 20Z" 
              fill="#2563EB" 
              className={`fill-primary ${animated ? 'animate-bounce' : ''}`}
            />
            <path 
              d="M17 15L19 17" 
              stroke="white" 
              strokeWidth="1.5" 
              strokeLinecap="round" 
            />
            <path 
              d="M19 15L17 17" 
              stroke="white" 
              strokeWidth="1.5" 
              strokeLinecap="round" 
            />
            <path 
              d="M41 15L43 17" 
              stroke="white" 
              strokeWidth="1.5" 
              strokeLinecap="round" 
            />
            <path 
              d="M43 15L41 17" 
              stroke="white" 
              strokeWidth="1.5" 
              strokeLinecap="round" 
            />
          </>
        );
      case 'working':
        return (
          <>
            <path 
              d="M18 20C19.6569 20 21 18.2091 21 16C21 13.7909 19.6569 12 18 12C16.3431 12 15 13.7909 15 16C15 18.2091 16.3431 20 18 20Z" 
              fill="#2563EB" 
              className="fill-primary"
            />
            <path 
              d="M42 20C43.6569 20 45 18.2091 45 16C45 13.7909 43.6569 12 42 12C40.3431 12 39 13.7909 39 16C39 18.2091 40.3431 20 42 20Z" 
              fill="#2563EB" 
              className="fill-primary"
            />
            <path 
              d="M17 14L19 18" 
              stroke="white" 
              strokeWidth="1.5" 
              strokeLinecap="round" 
              className={animated ? 'animate-ping-slow' : ''}
            />
            <path 
              d="M41 14L43 18" 
              stroke="white" 
              strokeWidth="1.5" 
              strokeLinecap="round" 
              className={animated ? 'animate-ping-slow' : ''}
            />
          </>
        );
      default:
        return (
          <>
            <circle cx="18" cy="16" r="3" fill="#2563EB" className="fill-primary" />
            <circle cx="42" cy="16" r="3" fill="#2563EB" className="fill-primary" />
          </>
        );
    }
  };

  const getMouthExpression = () => {
    switch (expression) {
      case 'happy':
        return (
          <path 
            d="M25 34C25 34 27 38 30 38C33 38 35 34 35 34" 
            stroke="#2563EB" 
            strokeWidth="2" 
            strokeLinecap="round" 
            className="stroke-primary"
          />
        );
      case 'thinking':
        return (
          <path 
            d="M28 34H32" 
            stroke="#2563EB" 
            strokeWidth="2" 
            strokeLinecap="round" 
            className={`stroke-primary ${animated ? 'animate-pulse' : ''}`}
          />
        );
      case 'surprised':
        return (
          <circle 
            cx="30" 
            cy="34" 
            r="3" 
            fill="#2563EB" 
            className={`fill-primary ${animated ? 'animate-pulse' : ''}`}
          />
        );
      case 'excited':
        return (
          <path 
            d="M23 34C23 34 26 40 30 40C34 40 37 34 37 34" 
            stroke="#2563EB" 
            strokeWidth="2" 
            strokeLinecap="round" 
            className={`stroke-primary ${animated ? 'animate-bounce' : ''}`}
          />
        );
      case 'working':
        return (
          <path 
            d="M24 35C24 35 26 33 30 33C34 33 36 35 36 35" 
            stroke="#2563EB" 
            strokeWidth="2" 
            strokeLinecap="round" 
            className={`stroke-primary ${animated ? 'animate-pulse-slow' : ''}`}
          />
        );
      default:
        return (
          <path 
            d="M25 34H35" 
            stroke="#2563EB" 
            strokeWidth="2" 
            strokeLinecap="round" 
            className="stroke-primary"
          />
        );
    }
  };

  const getSpecialFeature = () => {
    switch (expression) {
      case 'thinking':
        return (
          <circle 
            cx="50" 
            cy="10" 
            r="3" 
            fill="#2563EB" 
            className={`fill-primary ${animated ? 'animate-float-bubble' : ''}`}
          />
        );
      case 'working':
        return (
          <>
            <path 
              d="M55 17C55 17 59 15 58 12" 
              stroke="#2563EB" 
              strokeWidth="1.5" 
              strokeLinecap="round" 
              className={`stroke-primary ${animated ? 'animate-pulse-slow' : ''}`}
            />
            <path 
              d="M58 12C58 12 61 11 63 13" 
              stroke="#2563EB" 
              strokeWidth="1.5" 
              strokeLinecap="round" 
              className={`stroke-primary ${animated ? 'animate-pulse-slow' : ''}`}
            />
            <path 
              d="M63 13L64 10" 
              stroke="#2563EB" 
              strokeWidth="1.5" 
              strokeLinecap="round" 
              className={`stroke-primary ${animated ? 'animate-pulse-slow' : ''}`}
            />
          </>
        );
      case 'excited':
        return (
          <>
            <path 
              d="M54 8L56 12" 
              stroke="#2563EB" 
              strokeWidth="1.5" 
              strokeLinecap="round" 
              className="stroke-primary animate-ping-slow"
            />
            <path 
              d="M60 10L58 14" 
              stroke="#2563EB" 
              strokeWidth="1.5" 
              strokeLinecap="round" 
              className="stroke-primary animate-ping-slow"
            />
            <path 
              d="M56 6L58 10" 
              stroke="#2563EB" 
              strokeWidth="1.5" 
              strokeLinecap="round" 
              className="stroke-primary animate-ping-slow"
            />
          </>
        );
      default:
        return null;
    }
  };

  return (
    <svg
      viewBox="0 0 60 60"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={cn(sizes[size], 'select-none', className)}
    >
      {/* Head with gradient */}
      <circle 
        cx="30" 
        cy="30" 
        r="25" 
        fill="url(#mascot-gradient)" 
        stroke="rgba(var(--color-primary-rgb), 0.2)" 
        strokeWidth="2"
        className={animated && expression === 'excited' ? 'animate-subtle-bounce' : ''}
      />
      
      {/* Gradient definition */}
      <defs>
        <linearGradient id="mascot-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="white" />
          <stop offset="100%" stopColor="#f8f9ff" />
        </linearGradient>
        <filter id="mascot-glow">
          <feGaussianBlur stdDeviation="2" result="blur" />
          <feComposite in="SourceGraphic" in2="blur" operator="over" />
        </filter>
      </defs>
      
      {/* Eyes */}
      {getEyeExpression()}
      
      {/* Mouth */}
      {getMouthExpression()}
      
      {/* Special features for certain expressions */}
      {getSpecialFeature()}
      
      {/* Antenna for all expressions */}
      <path 
        d="M30 5C30 5 32 0 34 0" 
        stroke="rgba(var(--color-primary-rgb), 0.6)" 
        strokeWidth="1.5" 
        strokeLinecap="round"
        className={animated ? 'animate-sway origin-bottom' : ''}
      />
      <circle 
        cx="34" 
        cy="0" 
        r="1.5" 
        fill="url(#antenna-gradient)"
        className={animated ? 'animate-pulse-slow' : ''}
        filter="url(#mascot-glow)"
      />
      
      {/* Antenna gradient */}
      <defs>
        <linearGradient id="antenna-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="var(--primary)" />
          <stop offset="100%" stopColor="#9f6aff" />
        </linearGradient>
      </defs>
    </svg>
  );
}