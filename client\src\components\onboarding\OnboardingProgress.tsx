import React from 'react';
import { motion } from 'framer-motion';

interface OnboardingProgressProps {
  totalSteps: number;
  currentStep: number;
  onStepClick: (stepIndex: number) => void;
}

export function OnboardingProgress({
  totalSteps,
  currentStep,
  onStepClick,
}: OnboardingProgressProps) {
  return (
    <div className="flex items-center space-x-2">
      {Array.from({ length: totalSteps }).map((_, index) => (
        <button
          key={index}
          onClick={() => onStepClick(index)}
          className="relative w-12 h-2 rounded-full bg-gray-200 overflow-hidden"
          aria-label={`Go to step ${index + 1}`}
        >
          {currentStep === index && (
            <motion.div
              className="absolute inset-0 bg-primary"
              initial={{ x: "-100%" }}
              animate={{ x: "0%" }}
              exit={{ x: "100%" }}
              transition={{ duration: 0.5 }}
              layoutId="progressIndicator"
            />
          )}
        </button>
      ))}
    </div>
  );
}