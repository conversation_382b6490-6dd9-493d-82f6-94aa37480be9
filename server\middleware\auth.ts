import { Request, Response, NextFunction } from 'express';
import { AuthService } from '../services/auth';
import { storage } from '../storage';

/**
 * Authentication middleware
 * Checks if the user is authenticated via session or JWT token
 */
export async function authenticate(req: Request, res: Response, next: NextFunction) {
  try {
    // Check session authentication first
    if (req.session?.userId) {
      const user = await storage.getUser(req.session.userId);
      if (user) {
        // Attach user to request
        req.user = AuthService.createSafeUser(user);
        return next();
      }
    }

    // Check for JWT token in Authorization header
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const token = authHeader.split(' ')[1];
    const decoded = AuthService.verifyToken(token);
    
    if (!decoded || !decoded.id) {
      return res.status(401).json({ message: 'Invalid or expired token' });
    }

    const user = await storage.getUser(decoded.id);
    if (!user) {
      return res.status(401).json({ message: 'User not found' });
    }

    // Attach user to request
    req.user = AuthService.createSafeUser(user);
    next();
  } catch (error) {
    return res.status(401).json({ message: 'Authentication failed' });
  }
}

/**
 * Role-based access control middleware
 * Checks if the authenticated user has the required role
 */
export function authorize(roles: string[]) {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    if (!roles.includes(req.user.role)) {
      return res.status(403).json({ message: 'Insufficient permissions' });
    }

    next();
  };
}

// Extend Express Request interface to include user property
declare global {
  namespace Express {
    interface Request {
      user?: any;
    }
  }
}