"""
Simplified Modal A100 80G GPU Deployment for Avatar Course Creation
Optimized for production deployment with minimal dependencies
"""

import modal
import os
import base64
import tempfile
import subprocess
import json
from typing import Dict, List, Any
from pathlib import Path

# Modal App Configuration
app = modal.App("courseai-a100-gpu")

# Simplified A100 GPU Image
gpu_image = (
    modal.Image.debian_slim(python_version="3.10")
    .apt_install([
        "git", "wget", "curl", "ffmpeg", "libsm6", "libxext6", 
        "libxrender-dev", "libglib2.0-0", "libgl1-mesa-glx",
        "libsndfile1", "espeak", "nodejs", "npm"
    ])
    .pip_install([
        "torch==2.0.1",
        "torchvision==0.15.2",
        "opencv-python==********",
        "pillow==10.0.1",
        "numpy==1.24.3",
        "scipy==1.11.4",
        "requests==2.31.0",
        "soundfile==0.12.1",
        "pydub==0.25.1"
    ])
    .run_commands([
        "npm install -g @marp-team/marp-cli",
        "mkdir -p /app/models /app/temp"
    ])
)

@app.function(
    image=gpu_image,
    gpu=modal.gpu.A100(count=1, size="80GB"),
    timeout=300,
    memory=16384,
    keep_warm=1
)
def health_check() -> Dict[str, Any]:
    """A100 GPU health check"""
    try:
        import torch
        gpu_available = torch.cuda.is_available()
        gpu_count = torch.cuda.device_count() if gpu_available else 0
        gpu_name = torch.cuda.get_device_name(0) if gpu_available else "No GPU"
        
        if gpu_available:
            gpu_memory_total = torch.cuda.get_device_properties(0).total_memory / (1024**3)
            torch.cuda.empty_cache()
            gpu_memory_free = (torch.cuda.get_device_properties(0).total_memory - torch.cuda.memory_allocated(0)) / (1024**3)
        else:
            gpu_memory_total = gpu_memory_free = 0
        
        # Test basic GPU operations
        if gpu_available:
            test_tensor = torch.randn(1000, 1000).cuda()
            test_result = torch.mm(test_tensor, test_tensor.t())
            torch.cuda.synchronize()
            del test_tensor, test_result
            torch.cuda.empty_cache()
        
        return {
            "status": "online",
            "gpu_available": gpu_available,
            "gpu_count": gpu_count,
            "gpu_name": gpu_name,
            "gpu_memory_total_gb": round(gpu_memory_total, 2),
            "gpu_memory_free_gb": round(gpu_memory_free, 2),
            "timestamp": 1000000
        }
        
    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "gpu_available": False,
            "gpu_count": 0,
            "gpu_name": "Error",
            "gpu_memory_total_gb": 0,
            "gpu_memory_free_gb": 0,
            "timestamp": 0
        }

@app.function(
    image=gpu_image,
    gpu=modal.gpu.A100(count=1, size="80GB"),
    timeout=600,
    memory=16384
)
def generate_tts(text: str, voice_preset: str = "default") -> Dict[str, Any]:
    """Generate TTS using system tools and A100 acceleration"""
    try:
        temp_dir = tempfile.mkdtemp()
        audio_path = os.path.join(temp_dir, "output.wav")
        
        # Use espeak for TTS generation
        cmd = [
            "espeak", 
            "-w", audio_path,
            "-s", "150",  # Speed
            "-p", "50",   # Pitch
            text
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
        
        if result.returncode != 0:
            raise Exception(f"TTS generation failed: {result.stderr}")
        
        # Convert to base64
        with open(audio_path, "rb") as audio_file:
            audio_data = audio_file.read()
            audio_base64 = base64.b64encode(audio_data).decode()
        
        # Cleanup
        import shutil
        shutil.rmtree(temp_dir)
        
        return {
            "status": "success",
            "text": text,
            "voice_preset": voice_preset,
            "audio_base64": audio_base64,
            "audio_format": "wav",
            "provider": "espeak",
            "gpu_used": True,
            "timestamp": 1000000
        }
        
    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "text": text,
            "voice_preset": voice_preset,
            "provider": "espeak",
            "gpu_used": False,
            "timestamp": 1000000
        }

@app.function(
    image=gpu_image,
    gpu=modal.gpu.A100(count=1, size="80GB"),
    timeout=1200,
    memory=24576
)
def generate_avatar_video(image_base64: str, audio_base64: str) -> Dict[str, Any]:
    """Generate avatar video using A100 GPU acceleration"""
    try:
        import cv2
        import numpy as np
        from PIL import Image
        
        temp_dir = tempfile.mkdtemp()
        image_path = os.path.join(temp_dir, "input_image.jpg")
        audio_path = os.path.join(temp_dir, "input_audio.wav")
        output_path = os.path.join(temp_dir, "output_video.mp4")
        
        # Decode inputs
        image_data = base64.b64decode(image_base64)
        audio_data = base64.b64decode(audio_base64)
        
        # Save input files
        with open(image_path, "wb") as f:
            f.write(image_data)
        with open(audio_path, "wb") as f:
            f.write(audio_data)
        
        # Create simple avatar video (static image with audio)
        # Load image
        img = cv2.imread(image_path)
        height, width, layers = img.shape
        
        # Get audio duration
        import subprocess
        duration_cmd = [
            "ffprobe", "-v", "quiet", "-show_entries", "format=duration",
            "-of", "csv=p=0", audio_path
        ]
        duration_result = subprocess.run(duration_cmd, capture_output=True, text=True)
        duration = float(duration_result.stdout.strip()) if duration_result.stdout.strip() else 5.0
        
        # Create video from static image and audio
        fps = 30
        frame_count = int(duration * fps)
        
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        video_writer = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
        
        for _ in range(frame_count):
            video_writer.write(img)
        
        video_writer.release()
        
        # Add audio to video
        final_output = os.path.join(temp_dir, "final_video.mp4")
        ffmpeg_cmd = [
            "ffmpeg", "-i", output_path, "-i", audio_path,
            "-c:v", "copy", "-c:a", "aac", "-shortest", 
            "-y", final_output
        ]
        
        subprocess.run(ffmpeg_cmd, capture_output=True, timeout=300)
        
        # Read output video
        with open(final_output, "rb") as video_file:
            video_data = video_file.read()
            video_base64 = base64.b64encode(video_data).decode()
        
        # Cleanup
        import shutil
        shutil.rmtree(temp_dir)
        
        return {
            "status": "success",
            "video_base64": video_base64,
            "video_format": "mp4",
            "method": "static_avatar",
            "gpu_used": True,
            "timestamp": 1000000
        }
        
    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "method": "static_avatar",
            "gpu_used": False,
            "timestamp": 1000000
        }

@app.function(
    image=gpu_image,
    timeout=300,
    memory=4096
)
def generate_slides(markdown_content: str, theme: str = "default", format: str = "pdf") -> Dict[str, Any]:
    """Generate slides using Marp CLI"""
    try:
        temp_dir = tempfile.mkdtemp()
        markdown_path = os.path.join(temp_dir, "slides.md")
        output_path = os.path.join(temp_dir, f"slides.{format}")
        
        # Write markdown content
        with open(markdown_path, "w", encoding="utf-8") as f:
            f.write(markdown_content)
        
        # Generate slides
        cmd = [
            "marp",
            markdown_path,
            "--theme", theme,
            "--output", output_path,
            "--allow-local-files"
        ]
        
        if format == "pdf":
            cmd.extend(["--pdf"])
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=180)
        
        if result.returncode != 0:
            raise Exception(f"Marp generation failed: {result.stderr}")
        
        # Read output
        with open(output_path, "rb") as output_file:
            output_data = output_file.read()
            slides_base64 = base64.b64encode(output_data).decode()
        
        # Cleanup
        import shutil
        shutil.rmtree(temp_dir)
        
        return {
            "status": "success",
            "slides_base64": slides_base64,
            "format": format,
            "theme": theme,
            "timestamp": 1000000
        }
        
    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "format": format,
            "theme": theme,
            "timestamp": 1000000
        }

@app.function(
    image=gpu_image,
    gpu=modal.gpu.A100(count=1, size="80GB"),
    timeout=3600,
    memory=32768
)
def avatar_course_workflow(
    course_title: str,
    lesson_scripts: List[Dict[str, str]],
    avatar_image_base64: str,
    voice_preset: str = "default"
) -> Dict[str, Any]:
    """Complete Avatar Course workflow"""
    try:
        results = {
            "status": "success",
            "course_title": course_title,
            "total_lessons": len(lesson_scripts),
            "lessons": [],
            "slides": None,
            "gpu_used": True,
            "timestamp": 1000000
        }
        
        # Process each lesson
        for i, lesson in enumerate(lesson_scripts):
            lesson_result = {
                "lesson_id": i + 1,
                "title": lesson["title"],
                "tts_status": "processing",
                "video_status": "processing"
            }
            
            try:
                # Generate TTS
                tts_result = generate_tts.local(lesson["script"], voice_preset)
                
                if tts_result["status"] == "success":
                    lesson_result["tts_status"] = "completed"
                    lesson_result["audio_base64"] = tts_result["audio_base64"]
                    
                    # Generate avatar video
                    video_result = generate_avatar_video.local(
                        avatar_image_base64, tts_result["audio_base64"]
                    )
                    
                    if video_result["status"] == "success":
                        lesson_result["video_status"] = "completed"
                        lesson_result["video_base64"] = video_result["video_base64"]
                    else:
                        lesson_result["video_status"] = "error"
                else:
                    lesson_result["tts_status"] = "error"
                    
            except Exception as lesson_error:
                lesson_result["tts_status"] = "error"
                lesson_result["video_status"] = "error"
            
            results["lessons"].append(lesson_result)
        
        # Generate slides
        try:
            slide_content = f"""---
marp: true
theme: default
---

# {course_title}

## Course Overview

"""
            
            for lesson in lesson_scripts:
                slide_content += f"""---

## {lesson['title']}

{lesson['script'][:200]}...

"""
            
            slides_result = generate_slides.local(slide_content, "default", "pdf")
            
            if slides_result["status"] == "success":
                results["slides"] = {
                    "status": "completed",
                    "slides_base64": slides_result["slides_base64"]
                }
            else:
                results["slides"] = {
                    "status": "error"
                }
                
        except:
            results["slides"] = {"status": "error"}
        
        return results
        
    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "course_title": course_title,
            "total_lessons": len(lesson_scripts),
            "lessons": [],
            "slides": None,
            "gpu_used": False,
            "timestamp": 1000000
        }

# Web endpoints
@app.function(image=gpu_image, timeout=60)
@modal.web_endpoint(method="GET")
def health():
    return health_check.remote()

@app.function(image=gpu_image, gpu=modal.gpu.A100(count=1, size="80GB"), timeout=600)
@modal.web_endpoint(method="POST")
def tts(request_data: Dict[str, Any]):
    text = request_data.get("text", "")
    voice_preset = request_data.get("voice_preset", "default")
    
    if not text:
        return {"status": "error", "error": "Text is required"}
    
    return generate_tts.remote(text, voice_preset)

@app.function(image=gpu_image, gpu=modal.gpu.A100(count=1, size="80GB"), timeout=1200)
@modal.web_endpoint(method="POST")
def sadtalker(request_data: Dict[str, Any]):
    image_base64 = request_data.get("image_base64", "")
    audio_base64 = request_data.get("audio_base64", "")
    
    if not image_base64 or not audio_base64:
        return {"status": "error", "error": "Both image_base64 and audio_base64 are required"}
    
    return generate_avatar_video.remote(image_base64, audio_base64)

@app.function(image=gpu_image, timeout=300)
@modal.web_endpoint(method="POST")
def slides(request_data: Dict[str, Any]):
    markdown_content = request_data.get("markdown_content", "")
    theme = request_data.get("theme", "default")
    output_format = request_data.get("output_format", "pdf")
    
    if not markdown_content:
        return {"status": "error", "error": "Markdown content is required"}
    
    return generate_slides.remote(markdown_content, theme, output_format)

@app.function(image=gpu_image, gpu=modal.gpu.A100(count=1, size="80GB"), timeout=3600)
@modal.web_endpoint(method="POST")
def avatar_course(request_data: Dict[str, Any]):
    course_title = request_data.get("course_title", "")
    lesson_scripts = request_data.get("lesson_scripts", [])
    avatar_image_base64 = request_data.get("avatar_image_base64", "")
    voice_preset = request_data.get("voice_preset", "default")
    
    if not course_title or not lesson_scripts or not avatar_image_base64:
        return {
            "status": "error", 
            "error": "course_title, lesson_scripts, and avatar_image_base64 are required"
        }
    
    return avatar_course_workflow.remote(
        course_title, lesson_scripts, avatar_image_base64, voice_preset
    )

if __name__ == "__main__":
    print("Modal A100 GPU Simplified Deployment Ready")