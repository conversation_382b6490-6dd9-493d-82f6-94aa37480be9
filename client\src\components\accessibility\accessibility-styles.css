/* Accessibility Override Styles */

/* High Contrast Mode */
:root.high-contrast {
  --background: #000000;
  --foreground: #ffffff;
  --card: #121212;
  --card-foreground: #ffffff;
  --popover: #121212;
  --popover-foreground: #ffffff;
  --primary: #ffff00;
  --primary-foreground: #000000;
  --secondary: #00ffff;
  --secondary-foreground: #000000;
  --muted: #333333;
  --muted-foreground: #ffffff;
  --accent: #ffff00;
  --accent-foreground: #000000;
  --destructive: #ff0000;
  --destructive-foreground: #ffffff;
  --border: #ffffff;
  --input: #333333;
  --ring: #ffff00;
}

/* Increase contrast for text and interactive elements */
:root.high-contrast p,
:root.high-contrast h1,
:root.high-contrast h2,
:root.high-contrast h3,
:root.high-contrast h4,
:root.high-contrast h5,
:root.high-contrast h6 {
  color: var(--foreground);
}

:root.high-contrast a {
  color: var(--primary);
  text-decoration: underline;
}

:root.high-contrast button,
:root.high-contrast .button {
  border: 2px solid var(--primary);
}

:root.high-contrast button:focus,
:root.high-contrast a:focus,
:root.high-contrast input:focus,
:root.high-contrast select:focus,
:root.high-contrast textarea:focus {
  outline: 3px solid var(--primary);
  outline-offset: 2px;
}

/* Large Text Mode */
:root.large-text {
  --font-size-multiplier: 1.25;
}

:root.large-text body {
  font-size: calc(1rem * var(--font-size-multiplier));
}

:root.large-text h1 {
  font-size: calc(2.25rem * var(--font-size-multiplier));
}

:root.large-text h2 {
  font-size: calc(1.75rem * var(--font-size-multiplier));
}

:root.large-text h3 {
  font-size: calc(1.5rem * var(--font-size-multiplier));
}

:root.large-text h4 {
  font-size: calc(1.25rem * var(--font-size-multiplier));
}

:root.large-text button,
:root.large-text .button,
:root.large-text input,
:root.large-text select,
:root.large-text textarea {
  font-size: calc(1rem * var(--font-size-multiplier));
  padding: calc(0.5rem * var(--font-size-multiplier)) calc(1rem * var(--font-size-multiplier));
}

:root.large-text .text-xs {
  font-size: calc(0.75rem * var(--font-size-multiplier));
}

:root.large-text .text-sm {
  font-size: calc(0.875rem * var(--font-size-multiplier));
}

:root.large-text .text-base {
  font-size: calc(1rem * var(--font-size-multiplier));
}

:root.large-text .text-lg {
  font-size: calc(1.125rem * var(--font-size-multiplier));
}

:root.large-text .text-xl {
  font-size: calc(1.25rem * var(--font-size-multiplier));
}

:root.large-text .text-2xl {
  font-size: calc(1.5rem * var(--font-size-multiplier));
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.001ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.001ms !important;
    scroll-behavior: auto !important;
  }
}

:root.reduced-motion *,
:root.reduced-motion *::before,
:root.reduced-motion *::after {
  animation-duration: 0.001ms !important;
  animation-iteration-count: 1 !important;
  transition-duration: 0.001ms !important;
  scroll-behavior: auto !important;
}

/* Screen Reader Optimizations */
:root.screen-reader-optimized .sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

:root.screen-reader-optimized button,
:root.screen-reader-optimized a,
:root.screen-reader-optimized input,
:root.screen-reader-optimized [role="button"] {
  position: relative;
}

:root.screen-reader-optimized button:focus,
:root.screen-reader-optimized a:focus,
:root.screen-reader-optimized input:focus,
:root.screen-reader-optimized [role="button"]:focus {
  outline: 3px solid var(--ring);
  outline-offset: 2px;
}

/* Voice narration styles */
.voice-narration-active {
  position: relative;
}

.voice-narration-active [data-narrating="true"] {
  position: relative;
  background-color: rgba(var(--primary-rgb), 0.1);
  border-radius: 0.25rem;
  padding: 0.25rem;
}

.voice-narration-controls {
  position: fixed;
  bottom: 1rem;
  right: 1rem;
  z-index: 50;
  background-color: var(--background);
  border: 1px solid var(--border);
  border-radius: 0.5rem;
  padding: 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
              0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Add focus styles for keyboard navigation */
:focus-visible {
  outline: 3px solid var(--ring);
  outline-offset: 2px;
}