import { eq, and, desc } from 'drizzle-orm';
import { Request, Response } from 'express';
import { z } from 'zod';
import { db } from '../db';
import { emailTemplates, users, type EmailTemplate, type InsertEmailTemplate } from '@shared/schema';

// Get all email templates for the authenticated user
export async function getAllEmailTemplates(req: Request, res: Response) {
  try {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    const userId = req.user.id;
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const category = req.query.category as string;
    const search = req.query.search as string;
    const offset = (page - 1) * limit;

    // Build the query
    let query = db
      .select()
      .from(emailTemplates)
      .where(eq(emailTemplates.userId, userId))
      .orderBy(desc(emailTemplates.createdAt))
      .limit(limit)
      .offset(offset);

    // Add category filter if provided
    if (category && category !== 'all') {
      query = query.where(eq(emailTemplates.category, category));
    }

    // Add search filter if provided
    if (search) {
      // This is a simplified search - a real implementation would likely use more sophisticated search features
      query = query.where(eq(emailTemplates.name, search));
    }

    // Execute the query
    const templates = await query;

    // Get the total count for pagination
    const countQuery = db
      .select({ count: db.fn.count() })
      .from(emailTemplates)
      .where(eq(emailTemplates.userId, userId));

    if (category && category !== 'all') {
      countQuery.where(eq(emailTemplates.category, category));
    }

    const countResult = await countQuery;
    const totalCount = parseInt(countResult[0].count.toString());
    const totalPages = Math.ceil(totalCount / limit);

    // Get all categories for filtering
    const categoriesResult = await db
      .select({ category: emailTemplates.category })
      .from(emailTemplates)
      .where(eq(emailTemplates.userId, userId))
      .groupBy(emailTemplates.category);

    const categories = categoriesResult.map(row => row.category).filter(Boolean);

    return res.status(200).json({
      data: templates,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages
      },
      categories
    });
  } catch (error) {
    console.error('Error fetching email templates:', error);
    return res.status(500).json({ message: 'Server error', error: (error as Error).message });
  }
}

// Get a single email template by ID
export async function getEmailTemplateById(req: Request, res: Response) {
  try {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    const userId = req.user.id;
    const templateId = parseInt(req.params.id);

    if (isNaN(templateId)) {
      return res.status(400).json({ message: 'Invalid template ID' });
    }

    const template = await db
      .select()
      .from(emailTemplates)
      .where(and(
        eq(emailTemplates.id, templateId),
        eq(emailTemplates.userId, userId)
      ))
      .limit(1);

    if (!template.length) {
      return res.status(404).json({ message: 'Template not found' });
    }

    return res.status(200).json(template[0]);
  } catch (error) {
    console.error('Error fetching email template:', error);
    return res.status(500).json({ message: 'Server error', error: (error as Error).message });
  }
}

// Create a new email template
export async function createEmailTemplate(req: Request, res: Response) {
  try {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    const userId = req.user.id;

    // Validate request body
    const templateSchema = z.object({
      name: z.string().min(1, 'Name is required'),
      description: z.string().optional(),
      subject: z.string().min(1, 'Subject is required'),
      content: z.string().min(1, 'Content is required'),
      htmlContent: z.string().min(1, 'HTML content is required'),
      thumbnail: z.string().optional(),
      category: z.string().optional(),
      tags: z.array(z.string()).optional(),
      isDefault: z.boolean().optional()
    });

    const validationResult = templateSchema.safeParse(req.body);
    if (!validationResult.success) {
      return res.status(400).json({
        message: 'Validation error',
        errors: validationResult.error.errors
      });
    }

    const templateData = validationResult.data;
    
    // Insert the template
    const newTemplate = await db
      .insert(emailTemplates)
      .values({
        userId,
        name: templateData.name,
        description: templateData.description || null,
        subject: templateData.subject,
        content: templateData.content,
        htmlContent: templateData.htmlContent,
        thumbnail: templateData.thumbnail || null,
        category: templateData.category || 'general',
        tags: templateData.tags || [],
        isDefault: templateData.isDefault || false
      })
      .returning();

    return res.status(201).json(newTemplate[0]);
  } catch (error) {
    console.error('Error creating email template:', error);
    return res.status(500).json({ message: 'Server error', error: (error as Error).message });
  }
}

// Update an existing email template
export async function updateEmailTemplate(req: Request, res: Response) {
  try {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    const userId = req.user.id;
    const templateId = parseInt(req.params.id);

    if (isNaN(templateId)) {
      return res.status(400).json({ message: 'Invalid template ID' });
    }

    // Validate request body
    const templateSchema = z.object({
      name: z.string().min(1, 'Name is required').optional(),
      description: z.string().optional(),
      subject: z.string().min(1, 'Subject is required').optional(),
      content: z.string().min(1, 'Content is required').optional(),
      htmlContent: z.string().min(1, 'HTML content is required').optional(),
      thumbnail: z.string().optional(),
      category: z.string().optional(),
      tags: z.array(z.string()).optional(),
      isDefault: z.boolean().optional()
    });

    const validationResult = templateSchema.safeParse(req.body);
    if (!validationResult.success) {
      return res.status(400).json({
        message: 'Validation error',
        errors: validationResult.error.errors
      });
    }

    // Check if the template belongs to the user
    const existingTemplate = await db
      .select()
      .from(emailTemplates)
      .where(and(
        eq(emailTemplates.id, templateId),
        eq(emailTemplates.userId, userId)
      ))
      .limit(1);

    if (!existingTemplate.length) {
      return res.status(404).json({ message: 'Template not found or not authorized' });
    }

    // Update the template
    const templateData = validationResult.data;
    const updatedTemplate = await db
      .update(emailTemplates)
      .set({
        name: templateData.name || existingTemplate[0].name,
        description: templateData.description !== undefined ? templateData.description : existingTemplate[0].description,
        subject: templateData.subject || existingTemplate[0].subject,
        content: templateData.content || existingTemplate[0].content,
        htmlContent: templateData.htmlContent || existingTemplate[0].htmlContent,
        thumbnail: templateData.thumbnail !== undefined ? templateData.thumbnail : existingTemplate[0].thumbnail,
        category: templateData.category || existingTemplate[0].category,
        tags: templateData.tags || existingTemplate[0].tags,
        isDefault: templateData.isDefault !== undefined ? templateData.isDefault : existingTemplate[0].isDefault,
        updatedAt: new Date()
      })
      .where(and(
        eq(emailTemplates.id, templateId),
        eq(emailTemplates.userId, userId)
      ))
      .returning();

    return res.status(200).json(updatedTemplate[0]);
  } catch (error) {
    console.error('Error updating email template:', error);
    return res.status(500).json({ message: 'Server error', error: (error as Error).message });
  }
}

// Delete an email template
export async function deleteEmailTemplate(req: Request, res: Response) {
  try {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    const userId = req.user.id;
    const templateId = parseInt(req.params.id);

    if (isNaN(templateId)) {
      return res.status(400).json({ message: 'Invalid template ID' });
    }

    // Delete the template
    const deletedTemplate = await db
      .delete(emailTemplates)
      .where(and(
        eq(emailTemplates.id, templateId),
        eq(emailTemplates.userId, userId)
      ))
      .returning();

    if (!deletedTemplate.length) {
      return res.status(404).json({ message: 'Template not found or not authorized' });
    }

    return res.status(200).json({ message: 'Template deleted successfully' });
  } catch (error) {
    console.error('Error deleting email template:', error);
    return res.status(500).json({ message: 'Server error', error: (error as Error).message });
  }
}

// Get template categories
export async function getTemplateCategories(req: Request, res: Response) {
  try {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    const userId = req.user.id;

    // Get all categories
    const categoriesResult = await db
      .select({ category: emailTemplates.category })
      .from(emailTemplates)
      .where(eq(emailTemplates.userId, userId))
      .groupBy(emailTemplates.category);

    const categories = categoriesResult.map(row => row.category).filter(Boolean);

    return res.status(200).json(categories);
  } catch (error) {
    console.error('Error fetching template categories:', error);
    return res.status(500).json({ message: 'Server error', error: (error as Error).message });
  }
}