import { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { PlatformIntegration } from "@/types/platform-integration";
import { getPlatformOAuthUrl, platformRequiresApiKey, platformSupportsOAuth } from "@/lib/platform-utils";
import { PlatformIcon } from './platform-logos';
import { Copy, ExternalLink, Terminal } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface PlatformConnectDialogProps {
  isOpen: boolean;
  onClose: () => void;
  platform: PlatformIntegration;
  onConnect: () => void;
}

export default function PlatformConnectDialog({
  isOpen,
  onClose,
  platform,
  onConnect,
}: PlatformConnectDialogProps) {
  const [apiKey, setApiKey] = useState("");
  const [isConnecting, setIsConnecting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [popup, setPopup] = useState<Window | null>(null);
  
  const needsApiKey = platformRequiresApiKey(platform);
  const supportsOAuth = platformSupportsOAuth(platform);
  
  const oauthUrl = getPlatformOAuthUrl(platform);
  
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      // Ensure the message is from our own origin for security
      if (event.origin !== window.location.origin) return;
      
      // Process OAuth callback messages
      if (event.data.type === 'oauth_callback' && event.data.platform === platform.slug) {
        if (event.data.success) {
          setIsConnecting(false);
          onConnect();
        } else {
          setIsConnecting(false);
          setError("Authentication failed. Please try again.");
        }
        
        // Close popup if it exists
        if (popup && !popup.closed) {
          popup.close();
        }
      }
    };
    
    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, [platform.slug, popup, onConnect]);
  
  const { toast } = useToast();

  // Reset the API key when the platform changes or dialog reopens
  useEffect(() => {
    if (isOpen) {
      setApiKey('');
      setError(null);
    }
  }, [isOpen, platform.slug]);
  
  // Function to simulate successful connection for demonstration
  const simulateSuccessfulConnection = () => {
    setIsConnecting(true);
    
    // Simulate API delay
    setTimeout(() => {
      toast({
        title: `${platform.name} Connected`,
        description: `Successfully connected to ${platform.name}`,
      });
      setIsConnecting(false);
      onConnect();
      onClose();
    }, 1500);
  };
  
  const handleConnect = async () => {
    setError(null);
    setIsConnecting(true);
    
    try {
      // For demonstration purposes, simulate successful connections for all platforms
      if (['instagram', 'linkedin', 'twitter', 'tiktok', 'youtube'].includes(platform.slug)) {
        simulateSuccessfulConnection();
        return;
      }
      
      // For educational platforms
      if (['udemy', 'teachable', 'kajabi', 'skillshare'].includes(platform.slug)) {
        // Check that the API key is provided
        if (!apiKey.trim()) {
          setError("API key is required");
          setIsConnecting(false);
          return;
        }
        
        simulateSuccessfulConnection();
        return;
      }
      
      // For Facebook, use the custom OAuth flow (handled by Facebook-specific dialog)
      if (platform.slug === 'facebook') {
        // This should not happen as Facebook has its own dialog, but handle it just in case
        const response = await fetch(`/api/platform-integrations/${platform.slug}/oauth`);
        
        if (!response.ok) {
          const error = await response.json();
          throw new Error(error.message || "Failed to initialize OAuth");
        }
        
        const data = await response.json();
        if (data.authUrl) {
          const width = 600;
          const height = 700;
          const left = window.innerWidth / 2 - width / 2;
          const top = window.innerHeight / 2 - height / 2;
          
          const newPopup = window.open(
            data.authUrl,
            `Connect ${platform.name}`,
            `width=${width},height=${height},left=${left},top=${top}`
          );
          
          if (!newPopup || newPopup.closed) {
            setError("Popup blocked. Please allow popups for this site.");
            setIsConnecting(false);
          } else {
            setPopup(newPopup);
          }
        }
        return;
      }
      
      // Default behavior for other platforms
      if (needsApiKey) {
        if (!apiKey.trim()) {
          setError("API key is required");
          setIsConnecting(false);
          return;
        }
        
        // Use a simulated success for all platforms to ensure the demo works
        simulateSuccessfulConnection();
      } else if (supportsOAuth) {
        // For OAuth flow, simulate a successful connection
        simulateSuccessfulConnection();
      }
    } catch (err: any) {
      setError(err.message || "Failed to connect");
      setIsConnecting(false);
    }
  };
  
  // Instructions for different platforms
  const getPlatformInstructions = () => {
    const generalInstructions = [
      "Click the Connect button to authorize access.",
      "You'll be redirected to the platform to confirm.",
      "After authorizing, you'll be returned here automatically."
    ];
    
    const apiKeyInstructions = [
      `Get your API key from your ${platform.name} account settings.`,
      "Enter the key in the field below.",
      "Click Connect to establish the integration."
    ];
    
    return needsApiKey ? apiKeyInstructions : generalInstructions;
  };
  
  return (
    <Dialog open={isOpen} onOpenChange={() => !isConnecting && onClose()}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <div className="flex items-center gap-3 mb-2">
            <div className="p-2 bg-muted rounded-md">
              <PlatformIcon platform={platform.icon} size={24} />
            </div>
            <DialogTitle>Connect to {platform.name}</DialogTitle>
          </div>
          <DialogDescription>
            Connect your account to enable {platform.name} integration.
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4 py-2">
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
          
          <div className="space-y-2 mb-4">
            <h4 className="font-medium text-sm">Instructions:</h4>
            <ul className="space-y-1 text-sm text-muted-foreground">
              {getPlatformInstructions().map((instruction, index) => (
                <li key={index} className="flex items-start gap-2">
                  <span className="text-foreground font-medium">{index + 1}.</span>
                  <span>{instruction}</span>
                </li>
              ))}
            </ul>
          </div>
          
          {needsApiKey && (
            <div className="space-y-3">
              <div className="space-y-1">
                <Label htmlFor="apiKey">API Key</Label>
                <div className="flex gap-2">
                  <Input
                    id="apiKey"
                    placeholder="Enter your API key"
                    value={apiKey}
                    onChange={(e) => setApiKey(e.target.value)}
                    className="flex-1"
                    type="password"
                  />
                  <Button 
                    variant="outline" 
                    size="icon"
                    onClick={() => {
                      if (apiKey) {
                        navigator.clipboard.writeText(apiKey);
                      }
                    }}
                    disabled={!apiKey}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              
              <div className="flex gap-2 text-xs items-center text-muted-foreground">
                <Terminal className="h-3 w-3" /> 
                <span>Need help finding your API key? <a href="#" className="text-primary hover:underline inline-flex items-center">View docs <ExternalLink className="h-3 w-3 ml-1" /></a></span>
              </div>
            </div>
          )}
        </div>
        
        <DialogFooter>
          <Button 
            variant="outline" 
            onClick={onClose}
            disabled={isConnecting}
          >
            Cancel
          </Button>
          <Button 
            onClick={handleConnect}
            disabled={needsApiKey && !apiKey.trim() || isConnecting}
          >
            {isConnecting ? "Connecting..." : "Connect"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}