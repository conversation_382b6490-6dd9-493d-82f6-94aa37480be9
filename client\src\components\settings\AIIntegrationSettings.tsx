import React, { useState } from 'react';
import { useMutation, useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import { useToast } from '@/hooks/use-toast';
import { apiRequest, queryClient } from '@/lib/queryClient';
import { User, UserStats } from '@/types';
import { useApiKeys } from '@/hooks/use-api-keys';
import { 
  Brain, 
  Bot, 
  Settings2, 
  MicVocal, 
  Camera, 
  Clapperboard, 
  Plus, 
  Check, 
  X, 
  RefreshCw,
  Sparkles,
  Save,
  Mail
} from 'lucide-react';
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

interface AIIntegrationSettingsProps {
  user: User | undefined;
}

const aiGeneralSettingsSchema = z.object({
  aiEnabled: z.boolean().default(true),
  defaultModel: z.string(),
  temperature: z.number().min(0).max(1),
  maxTokens: z.number().min(100).max(4000),
  creditsNotificationThreshold: z.number().min(0).max(100),
});

const aiVoiceSettingsSchema = z.object({
  ttsEnabled: z.boolean().default(true),
  defaultVoiceId: z.string(),
  defaultVoiceModel: z.string(),
  voiceClarity: z.number().min(0).max(1),
  voiceStability: z.number().min(0).max(1),
  speechRate: z.number().min(0.5).max(2),
});

const aiVideoSettingsSchema = z.object({
  videoGenerationEnabled: z.boolean().default(true),
  defaultStyle: z.string(),
  defaultFormat: z.string(),
  defaultDuration: z.number().min(5).max(300),
  maxResolution: z.string(),
  enhancementLevel: z.number().min(0).max(10),
});

type AIGeneralSettingsValues = z.infer<typeof aiGeneralSettingsSchema>;
type AIVoiceSettingsValues = z.infer<typeof aiVoiceSettingsSchema>;
type AIVideoSettingsValues = z.infer<typeof aiVideoSettingsSchema>;

export default function AIIntegrationSettings({ user }: AIIntegrationSettingsProps) {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState("general");
  const [showRefillConfirm, setShowRefillConfirm] = useState(false);
  const [refillAmount, setRefillAmount] = useState(100);

  // Fetch user stats for AI credits info
  const { data: userStats, isLoading: isLoadingStats } = useQuery({
    queryKey: ['/api/user-stats'],
    queryFn: async () => {
      const response = await apiRequest('GET', '/api/user-stats');
      return await response.json();
    },
  });

  // AI General settings form
  const generalSettingsForm = useForm<AIGeneralSettingsValues>({
    resolver: zodResolver(aiGeneralSettingsSchema),
    defaultValues: {
      aiEnabled: true,
      defaultModel: "gpt-4o", // using the newest model
      temperature: 0.7,
      maxTokens: 2000,
      creditsNotificationThreshold: 20,
    },
  });

  // AI Voice settings form
  const voiceSettingsForm = useForm<AIVoiceSettingsValues>({
    resolver: zodResolver(aiVoiceSettingsSchema),
    defaultValues: {
      ttsEnabled: true,
      defaultVoiceId: "adam",
      defaultVoiceModel: "eleven_multilingual_v2",
      voiceClarity: 0.75,
      voiceStability: 0.7,
      speechRate: 1.0,
    },
  });

  // AI Video settings form
  const videoSettingsForm = useForm<AIVideoSettingsValues>({
    resolver: zodResolver(aiVideoSettingsSchema),
    defaultValues: {
      videoGenerationEnabled: true,
      defaultStyle: "professional",
      defaultFormat: "landscape",
      defaultDuration: 60,
      maxResolution: "1080p",
      enhancementLevel: 7,
    },
  });

  // Mutation for updating general AI settings
  const updateGeneralSettingsMutation = useMutation({
    mutationFn: async (data: AIGeneralSettingsValues) => {
      const response = await apiRequest('PATCH', '/api/settings/ai/general', data);
      return await response.json();
    },
    onSuccess: () => {
      toast({
        title: 'AI settings updated',
        description: 'Your AI general settings have been updated successfully.',
      });
    },
    onError: (error) => {
      toast({
        variant: 'destructive',
        title: 'Error updating settings',
        description: error.message || 'Something went wrong. Please try again.',
      });
    }
  });

  // Mutation for updating voice AI settings
  const updateVoiceSettingsMutation = useMutation({
    mutationFn: async (data: AIVoiceSettingsValues) => {
      const response = await apiRequest('PATCH', '/api/settings/ai/voice', data);
      return await response.json();
    },
    onSuccess: () => {
      toast({
        title: 'Voice settings updated',
        description: 'Your AI voice settings have been updated successfully.',
      });
    },
    onError: (error) => {
      toast({
        variant: 'destructive',
        title: 'Error updating settings',
        description: error.message || 'Something went wrong. Please try again.',
      });
    }
  });

  // Mutation for updating video AI settings
  const updateVideoSettingsMutation = useMutation({
    mutationFn: async (data: AIVideoSettingsValues) => {
      const response = await apiRequest('PATCH', '/api/settings/ai/video', data);
      return await response.json();
    },
    onSuccess: () => {
      toast({
        title: 'Video settings updated',
        description: 'Your AI video settings have been updated successfully.',
      });
    },
    onError: (error) => {
      toast({
        variant: 'destructive',
        title: 'Error updating settings',
        description: error.message || 'Something went wrong. Please try again.',
      });
    }
  });

  // Mutation for purchasing more AI credits
  const purchaseAICreditsMutation = useMutation({
    mutationFn: async (amount: number) => {
      const response = await apiRequest('POST', '/api/billing/purchase-ai-credits', { amount });
      return await response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/user-stats'] });
      setShowRefillConfirm(false);
      toast({
        title: 'AI credits purchased',
        description: `You've successfully purchased ${refillAmount} AI credits.`,
      });
    },
    onError: (error) => {
      toast({
        variant: 'destructive',
        title: 'Error purchasing credits',
        description: error.message || 'Something went wrong. Please try again.',
      });
    }
  });

  // Use our API keys management hook
  const {
    apiKeys,
    isLoading: isLoadingApiKeys,
    verifyApiKey,
    deleteApiKey,
    toggleApiKey,
    getApiKeyByService,
    isVerifying,
    isDeleting,
    isToggling
  } = useApiKeys();

  // Submit handlers
  const onGeneralSettingsSubmit = (data: AIGeneralSettingsValues) => {
    updateGeneralSettingsMutation.mutate(data);
  };

  const onVoiceSettingsSubmit = (data: AIVoiceSettingsValues) => {
    updateVoiceSettingsMutation.mutate(data);
  };

  const onVideoSettingsSubmit = (data: AIVideoSettingsValues) => {
    updateVideoSettingsMutation.mutate(data);
  };

  const handleApiKeyVerify = (service: string, apiKey: string) => {
    if (!apiKey) {
      toast({
        variant: 'destructive',
        title: 'Missing API key',
        description: 'Please enter an API key to verify.',
      });
      return;
    }
    
    verifyApiKey({ service, apiKey });
  };

  return (
    <div className="space-y-6">
      {/* Quick Links */}
      <div className="flex flex-wrap gap-4 mb-4">
        <Button variant="outline" asChild>
          <a href="/settings/email" className="flex items-center gap-2">
            <Mail className="h-4 w-4" /> Email Settings
          </a>
        </Button>
      </div>
      {/* AI Credits Summary Card */}
      <Card>
        <CardHeader>
          <CardTitle>AI Credits</CardTitle>
          <CardDescription>
            Monitor and manage your AI credit usage
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoadingStats ? (
            <div className="flex items-center justify-center h-32">
              <RefreshCw className="h-8 w-8 animate-spin text-primary/70" />
            </div>
          ) : (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <Card className="bg-primary/5 border-primary/20">
                  <CardContent className="pt-6">
                    <div className="text-center">
                      <Sparkles className="h-8 w-8 text-primary mx-auto mb-2" />
                      <h3 className="text-2xl font-bold">{userStats?.aiCredits || 0}</h3>
                      <p className="text-sm text-muted-foreground">Available Credits</p>
                    </div>
                  </CardContent>
                </Card>
                
                <Card className="bg-primary/5 border-primary/20">
                  <CardContent className="pt-6">
                    <div className="text-center">
                      <Bot className="h-8 w-8 text-primary mx-auto mb-2" />
                      <h3 className="text-2xl font-bold">450</h3>
                      <p className="text-sm text-muted-foreground">Credits Used This Month</p>
                    </div>
                  </CardContent>
                </Card>
                
                <Card className="bg-primary/5 border-primary/20">
                  <CardContent className="pt-6">
                    <div className="text-center">
                      <MicVocal className="h-8 w-8 text-primary mx-auto mb-2" />
                      <h3 className="text-2xl font-bold">25,000</h3>
                      <p className="text-sm text-muted-foreground">Words Generated</p>
                    </div>
                  </CardContent>
                </Card>
              </div>
              
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <Label>Monthly Credit Usage</Label>
                  <span className="text-sm text-muted-foreground">450 / 500</span>
                </div>
                <Progress value={90} className="h-2" />
                <p className="text-xs text-muted-foreground">Your plan includes 500 AI credits per month. Credits reset on the 1st of each month.</p>
              </div>
              
              <div className="flex justify-end gap-3">
                <AlertDialog open={showRefillConfirm} onOpenChange={setShowRefillConfirm}>
                  <AlertDialogTrigger asChild>
                    <Button variant="outline">
                      <Plus className="mr-2 h-4 w-4" />
                      Purchase Credits
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Purchase additional AI credits</AlertDialogTitle>
                      <AlertDialogDescription>
                        Additional credits will be added to your account and billed immediately.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <div className="py-4 space-y-4">
                      <div className="space-y-2">
                        <Label>Select amount:</Label>
                        <div className="grid grid-cols-3 gap-2">
                          <Button 
                            variant={refillAmount === 100 ? "default" : "outline"} 
                            onClick={() => setRefillAmount(100)}
                            className="h-auto py-2 px-4"
                          >
                            <div className="text-center">
                              <div className="font-semibold">100</div>
                              <div className="text-xs">$10</div>
                            </div>
                          </Button>
                          <Button 
                            variant={refillAmount === 500 ? "default" : "outline"} 
                            onClick={() => setRefillAmount(500)}
                            className="h-auto py-2 px-4"
                          >
                            <div className="text-center">
                              <div className="font-semibold">500</div>
                              <div className="text-xs">$45</div>
                            </div>
                          </Button>
                          <Button 
                            variant={refillAmount === 1000 ? "default" : "outline"} 
                            onClick={() => setRefillAmount(1000)}
                            className="h-auto py-2 px-4"
                          >
                            <div className="text-center">
                              <div className="font-semibold">1000</div>
                              <div className="text-xs">$80</div>
                            </div>
                          </Button>
                        </div>
                      </div>
                    </div>
                    <AlertDialogFooter>
                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                      <AlertDialogAction
                        onClick={() => purchaseAICreditsMutation.mutate(refillAmount)}
                        disabled={purchaseAICreditsMutation.isPending}
                      >
                        {purchaseAICreditsMutation.isPending ? 'Processing...' : 'Purchase Now'}
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
                
                <Button variant="outline" onClick={() => queryClient.invalidateQueries({ queryKey: ['/api/user-stats'] })}>
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Refresh
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* API Key Configuration */}
      <Card>
        <CardHeader>
          <CardTitle>API Keys Configuration</CardTitle>
          <CardDescription>
            Configure your API keys for external AI services
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {isLoadingApiKeys ? (
            <div className="flex items-center justify-center h-32">
              <RefreshCw className="h-8 w-8 animate-spin text-primary/70" />
            </div>
          ) : (
            <div className="grid gap-6">
              {/* OpenAI API Key */}
              <div className="space-y-3">
                <Label>OpenAI API Key</Label>
                {getApiKeyByService('openai') ? (
                  <div className="rounded-md border bg-muted/50 p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium flex items-center">
                          <Check className="h-4 w-4 mr-2 text-green-500" />
                          API Key Saved
                        </p>
                        <p className="text-xs text-muted-foreground mt-1">
                          Last verified: {new Date(getApiKeyByService('openai')?.lastVerified || Date.now()).toLocaleDateString()}
                        </p>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => toggleApiKey(getApiKeyByService('openai')?.id || 0)}
                          disabled={isToggling}
                        >
                          {getApiKeyByService('openai')?.isActive ? 'Disable' : 'Enable'}
                        </Button>
                        <Button 
                          variant="destructive" 
                          size="sm"
                          onClick={() => deleteApiKey(getApiKeyByService('openai')?.id || 0)}
                          disabled={isDeleting}
                        >
                          Remove
                        </Button>
                      </div>
                    </div>
                  </div>
                ) : (
                  <>
                    <div className="flex gap-2">
                      <Input 
                        type="password" 
                        placeholder="sk-..." 
                        className="font-mono"
                        id="openai-api-key"
                      />
                      <Button 
                        variant="outline" 
                        onClick={() => {
                          const input = document.getElementById('openai-api-key') as HTMLInputElement;
                          handleApiKeyVerify('openai', input.value);
                        }}
                        disabled={isVerifying}
                      >
                        {isVerifying ? 'Verifying...' : 'Verify & Save'}
                      </Button>
                    </div>
                  </>
                )}
                <p className="text-xs text-muted-foreground">
                  Used for generating content, scripts, and images. <a href="https://platform.openai.com/account/api-keys" target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">Get your API key</a>
                </p>
              </div>
              
              {/* ElevenLabs API Key */}
              <div className="space-y-3">
                <Label>ElevenLabs API Key</Label>
                {getApiKeyByService('elevenlabs') ? (
                  <div className="rounded-md border bg-muted/50 p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium flex items-center">
                          <Check className="h-4 w-4 mr-2 text-green-500" />
                          API Key Saved
                        </p>
                        <p className="text-xs text-muted-foreground mt-1">
                          Last verified: {new Date(getApiKeyByService('elevenlabs')?.lastVerified || Date.now()).toLocaleDateString()}
                        </p>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => toggleApiKey(getApiKeyByService('elevenlabs')?.id || 0)}
                          disabled={isToggling}
                        >
                          {getApiKeyByService('elevenlabs')?.isActive ? 'Disable' : 'Enable'}
                        </Button>
                        <Button 
                          variant="destructive" 
                          size="sm"
                          onClick={() => deleteApiKey(getApiKeyByService('elevenlabs')?.id || 0)}
                          disabled={isDeleting}
                        >
                          Remove
                        </Button>
                      </div>
                    </div>
                  </div>
                ) : (
                  <>
                    <div className="flex gap-2">
                      <Input 
                        type="password" 
                        placeholder="xxxxxxxx..." 
                        className="font-mono" 
                        id="elevenlabs-api-key"
                      />
                      <Button 
                        variant="outline" 
                        onClick={() => {
                          const input = document.getElementById('elevenlabs-api-key') as HTMLInputElement;
                          handleApiKeyVerify('elevenlabs', input.value);
                        }}
                        disabled={isVerifying}
                      >
                        {isVerifying ? 'Verifying...' : 'Verify & Save'}
                      </Button>
                    </div>
                  </>
                )}
                <p className="text-xs text-muted-foreground">
                  Used for text-to-speech voice generation. <a href="https://elevenlabs.io/subscription" target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">Get your API key</a>
                </p>
              </div>
              
              {/* Google API Key */}
              <div className="space-y-3">
                <Label>Google Gemini API Key</Label>
                {getApiKeyByService('google') ? (
                  <div className="rounded-md border bg-muted/50 p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium flex items-center">
                          <Check className="h-4 w-4 mr-2 text-green-500" />
                          API Key Saved
                        </p>
                        <p className="text-xs text-muted-foreground mt-1">
                          Last verified: {new Date(getApiKeyByService('google')?.lastVerified || Date.now()).toLocaleDateString()}
                        </p>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => toggleApiKey(getApiKeyByService('google')?.id || 0)}
                          disabled={isToggling}
                        >
                          {getApiKeyByService('google')?.isActive ? 'Disable' : 'Enable'}
                        </Button>
                        <Button 
                          variant="destructive" 
                          size="sm"
                          onClick={() => deleteApiKey(getApiKeyByService('google')?.id || 0)}
                          disabled={isDeleting}
                        >
                          Remove
                        </Button>
                      </div>
                    </div>
                  </div>
                ) : (
                  <>
                    <div className="flex gap-2">
                      <Input 
                        type="password" 
                        placeholder="AIzaSy..." 
                        className="font-mono" 
                        id="google-api-key"
                      />
                      <Button 
                        variant="outline" 
                        onClick={() => {
                          const input = document.getElementById('google-api-key') as HTMLInputElement;
                          handleApiKeyVerify('google', input.value);
                        }}
                        disabled={isVerifying}
                      >
                        {isVerifying ? 'Verifying...' : 'Verify & Save'}
                      </Button>
                    </div>
                  </>
                )}
                <p className="text-xs text-muted-foreground">
                  Used for alternative AI models and advanced features. <a href="https://ai.google.dev/tutorials/setup" target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">Get your API key</a>
                </p>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* AI Settings Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid grid-cols-3 w-full">
          <TabsTrigger value="general" className="flex items-center">
            <Brain className="h-4 w-4 mr-2" />
            <span>General</span>
          </TabsTrigger>
          <TabsTrigger value="voice" className="flex items-center">
            <MicVocal className="h-4 w-4 mr-2" />
            <span>Voice</span>
          </TabsTrigger>
          <TabsTrigger value="video" className="flex items-center">
            <Clapperboard className="h-4 w-4 mr-2" />
            <span>Video</span>
          </TabsTrigger>
        </TabsList>

        {/* General AI Settings */}
        <TabsContent value="general">
          <Card>
            <CardHeader>
              <CardTitle>AI General Settings</CardTitle>
              <CardDescription>
                Configure general AI behavior and defaults
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Form {...generalSettingsForm}>
                <form onSubmit={generalSettingsForm.handleSubmit(onGeneralSettingsSubmit)} className="space-y-6">
                  <FormField
                    control={generalSettingsForm.control}
                    name="aiEnabled"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4 shadow-sm">
                        <div className="space-y-0.5">
                          <FormLabel>Enable AI Features</FormLabel>
                          <FormDescription>
                            Enable or disable all AI-powered features
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <FormField
                      control={generalSettingsForm.control}
                      name="defaultModel"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Default AI Model</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select AI model" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="gpt-4o">GPT-4o (Recommended)</SelectItem>
                              <SelectItem value="gpt-3.5-turbo">GPT-3.5 Turbo (Faster)</SelectItem>
                              <SelectItem value="gemini-pro">Google Gemini Pro</SelectItem>
                              <SelectItem value="claude-3-opus">Claude 3 Opus</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            Default model for content generation
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={generalSettingsForm.control}
                      name="creditsNotificationThreshold"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Credits Notification Threshold (%)</FormLabel>
                          <div className="flex items-center gap-4">
                            <FormControl>
                              <Slider
                                min={0}
                                max={100}
                                step={5}
                                value={[field.value]}
                                onValueChange={(value) => field.onChange(value[0])}
                                className="flex-1"
                              />
                            </FormControl>
                            <span className="w-8 text-center">{field.value}%</span>
                          </div>
                          <FormDescription>
                            Get notified when credits fall below this percentage
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <Separator />
                  
                  <div className="space-y-4">
                    <div>
                      <h3 className="text-lg font-medium mb-2">Advanced Settings</h3>
                      <p className="text-sm text-muted-foreground mb-4">
                        Fine-tune AI behavior and responses
                      </p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <FormField
                        control={generalSettingsForm.control}
                        name="temperature"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Temperature</FormLabel>
                            <div className="flex items-center gap-4">
                              <FormControl>
                                <Slider
                                  min={0}
                                  max={1}
                                  step={0.1}
                                  value={[field.value]}
                                  onValueChange={(value) => field.onChange(value[0])}
                                  className="flex-1"
                                />
                              </FormControl>
                              <span className="w-10 text-center">{field.value.toFixed(1)}</span>
                            </div>
                            <FormDescription>
                              Higher values make output more random, lower values more deterministic
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={generalSettingsForm.control}
                        name="maxTokens"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Max Tokens</FormLabel>
                            <div className="flex items-center gap-4">
                              <FormControl>
                                <Slider
                                  min={100}
                                  max={4000}
                                  step={100}
                                  value={[field.value]}
                                  onValueChange={(value) => field.onChange(value[0])}
                                  className="flex-1"
                                />
                              </FormControl>
                              <span className="w-14 text-center">{field.value}</span>
                            </div>
                            <FormDescription>
                              Maximum length of generated responses
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>

                  <Button 
                    type="submit" 
                    className="w-full"
                    disabled={updateGeneralSettingsMutation.isPending}
                  >
                    {updateGeneralSettingsMutation.isPending ? (
                      <span className="flex items-center">
                        <Settings2 className="mr-2 h-4 w-4 animate-spin" />
                        Saving...
                      </span>
                    ) : (
                      <span className="flex items-center">
                        <Save className="mr-2 h-4 w-4" />
                        Save General Settings
                      </span>
                    )}
                  </Button>
                </form>
              </Form>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Voice AI Settings */}
        <TabsContent value="voice">
          <Card>
            <CardHeader>
              <CardTitle>AI Voice Settings</CardTitle>
              <CardDescription>
                Configure text-to-speech and voice generation settings
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Form {...voiceSettingsForm}>
                <form onSubmit={voiceSettingsForm.handleSubmit(onVoiceSettingsSubmit)} className="space-y-6">
                  <FormField
                    control={voiceSettingsForm.control}
                    name="ttsEnabled"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4 shadow-sm">
                        <div className="space-y-0.5">
                          <FormLabel>Enable Text-to-Speech</FormLabel>
                          <FormDescription>
                            Enable or disable text-to-speech features
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <FormField
                      control={voiceSettingsForm.control}
                      name="defaultVoiceId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Default Voice</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select default voice" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="adam">Adam (Male)</SelectItem>
                              <SelectItem value="emily">Emily (Female)</SelectItem>
                              <SelectItem value="daniel">Daniel (Male)</SelectItem>
                              <SelectItem value="sophia">Sophia (Female)</SelectItem>
                              <SelectItem value="james">James (Male)</SelectItem>
                              <SelectItem value="olivia">Olivia (Female)</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            Default voice for text-to-speech
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={voiceSettingsForm.control}
                      name="defaultVoiceModel"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Voice Model</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select voice model" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="eleven_monolingual_v1">Monolingual v1 (English only)</SelectItem>
                              <SelectItem value="eleven_multilingual_v2">Multilingual v2 (25 languages)</SelectItem>
                              <SelectItem value="eleven_turbo_v2">Turbo v2 (Faster generation)</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            Model for voice generation
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <Separator />
                  
                  <div className="space-y-4">
                    <div>
                      <h3 className="text-lg font-medium mb-2">Voice Quality Settings</h3>
                      <p className="text-sm text-muted-foreground mb-4">
                        Fine-tune generated voice quality and characteristics
                      </p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <FormField
                        control={voiceSettingsForm.control}
                        name="voiceClarity"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Voice Clarity</FormLabel>
                            <div className="flex items-center gap-4">
                              <FormControl>
                                <Slider
                                  min={0}
                                  max={1}
                                  step={0.05}
                                  value={[field.value]}
                                  onValueChange={(value) => field.onChange(value[0])}
                                  className="flex-1"
                                />
                              </FormControl>
                              <span className="w-12 text-center">{(field.value * 100).toFixed(0)}%</span>
                            </div>
                            <FormDescription>
                              Clarity level of the voice
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={voiceSettingsForm.control}
                        name="voiceStability"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Voice Stability</FormLabel>
                            <div className="flex items-center gap-4">
                              <FormControl>
                                <Slider
                                  min={0}
                                  max={1}
                                  step={0.05}
                                  value={[field.value]}
                                  onValueChange={(value) => field.onChange(value[0])}
                                  className="flex-1"
                                />
                              </FormControl>
                              <span className="w-12 text-center">{(field.value * 100).toFixed(0)}%</span>
                            </div>
                            <FormDescription>
                              Stability of voice characteristics
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={voiceSettingsForm.control}
                        name="speechRate"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Speech Rate</FormLabel>
                            <div className="flex items-center gap-4">
                              <FormControl>
                                <Slider
                                  min={0.5}
                                  max={2}
                                  step={0.1}
                                  value={[field.value]}
                                  onValueChange={(value) => field.onChange(value[0])}
                                  className="flex-1"
                                />
                              </FormControl>
                              <span className="w-12 text-center">{field.value.toFixed(1)}x</span>
                            </div>
                            <FormDescription>
                              Speed of speech (1.0x is normal)
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>

                  <Button 
                    type="submit" 
                    className="w-full"
                    disabled={updateVoiceSettingsMutation.isPending}
                  >
                    {updateVoiceSettingsMutation.isPending ? (
                      <span className="flex items-center">
                        <Settings2 className="mr-2 h-4 w-4 animate-spin" />
                        Saving...
                      </span>
                    ) : (
                      <span className="flex items-center">
                        <Save className="mr-2 h-4 w-4" />
                        Save Voice Settings
                      </span>
                    )}
                  </Button>
                </form>
              </Form>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Video AI Settings */}
        <TabsContent value="video">
          <Card>
            <CardHeader>
              <CardTitle>AI Video Settings</CardTitle>
              <CardDescription>
                Configure AI video generation settings
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Form {...videoSettingsForm}>
                <form onSubmit={videoSettingsForm.handleSubmit(onVideoSettingsSubmit)} className="space-y-6">
                  <FormField
                    control={videoSettingsForm.control}
                    name="videoGenerationEnabled"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4 shadow-sm">
                        <div className="space-y-0.5">
                          <FormLabel>Enable Video Generation</FormLabel>
                          <FormDescription>
                            Enable or disable AI video generation features
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <FormField
                      control={videoSettingsForm.control}
                      name="defaultStyle"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Default Video Style</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select video style" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="professional">Professional</SelectItem>
                              <SelectItem value="animated">Animated</SelectItem>
                              <SelectItem value="minimalist">Minimalist</SelectItem>
                              <SelectItem value="cinematic">Cinematic</SelectItem>
                              <SelectItem value="documentary">Documentary</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            Default visual style for generated videos
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={videoSettingsForm.control}
                      name="defaultFormat"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Default Format</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select format" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="landscape">Landscape (16:9)</SelectItem>
                              <SelectItem value="square">Square (1:1)</SelectItem>
                              <SelectItem value="vertical">Vertical (9:16)</SelectItem>
                              <SelectItem value="ultrawide">Ultrawide (21:9)</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            Default format for video generation
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <FormField
                      control={videoSettingsForm.control}
                      name="defaultDuration"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Default Duration (seconds)</FormLabel>
                          <div className="flex items-center gap-4">
                            <FormControl>
                              <Slider
                                min={5}
                                max={300}
                                step={5}
                                value={[field.value]}
                                onValueChange={(value) => field.onChange(value[0])}
                                className="flex-1"
                              />
                            </FormControl>
                            <span className="w-12 text-center">{field.value}s</span>
                          </div>
                          <FormDescription>
                            Default duration for generated videos
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={videoSettingsForm.control}
                      name="maxResolution"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Maximum Resolution</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select resolution" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="720p">720p HD</SelectItem>
                              <SelectItem value="1080p">1080p Full HD</SelectItem>
                              <SelectItem value="1440p">1440p Quad HD</SelectItem>
                              <SelectItem value="2160p">2160p 4K UHD</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            Maximum resolution for video generation
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <Separator />
                  
                  <FormField
                    control={videoSettingsForm.control}
                    name="enhancementLevel"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Video Enhancement Level</FormLabel>
                        <div className="flex items-center gap-4">
                          <FormControl>
                            <Slider
                              min={0}
                              max={10}
                              step={1}
                              value={[field.value]}
                              onValueChange={(value) => field.onChange(value[0])}
                              className="flex-1"
                            />
                          </FormControl>
                          <span className="w-8 text-center">{field.value}</span>
                        </div>
                        <FormDescription>
                          Higher values produce more detailed videos but use more credits (0-10)
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <Button 
                    type="submit" 
                    className="w-full"
                    disabled={updateVideoSettingsMutation.isPending}
                  >
                    {updateVideoSettingsMutation.isPending ? (
                      <span className="flex items-center">
                        <Settings2 className="mr-2 h-4 w-4 animate-spin" />
                        Saving...
                      </span>
                    ) : (
                      <span className="flex items-center">
                        <Save className="mr-2 h-4 w-4" />
                        Save Video Settings
                      </span>
                    )}
                  </Button>
                </form>
              </Form>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}