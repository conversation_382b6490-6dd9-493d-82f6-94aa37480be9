import { useQuery } from "@tanstack/react-query";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Trophy, Star, Flame, Target, Award, Clock } from "lucide-react";

interface Badge {
  id: number;
  name: string;
  description: string;
  icon: string;
  category: string;
  rarity: string;
  xpReward: number;
  isActive: boolean;
}

interface UserBadge {
  id: number;
  userId: number;
  badgeId: number;
  earnedAt: string;
  progress: number;
  isDisplayed: boolean;
  badge: Badge;
}

interface Challenge {
  id: number;
  name: string;
  description: string;
  icon: string;
  targetType: string;
  targetValue: number;
  xpReward: number;
  userProgress: {
    progress: number;
    completed: boolean;
    completedAt: string | null;
  };
}

interface ProgressSummary {
  level: number;
  totalXp: number;
  xpToNextLevel: number;
  totalBadges: number;
  streakDays: number;
  longestStreak: number;
  recentBadges: UserBadge[];
  todaysChallenges: Challenge[];
}

export default function Gamification() {
  const { data: progress, isLoading: progressLoading } = useQuery<ProgressSummary>({
    queryKey: ["/api/gamification/progress"]
  });

  const { data: allBadges, isLoading: badgesLoading } = useQuery<Badge[]>({
    queryKey: ["/api/gamification/badges"]
  });

  const { data: userBadges, isLoading: userBadgesLoading } = useQuery<UserBadge[]>({
    queryKey: ["/api/gamification/user-badges"]
  });

  const { data: challenges, isLoading: challengesLoading } = useQuery<Challenge[]>({
    queryKey: ["/api/gamification/challenges/today"]
  });

  if (progressLoading || badgesLoading || userBadgesLoading || challengesLoading) {
    return (
      <div className="container mx-auto p-6">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 rounded w-64"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'common': return 'bg-gray-100 text-gray-800';
      case 'rare': return 'bg-blue-100 text-blue-800';
      case 'epic': return 'bg-purple-100 text-purple-800';
      case 'legendary': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'learning': return <Star className="h-4 w-4" />;
      case 'achievement': return <Trophy className="h-4 w-4" />;
      case 'streak': return <Flame className="h-4 w-4" />;
      case 'milestone': return <Award className="h-4 w-4" />;
      default: return <Target className="h-4 w-4" />;
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="text-center space-y-2">
        <h1 className="text-4xl font-bold tracking-tight">Your Learning Journey</h1>
        <p className="text-muted-foreground text-lg">
          Track your progress, earn badges, and complete daily challenges
        </p>
      </div>

      {/* Progress Overview */}
      {progress && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="bg-gradient-to-br from-blue-50 to-blue-100">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Level</CardTitle>
              <Star className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-700">{progress.level}</div>
              <p className="text-xs text-blue-600">
                {progress.xpToNextLevel} XP to next level
              </p>
              <Progress 
                value={(progress.totalXp % 100)} 
                className="mt-2 h-2" 
              />
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-purple-50 to-purple-100">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total XP</CardTitle>
              <Award className="h-4 w-4 text-purple-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-purple-700">
                {progress.totalXp.toLocaleString()}
              </div>
              <p className="text-xs text-purple-600">Experience Points</p>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-orange-50 to-orange-100">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Current Streak</CardTitle>
              <Flame className="h-4 w-4 text-orange-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-700">{progress.streakDays}</div>
              <p className="text-xs text-orange-600">
                Best: {progress.longestStreak} days
              </p>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-green-50 to-green-100">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Badges Earned</CardTitle>
              <Trophy className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-700">{progress.totalBadges}</div>
              <p className="text-xs text-green-600">Achievements unlocked</p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Main Content */}
      <Tabs defaultValue="challenges" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="challenges">Daily Challenges</TabsTrigger>
          <TabsTrigger value="badges">My Badges</TabsTrigger>
          <TabsTrigger value="all-badges">All Badges</TabsTrigger>
        </TabsList>

        {/* Daily Challenges */}
        <TabsContent value="challenges" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5" />
                Today's Challenges
              </CardTitle>
              <CardDescription>
                Complete these challenges to earn XP and maintain your streak
              </CardDescription>
            </CardHeader>
            <CardContent>
              {challenges && challenges.length > 0 ? (
                <div className="grid gap-4">
                  {challenges.map((challenge) => (
                    <div
                      key={challenge.id}
                      className={`p-4 rounded-lg border-2 transition-colors ${
                        challenge.userProgress.completed
                          ? 'border-green-200 bg-green-50'
                          : 'border-gray-200 bg-white'
                      }`}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex items-center gap-3">
                          <div className="text-2xl">{challenge.icon}</div>
                          <div>
                            <h3 className="font-semibold">{challenge.name}</h3>
                            <p className="text-sm text-muted-foreground">
                              {challenge.description}
                            </p>
                          </div>
                        </div>
                        <div className="text-right">
                          <Badge variant={challenge.userProgress.completed ? "default" : "outline"}>
                            {challenge.userProgress.completed ? "Completed" : "In Progress"}
                          </Badge>
                          <p className="text-sm text-muted-foreground mt-1">
                            +{challenge.xpReward} XP
                          </p>
                        </div>
                      </div>
                      <div className="mt-3">
                        <div className="flex justify-between text-sm mb-1">
                          <span>Progress</span>
                          <span>
                            {challenge.userProgress.progress} / {challenge.targetValue}
                          </span>
                        </div>
                        <Progress
                          value={(challenge.userProgress.progress / challenge.targetValue) * 100}
                          className="h-2"
                        />
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <Target className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No challenges available today. Check back tomorrow!</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* My Badges */}
        <TabsContent value="badges" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Trophy className="h-5 w-5" />
                Your Achievements
              </CardTitle>
              <CardDescription>
                Badges you've earned on your learning journey
              </CardDescription>
            </CardHeader>
            <CardContent>
              {userBadges && userBadges.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {userBadges.map((userBadge) => (
                    <div
                      key={userBadge.id}
                      className="p-4 rounded-lg border bg-white shadow-sm hover:shadow-md transition-shadow"
                    >
                      <div className="text-center space-y-3">
                        <div className="text-4xl">{userBadge.badge.icon}</div>
                        <div>
                          <h3 className="font-semibold">{userBadge.badge.name}</h3>
                          <p className="text-sm text-muted-foreground">
                            {userBadge.badge.description}
                          </p>
                        </div>
                        <div className="flex items-center justify-between">
                          <Badge className={getRarityColor(userBadge.badge.rarity)}>
                            {userBadge.badge.rarity}
                          </Badge>
                          <div className="flex items-center gap-1 text-sm">
                            {getCategoryIcon(userBadge.badge.category)}
                            <span className="text-muted-foreground">
                              +{userBadge.badge.xpReward} XP
                            </span>
                          </div>
                        </div>
                        <p className="text-xs text-muted-foreground">
                          Earned {new Date(userBadge.earnedAt).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <Trophy className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No badges earned yet. Start learning to unlock your first achievement!</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* All Badges */}
        <TabsContent value="all-badges" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Award className="h-5 w-5" />
                Badge Collection
              </CardTitle>
              <CardDescription>
                All available badges you can earn
              </CardDescription>
            </CardHeader>
            <CardContent>
              {allBadges && (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {allBadges.map((badge) => {
                    const earned = userBadges?.find(ub => ub.badgeId === badge.id);
                    return (
                      <div
                        key={badge.id}
                        className={`p-4 rounded-lg border transition-all ${
                          earned 
                            ? 'bg-white shadow-sm border-green-200' 
                            : 'bg-gray-50 border-gray-200 opacity-75'
                        }`}
                      >
                        <div className="text-center space-y-3">
                          <div className={`text-4xl ${!earned ? 'grayscale' : ''}`}>
                            {badge.icon}
                          </div>
                          <div>
                            <h3 className="font-semibold">{badge.name}</h3>
                            <p className="text-sm text-muted-foreground">
                              {badge.description}
                            </p>
                          </div>
                          <div className="flex items-center justify-between">
                            <Badge className={getRarityColor(badge.rarity)}>
                              {badge.rarity}
                            </Badge>
                            <div className="flex items-center gap-1 text-sm">
                              {getCategoryIcon(badge.category)}
                              <span className="text-muted-foreground">
                                +{badge.xpReward} XP
                              </span>
                            </div>
                          </div>
                          {earned && (
                            <Badge variant="default" className="w-full">
                              ✓ Earned
                            </Badge>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}