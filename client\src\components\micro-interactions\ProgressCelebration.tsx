import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Spark<PERSON>, Trophy, Star, Zap, Target, CheckCircle2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';

interface ProgressCelebrationProps {
  isVisible: boolean;
  achievement: {
    type: 'lesson_complete' | 'module_complete' | 'quiz_passed' | 'streak' | 'badge_earned' | 'course_complete';
    title: string;
    description: string;
    points?: number;
    streakCount?: number;
    badgeName?: string;
  };
  onClose: () => void;
  onContinue?: () => void;
}

const achievementIcons = {
  lesson_complete: CheckCircle2,
  module_complete: Target,
  quiz_passed: Star,
  streak: Zap,
  badge_earned: Trophy,
  course_complete: Sparkles,
};

const achievementColors = {
  lesson_complete: 'from-green-400 to-green-600',
  module_complete: 'from-blue-400 to-blue-600',
  quiz_passed: 'from-yellow-400 to-yellow-600',
  streak: 'from-orange-400 to-orange-600',
  badge_earned: 'from-purple-400 to-purple-600',
  course_complete: 'from-pink-400 to-pink-600',
};

export default function ProgressCelebration({ 
  isVisible, 
  achievement, 
  onClose, 
  onContinue 
}: ProgressCelebrationProps) {
  const [showConfetti, setShowConfetti] = useState(false);
  const IconComponent = achievementIcons[achievement.type];
  const colorGradient = achievementColors[achievement.type];

  useEffect(() => {
    if (isVisible) {
      setShowConfetti(true);
      const timer = setTimeout(() => setShowConfetti(false), 3000);
      return () => clearTimeout(timer);
    }
  }, [isVisible]);

  const confettiParticles = Array.from({ length: 20 }, (_, i) => (
    <motion.div
      key={i}
      className="absolute w-2 h-2 bg-gradient-to-r from-pink-400 to-purple-500 rounded-full"
      initial={{ 
        x: 0, 
        y: 0, 
        scale: 0,
        rotate: 0 
      }}
      animate={{
        x: (Math.random() - 0.5) * 400,
        y: Math.random() * -200 - 100,
        scale: [0, 1, 0],
        rotate: Math.random() * 360,
      }}
      transition={{
        duration: 2,
        delay: Math.random() * 0.5,
        ease: "easeOut"
      }}
      style={{
        left: '50%',
        top: '50%',
      }}
    />
  ));

  if (!isVisible) return null;

  return (
    <AnimatePresence>
      <motion.div
        className="fixed inset-0 bg-black/60 flex items-center justify-center z-50 p-4"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
      >
        {/* Confetti Animation */}
        {showConfetti && (
          <div className="absolute inset-0 pointer-events-none overflow-hidden">
            {confettiParticles}
          </div>
        )}

        <motion.div
          initial={{ scale: 0.5, opacity: 0, rotateY: 180 }}
          animate={{ scale: 1, opacity: 1, rotateY: 0 }}
          exit={{ scale: 0.5, opacity: 0 }}
          transition={{ 
            type: "spring", 
            stiffness: 300, 
            damping: 25,
            duration: 0.6 
          }}
        >
          <Card className="w-full max-w-md p-8 text-center relative overflow-hidden">
            {/* Animated Background Gradient */}
            <motion.div
              className={`absolute inset-0 bg-gradient-to-br ${colorGradient} opacity-10`}
              animate={{
                scale: [1, 1.1, 1],
                opacity: [0.1, 0.2, 0.1]
              }}
              transition={{ duration: 3, repeat: Infinity }}
            />

            <div className="relative z-10">
              {/* Achievement Icon */}
              <motion.div
                className={`w-20 h-20 mx-auto mb-6 rounded-full bg-gradient-to-br ${colorGradient} p-4 shadow-lg`}
                initial={{ scale: 0, rotate: -180 }}
                animate={{ scale: 1, rotate: 0 }}
                transition={{ 
                  delay: 0.2, 
                  type: "spring", 
                  stiffness: 200 
                }}
              >
                <IconComponent className="w-full h-full text-white" />
              </motion.div>

              {/* Achievement Title */}
              <motion.h2
                className="text-2xl font-bold mb-3 bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent"
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.4 }}
              >
                {achievement.title}
              </motion.h2>

              {/* Achievement Description */}
              <motion.p
                className="text-gray-600 mb-6 text-sm leading-relaxed"
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.6 }}
              >
                {achievement.description}
              </motion.p>

              {/* Achievement Details */}
              <motion.div
                className="space-y-3 mb-8"
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.8 }}
              >
                {achievement.points && (
                  <div className="flex items-center justify-center gap-2 text-sm">
                    <Sparkles className="w-4 h-4 text-yellow-500" />
                    <span className="font-medium">+{achievement.points} XP</span>
                  </div>
                )}

                {achievement.streakCount && (
                  <div className="flex items-center justify-center gap-2 text-sm">
                    <Zap className="w-4 h-4 text-orange-500" />
                    <span className="font-medium">{achievement.streakCount} Day Streak!</span>
                  </div>
                )}

                {achievement.badgeName && (
                  <div className="flex items-center justify-center gap-2 text-sm">
                    <Trophy className="w-4 h-4 text-purple-500" />
                    <span className="font-medium">{achievement.badgeName} Badge</span>
                  </div>
                )}
              </motion.div>

              {/* Action Buttons */}
              <motion.div
                className="flex gap-3 justify-center"
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 1 }}
              >
                <Button
                  variant="outline"
                  onClick={onClose}
                  className="min-w-[100px]"
                >
                  Close
                </Button>
                {onContinue && (
                  <Button
                    onClick={onContinue}
                    className={`min-w-[100px] bg-gradient-to-r ${colorGradient} text-white border-0 hover:opacity-90 transform hover:scale-105 transition-all duration-200`}
                  >
                    Continue Learning
                  </Button>
                )}
              </motion.div>
            </div>
          </Card>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
}