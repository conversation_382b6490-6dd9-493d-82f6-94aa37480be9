.App {
  text-align: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.App-header {
  background: rgba(255, 255, 255, 0.95);
  padding: 20px;
  color: #333;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  margin-bottom: 20px;
}

.App-main {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.App-footer {
  margin-top: 40px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

/* Tab Styling */
.react-tabs__tab-list {
  border-bottom: 2px solid #ddd;
  margin: 0 0 20px;
  padding: 0;
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.react-tabs__tab {
  list-style: none;
  padding: 12px 20px;
  cursor: pointer;
  border: 1px solid #ddd;
  border-bottom: none;
  background: #f8f9fa;
  color: #666;
  border-radius: 8px 8px 0 0;
  font-weight: 500;
  transition: all 0.2s ease;
}

.react-tabs__tab:hover {
  background: #e9ecef;
  color: #333;
}

.react-tabs__tab--selected {
  background: white;
  color: #333;
  border-color: #667eea;
  border-bottom: 2px solid white;
  position: relative;
  z-index: 1;
}

.react-tabs__tab-panel {
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  min-height: 600px;
}

/* Component Specific Styles */
.image-generator, .video-generator, .tts-chatterbox, .tts-coqui, .slide-generator, .course-builder {
  text-align: left;
}

/* Card Enhancements */
.MuiCard-root {
  border-radius: 12px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
  transition: transform 0.2s ease, box-shadow 0.2s ease !important;
}

.MuiCard-root:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15) !important;
}

/* Button Enhancements */
.MuiButton-contained {
  border-radius: 8px !important;
  text-transform: none !important;
  font-weight: 600 !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
}

.MuiButton-outlined {
  border-radius: 8px !important;
  text-transform: none !important;
  font-weight: 500 !important;
}

/* Loading States */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

/* Responsive Design */
@media (max-width: 768px) {
  .App-main {
    padding: 10px;
  }
  
  .react-tabs__tab {
    padding: 8px 12px;
    font-size: 14px;
  }
  
  .react-tabs__tab-panel {
    padding: 15px;
  }
}

/* Drag and Drop Styling */
.dropzone {
  border: 2px dashed #ccc;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.dropzone:hover, .dropzone.active {
  border-color: #667eea;
  background-color: #f8f9ff;
}

/* Audio/Video Controls */
audio, video {
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Custom Scrollbars */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}