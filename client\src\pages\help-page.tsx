import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { 
  Search, 
  BookOpen, 
  Upload, 
  Image, 
  Video, 
  PenTool, 
  MessageSquare,
  Download,
  Clock,
  CreditCard,
  Mail,
  UserPlus,
  Lock
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";

export default function HelpPage() {
  const [searchQuery, setSearchQuery] = useState("");
  const [activeTab, setActiveTab] = useState("getting-started");

  const filterContent = (content: string) => {
    if (!searchQuery) return true;
    return content.toLowerCase().includes(searchQuery.toLowerCase());
  };

  return (
    <div className="container max-w-6xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
      <div className="flex flex-col space-y-3 mb-8">
        <h1 className="text-3xl font-bold tracking-tight">Help Center</h1>
        <p className="text-slate-600 max-w-3xl">
          Welcome to the CourseCreator AI platform help center. Find guides, tutorials, and answers to frequently asked questions about using our platform.
        </p>

        {/* Search Bar */}
        <div className="relative max-w-xl mt-4">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
          <Input
            placeholder="Search for help topics..."
            className="pl-10"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
          {searchQuery && (
            <Button
              variant="ghost"
              size="sm"
              className="absolute right-2 top-1/2 transform -translate-y-1/2 h-7 text-slate-400"
              onClick={() => setSearchQuery("")}
            >
              Clear
            </Button>
          )}
        </div>
      </div>

      {/* Quick Help Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-10">
        <Card className="border border-slate-200 hover:border-primary/30 hover:shadow-md transition-all">
          <CardHeader className="pb-2">
            <CardTitle className="text-base flex items-center">
              <BookOpen className="h-4 w-4 mr-2 text-primary" />
              Getting Started
            </CardTitle>
            <CardDescription>New to CourseCreator AI?</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-slate-600">
              Learn how to create your first course in minutes with our step-by-step guide.
            </p>
            <Button variant="link" className="p-0 h-auto mt-2" onClick={() => setActiveTab("getting-started")}>
              View Guide →
            </Button>
          </CardContent>
        </Card>
        
        <Card className="border border-slate-200 hover:border-primary/30 hover:shadow-md transition-all">
          <CardHeader className="pb-2">
            <CardTitle className="text-base flex items-center">
              <PenTool className="h-4 w-4 mr-2 text-primary" />
              AI Tools
            </CardTitle>
            <CardDescription>Supercharge your content</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-slate-600">
              Discover how to use AI to generate scripts, images, videos, and quizzes.
            </p>
            <Button variant="link" className="p-0 h-auto mt-2" onClick={() => setActiveTab("ai-tools")}>
              View Guide →
            </Button>
          </CardContent>
        </Card>
        
        <Card className="border border-slate-200 hover:border-primary/30 hover:shadow-md transition-all">
          <CardHeader className="pb-2">
            <CardTitle className="text-base flex items-center">
              <UserPlus className="h-4 w-4 mr-2 text-primary" />
              Collaboration
            </CardTitle>
            <CardDescription>Work with your team</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-slate-600">
              Learn how to invite collaborators and manage team access to your courses.
            </p>
            <Button variant="link" className="p-0 h-auto mt-2" onClick={() => setActiveTab("collaboration")}>
              View Guide →
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Main Content with Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid grid-cols-2 md:grid-cols-5 max-w-4xl">
          <TabsTrigger value="getting-started">Getting Started</TabsTrigger>
          <TabsTrigger value="courses">Course Creation</TabsTrigger>
          <TabsTrigger value="media">Media Library</TabsTrigger>
          <TabsTrigger value="ai-tools">AI Tools</TabsTrigger>
          <TabsTrigger value="collaboration">Collaboration</TabsTrigger>
        </TabsList>

        {/* Getting Started Tab */}
        <TabsContent value="getting-started" className={cn(!filterContent("getting started account login dashboard navigation") && "hidden")}>
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <BookOpen className="h-5 w-5 text-primary" />
              <h2 className="text-2xl font-semibold">Getting Started</h2>
            </div>
            <p className="text-slate-600">
              Welcome to CourseCreator AI! This guide will help you get started with creating your first course.
            </p>

            <Accordion type="single" collapsible className="w-full">
              <AccordionItem value="account-creation">
                <AccordionTrigger>
                  <div className="flex items-center">
                    <UserPlus className="h-4 w-4 mr-2 text-primary" />
                    Creating an account
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <div className="space-y-3 pl-6">
                    <p>Creating a new account on CourseCreator AI is quick and easy:</p>
                    <ol className="list-decimal pl-5 space-y-2">
                      <li>Visit the CourseCreator AI homepage and click on the "Sign Up" button in the top right corner.</li>
                      <li>Enter your name, email address, and choose a secure password.</li>
                      <li>Select your account type: Individual or Organization.</li>
                      <li>Read and accept the Terms of Service and Privacy Policy.</li>
                      <li>Click "Create Account" to complete the registration process.</li>
                      <li>Verify your email address by clicking the link in the verification email we send you.</li>
                    </ol>
                    <p className="text-sm text-slate-500 mt-2">
                      After verifying your email, you can log in and start creating courses immediately with your free account.
                    </p>
                  </div>
                </AccordionContent>
              </AccordionItem>

              <AccordionItem value="logging-in">
                <AccordionTrigger>
                  <div className="flex items-center">
                    <Lock className="h-4 w-4 mr-2 text-primary" />
                    Logging in to your account
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <div className="space-y-3 pl-6">
                    <p>To log in to your CourseCreator AI account:</p>
                    <ol className="list-decimal pl-5 space-y-2">
                      <li>Visit the CourseCreator AI homepage and click "Log In" in the top right corner.</li>
                      <li>Enter your registered email address and password.</li>
                      <li>Click "Log In" to access your account.</li>
                    </ol>
                    <p className="text-sm text-slate-500 mt-2">
                      If you've forgotten your password, click the "Forgot Password" link on the login page to reset it.
                    </p>
                  </div>
                </AccordionContent>
              </AccordionItem>

              <AccordionItem value="navigating-dashboard">
                <AccordionTrigger>
                  <div className="flex items-center">
                    <BookOpen className="h-4 w-4 mr-2 text-primary" />
                    Navigating the dashboard
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <div className="space-y-3 pl-6">
                    <p>Your dashboard is your command center for creating and managing courses:</p>
                    <ul className="list-disc pl-5 space-y-2">
                      <li><strong>Dashboard</strong>: View at-a-glance statistics and recent activity.</li>
                      <li><strong>My Courses</strong>: Access, edit, and manage all your courses.</li>
                      <li><strong>Teams</strong>: Create and manage teams for collaborative course creation.</li>
                      <li><strong>Collaborations</strong>: See courses you've been invited to collaborate on.</li>
                      <li><strong>AI Templates</strong>: Browse template options for quick course creation.</li>
                      <li><strong>Media Library</strong>: Manage images, videos, and other media assets.</li>
                      <li><strong>Video Generator</strong>: Create AI-powered videos for your courses.</li>
                      <li><strong>Marketplace</strong>: Explore additional templates and resources.</li>
                      <li><strong>Settings</strong>: Manage your account preferences and subscription.</li>
                    </ul>
                    <p className="text-sm text-slate-500 mt-2">
                      The sidebar menu allows quick navigation to any section of the platform.
                    </p>
                  </div>
                </AccordionContent>
              </AccordionItem>

              <AccordionItem value="understanding-subscription">
                <AccordionTrigger>
                  <div className="flex items-center">
                    <CreditCard className="h-4 w-4 mr-2 text-primary" />
                    Understanding subscription plans
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <div className="space-y-3 pl-6">
                    <p>CourseCreator AI offers several subscription tiers to match your needs:</p>
                    
                    <div className="space-y-4 mt-3">
                      <div>
                        <h4 className="font-medium">Free Plan</h4>
                        <p className="text-sm">Create 1 course with basic features to explore the platform.</p>
                        <ul className="list-disc pl-5 text-sm mt-1 text-slate-600">
                          <li>1 course limit</li>
                          <li>Basic AI generation features</li>
                          <li>No team collaboration</li>
                          <li>Standard support</li>
                        </ul>
                      </div>
                      
                      <div>
                        <h4 className="font-medium">Starter Plan - $49/month</h4>
                        <p className="text-sm">Perfect for individual creators just getting started.</p>
                        <ul className="list-disc pl-5 text-sm mt-1 text-slate-600">
                          <li>Unlimited courses</li>
                          <li>Advanced AI content generation</li>
                          <li>Download scripts as PDF</li>
                          <li>Basic collaboration features</li>
                        </ul>
                      </div>
                      
                      <div>
                        <h4 className="font-medium">Pro Plan - $129/month</h4>
                        <p className="text-sm">Ideal for professional creators and small teams.</p>
                        <ul className="list-disc pl-5 text-sm mt-1 text-slate-600">
                          <li>Unlimited courses</li>
                          <li>Priority AI generation</li>
                          <li>Advanced video generation</li>
                          <li>Team collaboration with up to 5 members</li>
                          <li>Priority support</li>
                        </ul>
                      </div>
                      
                      <div>
                        <h4 className="font-medium">Business Plan - $299/month</h4>
                        <p className="text-sm">For organizations and larger teams with advanced needs.</p>
                        <ul className="list-disc pl-5 text-sm mt-1 text-slate-600">
                          <li>Unlimited courses and team members</li>
                          <li>Highest-quality AI generation</li>
                          <li>Custom branding</li>
                          <li>Advanced analytics</li>
                          <li>Dedicated account manager</li>
                        </ul>
                      </div>
                      
                      <div>
                        <h4 className="font-medium">Enterprise Plan - Custom pricing</h4>
                        <p className="text-sm">Custom solutions for large organizations with specific requirements.</p>
                        <ul className="list-disc pl-5 text-sm mt-1 text-slate-600">
                          <li>Custom features and integrations</li>
                          <li>Unlimited everything</li>
                          <li>Dedicated infrastructure</li>
                          <li>White-labeling options</li>
                          <li>Enterprise-grade support</li>
                        </ul>
                      </div>
                    </div>
                    
                    <p className="text-sm text-slate-500 mt-2">
                      All paid plans offer annual discounts. Compare plans on the Pricing page to find the right fit for your needs.
                    </p>
                  </div>
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          </div>
        </TabsContent>

        {/* Course Creation Tab */}
        <TabsContent value="courses" className={cn(!filterContent("course creation curriculum modules lessons quiz") && "hidden")}>
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <PenTool className="h-5 w-5 text-primary" />
              <h2 className="text-2xl font-semibold">Course Creation</h2>
            </div>
            <p className="text-slate-600">
              Learn how to create engaging online courses with our powerful course builder.
            </p>

            <Accordion type="single" collapsible className="w-full">
              <AccordionItem value="creating-first-course">
                <AccordionTrigger>
                  <div className="flex items-center">
                    <BookOpen className="h-4 w-4 mr-2 text-primary" />
                    Creating your first course
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <div className="space-y-3 pl-6">
                    <p>To create your first course on CourseCreator AI:</p>
                    <ol className="list-decimal pl-5 space-y-2">
                      <li>Navigate to "My Courses" in the sidebar.</li>
                      <li>Click the "Create New Course" button.</li>
                      <li>Enter a title and description for your course.</li>
                      <li>Select a category that best fits your course content.</li>
                      <li>Define your target audience to help with AI-generated content.</li>
                      <li>Click "Create Course" to start building your curriculum.</li>
                    </ol>
                    <p className="text-sm text-slate-500 mt-2">
                      You'll be guided through a 6-step process to complete your course creation.
                    </p>
                  </div>
                </AccordionContent>
              </AccordionItem>

              <AccordionItem value="course-structure">
                <AccordionTrigger>
                  <div className="flex items-center">
                    <BookOpen className="h-4 w-4 mr-2 text-primary" />
                    Understanding course structure
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <div className="space-y-3 pl-6">
                    <p>CourseCreator AI organizes courses into a hierarchical structure:</p>
                    <ul className="list-disc pl-5 space-y-2">
                      <li><strong>Course</strong>: The overall course container, including title, description, and category.</li>
                      <li><strong>Modules</strong>: Major sections that organize your content into logical groups.</li>
                      <li><strong>Lessons</strong>: Individual teaching units within modules containing videos, text, and interactive elements.</li>
                      <li><strong>Quizzes</strong>: Assessments to test learner knowledge and engagement.</li>
                    </ul>
                    <p className="text-sm text-slate-500 mt-2">
                      This structure helps organize your content for better learning outcomes and easier navigation.
                    </p>
                  </div>
                </AccordionContent>
              </AccordionItem>

              <AccordionItem value="ai-course-generation">
                <AccordionTrigger>
                  <div className="flex items-center">
                    <PenTool className="h-4 w-4 mr-2 text-primary" />
                    Generating course content with AI
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <div className="space-y-3 pl-6">
                    <p>CourseCreator AI can generate course content for you:</p>
                    <ol className="list-decimal pl-5 space-y-2">
                      <li>
                        <strong>AI Course Structure</strong>: After creating a course, select "Generate Course Structure with AI" to automatically create modules and lessons based on your course title, description, and target audience.
                      </li>
                      <li>
                        <strong>AI Lesson Scripts</strong>: For each lesson, click "Generate Script" to create content tailored to your lesson objectives.
                      </li>
                      <li>
                        <strong>Editing Generated Content</strong>: Always review and edit AI-generated content to ensure it matches your voice and teaching style.
                      </li>
                    </ol>
                    <div className="bg-slate-50 p-3 rounded-md mt-3">
                      <p className="text-sm text-slate-700">
                        <strong>Pro Tip:</strong> Provide detailed course descriptions and target audience information to get better AI-generated content. The more context you provide, the more relevant the generated content will be.
                      </p>
                    </div>
                  </div>
                </AccordionContent>
              </AccordionItem>

              <AccordionItem value="quiz-creation">
                <AccordionTrigger>
                  <div className="flex items-center">
                    <MessageSquare className="h-4 w-4 mr-2 text-primary" />
                    Creating quizzes and assessments
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <div className="space-y-3 pl-6">
                    <p>Quizzes help reinforce learning and assess student understanding:</p>
                    <ol className="list-decimal pl-5 space-y-2">
                      <li>In the "Preview & Publish" step, select the "Generate Quiz" tab.</li>
                      <li>Choose the number of questions and difficulty level.</li>
                      <li>Select question types (multiple choice, true/false, short answer).</li>
                      <li>Click "Generate Quiz" to create questions based on your course content.</li>
                      <li>Review and edit the generated questions as needed.</li>
                      <li>Set quiz parameters like passing score and time limits.</li>
                      <li>Save the quiz to attach it to your course.</li>
                    </ol>
                    <p className="text-sm text-slate-500 mt-2">
                      Premium plans allow for more advanced quiz features, including randomized questions and custom feedback.
                    </p>
                  </div>
                </AccordionContent>
              </AccordionItem>

              <AccordionItem value="micro-learning">
                <AccordionTrigger>
                  <div className="flex items-center">
                    <Clock className="h-4 w-4 mr-2 text-primary" />
                    Setting up micro-learning
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <div className="space-y-3 pl-6">
                    <p>Micro-learning breaks content into bite-sized chunks for better retention:</p>
                    <ol className="list-decimal pl-5 space-y-2">
                      <li>In your lesson settings, enable the "Micro-Learning Mode" toggle.</li>
                      <li>Set the number of segments to divide your content into.</li>
                      <li>Define break intervals (time or content-based).</li>
                      <li>Set break duration.</li>
                      <li>Customize break content (reflection questions, key points, etc.).</li>
                    </ol>
                    <div className="bg-slate-50 p-3 rounded-md mt-3">
                      <p className="text-sm text-slate-700">
                        <strong>Research-backed:</strong> Studies show that micro-learning can improve knowledge retention by up to 80% compared to traditional learning formats.
                      </p>
                    </div>
                  </div>
                </AccordionContent>
              </AccordionItem>

              <AccordionItem value="publishing-course">
                <AccordionTrigger>
                  <div className="flex items-center">
                    <Download className="h-4 w-4 mr-2 text-primary" />
                    Publishing and exporting courses
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <div className="space-y-3 pl-6">
                    <p>Once your course is ready, you can publish or export it in multiple formats:</p>
                    <ol className="list-decimal pl-5 space-y-2">
                      <li>In the final "Preview & Publish" step, review your entire course.</li>
                      <li>Select your desired export format:
                        <ul className="list-disc pl-5 mt-1 space-y-1">
                          <li><strong>Video</strong>: Complete video course with AI narration</li>
                          <li><strong>Slides</strong>: Presentation format with speaker notes</li>
                          <li><strong>SCORM</strong>: Package for LMS compatibility</li>
                          <li><strong>PDF</strong>: Downloadable document version</li>
                        </ul>
                      </li>
                      <li>Configure quality settings for your chosen format.</li>
                      <li>Click "Publish" to make your course available or "Export" to download it.</li>
                    </ol>
                    <p className="text-sm text-slate-500 mt-2">
                      Free plan users have limited export options. Upgrade to access all formats and higher quality settings.
                    </p>
                  </div>
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          </div>
        </TabsContent>

        {/* Media Library Tab */}
        <TabsContent value="media" className={cn(!filterContent("media library images videos pexels pixabay upload") && "hidden")}>
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Image className="h-5 w-5 text-primary" />
              <h2 className="text-2xl font-semibold">Media Library</h2>
            </div>
            <p className="text-slate-600">
              Learn how to manage and utilize images, videos, and other media assets for your courses.
            </p>

            <Accordion type="single" collapsible className="w-full">
              <AccordionItem value="media-library-overview">
                <AccordionTrigger>
                  <div className="flex items-center">
                    <Image className="h-4 w-4 mr-2 text-primary" />
                    Media Library overview
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <div className="space-y-3 pl-6">
                    <p>The Media Library is your central hub for managing all course-related media:</p>
                    <ul className="list-disc pl-5 space-y-2">
                      <li><strong>My Media</strong>: View all your uploaded and created media files.</li>
                      <li><strong>Pexels</strong>: Search and import free stock photos and videos.</li>
                      <li><strong>Pixabay</strong>: Access additional free media resources.</li>
                      <li><strong>AI</strong>: Generate custom images using AI.</li>
                    </ul>
                    <p className="text-sm text-slate-500 mt-2">
                      All media is organized into categories and can be searched, filtered, and sorted for easy access.
                    </p>
                  </div>
                </AccordionContent>
              </AccordionItem>

              <AccordionItem value="uploading-media">
                <AccordionTrigger>
                  <div className="flex items-center">
                    <Upload className="h-4 w-4 mr-2 text-primary" />
                    Uploading your own media
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <div className="space-y-3 pl-6">
                    <p>To upload your own images, videos, or audio files:</p>
                    <ol className="list-decimal pl-5 space-y-2">
                      <li>Navigate to the Media Library in the sidebar.</li>
                      <li>Click the "Upload" button in the top right corner.</li>
                      <li>Select "Files" to upload from your computer, or "URL" to import from a web link.</li>
                      <li>Choose the files you want to upload (supported formats include JPG, PNG, MP4, MP3).</li>
                      <li>Add metadata like name, description, and tags to organize your media.</li>
                      <li>Click "Upload" to add the files to your library.</li>
                    </ol>
                    <div className="bg-slate-50 p-3 rounded-md mt-3">
                      <p className="text-sm text-slate-700">
                        <strong>Best practice:</strong> Use descriptive file names and add relevant tags to make your media easier to find later.
                      </p>
                    </div>
                  </div>
                </AccordionContent>
              </AccordionItem>

              <AccordionItem value="pexels-integration">
                <AccordionTrigger>
                  <div className="flex items-center">
                    <Image className="h-4 w-4 mr-2 text-primary" />
                    Using Pexels integration
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <div className="space-y-3 pl-6">
                    <p>CourseCreator AI integrates with Pexels to provide free, high-quality stock photos and videos:</p>
                    <ol className="list-decimal pl-5 space-y-2">
                      <li>In the Media Library, select the "Pexels" tab.</li>
                      <li>Use the search bar to find relevant images or videos.</li>
                      <li>Browse through the results and preview any media that interests you.</li>
                      <li>Click the "Import" button on any media to add it to your library.</li>
                      <li>Imported media will appear in your "My Media" tab for use in your courses.</li>
                    </ol>
                    <p className="text-sm text-slate-500 mt-2">
                      All Pexels media is free to use for commercial purposes, but attribution is appreciated.
                    </p>
                  </div>
                </AccordionContent>
              </AccordionItem>

              <AccordionItem value="pixabay-integration">
                <AccordionTrigger>
                  <div className="flex items-center">
                    <Image className="h-4 w-4 mr-2 text-primary" />
                    Using Pixabay integration
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <div className="space-y-3 pl-6">
                    <p>Similar to Pexels, CourseCreator AI also integrates with Pixabay for additional media options:</p>
                    <ol className="list-decimal pl-5 space-y-2">
                      <li>In the Media Library, select the "Pixabay" tab.</li>
                      <li>Search for images or videos using relevant keywords.</li>
                      <li>Filter results by orientation, size, or type as needed.</li>
                      <li>Preview and select the media you want to use.</li>
                      <li>Click "Import" to add selected media to your library.</li>
                    </ol>
                    <p className="text-sm text-slate-500 mt-2">
                      Pixabay offers a diverse selection of images and videos that complement the Pexels collection.
                    </p>
                  </div>
                </AccordionContent>
              </AccordionItem>

              <AccordionItem value="ai-image-generation">
                <AccordionTrigger>
                  <div className="flex items-center">
                    <PenTool className="h-4 w-4 mr-2 text-primary" />
                    Generating images with AI
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <div className="space-y-3 pl-6">
                    <p>Create custom images using AI for your courses:</p>
                    <ol className="list-decimal pl-5 space-y-2">
                      <li>In the Media Library, navigate to the "AI" tab.</li>
                      <li>Enter a detailed description of the image you want to create.</li>
                      <li>Select style options from the available presets (photorealistic, artistic, etc.).</li>
                      <li>Choose image dimensions and aspect ratio.</li>
                      <li>Click "Generate Image" to create your custom image.</li>
                      <li>Preview the result and click "Save to Library" if satisfied.</li>
                      <li>If needed, click "Regenerate" to try again with the same or modified parameters.</li>
                    </ol>
                    <div className="bg-slate-50 p-3 rounded-md mt-3">
                      <p className="text-sm text-slate-700">
                        <strong>Pro Tip:</strong> For best results, be specific in your descriptions. Include details about style, colors, composition, and mood.
                      </p>
                    </div>
                    <p className="text-sm text-slate-500 mt-2">
                      AI image generation uses credits from your subscription plan. Higher-tier plans offer more credits and higher-quality outputs.
                    </p>
                  </div>
                </AccordionContent>
              </AccordionItem>

              <AccordionItem value="managing-media">
                <AccordionTrigger>
                  <div className="flex items-center">
                    <Image className="h-4 w-4 mr-2 text-primary" />
                    Managing your media
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <div className="space-y-3 pl-6">
                    <p>Efficiently organize and manage your media library:</p>
                    <ul className="list-disc pl-5 space-y-2">
                      <li>
                        <strong>Tagging</strong>: Add descriptive tags to your media for better organization and searching.
                      </li>
                      <li>
                        <strong>Filtering</strong>: Use the filter options to narrow down media by type, date, source, or tags.
                      </li>
                      <li>
                        <strong>Sorting</strong>: Arrange media by name, date, size, or type.
                      </li>
                      <li>
                        <strong>Bulk Actions</strong>: Select multiple items to tag, delete, or move them together.
                      </li>
                      <li>
                        <strong>Editing Metadata</strong>: Update names, descriptions, and tags by clicking the edit icon on any media item.
                      </li>
                    </ul>
                    <p className="text-sm text-slate-500 mt-2">
                      Regular organization of your media library will save you time when creating courses.
                    </p>
                  </div>
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          </div>
        </TabsContent>

        {/* AI Tools Tab */}
        <TabsContent value="ai-tools" className={cn(!filterContent("ai tools generation script image video quiz") && "hidden")}>
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <PenTool className="h-5 w-5 text-primary" />
              <h2 className="text-2xl font-semibold">AI Tools</h2>
            </div>
            <p className="text-slate-600">
              Learn how to leverage our powerful AI tools to create professional course content quickly and easily.
            </p>

            <Accordion type="single" collapsible className="w-full">
              <AccordionItem value="ai-script-generation">
                <AccordionTrigger>
                  <div className="flex items-center">
                    <PenTool className="h-4 w-4 mr-2 text-primary" />
                    AI script generation
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <div className="space-y-3 pl-6">
                    <p>Generate professional course scripts with AI:</p>
                    <ol className="list-decimal pl-5 space-y-2">
                      <li>When creating or editing a lesson, click the "Generate Script with AI" button.</li>
                      <li>Enter key points or objectives you want to cover in the lesson.</li>
                      <li>Select the tone of voice (professional, conversational, academic, etc.).</li>
                      <li>Choose the target audience level (beginner, intermediate, advanced).</li>
                      <li>Set the desired length of the script.</li>
                      <li>Click "Generate" to create your script.</li>
                      <li>Review and edit the generated content as needed.</li>
                    </ol>
                    <div className="bg-slate-50 p-3 rounded-md mt-3">
                      <p className="text-sm text-slate-700">
                        <strong>Pro Tip:</strong> Even when using AI-generated scripts, add your personal insights and examples to make the content unique to your teaching style.
                      </p>
                    </div>
                  </div>
                </AccordionContent>
              </AccordionItem>

              <AccordionItem value="ai-image-generation">
                <AccordionTrigger>
                  <div className="flex items-center">
                    <Image className="h-4 w-4 mr-2 text-primary" />
                    AI image generation
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <div className="space-y-3 pl-6">
                    <p>Create custom images for your courses using our AI image generation tools:</p>
                    <ol className="list-decimal pl-5 space-y-2">
                      <li>Navigate to the Media Library and select the "AI" tab.</li>
                      <li>Enter a detailed prompt describing the image you want to create.</li>
                      <li>Choose from available style presets like photorealistic, artistic, cartoon, etc.</li>
                      <li>Select the desired aspect ratio and resolution.</li>
                      <li>Click "Generate Image" to create your custom visual.</li>
                      <li>Preview the result and save it to your library if satisfied.</li>
                    </ol>
                    <p className="mt-2">Our platform uses two advanced AI systems for image generation:</p>
                    <ul className="list-disc pl-5 space-y-2">
                      <li><strong>Stability AI</strong>: Offers high-quality image generation with extensive style options.</li>
                      <li><strong>OpenAI DALL-E</strong>: Provides creative interpretations of complex prompts.</li>
                    </ul>
                    <p className="text-sm text-slate-500 mt-2">
                      Each image generation consumes credits from your plan. Higher-tier plans offer more credits and higher-resolution options.
                    </p>
                  </div>
                </AccordionContent>
              </AccordionItem>

              <AccordionItem value="mini-course-generator">
                <AccordionTrigger>
                  <div className="flex items-center">
                    <Video className="h-4 w-4 mr-2 text-primary" />
                    Mini Course Generator
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <div className="space-y-3 pl-6">
                    <p>Create professional mini-courses with AI-generated videos and quizzes:</p>
                    <ol className="list-decimal pl-5 space-y-2">
                      <li>Go to the "Mini Course Generator" section in the sidebar.</li>
                      <li>Select from available content generation options or choose stock videos from our library.</li>
                      <li>Upload or select a script or use the integrated AI to generate one.</li>
                      <li>Choose voice options (25 languages available).</li>
                      <li>Select background settings, animations, and visual style.</li>
                      <li>Add branded elements like logos or custom colors.</li>
                      <li>Click "Generate Mini Course" to create your content.</li>
                      <li>Preview the generated content including video and quiz.</li>
                      <li>Click "Publish" to save your mini-course.</li>
                    </ol>
                    <div className="bg-slate-50 p-3 rounded-md mt-3">
                      <p className="text-sm text-slate-700">
                        <strong>Important:</strong> Video generation is a premium feature that uses significant credits. Plan your video content strategically to maximize your subscription value.
                      </p>
                    </div>
                  </div>
                </AccordionContent>
              </AccordionItem>

              <AccordionItem value="ai-quiz-generation">
                <AccordionTrigger>
                  <div className="flex items-center">
                    <MessageSquare className="h-4 w-4 mr-2 text-primary" />
                    AI quiz generation
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <div className="space-y-3 pl-6">
                    <p>Automatically create quizzes based on your course content:</p>
                    <ol className="list-decimal pl-5 space-y-2">
                      <li>In the "Preview & Publish" step of course creation, select the "Generate Quiz" tab.</li>
                      <li>Set the number of questions you want to create.</li>
                      <li>Select question types (multiple choice, true/false, short answer).</li>
                      <li>Choose difficulty level(s).</li>
                      <li>Optionally enable additional features:
                        <ul className="list-disc pl-5 mt-1 space-y-1">
                          <li>Flashcards</li>
                          <li>Summary points</li>
                          <li>Key takeaways</li>
                        </ul>
                      </li>
                      <li>Click "Generate Quiz" to create questions based on your course content.</li>
                      <li>Review and edit the generated questions as needed.</li>
                      <li>Configure quiz settings (passing score, time limits, randomization).</li>
                      <li>Save the quiz to attach it to your course.</li>
                    </ol>
                    <p className="text-sm text-slate-500 mt-2">
                      Quiz generation uses approximately 25 AI credits per quiz, depending on complexity and number of questions.
                    </p>
                  </div>
                </AccordionContent>
              </AccordionItem>

              <AccordionItem value="ai-text-to-speech">
                <AccordionTrigger>
                  <div className="flex items-center">
                    <MessageSquare className="h-4 w-4 mr-2 text-primary" />
                    AI text-to-speech narration
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <div className="space-y-3 pl-6">
                    <p>Add professional voiceovers to your courses using AI narration:</p>
                    <ol className="list-decimal pl-5 space-y-2">
                      <li>When editing a lesson, select the "Add Narration" option.</li>
                      <li>Choose from 25+ languages and various voice options.</li>
                      <li>Adjust voice parameters (speed, pitch, style).</li>
                      <li>Click "Generate" to create the audio narration.</li>
                      <li>Preview the generated audio and make adjustments if needed.</li>
                      <li>Save the narration to attach it to your lesson.</li>
                    </ol>
                    <p className="mt-2">We use advanced text-to-speech services:</p>
                    <ul className="list-disc pl-5 space-y-1">
                      <li><strong>ElevenLabs</strong>: Premium ultra-realistic voices</li>
                      <li><strong>OpenAI</strong>: High-quality alternative voices</li>
                    </ul>
                    <div className="bg-slate-50 p-3 rounded-md mt-3">
                      <p className="text-sm text-slate-700">
                        <strong>Credit usage:</strong> Text-to-speech uses approximately 1 credit per 100 words of narration.
                      </p>
                    </div>
                  </div>
                </AccordionContent>
              </AccordionItem>

              <AccordionItem value="ai-credits">
                <AccordionTrigger>
                  <div className="flex items-center">
                    <CreditCard className="h-4 w-4 mr-2 text-primary" />
                    Understanding AI credits
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <div className="space-y-3 pl-6">
                    <p>AI credits are consumed when using various AI features on the platform:</p>
                    <div className="overflow-x-auto">
                      <table className="min-w-full border-collapse text-sm mt-2">
                        <thead>
                          <tr className="border-b border-slate-200">
                            <th className="text-left pb-2 pr-4 font-medium">Feature</th>
                            <th className="text-left pb-2 pr-4 font-medium">Credit Cost</th>
                            <th className="text-left pb-2 font-medium">Notes</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr className="border-b border-slate-100">
                            <td className="py-2 pr-4">Course Structure Generation</td>
                            <td className="py-2 pr-4">25 credits</td>
                            <td className="py-2">Per complete course structure</td>
                          </tr>
                          <tr className="border-b border-slate-100">
                            <td className="py-2 pr-4">Script Generation</td>
                            <td className="py-2 pr-4">10 credits</td>
                            <td className="py-2">Per lesson script</td>
                          </tr>
                          <tr className="border-b border-slate-100">
                            <td className="py-2 pr-4">Text-to-Speech</td>
                            <td className="py-2 pr-4">1 credit</td>
                            <td className="py-2">Per 100 words</td>
                          </tr>
                          <tr className="border-b border-slate-100">
                            <td className="py-2 pr-4">Image Generation</td>
                            <td className="py-2 pr-4">5-15 credits</td>
                            <td className="py-2">Depends on size and quality</td>
                          </tr>
                          <tr className="border-b border-slate-100">
                            <td className="py-2 pr-4">Video Generation</td>
                            <td className="py-2 pr-4">30-100 credits</td>
                            <td className="py-2">Depends on length and features</td>
                          </tr>
                          <tr className="border-b border-slate-100">
                            <td className="py-2 pr-4">Quiz Generation</td>
                            <td className="py-2 pr-4">25 credits</td>
                            <td className="py-2">Per quiz</td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                    <p className="mt-2">Credit allocation by subscription tier:</p>
                    <ul className="list-disc pl-5 space-y-1">
                      <li><strong>Free</strong>: 50 credits/month</li>
                      <li><strong>Starter</strong>: 500 credits/month</li>
                      <li><strong>Pro</strong>: 2,000 credits/month</li>
                      <li><strong>Business</strong>: 5,000 credits/month</li>
                      <li><strong>Enterprise</strong>: Custom allocation</li>
                    </ul>
                    <p className="text-sm text-slate-500 mt-2">
                      Unused credits expire at the end of each billing cycle. Additional credits can be purchased separately if needed.
                    </p>
                  </div>
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          </div>
        </TabsContent>

        {/* Collaboration Tab */}
        <TabsContent value="collaboration" className={cn(!filterContent("collaboration teams invite manage permissions roles") && "hidden")}>
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <UserPlus className="h-5 w-5 text-primary" />
              <h2 className="text-2xl font-semibold">Collaboration</h2>
            </div>
            <p className="text-slate-600">
              Learn how to collaborate with team members on course creation.
            </p>

            <Accordion type="single" collapsible className="w-full">
              <AccordionItem value="teams-overview">
                <AccordionTrigger>
                  <div className="flex items-center">
                    <UserPlus className="h-4 w-4 mr-2 text-primary" />
                    Teams overview
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <div className="space-y-3 pl-6">
                    <p>The Teams feature allows you to collaborate with others on course creation:</p>
                    <ul className="list-disc pl-5 space-y-2">
                      <li><strong>Teams Page</strong>: View, create, and manage all your teams.</li>
                      <li><strong>Team Details</strong>: See members and courses associated with a specific team.</li>
                      <li><strong>Collaborations Page</strong>: Browse courses you've been invited to collaborate on.</li>
                    </ul>
                    <div className="bg-slate-50 p-3 rounded-md mt-3">
                      <p className="text-sm text-slate-700">
                        <strong>Note:</strong> Team collaboration features are limited or unavailable on the Free plan. Upgrade to access full collaboration capabilities.
                      </p>
                    </div>
                  </div>
                </AccordionContent>
              </AccordionItem>

              <AccordionItem value="creating-teams">
                <AccordionTrigger>
                  <div className="flex items-center">
                    <UserPlus className="h-4 w-4 mr-2 text-primary" />
                    Creating and managing teams
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <div className="space-y-3 pl-6">
                    <p>To create and manage teams for course collaboration:</p>
                    <ol className="list-decimal pl-5 space-y-2">
                      <li>Navigate to the "Teams" page from the sidebar.</li>
                      <li>Click "Create New Team" button.</li>
                      <li>Enter a team name and description.</li>
                      <li>Optionally upload a team avatar.</li>
                      <li>Click "Create Team" to finish.</li>
                    </ol>
                    <p className="mt-3">To manage an existing team:</p>
                    <ol className="list-decimal pl-5 space-y-2">
                      <li>Click on the team name from the Teams page.</li>
                      <li>Use the "Team Details" page to:
                        <ul className="list-disc pl-5 mt-1 space-y-1">
                          <li>Edit team information</li>
                          <li>Add or remove team members</li>
                          <li>Assign team roles</li>
                          <li>Add or remove courses from the team</li>
                        </ul>
                      </li>
                    </ol>
                  </div>
                </AccordionContent>
              </AccordionItem>

              <AccordionItem value="adding-team-members">
                <AccordionTrigger>
                  <div className="flex items-center">
                    <UserPlus className="h-4 w-4 mr-2 text-primary" />
                    Adding team members
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <div className="space-y-3 pl-6">
                    <p>To add members to your team:</p>
                    <ol className="list-decimal pl-5 space-y-2">
                      <li>Navigate to the team's detail page.</li>
                      <li>Click the "Add Member" button in the Members section.</li>
                      <li>Enter the email address of the person you want to invite.</li>
                      <li>Select their role: Admin, Editor, or Viewer.</li>
                      <li>Click "Send Invitation" to add them to the team.</li>
                    </ol>
                    <p className="mt-2">Team member roles and permissions:</p>
                    <ul className="list-disc pl-5 space-y-1">
                      <li><strong>Admin</strong>: Can manage team settings, members, and all courses.</li>
                      <li><strong>Editor</strong>: Can create and edit courses but cannot manage team members.</li>
                      <li><strong>Viewer</strong>: Can only view courses but not edit them.</li>
                    </ul>
                    <p className="text-sm text-slate-500 mt-2">
                      Invited users must have an account on CourseCreator AI to join the team. If they don't have an account, they'll be prompted to create one.
                    </p>
                  </div>
                </AccordionContent>
              </AccordionItem>

              <AccordionItem value="course-collaboration">
                <AccordionTrigger>
                  <div className="flex items-center">
                    <BookOpen className="h-4 w-4 mr-2 text-primary" />
                    Managing course collaborators
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <div className="space-y-3 pl-6">
                    <p>You can invite collaborators directly to specific courses without creating a team:</p>
                    <ol className="list-decimal pl-5 space-y-2">
                      <li>Navigate to "My Courses" in the sidebar.</li>
                      <li>Find the course you want to share and click the dropdown menu.</li>
                      <li>Select "Manage Collaborators" from the menu.</li>
                      <li>In the dialog box, enter the email address of your collaborator.</li>
                      <li>Select their role (Editor or Viewer) and permissions (Can Edit or View Only).</li>
                      <li>Click "Add Collaborator" to send the invitation.</li>
                    </ol>
                    <p className="mt-2">To manage existing collaborators:</p>
                    <ul className="list-disc pl-5 space-y-2">
                      <li>Use the same "Manage Collaborators" dialog to view current collaborators.</li>
                      <li>Change roles or permissions using the dropdown menus next to each collaborator.</li>
                      <li>Remove collaborators by clicking the delete icon.</li>
                    </ul>
                    <div className="bg-slate-50 p-3 rounded-md mt-3">
                      <p className="text-sm text-slate-700">
                        <strong>Pro Tip:</strong> For extensive collaboration across multiple courses, creating a team is more efficient than managing collaborators individually.
                      </p>
                    </div>
                  </div>
                </AccordionContent>
              </AccordionItem>

              <AccordionItem value="viewing-collaborations">
                <AccordionTrigger>
                  <div className="flex items-center">
                    <BookOpen className="h-4 w-4 mr-2 text-primary" />
                    Viewing your collaborations
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <div className="space-y-3 pl-6">
                    <p>To access courses others have invited you to collaborate on:</p>
                    <ol className="list-decimal pl-5 space-y-2">
                      <li>Click "Collaborations" in the sidebar menu.</li>
                      <li>View all courses you've been invited to collaborate on.</li>
                      <li>Use filters to sort by course status, owner, or your role.</li>
                      <li>Click on a course to view or edit it (based on your permissions).</li>
                    </ol>
                    <p className="text-sm text-slate-500 mt-2">
                      Your access level and editing capabilities depend on the permissions set by the course owner.
                    </p>
                  </div>
                </AccordionContent>
              </AccordionItem>

              <AccordionItem value="collaboration-best-practices">
                <AccordionTrigger>
                  <div className="flex items-center">
                    <UserPlus className="h-4 w-4 mr-2 text-primary" />
                    Collaboration best practices
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <div className="space-y-3 pl-6">
                    <p>Maximize productivity when collaborating on courses:</p>
                    <ul className="list-disc pl-5 space-y-2">
                      <li>
                        <strong>Assign clear responsibilities</strong>: Decide who will handle different aspects of the course (content creation, media selection, review, etc.).
                      </li>
                      <li>
                        <strong>Use appropriate permissions</strong>: Give editors full access to sections they're responsible for, but use view-only permissions for those who only need to reference content.
                      </li>
                      <li>
                        <strong>Communicate changes</strong>: Use comments and notifications to keep team members informed about significant updates.
                      </li>
                      <li>
                        <strong>Coordinate on AI resources</strong>: Plan AI usage (script generation, image creation) to avoid depleting shared credits unexpectedly.
                      </li>
                      <li>
                        <strong>Implement review cycles</strong>: Establish clear processes for content review and approval before publishing.
                      </li>
                    </ul>
                    <div className="bg-slate-50 p-3 rounded-md mt-3">
                      <p className="text-sm text-slate-700">
                        <strong>Pro Tip:</strong> For larger teams, create a project timeline with clear milestones and deadlines to keep everyone aligned on course development progress.
                      </p>
                    </div>
                  </div>
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          </div>
        </TabsContent>
      </Tabs>

      {/* FAQs Section */}
      <div className="mt-12 space-y-4">
        <div className="flex items-center space-x-2">
          <MessageSquare className="h-5 w-5 text-primary" />
          <h2 className="text-2xl font-semibold">Frequently Asked Questions</h2>
        </div>
        
        <Accordion type="single" collapsible className="w-full">
          <AccordionItem value="faq-1">
            <AccordionTrigger>How many courses can I create with a free account?</AccordionTrigger>
            <AccordionContent>
              With a free account, you can create one course. This limitation helps you explore the platform's features before committing to a paid subscription. For unlimited courses, upgrade to any of our paid plans.
            </AccordionContent>
          </AccordionItem>
          
          <AccordionItem value="faq-2">
            <AccordionTrigger>Can I export my courses to use on other platforms?</AccordionTrigger>
            <AccordionContent>
              Yes, CourseCreator AI supports exporting courses in multiple formats depending on your subscription plan. Free users have limited export options, while paid plans allow exports as videos, SCORM packages (for LMS integration), slide presentations, and PDF documents.
            </AccordionContent>
          </AccordionItem>
          
          <AccordionItem value="faq-3">
            <AccordionTrigger>How are AI credits calculated and allocated?</AccordionTrigger>
            <AccordionContent>
              AI credits are consumed when using AI features like content generation, image creation, and text-to-speech. Different features use varying amounts of credits based on their complexity. Each subscription tier includes a monthly credit allocation: Free (50), Starter (500), Pro (2,000), Business (5,000), and custom for Enterprise plans. Unused credits expire at the end of each billing cycle.
            </AccordionContent>
          </AccordionItem>
          
          <AccordionItem value="faq-4">
            <AccordionTrigger>Is my content safe and private?</AccordionTrigger>
            <AccordionContent>
              Yes, your content is secure and private. CourseCreator AI does not claim ownership of your content, and we implement robust security measures to protect your data. Only you and collaborators you specifically invite can access your courses. We use industry-standard encryption to secure your information, and you retain full intellectual property rights to all content you create.
            </AccordionContent>
          </AccordionItem>
          
          <AccordionItem value="faq-5">
            <AccordionTrigger>How do I cancel or change my subscription?</AccordionTrigger>
            <AccordionContent>
              You can manage your subscription from the Settings page. To change plans, select "Subscription" and choose "Change Plan" to upgrade or downgrade. To cancel, select "Cancel Subscription" and follow the prompts. You'll retain access to your current plan until the end of the billing period. Note that downgrading may restrict access to certain features and content if they exceed the limits of your new plan.
            </AccordionContent>
          </AccordionItem>
          
          <AccordionItem value="faq-6">
            <AccordionTrigger>What languages are supported for AI voice generation?</AccordionTrigger>
            <AccordionContent>
              CourseCreator AI supports text-to-speech narration in 25 languages, including English, Spanish, French, German, Italian, Portuguese, Japanese, Chinese, Korean, Arabic, Dutch, Hindi, Russian, and more. Premium voices are available in all supported languages, with various accents and speaking styles to choose from.
            </AccordionContent>
          </AccordionItem>
          
          <AccordionItem value="faq-7">
            <AccordionTrigger>How are AI-generated images and videos licensed?</AccordionTrigger>
            <AccordionContent>
              All AI-generated images and videos created through CourseCreator AI are licensed for your commercial use. You own the content you create and can use it in your courses, marketing materials, or other business purposes. However, be mindful that while the platform creates original content, you should avoid generating images that infringe on trademarks or attempt to recreate copyrighted characters or designs.
            </AccordionContent>
          </AccordionItem>
          
          <AccordionItem value="faq-8">
            <AccordionTrigger>How do I get help if I encounter technical issues?</AccordionTrigger>
            <AccordionContent>
              For technical support, you can:
              <ul className="list-disc pl-5 space-y-1 mt-2">
                <li>Contact our support team via <NAME_EMAIL></li>
                <li>Use the live chat feature in the bottom right corner of the platform</li>
                <li>Check our knowledge base for common issue resolutions</li>
                <li>Join our community forum to connect with other users</li>
              </ul>
              Premium plan subscribers receive priority support with faster response times.
            </AccordionContent>
          </AccordionItem>
          
          <AccordionItem value="faq-9">
            <AccordionTrigger>Can I upload my own media or do I have to use the provided resources?</AccordionTrigger>
            <AccordionContent>
              You can absolutely upload your own media files to use in your courses. CourseCreator AI supports uploading your own images, videos, audio files, and documents. We also provide integrations with Pexels and Pixabay for free stock media, plus AI generation tools if you prefer not to use your own assets. The platform is designed to be flexible, allowing you to use any combination of your own media and platform-provided resources.
            </AccordionContent>
          </AccordionItem>
          
          <AccordionItem value="faq-10">
            <AccordionTrigger>How many collaborators can I add to my courses?</AccordionTrigger>
            <AccordionContent>
              The number of collaborators you can add depends on your subscription plan:
              <ul className="list-disc pl-5 space-y-1 mt-2">
                <li><strong>Free Plan</strong>: No collaboration features</li>
                <li><strong>Starter Plan</strong>: Up to 2 collaborators per course</li>
                <li><strong>Pro Plan</strong>: Up to 5 team members</li>
                <li><strong>Business Plan</strong>: Unlimited team members</li>
                <li><strong>Enterprise Plan</strong>: Unlimited team members with enhanced collaboration features</li>
              </ul>
              Each collaborator must have their own CourseCreator AI account to access shared courses.
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </div>

      {/* Contact Support Section */}
      <div className="mt-12 border rounded-lg p-6 bg-slate-50">
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
          <div className="space-y-1">
            <h3 className="text-xl font-semibold">Need more help?</h3>
            <p className="text-slate-600">Our support team is ready to assist you with any questions.</p>
          </div>
          <div className="flex gap-3">
            <Button variant="outline">
              <Mail className="mr-2 h-4 w-4" />
              Contact Support
            </Button>
            <Button>
              <MessageSquare className="mr-2 h-4 w-4" />
              Live Chat
            </Button>
          </div>
        </div>
        <Separator className="my-6" />
        <div className="flex flex-col sm:flex-row gap-6">
          <div className="flex-1 space-y-2">
            <h4 className="font-medium">Support Hours</h4>
            <p className="text-sm text-slate-600">Monday - Friday: 9AM - 8PM EST</p>
            <p className="text-sm text-slate-600">Weekend: 10AM - 5PM EST</p>
          </div>
          <div className="flex-1 space-y-2">
            <h4 className="font-medium">Email</h4>
            <p className="text-sm text-slate-600"><EMAIL></p>
            <p className="text-sm text-slate-600">For urgent issues: <EMAIL></p>
          </div>
          <div className="flex-1 space-y-2">
            <h4 className="font-medium">Community</h4>
            <p className="text-sm text-slate-600">Join our <Button variant="link" className="p-0 h-auto text-primary">user community</Button> for tips and peer support.</p>
          </div>
        </div>
      </div>
    </div>
  );
}