import { useState } from 'react';
import { NotificationsProvider, useNotifications } from '@/hooks/use-notifications';
import { PageHeader } from '@/components/PageHeader';
import { Bell, Info, Loader2 } from 'lucide-react';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

export default function NotificationSettingsPage() {
  return (
    <NotificationsProvider>
      <div className="container py-6 max-w-5xl">
        <PageHeader
          title="Notification Settings"
          icon={<Bell className="h-6 w-6" />}
          description="Customize your notification preferences"
        />
        <div className="mt-6">
          <NotificationPreferences />
        </div>
      </div>
    </NotificationsProvider>
  );
}

function NotificationPreferences() {
  const { notificationTypes, preferences, updatePreferenceMutation, isLoading } = useNotifications();

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-20">
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        <span className="ml-3 text-muted-foreground">Loading preferences...</span>
      </div>
    );
  }

  if (!notificationTypes?.length || !preferences?.length) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>No notification preferences available</CardTitle>
          <CardDescription>Notification preferences will appear here as they become available.</CardDescription>
        </CardHeader>
      </Card>
    );
  }

  // Group notification types by category
  const groupedTypes = notificationTypes.reduce((acc, type) => {
    const category = type.category || 'other';
    if (!acc[category]) {
      acc[category] = [];
    }
    acc[category].push(type);
    return acc;
  }, {} as Record<string, typeof notificationTypes>);

  // Get preference for a type
  const getPreference = (typeId: number) => {
    return preferences.find(pref => pref.typeId === typeId);
  };

  // Toggle preference
  const togglePreference = (typeId: number, enabled: boolean) => {
    updatePreferenceMutation.mutate({ typeId, enabled });
  };

  // Format category name
  const formatCategoryName = (category: string) => {
    return category.charAt(0).toUpperCase() + category.slice(1);
  };

  return (
    <div className="space-y-8">
      {Object.entries(groupedTypes).map(([category, types]) => (
        <Card key={category}>
          <CardHeader>
            <CardTitle>{formatCategoryName(category)} Notifications</CardTitle>
            <CardDescription>
              Manage notification preferences for {category} related events.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {types.map(type => {
                const preference = getPreference(type.id);
                const isEnabled = preference?.enabled ?? true;

                return (
                  <div key={type.id} className="flex items-center justify-between py-2">
                    <div className="flex items-start gap-3">
                      <div className="flex flex-col">
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{type.displayName}</span>
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Info className="h-4 w-4 text-muted-foreground cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent side="right" className="max-w-sm">
                                <p>{type.description}</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </div>
                        <p className="text-sm text-muted-foreground">{type.description}</p>
                      </div>
                    </div>
                    <div className="flex-shrink-0">
                      <div className="flex items-center gap-2">
                        <Label htmlFor={`notification-${type.id}`} className="sr-only">
                          {type.displayName}
                        </Label>
                        <Switch
                          id={`notification-${type.id}`}
                          disabled={updatePreferenceMutation.isPending}
                          checked={isEnabled}
                          onCheckedChange={(checked) => togglePreference(type.id, checked)}
                        />
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
