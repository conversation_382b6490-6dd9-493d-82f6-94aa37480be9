import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Slider } from '@/components/ui/slider';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Play, Pause, Download, Settings, Volume2, Mic, Sparkles } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';

interface Voice {
  id: string;
  name: string;
  gender?: string;
  accent?: string;
  language?: string;
  preview_url?: string;
}

interface TTSService {
  name: string;
  id: string;
  status: 'available' | 'unavailable' | 'error';
  voices: Voice[];
  features: string[];
}

interface UnifiedVoicePanelProps {
  scripts: Array<{
    moduleId: string;
    lessonId: string;
    text: string;
    moduleTitle: string;
    lessonTitle: string;
  }>;
  onVoiceGenerated: (results: any[]) => void;
  onClose: () => void;
  isVisible: boolean;
}

export default function UnifiedVoicePanel({ 
  scripts, 
  onVoiceGenerated, 
  onClose, 
  isVisible 
}: UnifiedVoicePanelProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  
  // State management
  const [selectedService, setSelectedService] = useState<string>('elevenlabs');
  const [selectedVoice, setSelectedVoice] = useState<string>('');
  const [selectedTone, setSelectedTone] = useState<string>('professional');
  const [speed, setSpeed] = useState<number>(1.0);
  const [pitch, setPitch] = useState<number>(1.0);
  const [isGenerating, setIsGenerating] = useState<boolean>(false);
  const [playingPreview, setPlayingPreview] = useState<string | null>(null);

  // Tone options
  const toneOptions = [
    { value: 'professional', label: 'Professional', description: 'Clear, authoritative delivery' },
    { value: 'friendly', label: 'Friendly', description: 'Warm, approachable tone' },
    { value: 'enthusiastic', label: 'Enthusiastic', description: 'Energetic, engaging style' },
    { value: 'calm', label: 'Calm', description: 'Soothing, relaxed pace' },
    { value: 'instructional', label: 'Instructional', description: 'Clear, educational focus' }
  ];

  // Fetch available TTS services and voices
  const { data: servicesData, isLoading } = useQuery({
    queryKey: ['/api/voice/services'],
    enabled: isVisible,
  });

  const services: TTSService[] = servicesData?.services || [
    {
      name: 'ElevenLabs',
      id: 'elevenlabs',
      status: 'available',
      voices: [],
      features: ['High Quality', 'Multiple Languages', 'Voice Cloning']
    },
    {
      name: 'Coqui TTS',
      id: 'coqui',
      status: 'available', 
      voices: [],
      features: ['Open Source', 'Fast Generation', 'Custom Models']
    },
    {
      name: 'Kokoro TTS',
      id: 'kokoro',
      status: 'available',
      voices: [],
      features: ['Japanese Focused', 'Expressive', 'Natural Prosody']
    }
  ];

  const currentService = services.find(s => s.id === selectedService);
  const availableVoices = currentService?.voices || [];

  // Auto-select first available voice when service changes
  useEffect(() => {
    if (availableVoices.length > 0 && !selectedVoice) {
      setSelectedVoice(availableVoices[0].id);
    }
  }, [availableVoices, selectedVoice]);

  // Voice generation mutation
  const generateVoiceMutation = useMutation({
    mutationFn: async (params: any) => {
      return apiRequest('/api/voice/generate-batch', {
        method: 'POST',
        body: JSON.stringify(params),
      });
    },
    onSuccess: (data) => {
      toast({
        title: "Voice Generation Complete",
        description: `Successfully generated audio for ${data.results.length} scripts`,
      });
      onVoiceGenerated(data.results);
      queryClient.invalidateQueries({ queryKey: ['/api/voice'] });
    },
    onError: (error: any) => {
      toast({
        title: "Voice Generation Failed",
        description: error.message || "Failed to generate voice audio",
        variant: "destructive",
      });
    },
  });

  // Preview voice mutation
  const previewMutation = useMutation({
    mutationFn: async (params: any) => {
      return apiRequest('/api/voice/preview', {
        method: 'POST',
        body: JSON.stringify(params),
      });
    },
    onSuccess: (data) => {
      // Play preview audio
      const audio = new Audio(data.audioUrl);
      audio.play();
      setPlayingPreview(data.audioUrl);
      audio.onended = () => setPlayingPreview(null);
    },
  });

  const handlePreviewVoice = async () => {
    if (!selectedVoice || !currentService) return;
    
    const sampleText = scripts[0]?.text.substring(0, 200) + "..." || 
      "Welcome to this course. This is a preview of how your voice will sound with the selected settings.";
    
    previewMutation.mutate({
      service: selectedService,
      voiceId: selectedVoice,
      text: sampleText,
      tone: selectedTone,
      speed,
      pitch,
    });
  };

  const handleGenerateVoices = async () => {
    if (!selectedVoice || !currentService || scripts.length === 0) {
      toast({
        title: "Missing Requirements",
        description: "Please select a voice and ensure scripts are available",
        variant: "destructive",
      });
      return;
    }

    setIsGenerating(true);
    
    generateVoiceMutation.mutate({
      service: selectedService,
      voiceId: selectedVoice,
      scripts: scripts,
      settings: {
        tone: selectedTone,
        speed,
        pitch,
      },
    });
    
    setIsGenerating(false);
  };

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-4xl max-h-[90vh] overflow-auto">
        <CardHeader className="border-b">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Volume2 className="h-5 w-5" />
                Create Audio Content
              </CardTitle>
              <p className="text-sm text-muted-foreground mt-1">
                Transform your scripts into professional audio narration
              </p>
            </div>
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
          </div>
        </CardHeader>

        <CardContent className="p-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Service Selection */}
            <div className="lg:col-span-1">
              <h3 className="font-medium mb-3 flex items-center gap-2">
                <Mic className="h-4 w-4" />
                Voice Service
              </h3>
              
              <Tabs value={selectedService} onValueChange={setSelectedService} className="w-full">
                <TabsList className="grid w-full grid-cols-1 h-auto">
                  {services.map((service) => (
                    <TabsTrigger
                      key={service.id}
                      value={service.id}
                      className="flex flex-col items-start p-3 h-auto"
                    >
                      <div className="flex items-center justify-between w-full">
                        <span className="font-medium">{service.name}</span>
                        <Badge 
                          variant={service.status === 'available' ? 'default' : 'destructive'}
                          className="text-xs"
                        >
                          {service.status}
                        </Badge>
                      </div>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {service.features.slice(0, 2).map((feature) => (
                          <Badge key={feature} variant="outline" className="text-xs">
                            {feature}
                          </Badge>
                        ))}
                      </div>
                    </TabsTrigger>
                  ))}
                </TabsList>
              </Tabs>
            </div>

            {/* Voice Selection & Settings */}
            <div className="lg:col-span-2">
              <div className="space-y-6">
                {/* Voice Selection */}
                <div>
                  <h3 className="font-medium mb-3">Voice Selection</h3>
                  <Select value={selectedVoice} onValueChange={setSelectedVoice}>
                    <SelectTrigger>
                      <SelectValue placeholder={`Select a ${currentService?.name} voice`} />
                    </SelectTrigger>
                    <SelectContent>
                      {availableVoices.map((voice) => (
                        <SelectItem key={voice.id} value={voice.id}>
                          <div className="flex items-center justify-between w-full">
                            <span>{voice.name}</span>
                            {voice.gender && (
                              <Badge variant="outline" className="ml-2 text-xs">
                                {voice.gender}
                              </Badge>
                            )}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  
                  {selectedVoice && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handlePreviewVoice}
                      disabled={previewMutation.isPending}
                      className="mt-2"
                    >
                      <Play className="h-4 w-4 mr-2" />
                      {previewMutation.isPending ? 'Generating...' : 'Preview Voice'}
                    </Button>
                  )}
                </div>

                {/* Tone Selection */}
                <div>
                  <h3 className="font-medium mb-3 flex items-center gap-2">
                    <Sparkles className="h-4 w-4" />
                    Tone & Style
                  </h3>
                  <Select value={selectedTone} onValueChange={setSelectedTone}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {toneOptions.map((tone) => (
                        <SelectItem key={tone.value} value={tone.value}>
                          <div>
                            <div className="font-medium">{tone.label}</div>
                            <div className="text-xs text-muted-foreground">{tone.description}</div>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Advanced Settings */}
                <div className="space-y-4">
                  <h3 className="font-medium flex items-center gap-2">
                    <Settings className="h-4 w-4" />
                    Advanced Settings
                  </h3>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium">Speed: {speed}x</label>
                      <Slider
                        value={[speed]}
                        onValueChange={(value) => setSpeed(value[0])}
                        min={0.5}
                        max={2.0}
                        step={0.1}
                        className="mt-2"
                      />
                    </div>
                    
                    <div>
                      <label className="text-sm font-medium">Pitch: {pitch}x</label>
                      <Slider
                        value={[pitch]}
                        onValueChange={(value) => setPitch(value[0])}
                        min={0.8}
                        max={1.2}
                        step={0.1}
                        className="mt-2"
                      />
                    </div>
                  </div>
                </div>

                {/* Scripts Summary */}
                <div>
                  <h3 className="font-medium mb-3">Scripts to Generate</h3>
                  <div className="bg-muted p-3 rounded-md">
                    <p className="text-sm">
                      <strong>{scripts.length}</strong> scripts ready for voice generation
                    </p>
                    <p className="text-xs text-muted-foreground mt-1">
                      Estimated generation time: {Math.ceil(scripts.length * 2)} minutes
                    </p>
                  </div>
                </div>

                {/* Generate Button */}
                <div className="flex justify-end gap-3 pt-4 border-t">
                  <Button variant="outline" onClick={onClose}>
                    Cancel
                  </Button>
                  <Button
                    onClick={handleGenerateVoices}
                    disabled={!selectedVoice || scripts.length === 0 || isGenerating}
                    className="min-w-[140px]"
                  >
                    {isGenerating ? (
                      <>Generating...</>
                    ) : (
                      <>
                        <Volume2 className="h-4 w-4 mr-2" />
                        Generate Audio
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}