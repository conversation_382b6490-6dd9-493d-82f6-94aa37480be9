import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTrigger } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { useToast } from '@/hooks/use-toast';
import { Loader2, FileText, Download, Eye, Presentation, Image, FileImage } from 'lucide-react';

interface SlideGenerationModalProps {
  isOpen: boolean;
  onClose: () => void;
  courseTitle: string;
  lessonTitle?: string;
  initialScript?: string;
  onSlidesGenerated?: (slideData: any) => void;
  trigger?: React.ReactNode;
}

interface SlideTheme {
  id: string;
  name: string;
  description: string;
}

interface SlideGenerationResult {
  success: boolean;
  slide_data?: {
    marp_markdown: string;
    slide_structure: any[];
    slide_count: number;
    analysis: any;
    theme: string;
  };
  rendered_outputs?: {
    html_base64: string;
    pdf_base64: string;
    pptx_base64?: string;
    slides_png: string[];
  };
  metadata?: {
    course_title: string;
    lesson_title?: string;
    generation_time_seconds: number;
    actual_slide_count: number;
    theme_used: string;
    gpu_used: boolean;
  };
  error?: string;
}

export function SlideGenerationModal({
  isOpen,
  onClose,
  courseTitle,
  lessonTitle,
  initialScript = '',
  onSlidesGenerated,
  trigger
}: SlideGenerationModalProps) {
  const [script, setScript] = useState(initialScript);
  const [selectedTheme, setSelectedTheme] = useState('default');
  const [slideCount, setSlideCount] = useState(10);
  const [includeAnimations, setIncludeAnimations] = useState(true);
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationProgress, setGenerationProgress] = useState(0);
  const [availableThemes, setAvailableThemes] = useState<SlideTheme[]>([]);
  const [generatedSlides, setGeneratedSlides] = useState<SlideGenerationResult | null>(null);
  const [serviceStatus, setServiceStatus] = useState<any>(null);
  
  const { toast } = useToast();

  // Load available themes on component mount
  useEffect(() => {
    fetchAvailableThemes();
    checkServiceHealth();
  }, []);

  // Update script when initialScript changes
  useEffect(() => {
    if (initialScript) {
      setScript(initialScript);
    }
  }, [initialScript]);

  const fetchAvailableThemes = async () => {
    try {
      const response = await fetch('/api/slides/themes');
      if (response.ok) {
        const data = await response.json();
        setAvailableThemes(data.themes || []);
      }
    } catch (error) {
      console.error('Failed to fetch slide themes:', error);
    }
  };

  const checkServiceHealth = async () => {
    try {
      const response = await fetch('/api/slides/health');
      if (response.ok) {
        const health = await response.json();
        setServiceStatus(health);
      }
    } catch (error) {
      console.error('Health check failed:', error);
      setServiceStatus({ status: 'error', error: 'Service unavailable' });
    }
  };

  const generateSlides = async () => {
    if (!script.trim() || script.trim().length < 50) {
      toast({
        title: "Script Required",
        description: "Please provide a script with at least 50 characters for slide generation.",
        variant: "destructive"
      });
      return;
    }

    setIsGenerating(true);
    setGenerationProgress(0);
    setGeneratedSlides(null);

    // Simulate progress updates
    const progressInterval = setInterval(() => {
      setGenerationProgress(prev => {
        if (prev >= 90) return prev;
        return prev + Math.random() * 15;
      });
    }, 2000);

    try {
      const response = await fetch('/api/slides/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          script_content: script,
          course_title: courseTitle,
          lesson_title: lessonTitle,
          style_theme: selectedTheme,
          slide_count_target: slideCount,
          include_animations: includeAnimations,
          custom_branding: {
            primary_color: '#1e3a8a',
            text_color: '#1f2937',
            font_family: 'Inter, sans-serif'
          }
        })
      });

      clearInterval(progressInterval);
      setGenerationProgress(100);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Slide generation failed');
      }

      const result: SlideGenerationResult = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Slide generation failed');
      }

      setGeneratedSlides(result);
      
      if (onSlidesGenerated) {
        onSlidesGenerated(result);
      }

      toast({
        title: "Slides Generated Successfully",
        description: `Generated ${result.metadata?.actual_slide_count || slideCount} slides in ${result.metadata?.generation_time_seconds?.toFixed(1) || 'N/A'}s`,
      });

    } catch (error: any) {
      clearInterval(progressInterval);
      console.error('Slide generation error:', error);
      
      toast({
        title: "Slide Generation Failed",
        description: error.message || 'Failed to generate slides. Please try again.',
        variant: "destructive"
      });
    } finally {
      setIsGenerating(false);
      setGenerationProgress(0);
    }
  };

  const downloadSlide = (format: 'html' | 'pdf' | 'pptx', base64Data: string) => {
    try {
      const byteCharacters = atob(base64Data);
      const byteNumbers = new Array(byteCharacters.length);
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
      }
      const byteArray = new Uint8Array(byteNumbers);
      
      const mimeTypes = {
        html: 'text/html',
        pdf: 'application/pdf',
        pptx: 'application/vnd.openxmlformats-officedocument.presentationml.presentation'
      };
      
      const blob = new Blob([byteArray], { type: mimeTypes[format] });
      const url = URL.createObjectURL(blob);
      
      const link = document.createElement('a');
      link.href = url;
      link.download = `${courseTitle}-${lessonTitle || 'slides'}.${format}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      
      toast({
        title: "Download Started",
        description: `Downloading ${format.toUpperCase()} slides...`
      });
    } catch (error) {
      console.error('Download error:', error);
      toast({
        title: "Download Failed",
        description: "Failed to download slides. Please try again.",
        variant: "destructive"
      });
    }
  };

  const previewSlides = () => {
    if (!generatedSlides?.rendered_outputs?.html_base64) {
      toast({
        title: "No Preview Available",
        description: "HTML slides not available for preview.",
        variant: "destructive"
      });
      return;
    }

    try {
      const htmlContent = atob(generatedSlides.rendered_outputs.html_base64);
      const newWindow = window.open('', '_blank');
      if (newWindow) {
        newWindow.document.write(htmlContent);
        newWindow.document.close();
      } else {
        toast({
          title: "Preview Blocked",
          description: "Please allow popups to preview slides.",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('Preview error:', error);
      toast({
        title: "Preview Failed",
        description: "Failed to open slide preview.",
        variant: "destructive"
      });
    }
  };

  const modalContent = (
    <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
      <DialogHeader>
        <DialogTitle className="flex items-center gap-2">
          <Presentation className="h-5 w-5" />
          Generate Course Slides
        </DialogTitle>
      </DialogHeader>

      <div className="space-y-6">
        {/* Service Status */}
        {serviceStatus && (
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center gap-2">
                Service Status
                <Badge variant={serviceStatus.status === 'healthy' ? 'default' : 'destructive'}>
                  {serviceStatus.status}
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="text-sm text-muted-foreground">
                {serviceStatus.status === 'healthy' ? (
                  <span className="flex items-center gap-2">
                    A100 GPU Available • Marp Ready • AI Analysis Active
                  </span>
                ) : (
                  <span>Using local fallback • Limited features available</span>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Configuration */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div>
              <Label htmlFor="course-title">Course Title</Label>
              <Input
                id="course-title"
                value={courseTitle}
                disabled
                className="bg-muted"
              />
            </div>

            <div>
              <Label htmlFor="lesson-title">Lesson Title (Optional)</Label>
              <Input
                id="lesson-title"
                value={lessonTitle || ''}
                disabled
                className="bg-muted"
              />
            </div>

            <div>
              <Label htmlFor="slide-theme">Slide Theme</Label>
              <Select value={selectedTheme} onValueChange={setSelectedTheme}>
                <SelectTrigger>
                  <SelectValue placeholder="Select theme" />
                </SelectTrigger>
                <SelectContent>
                  {availableThemes.map((theme) => (
                    <SelectItem key={theme.id} value={theme.id}>
                      {theme.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="slide-count">Target Slide Count</Label>
              <Input
                id="slide-count"
                type="number"
                min="3"
                max="50"
                value={slideCount}
                onChange={(e) => setSlideCount(parseInt(e.target.value) || 10)}
              />
            </div>
          </div>

          <div className="space-y-4">
            <div>
              <Label htmlFor="script-content">Course Script</Label>
              <Textarea
                id="script-content"
                placeholder="Enter your course script content here..."
                value={script}
                onChange={(e) => setScript(e.target.value)}
                rows={8}
                className="resize-none"
              />
              <div className="text-xs text-muted-foreground mt-1">
                {script.length} characters (minimum 50 required)
              </div>
            </div>
          </div>
        </div>

        {/* Generation Progress */}
        {isGenerating && (
          <Card>
            <CardContent className="pt-6">
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span className="text-sm font-medium">Generating slides...</span>
                </div>
                <Progress value={generationProgress} className="w-full" />
                <div className="text-xs text-muted-foreground">
                  Analyzing script • Creating slide structure • Rendering presentations
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Generated Slides Results */}
        {generatedSlides && generatedSlides.success && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Generated Slides
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary">
                    {generatedSlides.metadata?.actual_slide_count || 0}
                  </div>
                  <div className="text-sm text-muted-foreground">Slides Created</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary">
                    {generatedSlides.metadata?.generation_time_seconds?.toFixed(1) || 'N/A'}s
                  </div>
                  <div className="text-sm text-muted-foreground">Generation Time</div>
                </div>
                <div className="text-center">
                  <Badge variant={generatedSlides.metadata?.gpu_used ? 'default' : 'secondary'}>
                    {generatedSlides.metadata?.gpu_used ? 'A100 GPU' : 'Local CPU'}
                  </Badge>
                  <div className="text-sm text-muted-foreground mt-1">Processing</div>
                </div>
              </div>

              <div className="flex flex-wrap gap-2">
                <Button
                  onClick={previewSlides}
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-2"
                >
                  <Eye className="h-4 w-4" />
                  Preview
                </Button>

                {generatedSlides.rendered_outputs?.html_base64 && (
                  <Button
                    onClick={() => downloadSlide('html', generatedSlides.rendered_outputs!.html_base64)}
                    variant="outline"
                    size="sm"
                    className="flex items-center gap-2"
                  >
                    <Download className="h-4 w-4" />
                    HTML
                  </Button>
                )}

                {generatedSlides.rendered_outputs?.pdf_base64 && (
                  <Button
                    onClick={() => downloadSlide('pdf', generatedSlides.rendered_outputs!.pdf_base64)}
                    variant="outline"
                    size="sm"
                    className="flex items-center gap-2"
                  >
                    <Download className="h-4 w-4" />
                    PDF
                  </Button>
                )}

                {generatedSlides.rendered_outputs?.pptx_base64 && (
                  <Button
                    onClick={() => downloadSlide('pptx', generatedSlides.rendered_outputs!.pptx_base64!)}
                    variant="outline"
                    size="sm"
                    className="flex items-center gap-2"
                  >
                    <Download className="h-4 w-4" />
                    PowerPoint
                  </Button>
                )}
              </div>

              {generatedSlides.rendered_outputs?.slides_png && generatedSlides.rendered_outputs.slides_png.length > 0 && (
                <div>
                  <Label className="text-sm font-medium">Individual Slide Images</Label>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-2 mt-2">
                    {generatedSlides.rendered_outputs.slides_png.slice(0, 8).map((slideImg, index) => (
                      <div key={index} className="relative group">
                        <img
                          src={`data:image/png;base64,${slideImg}`}
                          alt={`Slide ${index + 1}`}
                          className="w-full h-20 object-cover rounded border"
                        />
                        <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity rounded flex items-center justify-center">
                          <span className="text-white text-xs">Slide {index + 1}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                  {generatedSlides.rendered_outputs.slides_png.length > 8 && (
                    <div className="text-xs text-muted-foreground mt-2">
                      +{generatedSlides.rendered_outputs.slides_png.length - 8} more slides
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Action Buttons */}
        <div className="flex justify-between">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button
            onClick={generateSlides}
            disabled={isGenerating || !script.trim() || script.trim().length < 50}
            className="flex items-center gap-2"
          >
            {isGenerating ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin" />
                Generating...
              </>
            ) : (
              <>
                <Presentation className="h-4 w-4" />
                Generate Slides
              </>
            )}
          </Button>
        </div>
      </div>
    </DialogContent>
  );

  if (trigger) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogTrigger asChild>
          {trigger}
        </DialogTrigger>
        {modalContent}
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      {modalContent}
    </Dialog>
  );
}