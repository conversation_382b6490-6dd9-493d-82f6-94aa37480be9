import { useState, useEffect } from 'react';
import { useLocation } from 'wouter';
import { 
  Star, 
  Clock, 
  Zap, 
  VideoIcon, 
  BookOpen, 
  LayoutDashboard, 
  BrainCircuit, 
  ChevronLeft, 
  Code, 
  Settings,
  MessageSquare,
  Lightbulb,
  PenToolIcon
} from 'lucide-react';
import { ColorfulIcon, type IconCategory } from "@/components/ui/colorful-icon";
import { 
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";

type Action = {
  id: string;
  name: string;
  icon: React.ElementType;
  path: string;
  category: IconCategory;
  color?: string;
};

interface SmartSidebarProps {
  onCollapsedChange?: (collapsed: boolean) => void;
}

export function SmartSidebar({ onCollapsedChange }: SmartSidebarProps = {}) {
  const [location, setLocation] = useLocation();
  const [favorites, setFavorites] = useState<string[]>([]);
  const [recentActions, setRecentActions] = useState<string[]>([]);
  const [isCollapsed, setIsCollapsed] = useState(false);

  // Define all available actions
  const allActions: Action[] = [
    { 
      id: 'dashboard', 
      name: 'Dashboard', 
      icon: LayoutDashboard, 
      path: '/dashboard',
      category: 'dashboard',
      color: 'text-blue-500'
    },
    { 
      id: 'courses', 
      name: 'My Courses', 
      icon: BookOpen, 
      path: '/courses',
      category: 'courses',
      color: 'text-green-500'
    },
    { 
      id: 'course-creator', 
      name: 'Course Creator', 
      icon: VideoIcon, 
      path: '/course-creator',
      category: 'courses',
      color: 'text-purple-500'
    },
    { 
      id: 'mini-course', 
      name: 'Mini Course', 
      icon: Zap, 
      path: '/mini-course-creator',
      category: 'ai',
      color: 'text-amber-500'
    },
    // Removed AI Assistant entry as it's replaced by the floating chat assistant
    { 
      id: 'mind-map', 
      name: 'Mind Map', 
      icon: Lightbulb, 
      path: '/mind-map',
      category: 'content',
      color: 'text-teal-500'
    },
    { 
      id: 'whiteboard', 
      name: 'Whiteboard', 
      icon: PenToolIcon, 
      path: '/whiteboard',
      category: 'content',
      color: 'text-indigo-500'
    },
    { 
      id: 'templates', 
      name: 'Templates', 
      icon: Code, 
      path: '/templates',
      category: 'content',
      color: 'text-cyan-500'
    },
    { 
      id: 'settings', 
      name: 'Settings', 
      icon: Settings, 
      path: '/settings',
      category: 'settings',
      color: 'text-slate-500'
    },
    { 
      id: 'support', 
      name: 'Support', 
      icon: MessageSquare, 
      path: '/support',
      category: 'info',
      color: 'text-emerald-500'
    },
  ];

  // Load saved favorites, recent actions, and collapsed state from localStorage
  useEffect(() => {
    const savedFavorites = localStorage.getItem('smart-sidebar-favorites');
    if (savedFavorites) {
      setFavorites(JSON.parse(savedFavorites));
    } else {
      // Default favorites for new users
      setFavorites(['dashboard', 'course-creator', 'mini-course']);
    }

    const savedRecent = localStorage.getItem('smart-sidebar-recent');
    if (savedRecent) {
      setRecentActions(JSON.parse(savedRecent));
    }
    
    // Load collapsed state
    const savedCollapsed = localStorage.getItem('smart-sidebar-collapsed');
    if (savedCollapsed !== null) {
      const isCollapsedValue = JSON.parse(savedCollapsed);
      setIsCollapsed(isCollapsedValue);
      // Notify parent component of initial collapsed state
      if (onCollapsedChange) {
        onCollapsedChange(isCollapsedValue);
      }
    }
  }, [onCollapsedChange]);

  // Save favorites to localStorage when they change
  useEffect(() => {
    localStorage.setItem('smart-sidebar-favorites', JSON.stringify(favorites));
  }, [favorites]);

  // Save recent actions to localStorage when they change
  useEffect(() => {
    localStorage.setItem('smart-sidebar-recent', JSON.stringify(recentActions));
  }, [recentActions]);

  // Track page visits to update recent actions
  useEffect(() => {
    if (location === '/') return;
    
    const currentAction = allActions.find(action => action.path === location);
    if (!currentAction) return;
    
    // Update recent actions - remove if exists and add to the beginning
    setRecentActions(prev => {
      const newRecent = prev.filter(id => id !== currentAction.id);
      return [currentAction.id, ...newRecent].slice(0, 5); // Keep only 5 most recent
    });
  }, [location, allActions]);

  // Toggle an action as favorite
  const toggleFavorite = (id: string) => {
    setFavorites(prev => {
      if (prev.includes(id)) {
        return prev.filter(item => item !== id);
      } else {
        return [...prev, id];
      }
    });
  };

  // Get favorite actions sorted by order in all actions
  const favoriteActions = allActions.filter(action => favorites.includes(action.id));
  
  // Get recent actions not already in favorites
  const recentActionsFiltered = allActions
    .filter(action => recentActions.includes(action.id) && !favorites.includes(action.id))
    .sort((a, b) => {
      return recentActions.indexOf(a.id) - recentActions.indexOf(b.id);
    });

  // Handle action click
  const handleActionClick = (path: string) => {
    setLocation(path);
  };

  return (
    <div className={cn(
      "fixed right-0 top-16 h-[calc(100vh-64px)] bg-white border-l border-gray-200 z-20 transition-all duration-300",
      isCollapsed ? "w-12" : "w-64"
    )}>
      {/* Collapse button */}
      <button 
        className="absolute -left-3 top-4 bg-white border border-gray-200 rounded-full p-1 shadow-sm hover:bg-gray-50"
        onClick={() => {
          const newState = !isCollapsed;
          setIsCollapsed(newState);
          // Save to localStorage
          localStorage.setItem('smart-sidebar-collapsed', JSON.stringify(newState));
          // Notify parent component of collapsed state change
          if (onCollapsedChange) {
            onCollapsedChange(newState);
          }
        }}
      >
        <ChevronLeft 
          size={16} 
          className={cn(
            "transition-transform duration-300",
            isCollapsed ? "rotate-180" : ""
          )}
        />
      </button>

      <div className="px-3 py-4 h-full flex flex-col overflow-y-auto">
        {/* Header */}
        {!isCollapsed && (
          <div className="mb-4">
            <h2 className="text-sm font-semibold text-gray-500">SMART SIDEBAR</h2>
          </div>
        )}

        {/* Favorites Section */}
        <div className="mb-6">
          {!isCollapsed && (
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center">
                <Star size={14} className="text-amber-500 mr-2" />
                <h3 className="text-xs font-medium text-gray-500">FAVORITES</h3>
              </div>
              
              {/* Clear all favorites button */}
              {favorites.length > 0 && (
                <button 
                  onClick={() => setFavorites([])}
                  className="text-xs text-gray-400 hover:text-gray-600"
                >
                  Clear
                </button>
              )}
            </div>
          )}
          <div className="space-y-1">
            {favoriteActions.map(action => (
              <div key={action.id} className="relative group">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <button
                        onClick={() => handleActionClick(action.path)}
                        className={cn(
                          "w-full flex items-center py-2 px-3 rounded-md transition-colors",
                          location === action.path 
                            ? "bg-primary/10 text-primary" 
                            : "text-gray-700 hover:bg-gray-100",
                          isCollapsed 
                            ? "justify-center" 
                            : ""
                        )}
                      >
                        <ColorfulIcon 
                          icon={action.icon as any} 
                          category={location === action.path ? action.category : 'default'} 
                          size={20}
                        />
                        
                        {!isCollapsed && (
                          <span className="ml-3 text-sm font-medium">{action.name}</span>
                        )}
                      </button>
                    </TooltipTrigger>
                    {isCollapsed && (
                      <TooltipContent side="left">
                        {action.name}
                      </TooltipContent>
                    )}
                  </Tooltip>
                </TooltipProvider>

                {/* Star icon to toggle favorite - visible on hover or if favorited */}
                {!isCollapsed && (
                  <button
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      toggleFavorite(action.id);
                    }}
                    className={cn(
                      "absolute right-2 top-1/2 -translate-y-1/2 opacity-100",
                      "transition-opacity"
                    )}
                  >
                    <Star 
                      size={16} 
                      className="fill-amber-400 text-amber-400"
                    />
                  </button>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Recent Section */}
        {recentActionsFiltered.length > 0 && (
          <div className="mb-6">
            {!isCollapsed && (
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center">
                  <Clock size={14} className="text-gray-400 mr-2" />
                  <h3 className="text-xs font-medium text-gray-500">RECENT</h3>
                </div>
                
                {/* Clear all recent button */}
                <button 
                  onClick={() => setRecentActions([])}
                  className="text-xs text-gray-400 hover:text-gray-600"
                >
                  Clear
                </button>
              </div>
            )}
            <div className="space-y-1">
              {recentActionsFiltered.map(action => (
                <div key={action.id} className="relative group">
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <button
                          onClick={() => handleActionClick(action.path)}
                          className={cn(
                            "w-full flex items-center py-2 px-3 rounded-md transition-colors",
                            location === action.path 
                              ? "bg-primary/10 text-primary" 
                              : "text-gray-700 hover:bg-gray-100",
                            isCollapsed 
                              ? "justify-center" 
                              : ""
                          )}
                        >
                          <ColorfulIcon 
                            icon={action.icon as any} 
                            category={location === action.path ? action.category : 'default'} 
                            size={20}
                          />
                          
                          {!isCollapsed && (
                            <span className="ml-3 text-sm font-medium">{action.name}</span>
                          )}
                        </button>
                      </TooltipTrigger>
                      {isCollapsed && (
                        <TooltipContent side="left">
                          {action.name}
                        </TooltipContent>
                      )}
                    </Tooltip>
                  </TooltipProvider>

                  {/* Star icon to toggle favorite - visible on hover */}
                  {!isCollapsed && (
                    <button
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        toggleFavorite(action.id);
                      }}
                      className="absolute right-2 top-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity"
                    >
                      <Star 
                        size={16} 
                        className="text-gray-400 hover:text-amber-400"
                      />
                    </button>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
        
        {/* All Actions Section - only visible when there are no favorites and no recent actions */}
        {!isCollapsed && favoriteActions.length === 0 && recentActionsFiltered.length === 0 && (
          <div className="mb-6">
            <div className="flex items-center mb-2">
              <Zap size={14} className="text-gray-400 mr-2" />
              <h3 className="text-xs font-medium text-gray-500">ALL ACTIONS</h3>
            </div>
            <div className="space-y-1">
              {allActions.map(action => (
                <div key={action.id} className="relative group">
                  <button
                    onClick={() => handleActionClick(action.path)}
                    className={cn(
                      "w-full flex items-center py-2 px-3 rounded-md transition-colors",
                      location === action.path 
                        ? "bg-primary/10 text-primary" 
                        : "text-gray-700 hover:bg-gray-100"
                    )}
                  >
                    <ColorfulIcon 
                      icon={action.icon as any}
                      category={location === action.path ? action.category : 'default'} 
                      size={20}
                    />
                    <span className="ml-3 text-sm font-medium">{action.name}</span>
                  </button>
                  
                  {/* Star icon to toggle favorite - visible on hover */}
                  <button
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      toggleFavorite(action.id);
                    }}
                    className="absolute right-2 top-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity"
                  >
                    <Star 
                      size={16} 
                      className="text-gray-400 hover:text-amber-400"
                    />
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}