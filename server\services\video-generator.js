const fs = require('fs');
const path = require('path');
const { execSync, spawn } = require('child_process');
const { promisify } = require('util');
const writeFileAsync = promisify(fs.writeFile);
const mkdirAsync = promisify(fs.mkdir);
const existsAsync = promisify(fs.exists);

// Base directories for various assets
const TEMP_DIR = path.join(process.cwd(), 'temp');
const UPLOADS_DIR = path.join(process.cwd(), 'uploads');
const GENERATED_VIDEOS_DIR = path.join(UPLOADS_DIR, 'videos');

// Ensure directories exist
async function ensureDirectoriesExist() {
  const dirs = [TEMP_DIR, UPLOADS_DIR, GENERATED_VIDEOS_DIR];
  
  for (const dir of dirs) {
    if (!await existsAsync(dir)) {
      await mkdirAsync(dir, { recursive: true });
    }
  }
}

/**
 * Generate a video from audio and images
 * @param {Object} options - Configuration options
 * @param {string} options.audioPath - Path to the audio file
 * @param {string[]} options.imagePaths - Array of paths to images
 * @param {string} options.outputFileName - Name for the output file (without extension)
 * @param {string} options.subtitles - Optional subtitle text
 * @param {number} options.duration - Total duration for the video in seconds
 * @returns {Promise<string>} - Path to the generated video
 */
async function generateVideo(options) {
  await ensureDirectoriesExist();
  
  const {
    audioPath,
    imagePaths,
    outputFileName,
    subtitles,
    duration,
  } = options;
  
  // Create a unique output filename if not provided
  const uniqueFileName = outputFileName || `video_${Date.now()}`;
  const outputPath = path.join(GENERATED_VIDEOS_DIR, `${uniqueFileName}.mp4`);
  
  // Create a temporary file for the ffmpeg input
  const inputFilePath = path.join(TEMP_DIR, `${uniqueFileName}_input.txt`);
  
  let ffmpegInput = '';
  
  // Calculate duration per image if multiple images are provided
  const imageCount = imagePaths.length;
  const imageDuration = duration / imageCount;
  
  // Create the input file for FFmpeg with a slideshow of images
  for (const imagePath of imagePaths) {
    ffmpegInput += `file '${imagePath}'\n`;
    ffmpegInput += `duration ${imageDuration}\n`;
  }
  
  // The last image needs a duration too to avoid ffmpeg warnings
  if (imagePaths.length > 0) {
    ffmpegInput += `file '${imagePaths[imagePaths.length - 1]}'\n`;
    ffmpegInput += `duration 1\n`;
  }
  
  await writeFileAsync(inputFilePath, ffmpegInput);
  
  // Create FFmpeg command
  let ffmpegCommand = [
    '-f', 'concat', 
    '-safe', '0', 
    '-i', inputFilePath,
    '-i', audioPath,
    '-c:v', 'libx264',
    '-c:a', 'aac',
    '-shortest',
    '-pix_fmt', 'yuv420p',
    '-r', '30',
    '-preset', 'medium',
  ];
  
  // Add subtitles if provided
  if (subtitles) {
    const subtitlePath = path.join(TEMP_DIR, `${uniqueFileName}.srt`);
    await createSubtitleFile(subtitlePath, subtitles, duration);
    ffmpegCommand = [
      ...ffmpegCommand,
      '-vf', `subtitles=${subtitlePath}`,
    ];
  }
  
  ffmpegCommand = [...ffmpegCommand, outputPath];
  
  // Execute the FFmpeg command
  return new Promise((resolve, reject) => {
    console.log(`Generating video: ffmpeg ${ffmpegCommand.join(' ')}`);
    
    const ffmpeg = spawn('ffmpeg', ffmpegCommand);
    
    ffmpeg.stdout.on('data', (data) => {
      console.log(`FFmpeg output: ${data}`);
    });
    
    ffmpeg.stderr.on('data', (data) => {
      console.log(`FFmpeg stderr: ${data}`);
    });
    
    ffmpeg.on('close', (code) => {
      if (code === 0) {
        console.log(`Video generated successfully: ${outputPath}`);
        resolve({
          path: outputPath,
          url: `/uploads/videos/${path.basename(outputPath)}`,
        });
      } else {
        console.error(`FFmpeg process exited with code ${code}`);
        reject(new Error(`Video generation failed with code ${code}`));
      }
    });
  });
}

/**
 * Create a subtitle file in SRT format
 * @param {string} filePath - Path where to save the subtitle file
 * @param {string} text - Subtitle text
 * @param {number} duration - Duration in seconds
 * @returns {Promise<void>}
 */
async function createSubtitleFile(filePath, text, duration) {
  // Split text into lines of maximum 40 characters
  const lines = [];
  const words = text.split(' ');
  let currentLine = '';
  
  for (const word of words) {
    if ((currentLine + ' ' + word).length <= 40) {
      currentLine = currentLine ? `${currentLine} ${word}` : word;
    } else {
      lines.push(currentLine);
      currentLine = word;
    }
  }
  
  if (currentLine) {
    lines.push(currentLine);
  }
  
  // Create SRT content
  // For simplicity, we'll display each line for equal parts of the total duration
  const lineCount = lines.length;
  const timePerLine = duration / lineCount;
  
  let srtContent = '';
  
  for (let i = 0; i < lineCount; i++) {
    const startTime = i * timePerLine;
    const endTime = (i + 1) * timePerLine;
    
    srtContent += `${i + 1}\n`;
    srtContent += `${formatSRTTime(startTime)} --> ${formatSRTTime(endTime)}\n`;
    srtContent += `${lines[i]}\n\n`;
  }
  
  await writeFileAsync(filePath, srtContent);
}

/**
 * Format time for SRT subtitles (HH:MM:SS,mmm)
 * @param {number} seconds 
 * @returns {string}
 */
function formatSRTTime(seconds) {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  const milliseconds = Math.floor((seconds - Math.floor(seconds)) * 1000);
  
  return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(secs).padStart(2, '0')},${String(milliseconds).padStart(3, '0')}`;
}

/**
 * Create a video from a text script with narration
 * @param {Object} options - Configuration options
 * @param {string} options.script - Text script for narration and subtitles
 * @param {string[]} options.imagePaths - Array of image paths to use in the video
 * @param {string} options.outputFileName - Name for the output file
 * @param {string} options.audioPath - Optional pre-generated audio path
 * @returns {Promise<string>} - Path to the generated video
 */
async function createScriptedVideo(options) {
  const {
    script,
    imagePaths,
    outputFileName,
    audioPath: existingAudioPath,
  } = options;
  
  // If audio is not provided, generate it using TTS
  let audioPath = existingAudioPath;
  if (!audioPath) {
    // TODO: Implement TTS integration
    throw new Error('Audio path must be provided until TTS integration is complete');
  }
  
  // Get audio duration
  const audioDuration = getAudioDuration(audioPath);
  
  // Generate the video
  return generateVideo({
    audioPath,
    imagePaths,
    outputFileName,
    subtitles: script,
    duration: audioDuration,
  });
}

/**
 * Get audio duration in seconds
 * @param {string} audioPath - Path to the audio file
 * @returns {number} - Duration in seconds
 */
function getAudioDuration(audioPath) {
  try {
    // Validate that audioPath is a safe file path
    const fs = require('fs');
    const resolvedPath = path.resolve(audioPath);
    
    // Check if file exists and is within allowed directories
    if (!fs.existsSync(resolvedPath)) {
      throw new Error('Audio file does not exist');
    }
    
    // Use FFprobe with spawn instead of execSync for better security
    const { execFileSync } = require('child_process');
    const result = execFileSync('ffprobe', [
      '-v', 'error',
      '-show_entries', 'format=duration',
      '-of', 'default=noprint_wrappers=1:nokey=1',
      resolvedPath
    ], { encoding: 'utf-8' });
    
    return parseFloat(result.trim());
  } catch (error) {
    console.error('Error getting audio duration:', error);
    // If we can't get the duration, return a default value
    return 60; // Default to 60 seconds
  }
}

module.exports = {
  generateVideo,
  createScriptedVideo,
  ensureDirectoriesExist,
};