#!/usr/bin/env tsx
/**
 * Comprehensive Security Audit Testing Suite
 * Validates security fixes and identifies remaining vulnerabilities
 */

import dotenv from 'dotenv';
import { spawn } from 'child_process';
import fs from 'fs';
import path from 'path';
import crypto from 'crypto';

// Load environment variables
dotenv.config();

interface SecurityTestResult {
  testName: string;
  category: 'critical' | 'high' | 'medium' | 'low';
  passed: boolean;
  duration: number;
  error?: string;
  details?: any;
  recommendation?: string;
}

interface VulnerabilityReport {
  severity: 'critical' | 'high' | 'medium' | 'low';
  category: string;
  description: string;
  location: string;
  impact: string;
  remediation: string;
  status: 'open' | 'fixed' | 'mitigated';
}

class SecurityAuditTester {
  private results: SecurityTestResult[] = [];
  private vulnerabilities: VulnerabilityReport[] = [];
  private testStartTime: Date = new Date();

  /**
   * Run comprehensive security audit tests
   */
  async runSecurityAudit(): Promise<void> {
    console.log('🔒 Starting Comprehensive Security Audit Tests\n');
    console.log('=' .repeat(70));

    try {
      // Test 1: Code Security Vulnerabilities
      await this.testCodeSecurity();

      // Test 2: Database Security
      await this.testDatabaseSecurity();

      // Test 3: API Security
      await this.testAPISecurity();

      // Test 4: Authentication & Authorization
      await this.testAuthSecurity();

      // Test 5: Input Validation & Sanitization
      await this.testInputValidation();

      // Test 6: File Upload Security
      await this.testFileUploadSecurity();

      // Test 7: Session Management
      await this.testSessionSecurity();

      // Test 8: CORS & CSRF Protection
      await this.testCORSCSRFSecurity();

      // Test 9: Environment & Configuration Security
      await this.testEnvironmentSecurity();

      // Test 10: AI Service Security
      await this.testAIServiceSecurity();

      // Generate comprehensive security report
      this.generateSecurityReport();

    } catch (error) {
      console.error('❌ Security audit failed:', error);
      throw error;
    }
  }

  /**
   * Test code security vulnerabilities
   */
  private async testCodeSecurity(): Promise<void> {
    await this.runTest('Code Security Scan', 'high', async () => {
      const vulnerabilities = [];

      // Test for hardcoded secrets
      const secretsFound = await this.scanForHardcodedSecrets();
      if (secretsFound.length > 0) {
        vulnerabilities.push({
          type: 'hardcoded_secrets',
          count: secretsFound.length,
          locations: secretsFound
        });
      }

      // Test for SQL injection patterns
      const sqlInjectionRisks = await this.scanForSQLInjection();
      if (sqlInjectionRisks.length > 0) {
        vulnerabilities.push({
          type: 'sql_injection_risk',
          count: sqlInjectionRisks.length,
          locations: sqlInjectionRisks
        });
      }

      // Test for XSS vulnerabilities
      const xssRisks = await this.scanForXSSVulnerabilities();
      if (xssRisks.length > 0) {
        vulnerabilities.push({
          type: 'xss_risk',
          count: xssRisks.length,
          locations: xssRisks
        });
      }

      return {
        vulnerabilitiesFound: vulnerabilities.length,
        details: vulnerabilities
      };
    });
  }

  /**
   * Test database security
   */
  private async testDatabaseSecurity(): Promise<void> {
    await this.runTest('Database Security', 'critical', async () => {
      const issues = [];

      // Test database connection security
      const dbUrl = process.env.DATABASE_URL;
      if (!dbUrl) {
        issues.push('DATABASE_URL not configured');
      } else {
        if (!dbUrl.includes('sslmode=require')) {
          issues.push('SSL not enforced in database connection');
        }
      }

      // Test for overprivileged database user
      try {
        // This would require actual database connection
        // For now, we'll simulate the test
        const hasAdminPrivileges = false; // Would check actual privileges
        if (hasAdminPrivileges) {
          issues.push('Database user has excessive privileges');
        }
      } catch (error) {
        issues.push('Unable to verify database privileges');
      }

      return {
        securityIssues: issues.length,
        details: issues
      };
    });
  }

  /**
   * Test API security
   */
  private async testAPISecurity(): Promise<void> {
    await this.runTest('API Security', 'high', async () => {
      const issues = [];

      // Test for missing authentication
      const unprotectedEndpoints = await this.scanForUnprotectedEndpoints();
      if (unprotectedEndpoints.length > 0) {
        issues.push({
          type: 'unprotected_endpoints',
          count: unprotectedEndpoints.length,
          endpoints: unprotectedEndpoints
        });
      }

      // Test for missing rate limiting
      const unlimitedEndpoints = await this.scanForUnlimitedEndpoints();
      if (unlimitedEndpoints.length > 0) {
        issues.push({
          type: 'missing_rate_limiting',
          count: unlimitedEndpoints.length,
          endpoints: unlimitedEndpoints
        });
      }

      return {
        apiSecurityIssues: issues.length,
        details: issues
      };
    });
  }

  /**
   * Test authentication and authorization
   */
  private async testAuthSecurity(): Promise<void> {
    await this.runTest('Authentication & Authorization', 'critical', async () => {
      const issues = [];

      // Test session configuration
      const sessionConfig = this.analyzeSessionConfig();
      if (sessionConfig.issues.length > 0) {
        issues.push({
          type: 'session_security',
          issues: sessionConfig.issues
        });
      }

      // Test JWT security (if used)
      const jwtConfig = this.analyzeJWTConfig();
      if (jwtConfig.issues.length > 0) {
        issues.push({
          type: 'jwt_security',
          issues: jwtConfig.issues
        });
      }

      return {
        authSecurityIssues: issues.length,
        details: issues
      };
    });
  }

  /**
   * Test input validation and sanitization
   */
  private async testInputValidation(): Promise<void> {
    await this.runTest('Input Validation', 'high', async () => {
      const issues = [];

      // Test for missing input validation
      const unvalidatedInputs = await this.scanForUnvalidatedInputs();
      if (unvalidatedInputs.length > 0) {
        issues.push({
          type: 'missing_validation',
          count: unvalidatedInputs.length,
          locations: unvalidatedInputs
        });
      }

      // Test for prompt injection vulnerabilities
      const promptInjectionRisks = await this.scanForPromptInjection();
      if (promptInjectionRisks.length > 0) {
        issues.push({
          type: 'prompt_injection_risk',
          count: promptInjectionRisks.length,
          locations: promptInjectionRisks
        });
      }

      return {
        inputValidationIssues: issues.length,
        details: issues
      };
    });
  }

  /**
   * Test file upload security
   */
  private async testFileUploadSecurity(): Promise<void> {
    await this.runTest('File Upload Security', 'high', async () => {
      const issues = [];

      // Test for missing file type validation
      const uploadEndpoints = await this.scanForUploadEndpoints();
      const unsecureUploads = uploadEndpoints.filter(endpoint => 
        !endpoint.hasTypeValidation || !endpoint.hasSizeLimit
      );

      if (unsecureUploads.length > 0) {
        issues.push({
          type: 'insecure_uploads',
          count: unsecureUploads.length,
          endpoints: unsecureUploads
        });
      }

      // Test for path traversal vulnerabilities
      const pathTraversalRisks = await this.scanForPathTraversal();
      if (pathTraversalRisks.length > 0) {
        issues.push({
          type: 'path_traversal_risk',
          count: pathTraversalRisks.length,
          locations: pathTraversalRisks
        });
      }

      return {
        fileUploadIssues: issues.length,
        details: issues
      };
    });
  }

  /**
   * Test session management security
   */
  private async testSessionSecurity(): Promise<void> {
    await this.runTest('Session Management', 'medium', async () => {
      const sessionConfig = this.analyzeSessionConfig();
      
      return {
        sessionSecure: sessionConfig.issues.length === 0,
        issues: sessionConfig.issues,
        recommendations: sessionConfig.recommendations
      };
    });
  }

  /**
   * Test CORS and CSRF protection
   */
  private async testCORSCSRFSecurity(): Promise<void> {
    await this.runTest('CORS & CSRF Protection', 'high', async () => {
      const issues = [];

      // Test CORS configuration
      const corsConfig = await this.analyzeCORSConfig();
      if (corsConfig.isPermissive) {
        issues.push('CORS configuration too permissive');
      }

      // Test CSRF protection
      const csrfProtection = await this.analyzeCSRFProtection();
      if (!csrfProtection.enabled) {
        issues.push('CSRF protection not implemented');
      }

      return {
        corsCSRFIssues: issues.length,
        details: issues
      };
    });
  }

  /**
   * Test environment and configuration security
   */
  private async testEnvironmentSecurity(): Promise<void> {
    await this.runTest('Environment Security', 'medium', async () => {
      const issues = [];

      // Test for debug mode in production
      if (process.env.NODE_ENV === 'production' && process.env.DEBUG === 'true') {
        issues.push('Debug mode enabled in production');
      }

      // Test for missing security headers
      const securityHeaders = await this.checkSecurityHeaders();
      if (securityHeaders.missing.length > 0) {
        issues.push({
          type: 'missing_security_headers',
          headers: securityHeaders.missing
        });
      }

      // Test environment variable security
      const envSecurity = this.analyzeEnvironmentVariables();
      if (envSecurity.issues.length > 0) {
        issues.push({
          type: 'environment_security',
          issues: envSecurity.issues
        });
      }

      return {
        environmentIssues: issues.length,
        details: issues
      };
    });
  }

  /**
   * Test AI service security
   */
  private async testAIServiceSecurity(): Promise<void> {
    await this.runTest('AI Service Security', 'medium', async () => {
      const issues = [];

      // Test for prompt injection protection
      const promptProtection = await this.analyzePromptProtection();
      if (!promptProtection.enabled) {
        issues.push('Prompt injection protection not implemented');
      }

      // Test AI service authentication
      const aiAuthConfig = await this.analyzeAIAuthentication();
      if (aiAuthConfig.issues.length > 0) {
        issues.push({
          type: 'ai_auth_issues',
          issues: aiAuthConfig.issues
        });
      }

      return {
        aiSecurityIssues: issues.length,
        details: issues
      };
    });
  }

  /**
   * Scan for hardcoded secrets
   */
  private async scanForHardcodedSecrets(): Promise<string[]> {
    const secretPatterns = [
      /api[_-]?key\s*[:=]\s*['"][^'"]+['"]/gi,
      /secret[_-]?key\s*[:=]\s*['"][^'"]+['"]/gi,
      /password\s*[:=]\s*['"][^'"]+['"]/gi,
      /token\s*[:=]\s*['"][^'"]+['"]/gi,
      /sk-[a-zA-Z0-9]{32,}/g, // OpenAI API keys
      /xoxb-[a-zA-Z0-9-]+/g, // Slack tokens
    ];

    const foundSecrets: string[] = [];
    const filesToScan = await this.getSourceFiles();

    for (const file of filesToScan) {
      try {
        const content = fs.readFileSync(file, 'utf8');
        secretPatterns.forEach(pattern => {
          const matches = content.match(pattern);
          if (matches) {
            foundSecrets.push(`${file}: ${matches.length} potential secrets`);
          }
        });
      } catch (error) {
        // Skip files that can't be read
      }
    }

    return foundSecrets;
  }

  /**
   * Scan for SQL injection risks
   */
  private async scanForSQLInjection(): Promise<string[]> {
    const sqlInjectionPatterns = [
      /\$\{[^}]*\}/g, // Template literals in SQL
      /\+\s*['"][^'"]*['"]\s*\+/g, // String concatenation
      /execute\s*\(\s*`[^`]*\$\{/gi, // Template literals in execute
    ];

    const risks: string[] = [];
    const filesToScan = await this.getSourceFiles();

    for (const file of filesToScan) {
      try {
        const content = fs.readFileSync(file, 'utf8');
        sqlInjectionPatterns.forEach(pattern => {
          const matches = content.match(pattern);
          if (matches) {
            risks.push(`${file}: ${matches.length} potential SQL injection risks`);
          }
        });
      } catch (error) {
        // Skip files that can't be read
      }
    }

    return risks;
  }

  /**
   * Scan for XSS vulnerabilities
   */
  private async scanForXSSVulnerabilities(): Promise<string[]> {
    const xssPatterns = [
      /innerHTML\s*=\s*[^;]+/gi,
      /document\.write\s*\(/gi,
      /eval\s*\(/gi,
      /dangerouslySetInnerHTML/gi,
    ];

    const risks: string[] = [];
    const filesToScan = await this.getSourceFiles();

    for (const file of filesToScan) {
      try {
        const content = fs.readFileSync(file, 'utf8');
        xssPatterns.forEach(pattern => {
          const matches = content.match(pattern);
          if (matches) {
            risks.push(`${file}: ${matches.length} potential XSS risks`);
          }
        });
      } catch (error) {
        // Skip files that can't be read
      }
    }

    return risks;
  }

  /**
   * Get source files to scan
   */
  private async getSourceFiles(): Promise<string[]> {
    const extensions = ['.ts', '.js', '.tsx', '.jsx'];
    const files: string[] = [];
    
    const scanDirectory = (dir: string) => {
      try {
        const items = fs.readdirSync(dir);
        items.forEach(item => {
          const fullPath = path.join(dir, item);
          const stat = fs.statSync(fullPath);
          
          if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
            scanDirectory(fullPath);
          } else if (stat.isFile() && extensions.some(ext => item.endsWith(ext))) {
            files.push(fullPath);
          }
        });
      } catch (error) {
        // Skip directories that can't be read
      }
    };

    scanDirectory(process.cwd());
    return files;
  }

  /**
   * Analyze session configuration
   */
  private analyzeSessionConfig(): { issues: string[]; recommendations: string[] } {
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Check session secret
    const sessionSecret = process.env.SESSION_SECRET;
    if (!sessionSecret) {
      issues.push('SESSION_SECRET not configured');
    } else if (sessionSecret.length < 32) {
      issues.push('SESSION_SECRET too short (minimum 32 characters)');
    }

    // Check secure cookie settings
    if (process.env.NODE_ENV === 'production') {
      recommendations.push('Ensure secure cookies are enabled in production');
      recommendations.push('Set httpOnly flag on session cookies');
      recommendations.push('Use sameSite=strict for CSRF protection');
    }

    return { issues, recommendations };
  }

  /**
   * Analyze JWT configuration
   */
  private analyzeJWTConfig(): { issues: string[] } {
    const issues: string[] = [];

    const jwtSecret = process.env.JWT_SECRET;
    if (jwtSecret && jwtSecret.length < 32) {
      issues.push('JWT_SECRET too short (minimum 32 characters)');
    }

    return { issues };
  }

  /**
   * Additional helper methods for security testing
   */
  private async scanForUnprotectedEndpoints(): Promise<string[]> {
    // Simplified implementation - would scan actual route files
    return [];
  }

  private async scanForUnlimitedEndpoints(): Promise<string[]> {
    // Simplified implementation - would check for rate limiting
    return [];
  }

  private async scanForUnvalidatedInputs(): Promise<string[]> {
    // Simplified implementation - would scan for validation middleware
    return [];
  }

  private async scanForPromptInjection(): Promise<string[]> {
    // Simplified implementation - would scan AI prompt handling
    return [];
  }

  private async scanForUploadEndpoints(): Promise<any[]> {
    // Simplified implementation - would scan upload handlers
    return [];
  }

  private async scanForPathTraversal(): Promise<string[]> {
    // Simplified implementation - would scan file path handling
    return [];
  }

  private async analyzeCORSConfig(): Promise<{ isPermissive: boolean }> {
    // Simplified implementation - would check CORS settings
    return { isPermissive: false };
  }

  private async analyzeCSRFProtection(): Promise<{ enabled: boolean }> {
    // Simplified implementation - would check CSRF middleware
    return { enabled: false };
  }

  private async checkSecurityHeaders(): Promise<{ missing: string[] }> {
    // Simplified implementation - would check security headers
    return { missing: [] };
  }

  private analyzeEnvironmentVariables(): { issues: string[] } {
    const issues: string[] = [];
    
    // Check for development settings in production
    if (process.env.NODE_ENV === 'production') {
      if (process.env.DEBUG === 'true') {
        issues.push('Debug mode enabled in production');
      }
    }

    return { issues };
  }

  private async analyzePromptProtection(): Promise<{ enabled: boolean }> {
    // Simplified implementation - would check prompt sanitization
    return { enabled: false };
  }

  private async analyzeAIAuthentication(): Promise<{ issues: string[] }> {
    // Simplified implementation - would check AI service auth
    return { issues: [] };
  }

  /**
   * Run individual security test
   */
  private async runTest(
    name: string, 
    category: SecurityTestResult['category'], 
    testFn: () => Promise<any>
  ): Promise<void> {
    const startTime = Date.now();
    
    try {
      console.log(`🔍 Testing: ${name}...`);
      const result = await testFn();
      const duration = Date.now() - startTime;
      
      this.results.push({
        testName: name,
        category,
        passed: true,
        duration,
        details: result
      });
      
      console.log(`✅ ${name} - ${duration}ms`);
      
    } catch (error) {
      const duration = Date.now() - startTime;
      
      this.results.push({
        testName: name,
        category,
        passed: false,
        duration,
        error: error instanceof Error ? error.message : String(error)
      });
      
      console.log(`❌ ${name} - ${duration}ms - ${error}`);
    }
  }

  /**
   * Generate comprehensive security report
   */
  private generateSecurityReport(): void {
    console.log('\n' + '='.repeat(70));
    console.log('🔒 COMPREHENSIVE SECURITY AUDIT REPORT');
    console.log('='.repeat(70));

    const totalTests = this.results.length;
    const passedTests = this.results.filter(r => r.passed).length;
    const failedTests = totalTests - passedTests;
    const totalDuration = this.results.reduce((sum, r) => sum + r.duration, 0);

    // Calculate security score
    const criticalFailed = this.results.filter(r => !r.passed && r.category === 'critical').length;
    const highFailed = this.results.filter(r => !r.passed && r.category === 'high').length;
    const mediumFailed = this.results.filter(r => !r.passed && r.category === 'medium').length;
    const lowFailed = this.results.filter(r => !r.passed && r.category === 'low').length;

    const securityScore = Math.max(0, 100 - (criticalFailed * 25) - (highFailed * 15) - (mediumFailed * 8) - (lowFailed * 3));

    console.log(`\n📊 SECURITY AUDIT SUMMARY:`);
    console.log(`   Total Tests: ${totalTests}`);
    console.log(`   Passed: ${passedTests} ✅`);
    console.log(`   Failed: ${failedTests} ❌`);
    console.log(`   Security Score: ${securityScore}/100 ${securityScore >= 80 ? '✅' : securityScore >= 60 ? '⚠️' : '❌'}`);
    console.log(`   Total Duration: ${(totalDuration / 1000).toFixed(1)}s`);

    // Categorize failures
    console.log(`\n🚨 FAILURES BY SEVERITY:`);
    console.log(`   Critical: ${criticalFailed} ❌`);
    console.log(`   High: ${highFailed} ⚠️`);
    console.log(`   Medium: ${mediumFailed} 🔶`);
    console.log(`   Low: ${lowFailed} 🔵`);

    if (failedTests > 0) {
      console.log(`\n❌ FAILED SECURITY TESTS:`);
      this.results.filter(r => !r.passed).forEach(result => {
        console.log(`   ${result.category.toUpperCase()}: ${result.testName} - ${result.error}`);
      });
    }

    console.log(`\n✅ PASSED SECURITY TESTS:`);
    this.results.filter(r => r.passed).forEach(result => {
      console.log(`   ${result.category.toUpperCase()}: ${result.testName} (${result.duration}ms)`);
    });

    // Security recommendations
    console.log(`\n🛡️ SECURITY RECOMMENDATIONS:`);
    if (securityScore < 80) {
      console.log('   🚨 IMMEDIATE ACTION REQUIRED - Security score below acceptable threshold');
      console.log('   🔒 Address all Critical and High severity issues before deployment');
      console.log('   📋 Implement comprehensive security monitoring');
      console.log('   🧪 Conduct penetration testing');
    } else {
      console.log('   ✅ Security posture is acceptable for deployment');
      console.log('   🔄 Continue monitoring and regular security assessments');
      console.log('   📈 Consider implementing additional security enhancements');
    }

    console.log('\n' + '='.repeat(70));
    
    if (securityScore >= 80) {
      console.log('🎉 SECURITY AUDIT PASSED! Application ready for security review.');
    } else {
      console.log('⚠️  SECURITY AUDIT FAILED! Address issues before deployment.');
    }
  }
}

// CLI interface
async function main() {
  const tester = new SecurityAuditTester();
  await tester.runSecurityAudit();
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(error => {
    console.error('Security audit failed:', error);
    process.exit(1);
  });
}

export { SecurityAuditTester };
