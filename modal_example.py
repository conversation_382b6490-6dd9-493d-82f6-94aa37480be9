
try:
    import modal
    print("Modal imported successfully!")
except ImportError:
    import subprocess
    import sys
    import os
    
    print("Modal not found. Installing...")
    
    # Try multiple installation methods
    install_commands = [
        [sys.executable, "-m", "pip", "install", "modal", "--user"],
        [sys.executable, "-m", "pip", "install", "modal"],
        ["pip3", "install", "modal", "--user"],
        ["pip", "install", "modal", "--user"]
    ]
    
    installed = False
    for cmd in install_commands:
        try:
            subprocess.check_call(cmd, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            print(f"Modal installed using: {' '.join(cmd)}")
            installed = True
            break
        except (subprocess.CalledProcessError, FileNotFoundError):
            continue
    
    if not installed:
        print("Could not install Modal automatically.")
        print("Please run: pip install modal")
        sys.exit(1)
    
    # Try importing again
    try:
        import modal
        print("Modal imported after installation!")
    except ImportError:
        print("Modal import still failed after installation.")
        print("Environment may need to be restarted.")
        sys.exit(1)

# Create a Modal app
app = modal.App("my-replit-app")

# Define a simple function
@app.function()
def hello_world(name: str = "World"):
    return f"Hello, {name}!"

# Define a function with dependencies
@app.function(
    image=modal.Image.debian_slim().pip_install("requests")
)
def fetch_data(url: str):
    import requests
    response = requests.get(url)
    return response.json()

if __name__ == "__main__":
    with app.run():
        result = hello_world.remote("Modal")
        print(result)
