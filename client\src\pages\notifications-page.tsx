import NotificationsList from '../components/notifications/NotificationsList';
import { NotificationsProvider } from '../hooks/use-notifications';
import { PageHeader } from '../components/PageHeader';
import { Bell } from 'lucide-react';

export default function NotificationsPage() {
  return (
    <NotificationsProvider>
      <div className="container py-6 max-w-5xl">
        <PageHeader
          title="Notifications"
          icon={<Bell className="h-6 w-6" />}
          description="Manage your notifications and preferences"
        />
        <div className="mt-6">
          <NotificationsList showViewAll={false} />
        </div>
      </div>
    </NotificationsProvider>
  );
}
