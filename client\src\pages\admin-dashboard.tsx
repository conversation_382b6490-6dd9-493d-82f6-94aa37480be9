import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { 
  CreditCard, Users, ShoppingCart, DollarSign, Settings, Rss, 
  Youtube, TrendingUp, HardDrive, Zap, Star, BarChart4, Server
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { UserStats } from "@/types";
import { Separator } from "@/components/ui/separator";
import { Progress } from "@/components/ui/progress";
import { useAuth } from "@/hooks/use-auth";

// Chart components
import {
  Area,
  AreaChart,
  Bar,
  BarChart,
  CartesianGrid,
  Legend,
  Line,
  LineC<PERSON>,
  Responsive<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "recharts";

// Type definition for admin stats
interface AdminStats {
  totalUsers: number;
  activeSubscriptions: number;
  monthlyRevenue: number;
  totalRevenue: number;
  aiCreditsUsed: number;
  storageUsed: number;
  popularPlans: { name: string; count: number }[];
  revenueByMonth: { month: string; revenue: number }[];
  usersByMonth: { month: string; count: number }[];
  resourceUsage: { resource: string; usage: number; limit: number }[];
}

// Mock payment plan data
const PAYMENT_PLANS = [
  { id: "starter", name: "Starter", price: "$49/month", color: "bg-blue-100 text-blue-800" },
  { id: "pro", name: "Pro", price: "$129/month", color: "bg-purple-100 text-purple-800" },
  { id: "business", name: "Business", price: "$299/month", color: "bg-indigo-100 text-indigo-800" },
  { id: "enterprise", name: "Enterprise", price: "Custom", color: "bg-slate-100 text-slate-800" },
];

// Mock integration data
const INTEGRATIONS = [
  { id: "gemini", name: "Google Gemini", type: "ai", connected: true, icon: <Star className="h-5 w-5" /> },
  { id: "openai", name: "OpenAI", type: "ai", connected: true, icon: <Zap className="h-5 w-5" /> },
  { id: "elevenlabs", name: "ElevenLabs", type: "voice", connected: true, icon: <Rss className="h-5 w-5" /> },
  { id: "stripe", name: "Stripe", type: "payment", connected: true, icon: <CreditCard className="h-5 w-5" /> },
  { id: "youtube", name: "YouTube", type: "publishing", connected: false, icon: <Youtube className="h-5 w-5" /> },
  { id: "udemy", name: "Udemy", type: "marketplace", connected: true, icon: <TrendingUp className="h-5 w-5" /> },
  { id: "kajabi", name: "Kajabi", type: "publishing", connected: false, icon: <BarChart4 className="h-5 w-5" /> },
];

export default function AdminDashboard() {
  const { toast } = useToast();
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState("overview");

  // Fetch admin stats
  const { data: adminStats, isLoading } = useQuery({
    queryKey: ['/api/admin/stats'],
    queryFn: async () => {
      try {
        const res = await apiRequest('GET', '/api/admin/stats');
        return await res.json();
      } catch (error) {
        console.error("Error fetching admin stats:", error);
        // Return mock data for demonstration
        return {
          totalUsers: 1250,
          activeSubscriptions: 845,
          monthlyRevenue: 78500,
          totalRevenue: 950000,
          aiCreditsUsed: 3500000,
          storageUsed: 2450,
          popularPlans: [
            { name: "Pro", count: 456 },
            { name: "Business", count: 248 },
            { name: "Starter", count: 141 },
            { name: "Enterprise", count: 35 }
          ],
          revenueByMonth: [
            { month: "Jan", revenue: 65000 },
            { month: "Feb", revenue: 68000 },
            { month: "Mar", revenue: 72000 },
            { month: "Apr", revenue: 75000 },
            { month: "May", revenue: 78500 },
            { month: "Jun", revenue: 0 },
          ],
          usersByMonth: [
            { month: "Jan", count: 950 },
            { month: "Feb", count: 1020 },
            { month: "Mar", count: 1100 },
            { month: "Apr", count: 1180 },
            { month: "May", count: 1250 },
            { month: "Jun", count: 0 },
          ],
          resourceUsage: [
            { resource: "CPU", usage: 45, limit: 100 },
            { resource: "RAM", usage: 60, limit: 100 },
            { resource: "Storage", usage: 75, limit: 100 },
            { resource: "Bandwidth", usage: 30, limit: 100 },
          ]
        } as AdminStats;
      }
    },
    enabled: user?.role === "admin"
  });

  // Format currency for display
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      maximumFractionDigits: 0
    }).format(amount);
  };

  // Handle API connection toggle
  const handleToggleIntegration = (integration: any) => {
    toast({
      title: integration.connected ? "Disconnecting" : "Connecting",
      description: `${integration.connected ? "Disconnecting from" : "Connecting to"} ${integration.name}...`,
    });
  };

  // Handle plan edit
  const handleEditPlan = (plan: any) => {
    toast({
      title: "Edit Plan",
      description: `Editing ${plan.name} plan settings`,
    });
  };

  if (user?.role !== "admin") {
    return (
      <div className="flex items-center justify-center h-[80vh]">
        <Card className="w-[450px]">
          <CardHeader>
            <CardTitle className="text-xl">Admin Access Required</CardTitle>
            <CardDescription>
              You need administrator privileges to view this dashboard.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">
              Please contact your system administrator if you believe you should have access to this page.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6 bg-slate-50 min-h-screen">
      <div className="flex flex-col gap-2">
        <h1 className="text-3xl font-bold tracking-tight">Admin Dashboard</h1>
        <p className="text-muted-foreground">
          Manage platform settings, subscriptions, and monitor system performance.
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid grid-cols-4 lg:w-[600px]">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="subscriptions">Subscriptions</TabsTrigger>
          <TabsTrigger value="integrations">Integrations</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-4">
          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Total Users */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Users</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{adminStats?.totalUsers.toLocaleString()}</div>
                <p className="text-xs text-muted-foreground">
                  +{Math.floor(adminStats?.totalUsers * 0.05)} from last month
                </p>
              </CardContent>
            </Card>

            {/* Active Subscriptions */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Active Subscriptions</CardTitle>
                <ShoppingCart className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{adminStats?.activeSubscriptions.toLocaleString()}</div>
                <p className="text-xs text-muted-foreground">
                  {(adminStats?.activeSubscriptions / adminStats?.totalUsers * 100).toFixed(1)}% conversion rate
                </p>
              </CardContent>
            </Card>

            {/* Monthly Revenue */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Monthly Revenue</CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatCurrency(adminStats?.monthlyRevenue || 0)}</div>
                <p className="text-xs text-muted-foreground">
                  +{((adminStats?.monthlyRevenue || 0) - (adminStats?.revenueByMonth?.[4]?.revenue || 0)) / (adminStats?.revenueByMonth?.[4]?.revenue || 1) * 100}% from last month
                </p>
              </CardContent>
            </Card>

            {/* Total Revenue */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
                <CreditCard className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatCurrency(adminStats?.totalRevenue || 0)}</div>
                <p className="text-xs text-muted-foreground">
                  Lifetime value
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Revenue Chart */}
          <Card className="col-span-3">
            <CardHeader>
              <CardTitle>Revenue Growth</CardTitle>
              <CardDescription>
                Monthly revenue over the past 6 months
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart
                    data={adminStats?.revenueByMonth}
                    margin={{
                      top: 5,
                      right: 10,
                      left: 10,
                      bottom: 0,
                    }}
                  >
                    <defs>
                      <linearGradient id="colorRevenue" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor="#6366F1" stopOpacity={0.8} />
                        <stop offset="95%" stopColor="#6366F1" stopOpacity={0} />
                      </linearGradient>
                    </defs>
                    <XAxis dataKey="month" />
                    <YAxis
                      tickFormatter={(value) => `$${(value / 1000).toFixed(0)}k`}
                    />
                    <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
                    <Tooltip 
                      formatter={(value: number) => formatCurrency(value)}
                      labelFormatter={(label) => `Month: ${label}`}
                    />
                    <Area
                      type="monotone"
                      dataKey="revenue"
                      stroke="#6366F1"
                      fillOpacity={1}
                      fill="url(#colorRevenue)"
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          {/* Plan Distribution */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Plan Distribution</CardTitle>
                <CardDescription>
                  Breakdown of users by subscription plan
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={adminStats?.popularPlans}>
                      <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip />
                      <Bar dataKey="count" fill="#6366F1" radius={[4, 4, 0, 0]} />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            {/* User Growth */}
            <Card>
              <CardHeader>
                <CardTitle>User Growth</CardTitle>
                <CardDescription>
                  New registered users over time
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart data={adminStats?.usersByMonth}>
                      <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
                      <XAxis dataKey="month" />
                      <YAxis />
                      <Tooltip />
                      <Line type="monotone" dataKey="count" stroke="#6366F1" strokeWidth={2} />
                    </LineChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Subscriptions Tab */}
        <TabsContent value="subscriptions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Plan Management</CardTitle>
              <CardDescription>
                Configure subscription plans and pricing
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {PAYMENT_PLANS.map(plan => (
                <div key={plan.id} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className={`px-2 py-1 rounded text-sm font-medium ${plan.color}`}>
                        {plan.name}
                      </div>
                      <span className="font-medium">{plan.price}</span>
                    </div>
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => handleEditPlan(plan)}
                    >
                      <Settings className="h-4 w-4 mr-2" />
                      Configure
                    </Button>
                  </div>
                  <Separator className="my-4" />
                  <div className="text-sm">
                    {plan.id === "starter" && (
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <div className="font-medium">Features:</div>
                          <div className="text-muted-foreground">Basic AI course creation, 5 courses, Limited AI credits</div>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="font-medium">AI Credits:</div>
                          <div className="text-muted-foreground">100 / month</div>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="font-medium">Storage:</div>
                          <div className="text-muted-foreground">5 GB</div>
                        </div>
                      </div>
                    )}
                    {plan.id === "pro" && (
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <div className="font-medium">Features:</div>
                          <div className="text-muted-foreground">Advanced AI features, Unlimited courses, Priority support</div>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="font-medium">AI Credits:</div>
                          <div className="text-muted-foreground">500 / month</div>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="font-medium">Storage:</div>
                          <div className="text-muted-foreground">25 GB</div>
                        </div>
                      </div>
                    )}
                    {plan.id === "business" && (
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <div className="font-medium">Features:</div>
                          <div className="text-muted-foreground">Team collaboration, Advanced analytics, API access</div>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="font-medium">AI Credits:</div>
                          <div className="text-muted-foreground">1500 / month</div>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="font-medium">Storage:</div>
                          <div className="text-muted-foreground">100 GB</div>
                        </div>
                      </div>
                    )}
                    {plan.id === "enterprise" && (
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <div className="font-medium">Features:</div>
                          <div className="text-muted-foreground">Custom solutions, Unlimited AI credits, White-label option</div>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="font-medium">AI Credits:</div>
                          <div className="text-muted-foreground">Unlimited</div>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="font-medium">Storage:</div>
                          <div className="text-muted-foreground">Unlimited</div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Stripe Integration Settings</CardTitle>
              <CardDescription>
                Configure payment processor settings
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between border rounded-lg p-4">
                <div className="flex items-center gap-3">
                  <CreditCard className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <div className="font-medium">Stripe</div>
                    <div className="text-sm text-muted-foreground">Payment processor for subscriptions and one-time payments</div>
                  </div>
                </div>
                <Button variant="outline">
                  Configure
                </Button>
              </div>

              <div className="border rounded-lg p-4">
                <h3 className="font-medium mb-2">Webhooks</h3>
                <div className="text-sm text-muted-foreground mb-4">
                  Configure webhooks to handle payment events
                </div>
                <div className="rounded bg-muted p-3 font-mono text-sm">
                  https://courseai.yourdomain.com/api/webhook
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Integrations Tab */}
        <TabsContent value="integrations" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>AI Integrations</CardTitle>
                <CardDescription>
                  Configure AI providers for content generation
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {INTEGRATIONS.filter(i => i.type === 'ai').map(integration => (
                  <div key={integration.id} className="flex items-center justify-between border rounded-lg p-4">
                    <div className="flex items-center gap-3">
                      {integration.icon}
                      <div>
                        <div className="font-medium">{integration.name}</div>
                        <div className="text-xs text-muted-foreground">
                          {integration.connected ? (
                            <span className="text-green-600 flex items-center gap-1">
                              <span className="h-2 w-2 rounded-full bg-green-600"></span> Connected
                            </span>
                          ) : (
                            <span className="text-orange-600 flex items-center gap-1">
                              <span className="h-2 w-2 rounded-full bg-orange-600"></span> Not connected
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                    <Button
                      variant={integration.connected ? "outline" : "default"}
                      size="sm"
                      onClick={() => handleToggleIntegration(integration)}
                    >
                      {integration.connected ? "Configure" : "Connect"}
                    </Button>
                  </div>
                ))}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Publishing & Platforms</CardTitle>
                <CardDescription>
                  Connect to external platforms for content distribution
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {INTEGRATIONS.filter(i => i.type === 'publishing' || i.type === 'marketplace').map(integration => (
                  <div key={integration.id} className="flex items-center justify-between border rounded-lg p-4">
                    <div className="flex items-center gap-3">
                      {integration.icon}
                      <div>
                        <div className="font-medium">{integration.name}</div>
                        <div className="text-xs text-muted-foreground">
                          {integration.connected ? (
                            <span className="text-green-600 flex items-center gap-1">
                              <span className="h-2 w-2 rounded-full bg-green-600"></span> Connected
                            </span>
                          ) : (
                            <span className="text-orange-600 flex items-center gap-1">
                              <span className="h-2 w-2 rounded-full bg-orange-600"></span> Not connected
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                    <Button
                      variant={integration.connected ? "outline" : "default"}
                      size="sm"
                      onClick={() => handleToggleIntegration(integration)}
                    >
                      {integration.connected ? "Configure" : "Connect"}
                    </Button>
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>API Keys & Services</CardTitle>
              <CardDescription>
                Manage API keys for third-party services
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-4">
                  <div className="font-medium">API Keys</div>
                  <Button variant="outline" size="sm">Manage Keys</Button>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span>Google Gemini API Key</span>
                    <span className="font-mono bg-muted px-2 py-1 rounded">••••••••••••••••</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span>OpenAI API Key</span>
                    <span className="font-mono bg-muted px-2 py-1 rounded">••••••••••••••••</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span>ElevenLabs API Key</span>
                    <span className="font-mono bg-muted px-2 py-1 rounded">••••••••••••••••</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span>Stripe Secret Key</span>
                    <span className="font-mono bg-muted px-2 py-1 rounded">••••••••••••••••</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Performance Tab */}
        <TabsContent value="performance" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Resource Usage</CardTitle>
                <CardDescription>
                  Monitor system resource utilization
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {adminStats?.resourceUsage.map(resource => (
                  <div key={resource.resource} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">{resource.resource}</span>
                      <span className="text-sm text-muted-foreground">{resource.usage}% of {resource.limit}%</span>
                    </div>
                    <Progress value={resource.usage} className="h-2" />
                  </div>
                ))}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>AI API Usage</CardTitle>
                <CardDescription>
                  Monitor AI API usage and costs
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart 
                      data={[
                        { service: "Gemini", credits: 1500000 },
                        { service: "OpenAI", credits: 1200000 },
                        { service: "ElevenLabs", credits: 800000 }
                      ]}
                    >
                      <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
                      <XAxis dataKey="service" />
                      <YAxis 
                        tickFormatter={(value) => `${(value / 1000000).toFixed(1)}M`}
                      />
                      <Tooltip 
                        formatter={(value: number) => [`${(value).toLocaleString()} credits`, "Usage"]}
                      />
                      <Bar dataKey="credits" fill="#6366F1" radius={[4, 4, 0, 0]} />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Storage Analytics</CardTitle>
              <CardDescription>
                Monitor storage usage and optimize cloud resources
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-4">
                  <div className="font-medium">Storage Usage by Type</div>
                  <Button variant="outline" size="sm">Optimize</Button>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  {[
                    { type: "Videos", size: 1250, icon: <HardDrive className="h-5 w-5" /> },
                    { type: "Images", size: 750, icon: <Server className="h-5 w-5" /> },
                    { type: "Audio", size: 350, icon: <Rss className="h-5 w-5" /> },
                    { type: "Documents", size: 100, icon: <Zap className="h-5 w-5" /> }
                  ].map(item => (
                    <div key={item.type} className="flex flex-col items-center p-4 bg-muted rounded-lg">
                      {item.icon}
                      <div className="mt-2 font-medium">{item.type}</div>
                      <div className="text-muted-foreground">{item.size} GB</div>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}