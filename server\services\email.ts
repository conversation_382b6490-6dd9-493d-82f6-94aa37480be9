/**
 * Email Service
 * 
 * This is a simplified email service that simulates sending emails.
 * In a production environment, you would integrate with an actual email service like Sendgrid, AWS SES, etc.
 */

// For a real implementation, you would import a package like nodemailer
// import nodemailer from 'nodemailer';

type EmailOptions = {
  to: string;
  subject: string;
  html: string;
};

export class EmailService {
  /**
   * Send an email
   * Note: In this implementation, we're just logging the email to the console.
   * In a production environment, you would use a real email service.
   */
  static async sendEmail(options: EmailOptions): Promise<boolean> {
    try {
      // Log the email details to the console (simulating email sending)
      console.log('------- EMAIL SENT -------');
      console.log(`To: ${options.to}`);
      console.log(`Subject: ${options.subject}`);
      console.log(`Content: ${options.html}`);
      console.log('-------------------------');
      
      // In a real implementation, you would send the email using a service like:
      // await nodemailer.createTransport({...}).sendMail({
      //   from: '<EMAIL>',
      //   to: options.to,
      //   subject: options.subject,
      //   html: options.html
      // });
      
      return true;
    } catch (error) {
      console.error('Error sending email:', error);
      return false;
    }
  }

  /**
   * Send a password reset email
   */
  static async sendPasswordResetEmail(
    to: string,
    resetToken: string,
    appUrl: string = 'http://localhost:3000'
  ): Promise<boolean> {
    const resetUrl = `${appUrl}/auth?action=reset-password&token=${resetToken}`;
    
    const html = `
      <h1>Reset Your Password</h1>
      <p>You requested a password reset for your CourseAI account.</p>
      <p>Click the button below to reset your password. This link is valid for 1 hour.</p>
      <a href="${resetUrl}" style="background-color: #4F46E5; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 20px 0;">Reset Password</a>
      <p>If you didn't request this, please ignore this email.</p>
      <p>If the button doesn't work, copy and paste this URL into your browser:</p>
      <p>${resetUrl}</p>
    `;
    
    return this.sendEmail({
      to,
      subject: 'Reset Your CourseAI Password',
      html
    });
  }
}