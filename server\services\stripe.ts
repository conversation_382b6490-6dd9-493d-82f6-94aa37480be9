import Stripe from 'stripe';
import dotenv from 'dotenv';

dotenv.config();

// Initialize Strip<PERSON> with the secret key from environment variables
if (!process.env.STRIPE_SECRET_KEY) {
  throw new Error('STRIPE_SECRET_KEY environment variable is required');
}

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: '2023-10-16' as any, // Use compatible API version
});

// Helper function to normalize plan IDs
export function normalizePlanId(planId: string): string {
  // This would need to be updated with your actual plan IDs in Stripe
  // For now, we'll just ensure consistent formatting
  return planId.toLowerCase();
}

class StripeService {
  // Get checkout session information
  async getSessionInfo(sessionId: string) {
    try {
      // Handle mock sessions for development
      if (sessionId.startsWith('mock_session_')) {
        console.log('Returning mock session info for:', sessionId);
        
        // Extract additional data from URL query parameters in the session ID if they exist
        // This could include plan information that would have been passed via the success URL
        const urlParams = new URLSearchParams(sessionId.split('?')[1] || '');
        const plan = urlParams.get('plan') || 'starter';
        
        // Return a mock session object with the necessary properties
        return {
          id: sessionId,
          status: 'complete',
          customer: `cus_mock_${Date.now()}`,
          subscription: `sub_mock_${Date.now()}`,
          payment_intent: {
            id: `pi_mock_${Date.now()}`,
            client_secret: `pi_mock_secret_${Date.now()}`,
            status: 'succeeded'
          },
          metadata: { planId: plan }
        };
      }
      
      // For real sessions, retrieve from Stripe
      const session = await stripe.checkout.sessions.retrieve(sessionId, {
        expand: ['subscription', 'payment_intent'],
      });
      return session;
    } catch (error) {
      console.error('Error retrieving checkout session:', error);
      throw error;
    }
  }

  async createCustomerPortalSession({
    customerId,
    returnUrl,
  }: {
    customerId: string;
    returnUrl: string;
  }) {
    try {
      const session = await stripe.billingPortal.sessions.create({
        customer: customerId,
        return_url: returnUrl,
      });
      return session.url;
    } catch (error) {
      console.error('Error creating portal session:', error);
      throw error;
    }
  }

  async handleWebhookEvent(
    event: Stripe.Event,
    handleCheckoutSuccess: (session: Stripe.Checkout.Session) => Promise<void>,
    handleInvoicePaid: (invoice: Stripe.Invoice) => Promise<void>
  ) {
    try {
      switch (event.type) {
        case 'checkout.session.completed':
          await handleCheckoutSuccess(event.data.object as Stripe.Checkout.Session);
          break;
        case 'invoice.paid':
          await handleInvoicePaid(event.data.object as Stripe.Invoice);
          break;
        // Add more event types as needed
      }
    } catch (error) {
      console.error('Error handling webhook event:', error);
      throw error;
    }
  }

  async getAvailablePlans() {
    try {
      // Get all active prices from Stripe
      const prices = await stripe.prices.list({
        active: true,
        expand: ['data.product'],
      });
      
      // Format and return the plans
      return prices.data.map(price => {
        const product = price.product as Stripe.Product;
        const interval = price.recurring?.interval || 'month';
        
        return {
          id: price.id,
          name: product.name,
          description: product.description,
          price: price.unit_amount ? price.unit_amount / 100 : 0,
          currency: price.currency,
          interval,
          metadata: product.metadata,
        };
      });
    } catch (error) {
      console.error('Error fetching plans:', error);
      throw error;
    }
  }

  async createSetupIntent(customerId?: string) {
    try {
      const setupIntent = await stripe.setupIntents.create({
        ...(customerId && { customer: customerId }),
        payment_method_types: ['card'],
      });
      
      return {
        clientSecret: setupIntent.client_secret,
      };
    } catch (error) {
      console.error('Error creating setup intent:', error);
      throw error;
    }
  }

  async getOrCreateCustomer(email: string, name?: string) {
    try {
      // Check if a customer with this email already exists
      const customers = await stripe.customers.list({ email });
      
      if (customers.data.length > 0) {
        // Return the first customer with matching email
        return customers.data[0];
      }
      
      // Create a new customer
      const customer = await stripe.customers.create({
        email,
        name,
      });
      
      return customer;
    } catch (error) {
      console.error('Error creating customer:', error);
      throw error;
    }
  }

  async attachPaymentMethodToCustomer(customerId: string, paymentMethodId: string) {
    try {
      // Attach the payment method to the customer
      await stripe.paymentMethods.attach(paymentMethodId, {
        customer: customerId,
      });
      
      // Set it as the default payment method
      await stripe.customers.update(customerId, {
        invoice_settings: {
          default_payment_method: paymentMethodId,
        },
      });
      
      return true;
    } catch (error) {
      console.error('Error attaching payment method:', error);
      throw error;
    }
  }

  async getCustomerPaymentMethods(customerId: string) {
    try {
      // Get all payment methods for the customer
      const paymentMethods = await stripe.paymentMethods.list({
        customer: customerId,
        type: 'card',
      });
      
      // Get the customer to determine which payment method is default
      const customer = await stripe.customers.retrieve(customerId);
      const defaultPaymentMethodId = typeof customer !== 'string' ? 
        customer.invoice_settings.default_payment_method : 
        null;
      
      // Add isDefault flag to payment methods
      const methodsWithDefault = paymentMethods.data.map(method => ({
        ...method,
        isDefault: method.id === defaultPaymentMethodId,
      }));
      
      return methodsWithDefault;
    } catch (error) {
      console.error('Error fetching payment methods:', error);
      throw error;
    }
  }

  // Create a subscription for a customer
  async createSubscription(customerId: string, priceId: string) {
    try {
      // Normalize the price ID
      const normalizedPriceId = normalizePlanId(priceId);
      
      // Create a subscription with default settings
      const subscription = await stripe.subscriptions.create({
        customer: customerId,
        items: [{ price: normalizedPriceId }],
        payment_behavior: 'default_incomplete',
        expand: ['latest_invoice.payment_intent'],
      });
      
      return {
        subscriptionId: subscription.id,
        clientSecret: subscription.latest_invoice?.payment_intent?.client_secret,
      };
    } catch (error) {
      console.error('Error creating subscription:', error);
      throw error;
    }
  }

  // Cancel a subscription
  async cancelSubscription(subscriptionId: string) {
    try {
      // Cancel at period end to allow the customer to continue using the service
      // until the end of the current billing period
      const subscription = await stripe.subscriptions.update(subscriptionId, {
        cancel_at_period_end: true,
      });
      
      return subscription;
    } catch (error) {
      console.error('Error canceling subscription:', error);
      throw error;
    }
  }

  // Get customer's subscription details
  async getSubscription(subscriptionId: string) {
    try {
      const subscription = await stripe.subscriptions.retrieve(subscriptionId);
      return subscription;
    } catch (error) {
      console.error('Error retrieving subscription:', error);
      throw error;
    }
  }

  // Get customer's invoices
  async getCustomerInvoices(customerId: string) {
    try {
      const invoices = await stripe.invoices.list({
        customer: customerId,
        limit: 10,
      });
      
      return invoices.data;
    } catch (error) {
      console.error('Error fetching invoices:', error);
      throw error;
    }
  }

  // Create a payment intent for embedded checkout
  async createPaymentIntent({
    planId,
    billingInterval,
    user,
    customerId
  }: {
    planId: string;
    billingInterval?: 'monthly' | 'yearly';
    user?: any;
    customerId?: string;
  }) {
    try {
      // Find a customer for the user or create one
      let customerObj;
      
      if (customerId) {
        // Use provided customer ID
        customerObj = { id: customerId };
      } else if (user) {
        // Find or create customer based on user
        customerObj = await this.getOrCreateCustomer(user.email, user.username || user.name);
      } else {
        throw new Error("Either customerId or user must be provided");
      }
      
      // Calculate amount based on plan and billing interval
      let amount = 0;
      const interval = billingInterval || 'monthly';
      if (planId === 'starter') {
        amount = interval === 'yearly' ? 19000 : 1900; // $190/yr or $19/mo
      } else if (planId === 'pro') {
        amount = interval === 'yearly' ? 49000 : 4900; // $490/yr or $49/mo
      } else if (planId === 'business') {
        amount = interval === 'yearly' ? 99000 : 9900; // $990/yr or $99/mo
      } else if (planId === 'enterprise') {
        amount = interval === 'yearly' ? 299000 : 29900; // $2990/yr or $299/mo
      } else {
        throw new Error(`Invalid plan ID: ${planId}`);
      }
      
      // Create a direct one-time payment intent instead of a subscription for testing
      // This simplifies testing as it doesn't require setting up products and prices
      const paymentIntent = await stripe.paymentIntents.create({
        amount,
        currency: 'usd',
        customer: customerObj.id,
        payment_method_types: ['card'],
        description: `One-time payment for ${planId} plan (${interval})`,
        metadata: {
          planId,
          billingInterval: interval
        }
      });
      
      return {
        clientSecret: paymentIntent.client_secret,
        customerId: customerObj.id,
        amount,
        planId,
        billingInterval: interval
      };
    } catch (error) {
      console.error("Error creating payment intent:", error);
      throw error;
    }
  }

  // Create checkout session for redirect-based checkout
  async createCheckoutSession({
    planId,
    billingInterval,
    user,
    successUrl,
    cancelUrl
  }: {
    planId: string;
    billingInterval: 'monthly' | 'yearly';
    user: any;
    successUrl: string;
    cancelUrl: string;
  }) {
    try {
      // Normalize the plan ID for Stripe
      const normalizedPlanId = normalizePlanId(`${planId}-${billingInterval}`);
      
      // Get or create customer
      const customer = await this.getOrCreateCustomer(user.email, user.name || user.username);
      
      // Create checkout session
      const session = await stripe.checkout.sessions.create({
        payment_method_types: ['card'],
        line_items: [
          {
            price: normalizedPlanId,
            quantity: 1,
          },
        ],
        mode: 'subscription',
        success_url: successUrl,
        cancel_url: cancelUrl,
        customer: customer.id,
        metadata: {
          userId: user.id.toString(),
          planId,
          billingInterval
        }
      });
      
      return {
        id: session.id,
        url: session.url
      };
    } catch (error) {
      console.error('Error creating checkout session:', error);
      throw error;
    }
  }
}

export const stripeService = new StripeService();