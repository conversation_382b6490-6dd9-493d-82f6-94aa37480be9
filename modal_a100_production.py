#!/usr/bin/env python3
"""
Production Modal A100 GPU Application for Course AI Platform
Direct deployment with working authentication and GPU functions
"""

import os
import sys
import json
import base64
import subprocess
from typing import List, Dict, Any, Optional

# Direct Modal configuration using environment credentials
MODAL_TOKEN_ID = os.getenv('MODAL_TOKEN_ID')
MODAL_TOKEN_SECRET = os.getenv('MODAL_TOKEN_SECRET')

if not MODAL_TOKEN_ID or not MODAL_TOKEN_SECRET:
    print("ERROR: Modal credentials not found in environment")
    sys.exit(1)

# Configure Modal authentication
os.environ['MODAL_TOKEN_ID'] = MODAL_TOKEN_ID
os.environ['MODAL_TOKEN_SECRET'] = MODAL_TOKEN_SECRET

try:
    import modal
except ImportError:
    print("Installing Modal...")
    subprocess.run([sys.executable, "-m", "pip", "install", "modal"], check=True)
    import modal

# Create Modal app for A100 GPU
app = modal.App("courseai-a100-gpu-production")

# A100 GPU image with production-ready dependencies
a100_image = (
    modal.Image.debian_slim(python_version="3.11")
    .apt_install([
        "ffmpeg",
        "espeak-ng", 
        "espeak-ng-data",
        "git-lfs",
        "wget",
        "curl",
        "build-essential",
        "sox",
        "libsox-dev"
    ])
    .pip_install([
        # Core ML stack optimized for A100
        "torch>=2.1.0",
        "transformers>=4.35.0", 
        "accelerate>=0.24.0",
        "datasets>=2.14.0",
        "tokenizers>=0.15.0",
        
        # Audio processing
        "librosa>=0.10.1",
        "soundfile>=0.12.1",
        "scipy>=1.11.0",
        "numpy>=1.24.0",
        "pydub>=0.25.1",
        
        # TTS engines
        "bark>=1.2.0",
        "coqui-tts>=0.17.0",
        
        # Image processing
        "pillow>=10.0.0",
        "opencv-python>=4.8.0",
        "imageio>=2.31.0",
        
        # Diffusion models
        "diffusers>=0.21.0",
        "xformers>=0.0.22",
        
        # API utilities
        "fastapi>=0.104.0",
        "uvicorn>=0.24.0",
        "pydantic>=2.4.0",
        "httpx>=0.25.0",
        "python-multipart>=0.0.6",
    ])
    .run_commands([
        "git lfs install",
    ])
)

@app.function(
    image=a100_image,
    gpu="A100:1",
    timeout=3600,
    memory=32768,
    cpu=8,
    secrets=[modal.Secret.from_name("modal-credentials")]
)
def high_quality_tts_production(
    text: str,
    voice_preset: str = "v2/en_speaker_6",
    temperature: float = 0.7,
    silence_duration: float = 0.25
) -> str:
    """
    Production-grade high-quality TTS using A100 80G GPU
    """
    import torch
    import numpy as np
    import soundfile as sf
    import io
    import base64
    
    try:
        # Optimize for A100 performance
        torch.backends.cudnn.benchmark = True
        torch.set_float32_matmul_precision('high')
        
        # Import Bark after CUDA setup
        from bark import SAMPLE_RATE, generate_audio, preload_models
        
        # Preload models with A100 optimization
        preload_models()
        
        # Generate high-quality audio
        audio_array = generate_audio(
            text,
            history_prompt=voice_preset,
            text_temp=temperature,
            waveform_temp=0.7,
            silent=True
        )
        
        # Enhance audio quality
        audio_array = np.clip(audio_array, -1.0, 1.0)
        
        # Convert to high-quality WAV
        buffer = io.BytesIO()
        sf.write(buffer, audio_array, SAMPLE_RATE, format='WAV', subtype='PCM_24')
        
        return base64.b64encode(buffer.getvalue()).decode()
        
    except Exception as e:
        print(f"TTS generation error: {e}")
        raise

@app.function(
    image=a100_image,
    gpu="A100:1", 
    timeout=1800,
    memory=40960,
    cpu=8
)
def batch_course_narration_a100(
    lessons: List[Dict[str, Any]],
    voice_preset: str = "v2/en_speaker_6",
    optimize_quality: bool = True
) -> List[Dict[str, Any]]:
    """
    Batch TTS generation optimized for course content
    """
    import torch
    import numpy as np
    import soundfile as sf
    import io
    import base64
    from bark import SAMPLE_RATE, generate_audio, preload_models
    
    # A100 optimizations
    torch.backends.cudnn.benchmark = True
    torch.set_float32_matmul_precision('high')
    
    # Preload models once for batch efficiency
    preload_models()
    
    results = []
    
    for lesson in lessons:
        title = lesson.get("title", "Untitled")
        text = lesson.get("text", "")
        module_id = lesson.get("moduleId")
        lesson_id = lesson.get("lessonId")
        
        if not text:
            continue
        
        try:
            # Generate audio with quality optimization
            audio_array = generate_audio(
                text,
                history_prompt=voice_preset,
                text_temp=0.6 if optimize_quality else 0.7,
                waveform_temp=0.6 if optimize_quality else 0.7,
                silent=True
            )
            
            # Audio post-processing for quality
            if optimize_quality:
                audio_array = np.clip(audio_array, -1.0, 1.0)
                # Apply light compression for consistency
                audio_array = np.tanh(audio_array * 0.9)
            
            # High-quality encoding
            buffer = io.BytesIO()
            sf.write(buffer, audio_array, SAMPLE_RATE, format='WAV', subtype='PCM_24')
            audio_b64 = base64.b64encode(buffer.getvalue()).decode()
            
            result = {
                "title": title,
                "audioData": audio_b64,
                "format": "wav",
                "sampleRate": SAMPLE_RATE,
                "durationSeconds": len(audio_array) / SAMPLE_RATE,
                "sizeBytes": len(buffer.getvalue()),
                "quality": "high" if optimize_quality else "standard"
            }
            
            if module_id:
                result["moduleId"] = module_id
            if lesson_id:
                result["lessonId"] = lesson_id
                
            results.append(result)
            
        except Exception as e:
            print(f"Failed to generate audio for '{title}': {e}")
            continue
    
    return results

@app.function(
    image=a100_image,
    gpu="A100:1",
    timeout=3600,
    memory=65536,  # 64GB for large models
    cpu=12
)
def large_model_inference_a100(
    model_name: str,
    prompt: str,
    max_tokens: int = 1024,
    temperature: float = 0.7,
    system_prompt: Optional[str] = None
) -> Dict[str, Any]:
    """
    Large model inference optimized for A100 80G
    """
    import torch
    from transformers import AutoTokenizer, AutoModelForCausalLM, BitsAndBytesConfig
    
    try:
        # A100 optimization settings
        torch.backends.cudnn.benchmark = True
        torch.set_float32_matmul_precision('high')
        
        # Load tokenizer
        tokenizer = AutoTokenizer.from_pretrained(model_name, trust_remote_code=True)
        
        # Configure model for A100
        model = AutoModelForCausalLM.from_pretrained(
            model_name,
            torch_dtype=torch.bfloat16,
            device_map="auto",
            trust_remote_code=True,
            attn_implementation="flash_attention_2",
            low_cpu_mem_usage=True
        )
        
        # Prepare input
        if system_prompt:
            full_prompt = f"System: {system_prompt}\n\nUser: {prompt}\n\nAssistant:"
        else:
            full_prompt = prompt
            
        inputs = tokenizer(full_prompt, return_tensors="pt").to(model.device)
        
        # Generate with A100 optimizations
        with torch.no_grad():
            outputs = model.generate(
                inputs.input_ids,
                max_length=inputs.input_ids.shape[1] + max_tokens,
                temperature=temperature,
                do_sample=True,
                top_p=0.9,
                top_k=50,
                pad_token_id=tokenizer.eos_token_id,
                use_cache=True,
                num_beams=1  # Faster on A100
            )
        
        response = tokenizer.decode(
            outputs[0][inputs.input_ids.shape[1]:], 
            skip_special_tokens=True
        )
        
        return {
            "response": response,
            "model": model_name,
            "tokens_generated": len(outputs[0]) - len(inputs.input_ids[0]),
            "prompt_tokens": len(inputs.input_ids[0])
        }
        
    except Exception as e:
        return {"error": str(e), "model": model_name}

@app.function(
    image=a100_image,
    gpu="A100:1",
    timeout=1800,
    memory=32768,
    cpu=8
)
def sdxl_image_generation_a100(
    prompt: str,
    negative_prompt: str = "",
    width: int = 1024,
    height: int = 1024,
    num_inference_steps: int = 30,
    guidance_scale: float = 7.5,
    num_images: int = 1
) -> List[str]:
    """
    High-resolution image generation using SDXL on A100
    """
    import torch
    from diffusers import DiffusionPipeline, EulerDiscreteScheduler
    import io
    import base64
    
    try:
        # A100 optimizations
        torch.backends.cudnn.benchmark = True
        
        # Load SDXL pipeline
        pipe = DiffusionPipeline.from_pretrained(
            "stabilityai/stable-diffusion-xl-base-1.0",
            torch_dtype=torch.float16,
            use_safetensors=True,
            variant="fp16"
        )
        
        # Optimize for A100
        pipe = pipe.to("cuda")
        pipe.enable_attention_slicing()
        pipe.enable_model_cpu_offload()
        
        # Use optimized scheduler
        pipe.scheduler = EulerDiscreteScheduler.from_config(pipe.scheduler.config)
        
        images = []
        
        for _ in range(num_images):
            # Generate image
            image = pipe(
                prompt=prompt,
                negative_prompt=negative_prompt,
                width=width,
                height=height,
                num_inference_steps=num_inference_steps,
                guidance_scale=guidance_scale,
                generator=torch.Generator("cuda").manual_seed(torch.randint(0, 1000000, (1,)).item())
            ).images[0]
            
            # Convert to base64
            buffer = io.BytesIO()
            image.save(buffer, format="PNG", quality=95, optimize=True)
            image_b64 = base64.b64encode(buffer.getvalue()).decode()
            images.append(image_b64)
        
        return images
        
    except Exception as e:
        return [f"Image generation failed: {str(e)}"]

@app.function(image=a100_image, timeout=300)
def health_check_production():
    """Production health check for A100 GPU"""
    import torch
    
    health_info = {
        "timestamp": __import__("datetime").datetime.now().isoformat(),
        "gpu_available": torch.cuda.is_available(),
        "service_status": "healthy"
    }
    
    if torch.cuda.is_available():
        health_info.update({
            "gpu_count": torch.cuda.device_count(),
            "gpu_name": torch.cuda.get_device_name(0),
            "gpu_memory_total": torch.cuda.get_device_properties(0).total_memory,
            "cuda_version": torch.version.cuda,
            "pytorch_version": torch.__version__
        })
    
    return health_info

@app.function(image=a100_image, timeout=300)
def list_production_voices():
    """List available voice presets for production TTS"""
    return [
        {"id": "v2/en_speaker_0", "name": "English Speaker 0", "gender": "male", "language": "en", "quality": "high"},
        {"id": "v2/en_speaker_1", "name": "English Speaker 1", "gender": "female", "language": "en", "quality": "high"},
        {"id": "v2/en_speaker_2", "name": "English Speaker 2", "gender": "male", "language": "en", "quality": "high"},
        {"id": "v2/en_speaker_3", "name": "English Speaker 3", "gender": "female", "language": "en", "quality": "high"},
        {"id": "v2/en_speaker_4", "name": "English Speaker 4", "gender": "male", "language": "en", "quality": "high"},
        {"id": "v2/en_speaker_5", "name": "English Speaker 5", "gender": "female", "language": "en", "quality": "high"},
        {"id": "v2/en_speaker_6", "name": "English Speaker 6", "gender": "male", "language": "en", "quality": "premium"},
        {"id": "v2/en_speaker_7", "name": "English Speaker 7", "gender": "female", "language": "en", "quality": "premium"},
        {"id": "v2/en_speaker_8", "name": "English Speaker 8", "gender": "male", "language": "en", "quality": "premium"},
        {"id": "v2/en_speaker_9", "name": "English Speaker 9", "gender": "female", "language": "en", "quality": "premium"},
    ]

# Web API endpoints for Course AI Platform integration
@app.function(image=a100_image, timeout=600)
@modal.web_endpoint(method="POST")
def api_tts_generate(item):
    """REST API for TTS generation"""
    try:
        text = item.get("text", "")
        voice_preset = item.get("voice_preset", "v2/en_speaker_6")
        temperature = item.get("temperature", 0.7)
        
        if not text:
            return {"error": "Text is required"}
        
        audio_b64 = high_quality_tts_production.remote(text, voice_preset, temperature)
        
        return {
            "success": True,
            "audioData": audio_b64,
            "format": "wav",
            "voice": voice_preset,
            "quality": "production"
        }
        
    except Exception as e:
        return {"error": str(e)}

@app.function(image=a100_image, timeout=1800)
@modal.web_endpoint(method="POST")
def api_batch_tts(item):
    """REST API for batch TTS generation"""
    try:
        lessons = item.get("lessons", [])
        voice_preset = item.get("voice_preset", "v2/en_speaker_6")
        optimize_quality = item.get("optimize_quality", True)
        
        if not lessons:
            return {"error": "Lessons are required"}
        
        results = batch_course_narration_a100.remote(lessons, voice_preset, optimize_quality)
        
        total_duration = sum(r.get("durationSeconds", 0) for r in results)
        
        return {
            "success": True,
            "results": results,
            "totalLessons": len(results),
            "totalDuration": total_duration,
            "voice": voice_preset
        }
        
    except Exception as e:
        return {"error": str(e)}

if __name__ == "__main__":
    print("Modal A100 GPU Production Application")
    print("Ready for deployment to Course AI Platform")
    
    # Test basic functionality
    try:
        with app.run():
            print("Testing A100 GPU health...")
            health = health_check_production.remote()
            print(f"Health status: {health}")
            
            if health.get("gpu_available"):
                print("A100 GPU is available and ready")
            else:
                print("Warning: GPU not detected")
                
    except Exception as e:
        print(f"Initialization test failed: {e}")
        print("App created but requires manual deployment")