import { spawn } from 'child_process';
import path from 'path';
import fs from 'fs';
import fetch from 'cross-fetch';

// Default voice options
const DEFAULT_VOICES = [
  { id: 'english_american_female', name: 'American Female', language: 'en-US', service: 'coqui' },
  { id: 'english_british_male', name: 'British Male', language: 'en-US', service: 'coqui' },
  { id: 'english_standard', name: 'Standard English', language: 'en-US', service: 'coqui' }
];

// Default models for Coqui TTS
const DEFAULT_MODELS = [
  { id: 'coqui_standard', name: 'Standard', service: 'coqui' },
  { id: 'coqui_high_quality', name: 'High Quality', service: 'coqui' }
];

/**
 * Get available models for Coqui TTS
 */
export async function getModels(): Promise<Array<{ id: string, name: string, service: string }>> {
  return DEFAULT_MODELS;
}

/**
 * Get available voices for Coqui TTS
 */
export async function getAvailableVoices(): Promise<{ id: string, name: string, language: string, service: string }[]> {
  try {
    // In a real implementation, this would query Coqui TTS for available voices
    // For now, return some default options
    return DEFAULT_VOICES;
  } catch (error) {
    console.error('Error getting Coqui TTS voices:', error);
    throw error;
  }
}

/**
 * Generate speech from text using Coqui TTS via HTTP API
 * @param text Text to convert to speech
 * @param outputPath Path to save the audio file
 * @param voiceId Voice ID to use (optional)
 */
export async function textToSpeech(
  text: string,
  outputPath: string,
  voiceId?: string
): Promise<string> {
  try {
    // Get the configured Coqui endpoint or use the default
    const endpoint = process.env.RUNPOD_COQUI_ENDPOINT || 'https://8x1dneg4ifrfbc-5000.proxy.runpod.net/speak';
    
    console.log(`Using Coqui TTS endpoint: ${endpoint}`);
    
    // Make API request
    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        text: text,
        voice: voiceId || 'english_standard'
      }),
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Coqui TTS API error (${response.status}): ${errorText}`);
    }
    
    // Get audio data as arrayBuffer
    const audioBuffer = await response.arrayBuffer();
    
    // Write to file
    fs.writeFileSync(outputPath, Buffer.from(audioBuffer));
    
    // Return the path to the saved file
    return outputPath;
  } catch (error) {
    console.error('Error generating speech with Coqui TTS:', error);
    throw error;
  }
}

/**
 * Generate speech directly from Coqui TTS API and return audio data
 */
export async function generateSpeech(params: {
  text: string;
  voice?: string;
  speed?: number;
}): Promise<{ audio: string; duration?: number }> {
  try {
    // Get the configured Coqui endpoint or use the default
    const endpoint = process.env.RUNPOD_COQUI_ENDPOINT || 'https://8x1dneg4ifrfbc-5000.proxy.runpod.net/speak';
    
    // Make API request
    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        text: params.text,
        voice: params.voice || 'english_standard',
        speed: params.speed || 1.0
      }),
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Coqui TTS API error (${response.status}): ${errorText}`);
    }
    
    // Get audio data as arrayBuffer
    const audioBuffer = await response.arrayBuffer();
    
    // Convert to base64
    const base64Audio = Buffer.from(audioBuffer).toString('base64');
    
    // Calculate approximate duration (rough estimate)
    // This is a very rough estimate: ~3 words per second at normal speech rate
    const wordCount = params.text.split(/\s+/).length;
    const duration = Math.max(1, Math.round(wordCount / 3)) * (params.speed ? 1 / params.speed : 1);
    
    return {
      audio: base64Audio,
      duration: duration
    };
  } catch (error) {
    console.error('Error generating speech with Coqui TTS:', error);
    throw error;
  }
}

/**
 * Utility function to check if Coqui TTS is available
 */
export async function isCoquiTTSAvailable(): Promise<boolean> {
  try {
    // Get the configured Coqui endpoint or use the default
    const endpoint = process.env.RUNPOD_COQUI_ENDPOINT || 'https://8x1dneg4ifrfbc-5000.proxy.runpod.net/speak';
    
    // Make a simple test request (just to check if the endpoint is responding)
    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        text: 'Test',
        voice: 'english_standard'
      }),
    });
    
    return response.ok;
  } catch (error) {
    console.error('Error checking Coqui TTS availability:', error);
    return false;
  }
}