import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Skeleton } from "@/components/ui/skeleton";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  ChevronDown,
  ChevronRight,
  FileIcon,
  FilePlus,
  Image,
  Loader2,
  Music,
  Play,
  Pause,
  Trash,
  Upload,
  FileText,
  Download,
  AlertCircle,
  FileSpreadsheet,
  FileCode,
  FileVideo,
  File,
  Settings
} from "lucide-react";

import { Module, Lesson } from "./ContentStructure";

interface MediaCreationProps {
  onNext: () => void;
  onPrevious: () => void;
  courseDetails: {
    title: string;
    category: string;
    description: string;
  };
  courseStructure: {
    modules: Module[];
  };
  courseScripts?: {
    [key: string]: {
      [key: string]: string;
    };
  };
  onUpdateGeneratedAudio?: (audio: Record<string, Media[]>) => void;
}

interface Media {
  id: number;
  url: string;
  type: string;
  name: string;
  size: number;
  createdAt: string;
  userId: number;
  duration?: number;
  originalFilename?: string;
}

// Get file icon based on file type
const getFileIcon = (fileType: string) => {
  const iconProps = { className: "h-5 w-5 mr-2" };
  switch (fileType) {
    case "image":
      return <Image {...iconProps} />;
    case "audio":
      return <Music {...iconProps} />;
    case "video":
      return <FileVideo {...iconProps} />;
    case "pdf":
      return <File {...iconProps} />;
    case "spreadsheet":
      return <FileSpreadsheet {...iconProps} />;
    case "document":
      return <FileText {...iconProps} />;
    case "code":
      return <FileCode {...iconProps} />;
    default:
      return <FileIcon {...iconProps} />;
  }
};

// Format file size
const formatFileSize = (bytes: number) => {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

interface Voice {
  voice_id: string;
  name: string;
  source?: string;
  language?: string;
  description?: string;
}

export function MediaCreation({ onNext, onPrevious, courseDetails, courseStructure, courseScripts, onUpdateGeneratedAudio }: MediaCreationProps) {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState<string>("my-media");
  const [activeModuleIndex, setActiveModuleIndex] = useState<number>(0);
  const [expandedModules, setExpandedModules] = useState<Record<number, boolean>>({});
  const [mediaLibrary, setMediaLibrary] = useState<Media[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isUploading, setIsUploading] = useState<boolean>(false);
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const [generatedAudio, setGeneratedAudio] = useState<Record<string, Media[]>>({});
  const [playingAudio, setPlayingAudio] = useState<string | null>(null);
  const [generatingAudio, setGeneratingAudio] = useState<Record<string, boolean>>({});
  const [audioGenerationProgress, setAudioGenerationProgress] = useState<Record<string, number>>({});
  const [voiceProvider, setVoiceProvider] = useState<string>("elevenlabs");
  const [availableVoices, setAvailableVoices] = useState<Voice[]>([]);
  const [elevenLabsVoices, setElevenLabsVoices] = useState<Voice[]>([]);
  const [coquiVoices, setCoquiVoices] = useState<Voice[]>([]);
  const [selectedVoice, setSelectedVoice] = useState<string>("EXAVITQu4vr4xnSDxMaL"); // Default ElevenLabs voice
  const [isVoiceDialogOpen, setIsVoiceDialogOpen] = useState<boolean>(false);
  const [currentLessonIndexes, setCurrentLessonIndexes] = useState<{moduleIndex: number; lessonIndex: number} | null>(null);
  const audioRef = React.useRef<HTMLAudioElement | null>(null);
  
  // Fetch media library
  useEffect(() => {
    fetchMediaLibrary();
    fetchVoices();
  }, []);
  
  // Fetch available voices from different providers
  const fetchVoices = async () => {
    try {
      // Fetch ElevenLabs voices
      const elevenLabsResponse = await apiRequest("GET", "/api/ai/voices");
      if (elevenLabsResponse.ok) {
        const data = await elevenLabsResponse.json();
        setElevenLabsVoices(data);
      }
      
      // Fetch Coqui voices
      const coquiResponse = await apiRequest("GET", "/api/ai/coqui-voices");
      if (coquiResponse.ok) {
        const data = await coquiResponse.json();
        setCoquiVoices(data);
      }
      
      // Update available voices based on the selected provider
      setAvailableVoices(elevenLabsVoices);
    } catch (error) {
      console.error("Error fetching voices:", error);
      toast({
        title: "Error fetching voices",
        description: "Failed to load available voices for TTS",
        variant: "destructive"
      });
    }
  };
  
  // Update available voices when provider changes
  useEffect(() => {
    if (voiceProvider === 'elevenlabs') {
      setAvailableVoices(elevenLabsVoices);
      // Set default ElevenLabs voice if available
      if (elevenLabsVoices.length > 0 && !elevenLabsVoices.find(voice => voice.voice_id === selectedVoice)) {
        setSelectedVoice(elevenLabsVoices[0]?.voice_id || "EXAVITQu4vr4xnSDxMaL");
      }
    } else if (voiceProvider === 'coqui') {
      setAvailableVoices(coquiVoices);
      // Set default Coqui voice if available
      if (coquiVoices.length > 0 && !coquiVoices.find(voice => voice.voice_id === selectedVoice)) {
        setSelectedVoice(coquiVoices[0]?.voice_id || "coqui-en-female");
      }
    }
  }, [voiceProvider, elevenLabsVoices, coquiVoices]);

  // Handle audio playback
  useEffect(() => {
    if (audioRef.current) {
      if (playingAudio) {
        audioRef.current.src = playingAudio;
        audioRef.current.play().catch(err => {
          console.error("Error playing audio:", err);
          toast({
            title: "Error playing audio",
            description: "Could not play the audio file",
            variant: "destructive"
          });
        });
      } else {
        audioRef.current.pause();
      }
    }
    
    return () => {
      if (audioRef.current) {
        audioRef.current.pause();
      }
    };
  }, [playingAudio]);

  const fetchMediaLibrary = async () => {
    setIsLoading(true);
    try {
      const response = await apiRequest("GET", "/api/media");
      if (response.ok) {
        const data = await response.json();
        setMediaLibrary(data);
        
        // Group audio files by module/lesson
        if (data && data.length > 0) {
          const audioFiles = data.filter((file: Media) => file.type === "audio");
          const groupedAudio: Record<string, Media[]> = {};
          
          courseStructure.modules.forEach((module, moduleIndex) => {
            module.lessons.forEach((lesson: Lesson, lessonIndex: number) => {
              const key = `module-${moduleIndex}-lesson-${lessonIndex}`;
              const lessonAudio = audioFiles.filter((file: Media) => 
                file.name.includes(`${moduleIndex}-${lessonIndex}`) || 
                file.originalFilename?.includes(`${moduleIndex}-${lessonIndex}`)
              );
              if (lessonAudio.length > 0) {
                groupedAudio[key] = lessonAudio;
              }
            });
          });
          
          setGeneratedAudio(groupedAudio);
          
          // Pass generated audio to parent component if the callback is provided
          if (onUpdateGeneratedAudio) {
            onUpdateGeneratedAudio(groupedAudio);
          }
        }
      }
    } catch (error) {
      console.error("Error fetching media:", error);
      toast({
        title: "Error fetching media",
        description: "Failed to load your media library",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;
    
    setIsUploading(true);
    setUploadProgress(0);
    
    try {
      const formData = new FormData();
      formData.append("file", files[0]);
      
      // Use fetch directly for upload progress
      const xhr = new XMLHttpRequest();
      xhr.open("POST", "/api/media/upload", true);
      
      xhr.upload.onprogress = (e) => {
        if (e.lengthComputable) {
          const progress = Math.round((e.loaded / e.total) * 100);
          setUploadProgress(progress);
        }
      };
      
      xhr.onload = async () => {
        if (xhr.status === 200 || xhr.status === 201) {
          await fetchMediaLibrary();
          toast({
            title: "Upload successful",
            description: "File has been uploaded to your media library",
          });
        } else {
          throw new Error(`Upload failed with status ${xhr.status}`);
        }
        setIsUploading(false);
        event.target.value = '';
      };
      
      xhr.onerror = () => {
        toast({
          title: "Upload failed",
          description: "There was an error uploading your file",
          variant: "destructive"
        });
        setIsUploading(false);
        event.target.value = '';
      };
      
      xhr.send(formData);
      
    } catch (error) {
      console.error("Error uploading file:", error);
      toast({
        title: "Upload failed",
        description: "There was an error uploading your file",
        variant: "destructive"
      });
      setIsUploading(false);
      event.target.value = '';
    }
  };

  const handleGenerateAudio = async (moduleIndex: number, lessonIndex: number, script: string) => {
    if (!script || script.trim() === "") {
      toast({
        title: "No script available",
        description: "Please generate or write a script for this lesson first",
        variant: "destructive"
      });
      return;
    }
    
    const key = `module-${moduleIndex}-lesson-${lessonIndex}`;
    
    try {
      // Set generating state for this lesson
      setGeneratingAudio(prev => ({
        ...prev,
        [key]: true
      }));
      
      // Initialize progress
      setAudioGenerationProgress(prev => ({
        ...prev,
        [key]: 0
      }));
      
      // Estimate progress based on word count
      const wordCount = script.split(/\s+/).length;
      const estimatedTimePerWord = 0.05; // 50ms per word (approximate)
      const totalEstimatedTime = wordCount * estimatedTimePerWord; // seconds
      
      // Start progress simulation
      let progress = 0;
      const progressInterval = setInterval(() => {
        // Cap progress at 95% until we get confirmation of completion
        if (progress < 95) {
          progress += 5;
          setAudioGenerationProgress(prev => ({
            ...prev,
            [key]: progress
          }));
        }
      }, totalEstimatedTime * 10); // Update every ~10% of estimated time
      
      const module = courseStructure.modules[moduleIndex];
      const lesson = module.lessons[lessonIndex];
      
      const response = await apiRequest("POST", "/api/ai/text-to-speech", {
        text: script,
        voiceId: selectedVoice,
        fileName: `${module.title}-${lesson.title}-${moduleIndex}-${lessonIndex}`,
        modelId: voiceProvider === 'elevenlabs' ? "eleven_multilingual_v2" : undefined,
        provider: voiceProvider
      });
      
      // Clear the interval
      clearInterval(progressInterval);
      
      if (response.ok) {
        // Set progress to 100%
        setAudioGenerationProgress(prev => ({
          ...prev,
          [key]: 100
        }));
        
        // Wait a moment to show 100% before clearing
        setTimeout(() => {
          setGeneratingAudio(prev => {
            const updated = {...prev};
            delete updated[key];
            return updated;
          });
          
          setAudioGenerationProgress(prev => {
            const updated = {...prev};
            delete updated[key];
            return updated;
          });
        }, 1000);
        
        const data = await response.json();
        toast({
          title: "Audio generated",
          description: "Lesson audio has been successfully generated and saved to Media Library",
        });
        
        // Update media library to include the new audio
        await fetchMediaLibrary();
      } else {
        // Clear generating state
        setGeneratingAudio(prev => {
          const updated = {...prev};
          delete updated[key];
          return updated;
        });
        
        setAudioGenerationProgress(prev => {
          const updated = {...prev};
          delete updated[key];
          return updated;
        });
        
        const error = await response.json();
        throw new Error(error.message || "Failed to generate audio");
      }
    } catch (error) {
      // Clear generating state
      setGeneratingAudio(prev => {
        const updated = {...prev};
        delete updated[key];
        return updated;
      });
      
      setAudioGenerationProgress(prev => {
        const updated = {...prev};
        delete updated[key];
        return updated;
      });
      
      console.error("Error generating audio:", error);
      toast({
        title: "Generation failed",
        description: error instanceof Error ? error.message : "Failed to generate audio",
        variant: "destructive"
      });
    }
  };

  const handleDeleteMedia = async (mediaId: number) => {
    try {
      const response = await apiRequest("DELETE", `/api/media/${mediaId}`);
      
      if (response.ok) {
        // Remove from state
        setMediaLibrary(prevMedia => prevMedia.filter(media => media.id !== mediaId));
        
        // If it was an audio file, also update the generatedAudio state
        const updatedAudio = { ...generatedAudio };
        Object.keys(updatedAudio).forEach(key => {
          updatedAudio[key] = updatedAudio[key].filter(media => media.id !== mediaId);
          if (updatedAudio[key].length === 0) {
            delete updatedAudio[key];
          }
        });
        
        setGeneratedAudio(updatedAudio);
        
        // Also update the parent component's state
        if (onUpdateGeneratedAudio) {
          onUpdateGeneratedAudio(updatedAudio);
        }
        
        toast({
          title: "File deleted",
          description: "The file has been removed from your media library",
        });
      } else {
        throw new Error("Failed to delete file");
      }
    } catch (error) {
      console.error("Error deleting file:", error);
      toast({
        title: "Deletion failed",
        description: "There was an error deleting the file",
        variant: "destructive"
      });
    }
  };

  const toggleModule = (moduleIndex: number) => {
    setExpandedModules(prev => ({
      ...prev,
      [moduleIndex]: !prev[moduleIndex]
    }));
  };
  
  const openVoiceDialog = (moduleIndex: number, lessonIndex: number) => {
    setCurrentLessonIndexes({ moduleIndex, lessonIndex });
    setIsVoiceDialogOpen(true);
  };
  
  const handleVoiceSelection = () => {
    setIsVoiceDialogOpen(false);
    
    // If we have a script and valid lesson indexes, generate audio with the selected voice
    if (currentLessonIndexes && courseScripts) {
      const { moduleIndex, lessonIndex } = currentLessonIndexes;
      const moduleKey = `module-${moduleIndex}`;
      const lessonKey = `lesson-${lessonIndex}`;
      
      if (courseScripts[moduleKey] && courseScripts[moduleKey][lessonKey]) {
        handleGenerateAudio(
          moduleIndex,
          lessonIndex,
          courseScripts[moduleKey][lessonKey]
        );
      }
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-xl font-semibold">Media Creation</h3>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={onPrevious}>Back</Button>
          <Button onClick={onNext}>Continue</Button>
        </div>
      </div>
      
      <div className="bg-slate-50 p-4 rounded-md border border-slate-200">
        <p className="text-sm text-slate-600">
          In this step, you can upload media files for your course (images, PDFs, spreadsheets, etc.) 
          and generate audio narrations from your lesson scripts.
        </p>
      </div>
      
      {/* Hidden audio element for audio playback */}
      <audio ref={audioRef} onEnded={() => setPlayingAudio(null)} />
      
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="my-media">My Media Library</TabsTrigger>
          <TabsTrigger value="course-audio">Course Audio</TabsTrigger>
        </TabsList>
        
        <TabsContent value="my-media" className="space-y-4">
          <div className="flex justify-between items-center">
            <h4 className="font-medium">Media Library</h4>
            <div className="flex space-x-2">
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => document.getElementById("file-upload")?.click()}
                disabled={isUploading}
              >
                {isUploading ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Uploading {uploadProgress}%
                  </>
                ) : (
                  <>
                    <Upload className="h-4 w-4 mr-2" />
                    Upload File
                  </>
                )}
              </Button>
              <input 
                id="file-upload" 
                type="file" 
                className="hidden" 
                onChange={handleFileUpload}
                disabled={isUploading}
              />
            </div>
          </div>
          
          <Card>
            <CardContent className="p-4">
              <ScrollArea className="h-[350px]">
                {isLoading ? (
                  <div className="space-y-3">
                    {Array.from({ length: 5 }).map((_, i) => (
                      <div key={i} className="flex items-center space-x-3">
                        <Skeleton className="h-10 w-10 rounded" />
                        <div className="space-y-1">
                          <Skeleton className="h-4 w-[250px]" />
                          <Skeleton className="h-3 w-[180px]" />
                        </div>
                      </div>
                    ))}
                  </div>
                ) : mediaLibrary.length > 0 ? (
                  <div className="space-y-2">
                    {mediaLibrary.map((file) => (
                      <div key={file.id} className="flex items-center justify-between p-2 border rounded hover:bg-slate-50">
                        <div className="flex items-center space-x-3">
                          {getFileIcon(file.type)}
                          <div>
                            <p className="font-medium text-sm truncate max-w-[200px]">{file.originalFilename || file.name}</p>
                            <p className="text-xs text-slate-500">{formatFileSize(file.size)} • {new Date(file.createdAt).toLocaleDateString()}</p>
                          </div>
                        </div>
                        <div className="flex space-x-1">
                          {file.type === "audio" && (
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0"
                              onClick={() => {
                                if (playingAudio === file.url) {
                                  setPlayingAudio(null);
                                } else {
                                  setPlayingAudio(file.url);
                                }
                              }}
                            >
                              {playingAudio === file.url ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                            </Button>
                          )}
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0"
                            asChild
                          >
                            <a href={file.url} download={file.originalFilename || file.name} target="_blank" rel="noopener noreferrer">
                              <Download className="h-4 w-4" />
                            </a>
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0 text-red-500 hover:text-red-600 hover:bg-red-50"
                            onClick={() => handleDeleteMedia(file.id)}
                          >
                            <Trash className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center py-8 text-center">
                    <FilePlus className="h-12 w-12 text-slate-300 mb-3" />
                    <h4 className="text-base font-medium mb-1">No media files yet</h4>
                    <p className="text-sm text-slate-500 mb-4 max-w-md">
                      Upload images, documents, audio, and more to use in your course.
                    </p>
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => document.getElementById("file-upload")?.click()}
                    >
                      <Upload className="h-4 w-4 mr-2" />
                      Upload First File
                    </Button>
                  </div>
                )}
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="course-audio" className="space-y-4">
          <div className="flex justify-between items-center">
            <h4 className="font-medium">Course Audio Generation</h4>
            <div className="text-sm text-slate-500">
              Create audio narrations for your lesson scripts
            </div>
          </div>
          
          <Card>
            <CardContent className="p-4">
              <ScrollArea className="h-[350px]">
                {courseStructure.modules.length === 0 ? (
                  <div className="flex flex-col items-center justify-center py-8 text-center">
                    <AlertCircle className="h-12 w-12 text-slate-300 mb-3" />
                    <h4 className="text-base font-medium mb-1">No course structure</h4>
                    <p className="text-sm text-slate-500 mb-4 max-w-md">
                      Please create your course structure in the Content Structure step first.
                    </p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {courseStructure.modules.map((module, moduleIndex) => (
                      <div key={moduleIndex} className="border rounded-md">
                        <div 
                          className="flex items-center justify-between p-3 bg-slate-50 cursor-pointer"
                          onClick={() => toggleModule(moduleIndex)}
                        >
                          <div className="flex items-center">
                            {expandedModules[moduleIndex] ? (
                              <ChevronDown className="h-4 w-4 mr-2" />
                            ) : (
                              <ChevronRight className="h-4 w-4 mr-2" />
                            )}
                            <h5 className="font-medium">{module.title}</h5>
                          </div>
                        </div>
                        
                        {expandedModules[moduleIndex] && (
                          <div className="p-3 space-y-3">
                            {module.lessons.map((lesson: Lesson, lessonIndex: number) => {
                              const key = `module-${moduleIndex}-lesson-${lessonIndex}`;
                              const hasScript = courseScripts?.[`module-${moduleIndex}`]?.[`lesson-${lessonIndex}`];
                              const audioFiles = generatedAudio[key] || [];
                              
                              return (
                                <div key={lessonIndex} className="pl-6 border-l-2 border-slate-200">
                                  <div className="mb-2">
                                    <div className="flex items-center justify-between">
                                      <h6 className="font-medium text-sm">{lesson.title}</h6>
                                      {hasScript && (
                                        <div>
                                          {/* Generate audio button with progress tracking */}
                                          {generatingAudio[`module-${moduleIndex}-lesson-${lessonIndex}`] ? (
                                            <div className="flex flex-col items-center">
                                              <div className="flex items-center mb-1">
                                                <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                                                <span className="text-xs text-slate-600">
                                                  Generating {audioGenerationProgress[`module-${moduleIndex}-lesson-${lessonIndex}`] || 0}%
                                                </span>
                                              </div>
                                              <div className="w-full bg-slate-200 rounded-full h-1.5 mb-1">
                                                <div
                                                  className="bg-primary h-1.5 rounded-full transition-all duration-300"
                                                  style={{ width: `${audioGenerationProgress[`module-${moduleIndex}-lesson-${lessonIndex}`] || 0}%` }}
                                                ></div>
                                              </div>
                                            </div>
                                          ) : (
                                            <Button
                                              variant="outline"
                                              size="sm"
                                              className="h-7 text-xs"
                                              onClick={() => openVoiceDialog(moduleIndex, lessonIndex)}
                                            >
                                              <Settings className="h-3 w-3 mr-1" />
                                              Voice Options
                                            </Button>
                                          )}
                                        </div>
                                      )}
                                    </div>
                                    {!hasScript && (
                                      <p className="text-xs text-amber-600 mt-1 flex items-center">
                                        <AlertCircle className="h-3 w-3 mr-1" />
                                        No script available
                                      </p>
                                    )}
                                  </div>
                                  
                                  {audioFiles.length > 0 && (
                                    <div className="space-y-2 mt-2">
                                      {audioFiles.map((file) => (
                                        <div key={file.id} className="flex items-center justify-between py-2 px-3 bg-slate-50 rounded-md text-sm">
                                          <div className="flex items-center">
                                            <Music className="h-4 w-4 mr-2 text-slate-500" />
                                            <span className="truncate max-w-[150px]">
                                              {file.originalFilename || file.name}
                                            </span>
                                            {file.duration && (
                                              <span className="text-xs text-slate-500 ml-2">
                                                {Math.floor(file.duration / 60)}:{(file.duration % 60).toString().padStart(2, '0')}
                                              </span>
                                            )}
                                          </div>
                                          <div className="flex space-x-1">
                                            <Button
                                              variant="ghost"
                                              size="sm"
                                              className="h-6 w-6 p-0"
                                              onClick={() => {
                                                if (playingAudio === file.url) {
                                                  setPlayingAudio(null);
                                                } else {
                                                  setPlayingAudio(file.url);
                                                }
                                              }}
                                            >
                                              {playingAudio === file.url ? (
                                                <Pause className="h-3 w-3" />
                                              ) : (
                                                <Play className="h-3 w-3" />
                                              )}
                                            </Button>
                                            <Button
                                              variant="ghost"
                                              size="sm"
                                              className="h-6 w-6 p-0"
                                              asChild
                                            >
                                              <a href={file.url} download target="_blank" rel="noopener noreferrer">
                                                <Download className="h-3 w-3" />
                                              </a>
                                            </Button>
                                            <Button
                                              variant="ghost"
                                              size="sm"
                                              className="h-6 w-6 p-0 text-red-500 hover:text-red-600"
                                              onClick={() => handleDeleteMedia(file.id)}
                                            >
                                              <Trash className="h-3 w-3" />
                                            </Button>
                                          </div>
                                        </div>
                                      ))}
                                    </div>
                                  )}
                                </div>
                              );
                            })}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
      
      {/* Voice Selection Dialog */}
      <Dialog open={isVoiceDialogOpen} onOpenChange={setIsVoiceDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Voice Selection Options</DialogTitle>
            <DialogDescription>
              Choose a voice provider and specific voice for generating your lesson audio.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Voice Provider</label>
              <Select value={voiceProvider} onValueChange={setVoiceProvider}>
                <SelectTrigger>
                  <SelectValue placeholder="Select voice provider" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="elevenlabs">ElevenLabs (Professional Quality)</SelectItem>
                  <SelectItem value="coqui">Coqui TTS (Default)</SelectItem>
                </SelectContent>
              </Select>
              <p className="text-xs text-slate-500">
                {voiceProvider === 'elevenlabs' 
                  ? 'ElevenLabs offers professional quality voice synthesis with realistic intonation and expressions.' 
                  : 'Coqui TTS is the default text-to-speech provider with reliable performance and no usage limits.'}
              </p>
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium">Voice Selection</label>
              <Select 
                value={selectedVoice} 
                onValueChange={setSelectedVoice} 
                disabled={availableVoices.length === 0}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a voice" />
                </SelectTrigger>
                <SelectContent>
                  {availableVoices.map((voice) => (
                    <SelectItem key={voice.voice_id} value={voice.voice_id}>
                      {voice.name} {voice.language ? `(${voice.language})` : ''}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {availableVoices.length === 0 && (
                <p className="text-xs text-amber-600">
                  No voices available. Please check your API keys or connection.
                </p>
              )}
            </div>
          </div>
          
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setIsVoiceDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button 
              onClick={handleVoiceSelection}
              disabled={availableVoices.length === 0}
            >
              Generate Audio
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}