import OpenAI from "openai";
import { GeneratedQuiz } from "./quizGenerator";

// Initialize OpenAI client
const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });

// Shared interface for course structure
interface CourseStructure {
  title: string;
  description: string;
  modules: {
    title: string;
    description: string;
    lessons: {
      title: string;
      description: string;
    }[];
  }[];
}

/**
 * Generate course structure using only OpenAI
 */
export async function generateCourseStructureWithOpenAI(
  courseTitle: string,
  courseDescription: string,
  category: string,
  moduleCount?: number
): Promise<CourseStructure> {
  // The newest OpenAI model is "gpt-4o" which was released May 13, 2024. do not change this unless explicitly requested by the user
  const response = await openai.chat.completions.create({
    model: "gpt-4o",
    messages: [
      {
        role: "system",
        content: `You are an expert course creator specialized in creating well-structured educational content. 
        Your task is to create a detailed course structure for a course about "${courseTitle}" in the category "${category}".
        The course should include exactly ${moduleCount || 4} module${(moduleCount || 4) === 1 ? '' : 's'}, each with 3-6 lessons. Each module and lesson should have a clear title and description.
        Return your response as a JSON object.`
      },
      {
        role: "user",
        content: `I need a detailed course structure for my course titled "${courseTitle}" with the description "${courseDescription}" in the category "${category}". 
        Please create a comprehensive structure with modules and lessons that provides a complete learning journey.
        
        Return the response in JSON format with this structure:
        {
          "modules": [
            {
              "title": "Module Title",
              "description": "Module description text",
              "lessons": [
                {
                  "title": "Lesson Title",
                  "description": "Lesson description text"
                }
              ]
            }
          ]
        }`
      }
    ],
    response_format: { type: "json_object" },
    temperature: 0.7,
  });

  try {
    // Parse the JSON response
    const content = response.choices[0].message.content || '{"modules":[]}';
    const structureData = JSON.parse(content);
    
    // Format as CourseStructure
    return {
      title: courseTitle,
      description: courseDescription,
      modules: structureData.modules || []
    };
  } catch (error) {
    console.error("Error parsing course structure from OpenAI:", error);
    // Return a basic structure if parsing fails
    return {
      title: courseTitle,
      description: courseDescription,
      modules: [
        {
          title: "Introduction to " + courseTitle,
          description: "Get started with the basics of " + courseTitle,
          lessons: [
            {
              title: "Course Overview",
              description: "A brief introduction to the course content"
            },
            {
              title: "Key Concepts",
              description: "Essential concepts you need to understand for this course"
            }
          ]
        }
      ]
    };
  }
}

/**
 * Generate script for a lesson using only OpenAI
 */
export async function generateLessonScriptWithOpenAI(
  courseTitle: string, 
  courseDescription: string,
  moduleTitle: string,
  moduleDescription: string,
  lessonTitle: string,
  lessonDescription: string,
  targetAudience: string
): Promise<string> {
  try {
    // The newest OpenAI model is "gpt-4o" which was released May 13, 2024. do not change this unless explicitly requested by the user
    const response = await openai.chat.completions.create({
      model: "gpt-4o",
      messages: [
        {
          role: "system",
          content: `You are an expert course content creator specialized in writing educational scripts.
          Your task is to create a detailed script for a lesson titled "${lessonTitle}" which is part of the module "${moduleTitle}" 
          in the course "${courseTitle}". The script should be comprehensive, engaging, and educational, 
          targeted at ${targetAudience}.`
        },
        {
          role: "user",
          content: `Please write a detailed script for the lesson "${lessonTitle}" with description "${lessonDescription}".
          This lesson is part of the module "${moduleTitle}" (${moduleDescription}) in my course "${courseTitle}" (${courseDescription}).
          The target audience is: ${targetAudience}.
          The script should be educational, engaging, and cover all necessary aspects of the topic.
          Format it as a complete lesson script with introduction, main content points, and conclusion.`
        }
      ],
      temperature: 0.7,
    });

    return response.choices[0].message.content || "Script generation failed. Please try again.";
  } catch (error) {
    console.error("Error generating lesson script with OpenAI:", error);
    return `# ${lessonTitle}\n\n## Introduction\n\nWelcome to this lesson on ${lessonTitle}. In this lesson, we will explore ${lessonDescription}.\n\n## Content\n\n[This is a placeholder for the lesson content. The content generation failed. Please try again or edit this script manually.]\n\n## Conclusion\n\nThank you for completing this lesson.`;
  }
}

/**
 * Generate a quiz using only OpenAI
 */
export async function generateQuizWithOpenAI(
  courseTitle: string,
  courseDescription: string,
  lessonScript?: string,
  numQuestions: number = 5,
  includeFlashcards: boolean = true,
  includeSummary: boolean = true,
  difficulty: string = "medium"
): Promise<GeneratedQuiz> {
  try {
    // The newest OpenAI model is "gpt-4o" which was released May 13, 2024. do not change this unless explicitly requested by the user
    const response = await openai.chat.completions.create({
      model: "gpt-4o",
      messages: [
        {
          role: "system",
          content: `You are an expert educational assessment creator. Your task is to create a quiz for a course titled "${courseTitle}".
          The quiz should include multiple-choice questions, true/false questions, and short-answer questions at a ${difficulty} difficulty level.
          Each question should have an explanation for the correct answer.
          ${includeFlashcards ? 'Also include flashcards that help students remember key concepts.' : 'Do not include flashcards.'}
          ${includeSummary ? 'Include a detailed summary of the key points.' : 'No summary is required.'}`
        },
        {
          role: "user",
          content: `Please create a ${difficulty} difficulty quiz for my course "${courseTitle}" with the description "${courseDescription}".
          ${lessonScript ? `I'm providing the lesson script to help you create relevant questions: ${lessonScript}` : ''}
          Create ${numQuestions} diverse questions (multiple-choice, true/false, short-answer).
          ${includeFlashcards ? 'Include 3-5 flashcards to help students remember key concepts.' : 'Do not include flashcards.'}
          ${includeSummary ? 'Include a comprehensive summary of the key points covered in the quiz.' : 'No summary is needed.'}
          
          Return the quiz in a JSON format that includes:
          - title: the quiz title
          - description: brief description of the quiz
          - questions: array of questions with questionType, questionText, explanation, points, and an array of answers (each with answerText and isCorrect)
          ${includeFlashcards ? '- flashcards: array of flashcards with front and back content' : ''}
          ${includeSummary ? '- summary: a comprehensive summary of key concepts' : ''}
          - passingScore: recommended passing score (percentage) for this ${difficulty} difficulty level
          - timeLimit: recommended time limit in seconds
          - randomizeQuestions: boolean recommendation for randomizing questions
          - showCorrectAnswers: boolean recommendation for showing correct answers after quiz`
        }
      ],
      response_format: { type: "json_object" },
      temperature: 0.7,
    });

    const content = response.choices[0].message.content || '{"questions":[], "flashcards":[]}';
    const quizData = JSON.parse(content);
    
    // Process to ensure it matches the expected GeneratedQuiz format
    const processedQuiz: GeneratedQuiz = {
      title: quizData.title || `Quiz for ${courseTitle}`,
      description: quizData.description || `Test your knowledge of ${courseTitle}`,
      questions: quizData.questions.map((q: any, index: number) => ({
        questionType: q.questionType || "multiple_choice",
        questionText: q.questionText || `Question ${index + 1}`,
        explanation: q.explanation || "",
        points: q.points || 1,
        answers: (q.answers || []).map((a: any) => ({
          answerText: a.answerText || "",
          isCorrect: a.isCorrect || false,
          explanation: a.explanation || ""
        }))
      })),
      flashcards: (quizData.flashcards || []).map((f: any, index: number) => ({
        front: f.front || `Flashcard ${index + 1} front`,
        back: f.back || `Flashcard ${index + 1} back`,
        category: f.category || "General"
      })),
      summary: quizData.summary || `This quiz covers the main concepts from ${courseTitle}.`,
      passingScore: quizData.passingScore || 70,
      timeLimit: quizData.timeLimit || 600,
      randomizeQuestions: quizData.randomizeQuestions !== undefined ? quizData.randomizeQuestions : true,
      showCorrectAnswers: quizData.showCorrectAnswers !== undefined ? quizData.showCorrectAnswers : true
    };

    return processedQuiz;
  } catch (error) {
    console.error("Error generating quiz with OpenAI:", error);
    
    // Return a basic fallback quiz
    return {
      title: `Quiz for ${courseTitle}`,
      description: `Test your knowledge of ${courseTitle}`,
      questions: [
        {
          questionType: "multiple_choice",
          questionText: `What is the main focus of ${courseTitle}?`,
          explanation: "This checks your understanding of the course's main topic.",
          points: 1,
          answers: [
            { answerText: "The course description", isCorrect: true },
            { answerText: "Something unrelated", isCorrect: false },
            { answerText: "A different topic", isCorrect: false }
          ]
        },
        {
          questionType: "true_false",
          questionText: `This course is about ${courseTitle}.`,
          explanation: "Checking basic course understanding.",
          points: 1,
          answers: [
            { answerText: "True", isCorrect: true },
            { answerText: "False", isCorrect: false }
          ]
        }
      ],
      flashcards: [
        {
          front: "What is this course about?",
          back: courseTitle,
          category: "Basic"
        }
      ],
      summary: "This quiz covers basic concepts related to the course.",
      passingScore: 70,
      timeLimit: 600,
      randomizeQuestions: true,
      showCorrectAnswers: true
    };
  }
}

/**
 * Generate assistant chat response using OpenAI
 */
export async function generateAssistantChatResponse(
  message: string,
  history: { role: 'user' | 'assistant', content: string }[] = []
): Promise<string> {
  try {
    // Create a system message that defines the assistant's role
    const systemMessage = {
      role: "system",
      content: `You are an AI learning assistant for a course creation platform. 
      Your primary role is to help users create and manage online courses.
      
      You can help with:
      - Course structure and organization
      - Content creation ideas and best practices
      - Feature usage (text-to-speech, AI video generation, course templates)
      - Platform navigation
      - Learning methodologies
      
      Be friendly, concise, and helpful. Focus on practical advice that users can implement right away.
      If you're not sure about something, be honest about limitations.`
    };
    
    // Format the messages for OpenAI API
    const messages = [
      { role: "system", content: systemMessage.content },
      ...history.map(msg => ({
        role: msg.role === 'user' ? 'user' : 'assistant',
        content: msg.content
      })),
      { role: "user", content: message }
    ];
    
    // The newest OpenAI model is "gpt-4o" which was released May 13, 2024. do not change this unless explicitly requested by the user
    const response = await openai.chat.completions.create({
      model: "gpt-4o",
      messages,
      temperature: 0.7,
      max_tokens: 500
    });

    return response.choices[0].message.content || "I'm sorry, I couldn't generate a response. Please try again.";
  } catch (error) {
    console.error("Error generating assistant chat response with OpenAI:", error);
    return "I'm sorry, I'm having trouble connecting to my knowledge base right now. Please try again later.";
  }
}

/**
 * Generate module structure using OpenAI
 */
export async function generateModuleStructureWithOpenAI(
  courseTitle: string,
  courseDescription: string,
  existingModules: string[] = [],
  moduleTitle?: string,
  moduleIndex?: number,
  totalModules?: number
): Promise<{ title: string; description: string }> {
  try {
    // The newest OpenAI model is "gpt-4o" which was released May 13, 2024. do not change this unless explicitly requested by the user
    const response = await openai.chat.completions.create({
      model: "gpt-4o",
      messages: [
        {
          role: "system",
          content: `You are an expert course designer specializing in creating educational modules.
          Your task is to create a single module for a course titled "${courseTitle}".
          ${moduleIndex !== undefined && totalModules !== undefined 
            ? `This is module ${moduleIndex} out of ${totalModules} total modules.` 
            : ''}
          The module should have a clear title and detailed description that fits logically with the course topic.
          If existing module titles are provided, create a module that complements them without repeating content.`
        },
        {
          role: "user",
          content: `Please create a single module for my course titled "${courseTitle}" with description "${courseDescription}".
          ${existingModules.length > 0 
            ? `The course already has these modules: ${existingModules.join(", ")}. Create a module that fits well with these.` 
            : 'This will be the first module in the course.'}
          ${moduleTitle 
            ? `I've already chosen the title "${moduleTitle}" for this module. Please just provide a suitable description.` 
            : 'Please provide both a title and description for this module.'}
          ${moduleIndex !== undefined && totalModules !== undefined 
            ? `This is module ${moduleIndex} out of ${totalModules}. Position it appropriately in the learning sequence.` 
            : ''}
          
          Return the module in JSON format with this structure:
          {
            "title": "Module Title",
            "description": "Module description text that thoroughly explains what the module covers"
          }`
        }
      ],
      response_format: { type: "json_object" },
      temperature: 0.7,
    });

    // Parse the JSON response
    const content = response.choices[0].message.content || '{}';
    try {
      const moduleData = JSON.parse(content);
      
      return {
        title: moduleTitle || moduleData.title || `Module for ${courseTitle}`,
        description: moduleData.description || `A comprehensive module covering important topics in ${courseTitle}.`
      };
    } catch (parseError) {
      console.error("Error parsing module structure JSON from OpenAI:", parseError);
      
      // If we can't parse JSON, extract text between quotes
      const titleMatch = content.match(/"title":\s*"([^"]+)"/);
      const descMatch = content.match(/"description":\s*"([^"]+)"/);
      
      return {
        title: moduleTitle || (titleMatch ? titleMatch[1] : `Module for ${courseTitle}`),
        description: descMatch ? descMatch[1] : `A comprehensive module covering important topics in ${courseTitle}.`
      };
    }
  } catch (error) {
    console.error("Error generating module structure with OpenAI:", error);
    // Return a fallback module if API fails
    return {
      title: moduleTitle || `Module ${moduleIndex || ''} for ${courseTitle}`,
      description: `A comprehensive module covering important topics related to ${courseTitle}.`
    };
  }
}

export async function generateFullCourseWithOpenAI(
  courseTitle: string,
  courseDescription: string,
  category: string
) {
  // Step 1: Generate course structure
  const courseStructure = await generateCourseStructureWithOpenAI(courseTitle, courseDescription, category);
  
  // Step 2: Generate scripts for each lesson
  const courseScripts: Record<string, Record<string, string>> = {};
  
  for (let moduleIndex = 0; moduleIndex < courseStructure.modules.length; moduleIndex++) {
    const module = courseStructure.modules[moduleIndex];
    courseScripts[`module-${moduleIndex}`] = {};
    
    for (let lessonIndex = 0; lessonIndex < module.lessons.length; lessonIndex++) {
      const lesson = module.lessons[lessonIndex];
      
      const script = await generateLessonScriptWithOpenAI(
        courseTitle, 
        courseDescription,
        module.title,
        module.description,
        lesson.title,
        lesson.description,
        "General audience" // Default target audience
      );
      
      courseScripts[`module-${moduleIndex}`][`lesson-${lessonIndex}`] = script;
    }
  }
  
  // Step 3: Generate a quiz
  const combinedScripts = Object.values(courseScripts)
    .flatMap(module => Object.values(module))
    .join("\n\n");
    
  const quiz = await generateQuizWithOpenAI(
    courseTitle,
    courseDescription,
    combinedScripts,
    courseStructure.modules.length + 3, // Generate a reasonable number of questions
    true, // Include flashcards
    true, // Include summary
    "medium" // Medium difficulty
  );
  
  // Return the full course package
  return {
    courseStructure,
    courseScripts,
    quiz
  };
}