import { <PERSON> } from "wouter";
import { <PERSON>, Card<PERSON>ontent, CardDescription, <PERSON><PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Mail, Newspaper, BarChart3, Globe, Users, ArrowRight } from "lucide-react";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { useEffect, useState } from "react";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";

export default function MarketingPage() {
  const { toast } = useToast();
  const [courses, setCourses] = useState([]);
  
  useEffect(() => {
    const fetchCourses = async () => {
      try {
        const response = await apiRequest("GET", "/api/courses");
        const data = await response.json();
        setCourses(data || []);
      } catch (error) {
        console.error("Error fetching courses:", error);
        toast({
          variant: "destructive",
          title: "Error",
          description: "Failed to fetch courses. Please try again later.",
        });
      }
    };
    
    fetchCourses();
  }, [toast]);

  const marketingFeatures = [
    {
      icon: <Mail className="h-10 w-10 text-pink-500" />,
      title: "Email Marketing",
      description: "Create and manage email campaigns to promote your courses and engage with your audience.",
      link: "/marketing/email",
      bgColor: "bg-pink-50",
      hoverColor: "hover:bg-pink-100",
    },
    {
      icon: <Newspaper className="h-10 w-10 text-pink-500" />,
      title: "Landing Pages",
      description: "Generate AI-powered landing pages for your courses to boost conversions and enrollments.",
      link: "/marketing/landing-page",
      bgColor: "bg-pink-50",
      hoverColor: "hover:bg-pink-100",
    },
    {
      icon: <BarChart3 className="h-10 w-10 text-pink-500" />,
      title: "Analytics & Reports",
      description: "Track your marketing performance and get insights into your audience engagement.",
      link: "/marketing/analytics",
      bgColor: "bg-pink-50",
      hoverColor: "hover:bg-pink-100",
    },
    {
      icon: <Globe className="h-10 w-10 text-gray-500" />,
      title: "Social Media",
      description: "Manage social media campaigns and schedule posts to promote your courses.",
      link: "/marketing/social-media",
      bgColor: "bg-gray-50",
      hoverColor: "hover:bg-gray-100",
      comingSoon: true,
    },
    {
      icon: <Users className="h-10 w-10 text-gray-500" />,
      title: "Audience Management",
      description: "Manage your subscriber lists and segment your audience for targeted marketing.",
      link: "/marketing/audience",
      bgColor: "bg-gray-50",
      hoverColor: "hover:bg-gray-100",
      comingSoon: true,
    },
  ];

  return (
    <div className="container max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Marketing Hub</h1>
          <p className="mt-2 text-lg text-gray-600">Promote your courses and grow your audience</p>
        </div>
        <div className="mt-4 md:mt-0">
          <Button asChild className="bg-pink-600 hover:bg-pink-700">
            <Link href="/marketing/email">
              <Mail className="mr-2 h-4 w-4" />
              Create Email Campaign
            </Link>
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
        {marketingFeatures.map((feature, index) => (
          <Card 
            key={index} 
            className={`${feature.bgColor} border-0 transition-all ${feature.hoverColor} ${feature.comingSoon ? 'opacity-70' : ''}`}
          >
            <CardHeader>
              <div className="mb-4">{feature.icon}</div>
              <CardTitle className="text-xl font-bold">{feature.title}</CardTitle>
              <CardDescription className="text-gray-700">
                {feature.description}
              </CardDescription>
            </CardHeader>
            <CardFooter className="pt-0">
              {feature.comingSoon ? (
                <div className="text-sm font-medium text-gray-500 bg-gray-200 px-3 py-1 rounded-full">
                  Coming Soon
                </div>
              ) : (
                <Button asChild variant="outline" className="mt-2 border-pink-200 hover:bg-pink-100 hover:text-pink-700">
                  <Link href={feature.link}>
                    <span>Get Started</span>
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
              )}
            </CardFooter>
          </Card>
        ))}
      </div>

      <div className="mb-12">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Your Courses to Promote</h2>
        
        {courses.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {courses.slice(0, 3).map((course) => (
              <Card key={course.id} className="border border-gray-200">
                <CardHeader className="pb-2">
                  <div className="h-40 bg-gray-100 rounded-md mb-4 overflow-hidden">
                    {course.thumbnailUrl ? (
                      <img 
                        src={course.thumbnailUrl} 
                        alt={course.title} 
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center text-gray-400">
                        No Thumbnail
                      </div>
                    )}
                  </div>
                  <CardTitle className="text-lg">{course.title}</CardTitle>
                </CardHeader>
                <CardContent className="pb-2">
                  <p className="text-sm text-gray-500 line-clamp-2">
                    {course.description || "No description available"}
                  </p>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button variant="outline" size="sm" asChild>
                    <Link href={`/courses/${course.id}`}>
                      View Course
                    </Link>
                  </Button>
                  <Button 
                    className="bg-pink-600 hover:bg-pink-700" 
                    size="sm"
                    asChild
                  >
                    <Link href={`/marketing/landing-page?courseId=${course.id}`}>
                      Create Landing Page
                    </Link>
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>
        ) : (
          <div className="bg-gray-50 rounded-lg p-8 text-center">
            <h3 className="text-lg font-medium text-gray-900 mb-2">No courses found</h3>
            <p className="text-gray-600 mb-4">Create courses to start promoting them with our marketing tools.</p>
            <Button asChild>
              <Link href="/courses/create">
                Create a Course
              </Link>
            </Button>
          </div>
        )}
      </div>

      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Marketing Performance</h2>
        <Tabs defaultValue="overview">
          <TabsList className="mb-6">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="campaigns">Campaigns</TabsTrigger>
            <TabsTrigger value="subscribers">Subscribers</TabsTrigger>
          </TabsList>
          
          <TabsContent value="overview">
            <div className="bg-white p-6 rounded-lg border border-gray-200">
              <div className="text-center py-12">
                <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Marketing Analytics</h3>
                <p className="text-gray-600 mb-4 max-w-md mx-auto">
                  Start your marketing campaigns to see analytics and performance metrics here.
                </p>
                <Button asChild className="bg-pink-600 hover:bg-pink-700">
                  <Link href="/marketing/email">
                    Create First Campaign
                  </Link>
                </Button>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="campaigns">
            <div className="bg-white p-6 rounded-lg border border-gray-200">
              <div className="text-center py-12">
                <Mail className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No Campaigns Yet</h3>
                <p className="text-gray-600 mb-4 max-w-md mx-auto">
                  Create your first email campaign to start promoting your courses.
                </p>
                <Button asChild className="bg-pink-600 hover:bg-pink-700">
                  <Link href="/marketing/email">
                    Create First Campaign
                  </Link>
                </Button>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="subscribers">
            <div className="bg-white p-6 rounded-lg border border-gray-200">
              <div className="text-center py-12">
                <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No Subscribers Yet</h3>
                <p className="text-gray-600 mb-4 max-w-md mx-auto">
                  Use our marketing tools to build your subscriber base and grow your audience.
                </p>
                <Button asChild className="bg-pink-600 hover:bg-pink-700">
                  <Link href="/marketing/email">
                    Set Up Email Marketing
                  </Link>
                </Button>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
