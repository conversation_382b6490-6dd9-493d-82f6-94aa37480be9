import { useState, useEffect } from "react";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { 
  Loader2, ArrowRight, ArrowLeft, BrainCircuit, Mic, Image, Video, 
  Sparkles, FileText, CheckCircle2, BookOpen, PenTool, FileCheck, 
  Save, RefreshCw, AlertCircle, Users, Play, Puzzle, ScrollText, ImageIcon
} from "lucide-react";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Checkbox } from "@/components/ui/checkbox";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import TextGenerationTool from "@/components/ai-tools/TextGenerationTool";
import TextToSpeechTool from "@/components/ai-tools/TextToSpeechTool";
import ImageGenerationTool from "@/components/ai-tools/ImageGenerationTool";
import AnimationGenerationTool from "@/components/ai-tools/AnimationGenerationTool";
import { useLocation } from "wouter";
import TeamCourseCollaboration from "@/components/collaboration/TeamCourseCollaboration";

// Interface for generatable content
interface ContentItem {
  id: string;
  title: string;
  content: string;
  status: 'pending' | 'generating' | 'completed' | 'failed';
  type: 'script' | 'narration' | 'image' | 'animation';
  mediaUrl?: string;
  error?: string;
}

// Interface for AI Credits
interface AICreditInfo {
  cost: number;
  available: number;
  sufficient: boolean;
}

// Interface for AI Job status
interface AIJobStatus {
  id: string;
  status: 'queued' | 'processing' | 'completed' | 'failed';
  progress: number;
  result?: {
    url?: string;
    text?: string;
    [key: string]: any;
  };
  error?: string;
}

export default function CourseStudio() {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState<"script" | "narration" | "visuals" | "integration" | "preview" | "collaboration">("script");
  const [courseTitle, setCourseTitle] = useState("");
  const [courseDescription, setCourseDescription] = useState("");
  const [contentItems, setContentItems] = useState<ContentItem[]>([]);
  const [selectedContentId, setSelectedContentId] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [creditInfo, setCreditInfo] = useState<AICreditInfo>({
    cost: 0,
    available: 0,
    sufficient: false
  });
  const [location, setLocation] = useLocation();
  const [showApiWarning, setShowApiWarning] = useState(false);
  const [courseId, setCourseId] = useState<number | null>(null);
  const [teamId, setTeamId] = useState<number | null>(null);
  
  // Extract course ID from URL if present
  useEffect(() => {
    const match = /\/course-studio\/([0-9]+)/.exec(location);
    if (match && match[1]) {
      setCourseId(parseInt(match[1], 10));
    }
  }, [location]);

  // Check API configuration on mount
  useEffect(() => {
    const checkApiConfig = async () => {
      try {
        const response = await apiRequest('GET', '/api/ai-tools/health');
        if (!response.ok) {
          setShowApiWarning(true);
        } else {
          const data = await response.json();
          // Check if all required endpoints are configured
          // We need all four: Coqui TTS, Kokoro TTS, SadTalker, and Marp
          if (data.status !== 'ok') {
            setShowApiWarning(true);
            console.log("AI Tools configuration incomplete. Status:", data.status);
            console.log("Missing endpoints:", 
              (!data.endpoints?.coqui ? 'Coqui TTS, ' : '') +
              (!data.endpoints?.kokoro ? 'Kokoro TTS, ' : '') +
              (!data.endpoints?.sadtalker ? 'SadTalker, ' : '') +
              (!data.endpoints?.marp ? 'Marp' : '')
            );
          }
        }
      } catch (error) {
        console.error("Error checking AI tools health:", error);
        setShowApiWarning(true);
      }
    };

    checkApiConfig();
  }, []);

  // Get AI credits
  const { data: credits, isLoading: isLoadingCredits } = useQuery({
    queryKey: ['/api/credits'],
    queryFn: async () => {
      try {
        const response = await apiRequest('GET', '/api/credits');
        const data = await response.json();
        return {
          available: data.available || 0,
          used: data.used || 0,
          subscriptionType: data.subscriptionType || 'free'
        };
      } catch (error) {
        console.error("Failed to fetch credits:", error);
        return { available: 0, used: 0, subscriptionType: 'free' };
      }
    }
  });

  // Update credit info when credits data changes
  useEffect(() => {
    if (credits) {
      const estimatedCost = calculateCreditCost();
      setCreditInfo({
        cost: estimatedCost,
        available: credits.available,
        sufficient: credits.available >= estimatedCost
      });
    }
  }, [credits, contentItems]);

  // Mutation for script generation
  const scriptGenerationMutation = useMutation({
    mutationFn: async ({ title, description }: { title: string, description: string }) => {
      // Create a script content item
      const scriptItem: ContentItem = {
        id: `script-${Date.now()}`,
        title: `Script for ${title}`,
        content: '',
        status: 'generating',
        type: 'script'
      };
      
      // Add to content items
      setContentItems(prev => [...prev, scriptItem]);
      
      try {
        // Call the API endpoint for text generation
        const response = await apiRequest('POST', '/api/ai-tools/generate-text', {
          prompt: `Create an educational script for a course titled "${title}". ${description ? `The course is about: ${description}` : ''}`,
          maxLength: 2000,
          temperature: 0.7,
          model: 'mistral'
        });
        
        if (!response.ok) {
          throw new Error('Failed to generate script');
        }
        
        const data = await response.json();
        
        // Return updated content item
        return {
          ...scriptItem,
          content: data.text,
          status: 'completed'
        };
      } catch (error: any) {
        return {
          ...scriptItem,
          status: 'failed',
          error: error.message || 'Failed to generate script'
        };
      }
    },
    onSuccess: (updatedItem) => {
      // Update content items with the completed script
      setContentItems(prev => 
        prev.map(item => item.id === updatedItem.id ? updatedItem : item)
      );
      
      // Select the script for editing
      setSelectedContentId(updatedItem.id);
      
      toast({
        title: 'Script Generated',
        description: 'Your script has been generated successfully.'
      });
    },
    onError: (error: Error, variables) => {
      // Handle error by updating the content item status
      setContentItems(prev => 
        prev.map(item => 
          item.id === `script-${Date.now()}` 
            ? { ...item, status: 'failed', error: error.message } 
            : item
        )
      );
      
      toast({
        title: 'Script Generation Failed',
        description: error.message || 'Failed to generate script',
        variant: 'destructive'
      });
    }
  });

  // Mutation for narration generation
  const narrationGenerationMutation = useMutation({
    mutationFn: async ({ text, voice }: { text: string, voice: string }) => {
      // Create a narration content item
      const narrationItem: ContentItem = {
        id: `narration-${Date.now()}`,
        title: `Narration for ${courseTitle}`,
        content: text,
        status: 'generating',
        type: 'narration'
      };
      
      // Add to content items
      setContentItems(prev => [...prev, narrationItem]);
      
      try {
        // Call the API endpoint for text-to-speech
        const response = await apiRequest('POST', '/api/ai-tools/text-to-speech', {
          text,
          voice,
          model: 'coqui'
        });
        
        if (!response.ok) {
          throw new Error('Failed to generate narration');
        }
        
        const data = await response.json();
        
        // Return updated content item
        return {
          ...narrationItem,
          mediaUrl: data.audioUrl,
          status: 'completed'
        };
      } catch (error: any) {
        return {
          ...narrationItem,
          status: 'failed',
          error: error.message || 'Failed to generate narration'
        };
      }
    },
    onSuccess: (updatedItem) => {
      // Update content items with the completed narration
      setContentItems(prev => 
        prev.map(item => item.id === updatedItem.id ? updatedItem : item)
      );
      
      // Select the narration
      setSelectedContentId(updatedItem.id);
      
      toast({
        title: 'Narration Generated',
        description: 'Your narration has been generated successfully.'
      });
    },
    onError: (error: Error) => {
      // Handle error by updating the content item status
      setContentItems(prev => 
        prev.map(item => 
          item.id === `narration-${Date.now()}` 
            ? { ...item, status: 'failed', error: error.message } 
            : item
        )
      );
      
      toast({
        title: 'Narration Generation Failed',
        description: error.message || 'Failed to generate narration',
        variant: 'destructive'
      });
    }
  });

  // Mutation for image generation
  const imageGenerationMutation = useMutation({
    mutationFn: async ({ prompt, style }: { prompt: string, style: string }) => {
      // Create an image content item
      const imageItem: ContentItem = {
        id: `image-${Date.now()}`,
        title: `Image for ${prompt.substring(0, 30)}...`,
        content: prompt,
        status: 'generating',
        type: 'image'
      };
      
      // Add to content items
      setContentItems(prev => [...prev, imageItem]);
      
      try {
        // Call the API endpoint for image generation
        const response = await apiRequest('POST', '/api/ai-tools/generate-image', {
          prompt,
          style,
          model: 'kandinsky'
        });
        
        if (!response.ok) {
          throw new Error('Failed to generate image');
        }
        
        const data = await response.json();
        
        // Return updated content item
        return {
          ...imageItem,
          mediaUrl: data.imageUrl,
          status: 'completed'
        };
      } catch (error: any) {
        return {
          ...imageItem,
          status: 'failed',
          error: error.message || 'Failed to generate image'
        };
      }
    },
    onSuccess: (updatedItem) => {
      // Update content items with the completed image
      setContentItems(prev => 
        prev.map(item => item.id === updatedItem.id ? updatedItem : item)
      );
      
      // Select the image
      setSelectedContentId(updatedItem.id);
      
      toast({
        title: 'Image Generated',
        description: 'Your image has been generated successfully.'
      });
    },
    onError: (error: Error) => {
      // Handle error by updating the content item status
      setContentItems(prev => 
        prev.map(item => 
          item.id === `image-${Date.now()}` 
            ? { ...item, status: 'failed', error: error.message } 
            : item
        )
      );
      
      toast({
        title: 'Image Generation Failed',
        description: error.message || 'Failed to generate image',
        variant: 'destructive'
      });
    }
  });

  // Mutation for animation generation
  const animationGenerationMutation = useMutation({
    mutationFn: async ({ prompt, style }: { prompt: string, style: string }) => {
      // Create an animation content item
      const animationItem: ContentItem = {
        id: `animation-${Date.now()}`,
        title: `Animation for ${prompt.substring(0, 30)}...`,
        content: prompt,
        status: 'generating',
        type: 'animation'
      };
      
      // Add to content items
      setContentItems(prev => [...prev, animationItem]);
      
      try {
        // Call the API endpoint for animation generation
        const response = await apiRequest('POST', '/api/ai-tools/generate-animation', {
          prompt,
          style,
          model: 'animateDiff'
        });
        
        if (!response.ok) {
          throw new Error('Failed to generate animation');
        }
        
        const data = await response.json();
        
        // Return updated content item
        return {
          ...animationItem,
          mediaUrl: data.animationUrl,
          status: 'completed'
        };
      } catch (error: any) {
        return {
          ...animationItem,
          status: 'failed',
          error: error.message || 'Failed to generate animation'
        };
      }
    },
    onSuccess: (updatedItem) => {
      // Update content items with the completed animation
      setContentItems(prev => 
        prev.map(item => item.id === updatedItem.id ? updatedItem : item)
      );
      
      // Select the animation
      setSelectedContentId(updatedItem.id);
      
      toast({
        title: 'Animation Generated',
        description: 'Your animation has been generated successfully.'
      });
    },
    onError: (error: Error) => {
      // Handle error by updating the content item status
      setContentItems(prev => 
        prev.map(item => 
          item.id === `animation-${Date.now()}` 
            ? { ...item, status: 'failed', error: error.message } 
            : item
        )
      );
      
      toast({
        title: 'Animation Generation Failed',
        description: error.message || 'Failed to generate animation',
        variant: 'destructive'
      });
    }
  });

  // Calculate the estimated credit cost for all content items
  const calculateCreditCost = () => {
    let totalCost = 0;
    
    // Base cost for script generation
    totalCost += 50; // Script generation costs 50 credits
    
    // Cost for narration based on word count
    const scriptItems = contentItems.filter(item => item.type === 'script');
    if (scriptItems.length > 0) {
      const scriptContent = scriptItems[0].content;
      const wordCount = scriptContent ? scriptContent.split(/\s+/).length : 0;
      const narrationCost = Math.ceil(wordCount / 100); // 1 credit per 100 words
      totalCost += narrationCost;
    }
    
    // Cost for visuals
    const imagesCount = contentItems.filter(item => item.type === 'image').length;
    totalCost += imagesCount * 5; // 5 credits per image
    
    const animationsCount = contentItems.filter(item => item.type === 'animation').length;
    totalCost += animationsCount * 20; // 20 credits per animation
    
    return totalCost;
  };

  // Handle script generation
  const handleGenerateScript = () => {
    if (!courseTitle) {
      toast({
        title: 'Course Title Required',
        description: 'Please enter a title for your course.',
        variant: 'destructive'
      });
      return;
    }
    
    scriptGenerationMutation.mutate({ 
      title: courseTitle, 
      description: courseDescription 
    });
  };

  // Handle narration generation
  const handleGenerateNarration = (text: string, voice: string = 'default') => {
    narrationGenerationMutation.mutate({ text, voice });
  };

  // Handle image generation
  const handleGenerateImage = (prompt: string, style: string = 'default') => {
    imageGenerationMutation.mutate({ prompt, style });
  };

  // Handle animation generation
  const handleGenerateAnimation = (prompt: string, style: string = 'default') => {
    animationGenerationMutation.mutate({ prompt, style });
  };

  // Get selected content item
  const selectedContent = contentItems.find(item => item.id === selectedContentId) || null;

  // Handle editing content item
  const handleEditContent = (content: string) => {
    if (selectedContentId) {
      setContentItems(prev => 
        prev.map(item => 
          item.id === selectedContentId 
            ? { ...item, content } 
            : item
        )
      );
    }
  };

  // Handle navigation between tabs
  const handleNext = () => {
    const tabOrder: Array<"script" | "narration" | "visuals" | "integration" | "preview" | "collaboration"> = [
      "script", "narration", "visuals", "integration", "preview", "collaboration"
    ];
    const currentIndex = tabOrder.indexOf(activeTab);
    if (currentIndex < tabOrder.length - 1) {
      setActiveTab(tabOrder[currentIndex + 1]);
    }
  };

  const handlePrevious = () => {
    const tabOrder: Array<"script" | "narration" | "visuals" | "integration" | "preview" | "collaboration"> = [
      "script", "narration", "visuals", "integration", "preview", "collaboration"
    ];
    const currentIndex = tabOrder.indexOf(activeTab);
    if (currentIndex > 0) {
      setActiveTab(tabOrder[currentIndex - 1]);
    }
  };

  // Handle creating a course from the content
  const handleCreateCourse = async () => {
    try {
      setIsProcessing(true);
      
      // Get script and narration from content items
      const scriptItem = contentItems.find(item => item.type === 'script');
      const narrationItem = contentItems.find(item => item.type === 'narration');
      
      // Get images and animations for visuals
      const imageItems = contentItems.filter(item => item.type === 'image' && item.status === 'completed');
      const animationItems = contentItems.filter(item => item.type === 'animation' && item.status === 'completed');
      
      if (!scriptItem) {
        throw new Error('No script found. Please generate a script first.');
      }
      
      // Create the course structure
      const courseStructure = {
        title: courseTitle,
        description: courseDescription,
        script: scriptItem.content,
        narration: narrationItem?.mediaUrl || null,
        visuals: [
          ...imageItems.map(item => ({ type: 'image', url: item.mediaUrl, prompt: item.content })),
          ...animationItems.map(item => ({ type: 'animation', url: item.mediaUrl, prompt: item.content }))
        ]
      };
      
      // Submit to the API to create the course
      const response = await apiRequest('POST', '/api/courses', courseStructure);
      
      if (!response.ok) {
        throw new Error('Failed to create course');
      }
      
      const newCourse = await response.json();
      
      toast({
        title: 'Course Created',
        description: 'Your course has been created successfully.'
      });
      
      // Navigate to the course editor
      setLocation(`/courses/${newCourse.id}/edit`);
    } catch (error: any) {
      toast({
        title: 'Course Creation Failed',
        description: error.message || 'Failed to create course',
        variant: 'destructive'
      });
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="container mx-auto p-4 space-y-6">
      <h1 className="text-3xl font-bold">Course Studio</h1>
      <p className="text-muted-foreground">
        Create professional course content using AI tools
      </p>
      
      {showApiWarning && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>AI Tools Configuration Required</AlertTitle>
          <AlertDescription>
            Course Studio requires four AI tools: Coqui TTS, Kokoro TTS, SadTalker, and Marp Slide Generator.
            <Button 
              variant="link" 
              onClick={() => setLocation('/ai-tools')}
              className="px-2 py-0"
            >
              Configure AI Tools
            </Button>
          </AlertDescription>
        </Alert>
      )}
      
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        <div className="lg:col-span-1 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Course Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="courseTitle">Course Title</Label>
                <Input
                  id="courseTitle"
                  value={courseTitle}
                  onChange={(e) => setCourseTitle(e.target.value)}
                  placeholder="Enter course title"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="courseDescription">Description</Label>
                <Textarea
                  id="courseDescription"
                  value={courseDescription}
                  onChange={(e) => setCourseDescription(e.target.value)}
                  placeholder="Enter course description"
                  rows={3}
                />
              </div>
              <Separator />
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm font-medium">AI Credits</span>
                  <span className="text-sm">{credits?.available || 0} available</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Estimated cost</span>
                  <span className="text-sm">{creditInfo.cost} credits</span>
                </div>
                <Progress value={(credits?.available || 0) > 0 ? (100 * creditInfo.cost) / (credits?.available || 1) : 100} 
                  className={creditInfo.sufficient ? "bg-green-100" : "bg-red-100"} />
              </div>
              <div className="pt-2">
                <span className="text-xs text-muted-foreground block mb-1">Content items:</span>
                <div className="space-y-1">
                  {contentItems.map((item) => (
                    <div 
                      key={item.id} 
                      className={`text-sm p-2 rounded flex justify-between items-center cursor-pointer ${
                        selectedContentId === item.id ? 'bg-primary/10 border border-primary/30' : 'border hover:bg-accent'
                      }`}
                      onClick={() => setSelectedContentId(item.id)}
                    >
                      <div className="flex items-center">
                        {item.type === 'script' && <FileText className="h-3 w-3 mr-1 text-blue-500" />}
                        {item.type === 'narration' && <Mic className="h-3 w-3 mr-1 text-purple-500" />}
                        {item.type === 'image' && <Image className="h-3 w-3 mr-1 text-amber-500" />}
                        {item.type === 'animation' && <Video className="h-3 w-3 mr-1 text-emerald-500" />}
                        <span className="truncate max-w-[120px]">{item.title}</span>
                      </div>
                      <Badge variant={
                        item.status === 'completed' ? 'default' : 
                        item.status === 'generating' ? 'outline' :
                        item.status === 'failed' ? 'destructive' : 'secondary'
                      } className="text-xs">
                        {item.status === 'completed' && <CheckCircle2 className="h-2 w-2 mr-1" />}
                        {item.status === 'generating' && <Loader2 className="h-2 w-2 mr-1 animate-spin" />}
                        {item.status === 'failed' && <AlertCircle className="h-2 w-2 mr-1" />}
                        {item.status}
                      </Badge>
                    </div>
                  ))}
                  {contentItems.length === 0 && (
                    <div className="text-sm text-muted-foreground italic">
                      No content items yet
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
          
          {selectedContent && (
            <Card>
              <CardHeader className="py-3">
                <CardTitle className="text-md">{selectedContent.title}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {selectedContent.type === 'script' && (
                  <div className="space-y-2">
                    <Textarea 
                      value={selectedContent.content}
                      onChange={(e) => handleEditContent(e.target.value)}
                      className="min-h-[150px] text-sm"
                    />
                  </div>
                )}
                
                {selectedContent.type === 'narration' && (
                  <div className="space-y-2">
                    <div className="text-sm mb-2">Generated audio:</div>
                    {selectedContent.mediaUrl ? (
                      <audio 
                        src={selectedContent.mediaUrl} 
                        controls 
                        className="w-full" 
                      />
                    ) : (
                      <div className="text-sm text-muted-foreground italic">
                        Audio not available
                      </div>
                    )}
                  </div>
                )}
                
                {selectedContent.type === 'image' && (
                  <div className="space-y-2">
                    <div className="text-sm mb-2">Generated image:</div>
                    {selectedContent.mediaUrl ? (
                      <img 
                        src={selectedContent.mediaUrl} 
                        alt={selectedContent.title}
                        className="w-full rounded-md border" 
                      />
                    ) : (
                      <div className="text-sm text-muted-foreground italic">
                        Image not available
                      </div>
                    )}
                  </div>
                )}
                
                {selectedContent.type === 'animation' && (
                  <div className="space-y-2">
                    <div className="text-sm mb-2">Generated animation:</div>
                    {selectedContent.mediaUrl ? (
                      <video 
                        src={selectedContent.mediaUrl} 
                        controls 
                        className="w-full rounded-md border" 
                      />
                    ) : (
                      <div className="text-sm text-muted-foreground italic">
                        Animation not available
                      </div>
                    )}
                  </div>
                )}
                
                {selectedContent.status === 'failed' && (
                  <Alert variant="destructive" className="py-2">
                    <AlertTitle className="text-xs">Error</AlertTitle>
                    <AlertDescription className="text-xs">
                      {selectedContent.error || 'Content generation failed'}
                    </AlertDescription>
                  </Alert>
                )}
              </CardContent>
            </Card>
          )}
        </div>
        
        <div className="lg:col-span-3">
          <Tabs value={activeTab} onValueChange={(value: string) => setActiveTab(value as any)}>
            <TabsList className="grid grid-cols-6 mb-4">
              <TabsTrigger value="script">
                <FileText className="h-4 w-4 mr-2" />
                Script
              </TabsTrigger>
              <TabsTrigger value="narration">
                <Mic className="h-4 w-4 mr-2" />
                Narration
              </TabsTrigger>
              <TabsTrigger value="visuals">
                <Image className="h-4 w-4 mr-2" />
                Visuals
              </TabsTrigger>
              <TabsTrigger value="integration">
                <PenTool className="h-4 w-4 mr-2" />
                Integration
              </TabsTrigger>
              <TabsTrigger value="preview">
                <BookOpen className="h-4 w-4 mr-2" />
                Preview
              </TabsTrigger>
              <TabsTrigger value="collaboration">
                <Users className="h-4 w-4 mr-2" />
                Collaboration
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value="script" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Script Generation</CardTitle>
                  <CardDescription>
                    Create or generate the script for your course
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex flex-col space-y-2">
                    <div className="flex justify-end">
                      <Button
                        variant="secondary"
                        onClick={handleGenerateScript}
                        disabled={!courseTitle || scriptGenerationMutation.isPending}
                        className="mb-4"
                      >
                        {scriptGenerationMutation.isPending ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Generating...
                          </>
                        ) : (
                          <>
                            <BrainCircuit className="mr-2 h-4 w-4" />
                            Generate Script
                          </>
                        )}
                      </Button>
                    </div>
                    
                    {/* Integrate with the TextGenerationTool */}
                    <TextGenerationTool 
                      onTextGenerated={(text) => {
                        const scriptItem: ContentItem = {
                          id: `script-${Date.now()}`,
                          title: `Script for ${courseTitle || 'Course'}`,
                          content: text,
                          status: 'completed',
                          type: 'script'
                        };
                        
                        setContentItems(prev => [
                          ...prev.filter(item => item.type !== 'script'),
                          scriptItem
                        ]);
                        
                        setSelectedContentId(scriptItem.id);
                        
                        toast({
                          title: 'Script Created',
                          description: 'Your script has been created successfully.'
                        });
                      }} 
                      lessonId={null}
                      courseId={null}
                    />
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button
                    variant="ghost"
                    onClick={() => {}}
                    disabled={true}
                  >
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    Previous
                  </Button>
                  <Button
                    onClick={handleNext}
                    disabled={!contentItems.some(item => item.type === 'script' && item.status === 'completed')}
                  >
                    Next
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </CardFooter>
              </Card>
            </TabsContent>
            
            <TabsContent value="narration" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Narration</CardTitle>
                  <CardDescription>
                    Convert your script into professional voice narration
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Integrate with the TextToSpeechTool */}
                  <TextToSpeechTool 
                    onAudioGenerated={(url) => {
                      // Get the script content
                      const scriptItem = contentItems.find(item => item.type === 'script');
                      
                      if (scriptItem) {
                        const narrationItem: ContentItem = {
                          id: `narration-${Date.now()}`,
                          title: `Narration for ${courseTitle || 'Course'}`,
                          content: scriptItem.content,
                          status: 'completed',
                          type: 'narration',
                          mediaUrl: url
                        };
                        
                        setContentItems(prev => [
                          ...prev.filter(item => item.type !== 'narration'),
                          narrationItem
                        ]);
                        
                        setSelectedContentId(narrationItem.id);
                        
                        toast({
                          title: 'Narration Created',
                          description: 'Your narration has been created successfully.'
                        });
                      } else {
                        toast({
                          title: 'Script Required',
                          description: 'Please create a script first before generating narration.',
                          variant: 'destructive'
                        });
                      }
                    }}
                    initialText={contentItems.find(item => item.type === 'script')?.content || ''}
                    lessonId={null}
                    courseId={null}
                  />
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button
                    variant="ghost"
                    onClick={handlePrevious}
                  >
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    Previous
                  </Button>
                  <Button
                    onClick={handleNext}
                  >
                    Next
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </CardFooter>
              </Card>
            </TabsContent>
            
            <TabsContent value="visuals" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Visual Content</CardTitle>
                  <CardDescription>
                    Generate images and animations for your course
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h3 className="text-lg font-medium mb-3">Image Generation</h3>
                      
                      {/* Integrate with the ImageGenerationTool */}
                      <ImageGenerationTool 
                        onImageGenerated={(url) => {
                          const imageItem: ContentItem = {
                            id: `image-${Date.now()}`,
                            title: `Image for ${courseTitle || 'Course'}`,
                            content: 'Generated image',
                            status: 'completed',
                            type: 'image',
                            mediaUrl: url
                          };
                          
                          setContentItems(prev => [...prev, imageItem]);
                          
                          setSelectedContentId(imageItem.id);
                          
                          toast({
                            title: 'Image Created',
                            description: 'Your image has been created successfully.'
                          });
                        }}
                        lessonId={null}
                        courseId={null}
                      />
                    </div>
                    
                    <div>
                      <h3 className="text-lg font-medium mb-3">Animation Generation</h3>
                      
                      {/* Integrate with the AnimationGenerationTool */}
                      <AnimationGenerationTool 
                        onAnimationGenerated={(url) => {
                          const animationItem: ContentItem = {
                            id: `animation-${Date.now()}`,
                            title: `Animation for ${courseTitle || 'Course'}`,
                            content: 'Generated animation',
                            status: 'completed',
                            type: 'animation',
                            mediaUrl: url
                          };
                          
                          setContentItems(prev => [...prev, animationItem]);
                          
                          setSelectedContentId(animationItem.id);
                          
                          toast({
                            title: 'Animation Created',
                            description: 'Your animation has been created successfully.'
                          });
                        }}
                        lessonId={null}
                        courseId={null}
                      />
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button
                    variant="ghost"
                    onClick={handlePrevious}
                  >
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    Previous
                  </Button>
                  <Button
                    onClick={handleNext}
                  >
                    Next
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </CardFooter>
              </Card>
            </TabsContent>
            
            <TabsContent value="integration" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Content Integration</CardTitle>
                  <CardDescription>
                    Arrange and finalize your course content
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div>
                    <h3 className="text-lg font-medium mb-3">Content Structure</h3>
                    <div className="border rounded-md p-4 space-y-4">
                      <div className="flex flex-col space-y-1">
                        <span className="text-sm font-medium">Course Title</span>
                        <span className="text-sm">{courseTitle || 'Untitled Course'}</span>
                      </div>
                      
                      <div className="flex flex-col space-y-1">
                        <span className="text-sm font-medium">Course Description</span>
                        <span className="text-sm">{courseDescription || 'No description'}</span>
                      </div>
                      
                      <Separator />
                      
                      <div className="flex flex-col space-y-1">
                        <span className="text-sm font-medium">Script</span>
                        <span className="text-sm line-clamp-3">
                          {contentItems.find(item => item.type === 'script')?.content || 'No script available'}
                        </span>
                      </div>
                      
                      <div className="flex flex-col space-y-1">
                        <span className="text-sm font-medium">Narration</span>
                        {contentItems.find(item => item.type === 'narration')?.mediaUrl ? (
                          <audio 
                            src={contentItems.find(item => item.type === 'narration')?.mediaUrl} 
                            controls 
                            className="w-full" 
                          />
                        ) : (
                          <span className="text-sm text-muted-foreground italic">No narration available</span>
                        )}
                      </div>
                      
                      <div className="space-y-1">
                        <span className="text-sm font-medium">Visuals</span>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                          {contentItems
                            .filter(item => item.type === 'image' || item.type === 'animation')
                            .map((item) => (
                              <div key={item.id} className="relative rounded-md overflow-hidden border h-24">
                                {item.type === 'image' ? (
                                  <img 
                                    src={item.mediaUrl} 
                                    alt={item.title}
                                    className="w-full h-full object-cover" 
                                  />
                                ) : (
                                  <video 
                                    src={item.mediaUrl} 
                                    className="w-full h-full object-cover" 
                                  />
                                )}
                                <div className="absolute bottom-0 right-0 p-1 bg-black/50 rounded-tl-md">
                                  {item.type === 'image' ? (
                                    <Image className="h-3 w-3 text-white" />
                                  ) : (
                                    <Video className="h-3 w-3 text-white" />
                                  )}
                                </div>
                              </div>
                            ))}
                          
                          {contentItems.filter(item => item.type === 'image' || item.type === 'animation').length === 0 && (
                            <div className="col-span-full text-sm text-muted-foreground italic">
                              No visuals available
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button
                    variant="ghost"
                    onClick={handlePrevious}
                  >
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    Previous
                  </Button>
                  <Button
                    onClick={handleNext}
                  >
                    Next
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </CardFooter>
              </Card>
            </TabsContent>
            
            <TabsContent value="preview" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Course Preview</CardTitle>
                  <CardDescription>
                    Review and finalize your course before creating it
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="border rounded-md overflow-hidden">
                    <div className="bg-slate-900 p-4 text-white">
                      <h3 className="text-xl font-bold">{courseTitle || 'Untitled Course'}</h3>
                      <p className="text-slate-300 mt-1">{courseDescription || 'No description'}</p>
                    </div>
                    
                    <div className="p-4 bg-slate-50 dark:bg-slate-800">
                      <div className="flex items-center gap-4 mb-4">
                        <div className="flex-1">
                          <span className="text-xs text-muted-foreground block mb-1">Course Content</span>
                          <div className="h-2 bg-slate-200 dark:bg-slate-700 rounded-full overflow-hidden">
                            <div className="h-full bg-primary" style={{ width: '100%' }}></div>
                          </div>
                        </div>
                        
                        <div>
                          <span className="text-xs text-muted-foreground">Credit Usage</span>
                          <div className="flex items-center gap-2">
                            <span className="text-sm font-medium">{creditInfo.cost}</span>
                            <span className="text-xs text-muted-foreground">credits</span>
                          </div>
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="space-y-2">
                          <span className="text-xs font-medium">Script</span>
                          <div className="border rounded-md p-2 bg-white dark:bg-slate-900 h-32 overflow-y-auto">
                            <p className="text-xs line-clamp-6">
                              {contentItems.find(item => item.type === 'script')?.content || 'No script available'}
                            </p>
                          </div>
                        </div>
                        
                        <div className="space-y-2">
                          <span className="text-xs font-medium">Narration</span>
                          <div className="border rounded-md p-2 bg-white dark:bg-slate-900 h-32 flex items-center justify-center">
                            {contentItems.find(item => item.type === 'narration')?.mediaUrl ? (
                              <audio 
                                src={contentItems.find(item => item.type === 'narration')?.mediaUrl} 
                                controls 
                                className="w-full" 
                              />
                            ) : (
                              <span className="text-xs text-muted-foreground italic">No narration available</span>
                            )}
                          </div>
                        </div>
                        
                        <div className="space-y-2">
                          <span className="text-xs font-medium">Visuals</span>
                          <div className="border rounded-md p-2 bg-white dark:bg-slate-900 h-32 overflow-y-auto">
                            <div className="grid grid-cols-2 gap-2">
                              {contentItems
                                .filter(item => item.type === 'image' || item.type === 'animation')
                                .slice(0, 4)
                                .map((item) => (
                                  <div key={item.id} className="relative rounded-md overflow-hidden border h-12">
                                    {item.type === 'image' ? (
                                      <img 
                                        src={item.mediaUrl} 
                                        alt={item.title}
                                        className="w-full h-full object-cover" 
                                      />
                                    ) : (
                                      <video 
                                        src={item.mediaUrl} 
                                        className="w-full h-full object-cover" 
                                      />
                                    )}
                                  </div>
                                ))}
                              
                              {contentItems.filter(item => item.type === 'image' || item.type === 'animation').length === 0 && (
                                <div className="col-span-full text-xs text-muted-foreground italic flex items-center justify-center h-full">
                                  No visuals available
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button
                    variant="ghost"
                    onClick={handlePrevious}
                  >
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    Previous
                  </Button>
                  <Button
                    variant="default"
                    onClick={handleCreateCourse}
                    disabled={
                      isProcessing || 
                      !contentItems.some(item => item.type === 'script' && item.status === 'completed') ||
                      !courseTitle
                    }
                  >
                    {isProcessing ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Creating...
                      </>
                    ) : (
                      <>
                        <FileCheck className="mr-2 h-4 w-4" />
                        Create Course
                      </>
                    )}
                  </Button>
                </CardFooter>
              </Card>
            </TabsContent>
            
            <TabsContent value="collaboration" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Team Collaboration</CardTitle>
                  <CardDescription>
                    Collaborate with your team on this course
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <TeamCourseCollaboration 
                    courseId={courseId === null ? 1 : courseId} 
                    teamId={teamId === null ? undefined : teamId} 
                    onTeamSelected={(selectedTeamId) => setTeamId(selectedTeamId)}
                  />
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button
                    variant="ghost"
                    onClick={handlePrevious}
                  >
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    Previous
                  </Button>
                  <div></div>
                </CardFooter>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
}