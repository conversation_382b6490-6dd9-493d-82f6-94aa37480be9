import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Skeleton } from "@/components/ui/skeleton";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { DragDropContext, Droppable, Draggable } from "@hello-pangea/dnd";
import { ChevronDown, ChevronRight, Paperclip, Video, Image, Music, FileText, FilePlus, Trash, FileAudio, MoveVertical, Copy, X, Play, Pause, Eye, Info, LayoutTemplate, PlusCircle, FileImage, ImagePlus } from "lucide-react";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Module, Lesson } from "./ContentStructure";
import { Badge } from "@/components/ui/badge";
import { apiRequest } from "@/lib/queryClient";

interface CourseAssemblyProps {
  onNext: () => void;
  onPrevious: () => void;
  courseDetails: {
    title: string;
    category: string;
    description: string;
  };
  courseStructure: {
    modules: Module[];
  };
  courseScripts?: {
    [key: string]: {
      [key: string]: string;
    };
  };
  exportSettings?: {
    format: string;
    quality: string;
  };
  onUpdateExportSettings?: (settings: any) => void;
}

// Interface for media items
interface Media {
  id: number;
  name: string;
  type: string;
  url: string;
  mimeType: string;
  fileSize: number;
  courseId?: number | null;
  lessonId?: number | null;
  createdAt: string;
  originalFilename?: string | null;
  duration?: number | null;
  source?: string;
  sourceId?: string | null;
  sourceData?: any;
}

// Interface for a course element (representing a specific component in the lesson)
interface CourseElement {
  id: string;
  type: 'title' | 'text' | 'image' | 'video' | 'audio' | 'quiz' | 'slide' | 'file' | 'separator';
  content: string | null;
  mediaId?: number;
  mediaUrl?: string;
  order: number;
  duration?: number;
  config?: any;
}

// Object to store lesson elements by moduleId and lessonId
type LessonElements = {
  [moduleId: string]: {
    [lessonId: string]: CourseElement[];
  }
};

export function CourseAssembly({ 
  onNext, 
  onPrevious, 
  courseDetails, 
  courseStructure, 
  courseScripts,
  exportSettings,
  onUpdateExportSettings 
}: CourseAssemblyProps) {
  const { toast } = useToast();
  
  // State for modules and active items
  const [expandedModules, setExpandedModules] = useState<Record<number, boolean>>({});
  const [activeModuleIndex, setActiveModuleIndex] = useState<number>(0);
  const [activeLessonIndex, setActiveLessonIndex] = useState<number>(0);

  // State for media
  const [mediaLibrary, setMediaLibrary] = useState<Media[]>([]);
  const [isLoadingMedia, setIsLoadingMedia] = useState<boolean>(false);
  const [activeMediaTab, setActiveMediaTab] = useState<string>("my-media");
  const [mediaSearchQuery, setMediaSearchQuery] = useState<string>("");
  const [pexelsResults, setPexelsResults] = useState<any[]>([]);
  const [pixabayResults, setPixabayResults] = useState<any[]>([]);
  const [isSearching, setIsSearching] = useState<boolean>(false);
  const [exportFormat, setExportFormat] = useState<string>(exportSettings?.format || "video");
  
  // State for lesson elements (organized by moduleId and lessonId)
  const [lessonElements, setLessonElements] = useState<LessonElements>({});

  // State for audio player
  const [playingAudio, setPlayingAudio] = useState<string | null>(null);
  const audioRef = React.useRef<HTMLAudioElement | null>(null);

  // State for selected element (for editing)
  const [selectedElement, setSelectedElement] = useState<CourseElement | null>(null);
  
  // State for showing available templates
  const [showTemplates, setShowTemplates] = useState(false);
  
  // State for media selection dialog
  const [showMediaDialog, setShowMediaDialog] = useState(false);
  const [elementForMedia, setElementForMedia] = useState<{id: string, type: string} | null>(null);

  // Initialize expanded modules
  useEffect(() => {
    if (courseStructure.modules.length > 0) {
      const expanded: Record<number, boolean> = {};
      courseStructure.modules.forEach((_, index) => {
        expanded[index] = index === 0;
      });
      setExpandedModules(expanded);
    }
  }, [courseStructure]);

  // Fetch media library
  useEffect(() => {
    fetchMediaLibrary();
  }, []);

  // Handle audio playback
  useEffect(() => {
    if (audioRef.current) {
      if (playingAudio) {
        audioRef.current.src = playingAudio;
        audioRef.current.play().catch(err => {
          console.error("Error playing audio:", err);
          toast({
            title: "Error playing audio",
            description: "Could not play the audio file",
            variant: "destructive"
          });
        });
      } else {
        audioRef.current.pause();
      }
    }
    
    return () => {
      if (audioRef.current) {
        audioRef.current.pause();
      }
    };
  }, [playingAudio, toast]);

  // Initialize lesson elements with titles and scripts
  useEffect(() => {
    if (courseStructure.modules.length > 0 && Object.keys(lessonElements).length === 0) {
      const initialElements: LessonElements = {};
      
      courseStructure.modules.forEach((module, moduleIndex) => {
        initialElements[moduleIndex] = {};
        
        module.lessons.forEach((lesson, lessonIndex) => {
          const elements: CourseElement[] = [];
          
          // Add title element
          elements.push({
            id: `title-${moduleIndex}-${lessonIndex}`,
            type: 'title',
            content: lesson.title,
            order: 0,
          });
          
          // Add script content if available
          if (courseScripts && 
              courseScripts[moduleIndex.toString()] && 
              courseScripts[moduleIndex.toString()][lessonIndex.toString()]) {
            elements.push({
              id: `script-${moduleIndex}-${lessonIndex}`,
              type: 'text',
              content: courseScripts[moduleIndex.toString()][lessonIndex.toString()],
              order: 1,
            });
          }
          
          initialElements[moduleIndex][lessonIndex] = elements;
        });
      });
      
      setLessonElements(initialElements);
    }
  }, [courseStructure, courseScripts]);

  const fetchMediaLibrary = async () => {
    setIsLoadingMedia(true);
    try {
      const response = await apiRequest("GET", "/api/media");
      const data = await response.json();
      setMediaLibrary(data);
    } catch (error) {
      console.error("Error fetching media library:", error);
      toast({
        title: "Error",
        description: "Failed to load media library",
        variant: "destructive",
      });
    } finally {
      setIsLoadingMedia(false);
    }
  };
  
  const searchMedia = async (query: string, source: 'pexels' | 'pixabay') => {
    if (!query.trim()) return;
    
    setIsSearching(true);
    try {
      if (source === 'pexels') {
        const response = await apiRequest("GET", `/api/pexels/photos?query=${encodeURIComponent(query)}`);
        const photoData = await response.json();
        
        const videoResponse = await apiRequest("GET", `/api/pexels/videos?query=${encodeURIComponent(query)}`);
        const videoData = await videoResponse.json();
        
        // Combine photos and videos
        setPexelsResults([...photoData.photos, ...videoData.videos]);
      } else {
        // Pixabay
        const response = await apiRequest("GET", `/api/pixabay/photos?query=${encodeURIComponent(query)}`);
        const photoData = await response.json();
        
        const videoResponse = await apiRequest("GET", `/api/pixabay/videos?query=${encodeURIComponent(query)}`);
        const videoData = await videoResponse.json();
        
        // Combine photos and videos
        setPixabayResults([...(photoData.hits || []), ...(videoData.hits || [])]);
      }
    } catch (error) {
      console.error(`Error searching ${source}:`, error);
      toast({
        title: "Search Failed",
        description: `Unable to search ${source} for media.`,
        variant: "destructive",
      });
    } finally {
      setIsSearching(false);
    }
  };
  
  const importExternalMedia = async (item: any, source: 'pexels' | 'pixabay') => {
    try {
      // Determine if it's a photo or video based on available properties
      const isPexels = source === 'pexels';
      const isVideo = isPexels ? !!item.video_files : !!item.videos;
      const type = isVideo ? "video" : "photo";
      
      console.log(`Importing ${source} ${type}:`, item);
      
      // Different API endpoints have different validation requirements
      if (isPexels) {
        // For Pexels
        const mediaType = isVideo ? "video" : "image";
        const name = isVideo 
          ? `Pexels Video ${item.id}` 
          : (item.alt || `Pexels Photo ${item.id}`);
        const estimatedSize = isVideo ? 5 * 1024 * 1024 : 500 * 1024;
        
        const response = await apiRequest("POST", `/api/pexels/import`, {
          id: item.id,
          type: type, // "photo" or "video" for API validation
          mediaType: mediaType, // "image" or "video" for storage
          name: name,
          fileSize: estimatedSize
        });
        
        if (response.ok) {
          handleImportSuccess(response);
        } else {
          handleImportError(response);
        }
      } else {
        // For Pixabay
        const title = isVideo 
          ? `Pixabay Video ${item.id}` 
          : item.tags?.split(',')[0] || `Pixabay Image ${item.id}`;
        
        const description = item.tags || '';
        
        const response = await apiRequest("POST", `/api/pixabay/import`, {
          id: item.id,
          type: type, // "photo" or "video" for API validation
          title: title,
          description: description
        });
        
        if (response.ok) {
          handleImportSuccess(response);
        } else {
          handleImportError(response);
        }
      }
    } catch (error) {
      console.error("Import error:", error);
      toast({
        title: "Import Failed",
        description: "Could not import the selected media",
        variant: "destructive",
      });
    }
  };
  
  const handleImportSuccess = async (response: Response) => {
    const importedMedia = await response.json();
    setMediaLibrary(prev => [...prev, importedMedia]);
    toast({
      title: "Media Imported",
      description: "Media has been added to your library",
    });
  };
  
  const handleImportError = async (response: Response) => {
    const errorData = await response.json().catch(() => ({}));
    console.error("Import error response:", errorData);
    throw new Error(errorData.message || "Import failed");
  };

  const toggleModuleExpanded = (index: number) => {
    setExpandedModules(prev => ({
      ...prev,
      [index]: !prev[index]
    }));
  };

  const selectLessonForEditing = (moduleIndex: number, lessonIndex: number) => {
    setActiveModuleIndex(moduleIndex);
    setActiveLessonIndex(lessonIndex);
    // Ensure this lesson has an elements array
    if (!lessonElements[moduleIndex] || !lessonElements[moduleIndex][lessonIndex]) {
      const updatedElements = { ...lessonElements };
      if (!updatedElements[moduleIndex]) {
        updatedElements[moduleIndex] = {};
      }
      if (!updatedElements[moduleIndex][lessonIndex]) {
        updatedElements[moduleIndex][lessonIndex] = [
          {
            id: `title-${moduleIndex}-${lessonIndex}`,
            type: 'title',
            content: courseStructure.modules[moduleIndex].lessons[lessonIndex].title,
            order: 0,
          }
        ];
      }
      setLessonElements(updatedElements);
    }
  };

  const handleDragEnd = (result: any) => {
    const { destination, source, draggableId } = result;
    
    // Drop was cancelled or outside a droppable area
    if (!destination) {
      return;
    }
    
    // Dropped in the same position
    if (
      destination.droppableId === source.droppableId &&
      destination.index === source.index
    ) {
      return;
    }
    
    // Handle dropping from media library to elements
    if (source.droppableId === 'media-library' && destination.droppableId === 'lesson-elements') {
      const mediaId = parseInt(draggableId.replace('media-', ''));
      const mediaItem = mediaLibrary.find(item => item.id === mediaId);
      
      if (mediaItem) {
        // Create a new element based on the media type
        let elementType: 'image' | 'video' | 'audio' | 'file' = 'file';
        if (mediaItem.type === 'image') elementType = 'image';
        else if (mediaItem.type === 'video') elementType = 'video';
        else if (mediaItem.type === 'audio') elementType = 'audio';
        
        const newElement: CourseElement = {
          id: `element-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          type: elementType,
          content: mediaItem.name,
          mediaId: mediaItem.id,
          mediaUrl: mediaItem.url,
          order: destination.index,
          duration: mediaItem.duration || undefined,
        };
        
        // Add the new element to the current lesson
        const updatedElements = { ...lessonElements };
        const currentElements = [...(updatedElements[activeModuleIndex]?.[activeLessonIndex] || [])];
        
        // Insert at the destination index
        currentElements.splice(destination.index, 0, newElement);
        
        // Update orders
        currentElements.forEach((element, index) => {
          element.order = index;
        });
        
        // Save back to state
        if (!updatedElements[activeModuleIndex]) {
          updatedElements[activeModuleIndex] = {};
        }
        updatedElements[activeModuleIndex][activeLessonIndex] = currentElements;
        setLessonElements(updatedElements);
        
        return;
      }
    }
    
    // Reordering elements within the same lesson
    if (source.droppableId === 'lesson-elements' && destination.droppableId === 'lesson-elements') {
      const updatedElements = { ...lessonElements };
      const currentElements = [...(updatedElements[activeModuleIndex]?.[activeLessonIndex] || [])];
      
      // Remove the element from its current position
      const [movedElement] = currentElements.splice(source.index, 1);
      
      // Insert it at the destination
      currentElements.splice(destination.index, 0, movedElement);
      
      // Update orders
      currentElements.forEach((element, index) => {
        element.order = index;
      });
      
      // Save back to state
      if (!updatedElements[activeModuleIndex]) {
        updatedElements[activeModuleIndex] = {};
      }
      updatedElements[activeModuleIndex][activeLessonIndex] = currentElements;
      setLessonElements(updatedElements);
    }
  };

  const addNewElement = (type: CourseElement['type']) => {
    const updatedElements = { ...lessonElements };
    if (!updatedElements[activeModuleIndex]) {
      updatedElements[activeModuleIndex] = {};
    }
    if (!updatedElements[activeModuleIndex][activeLessonIndex]) {
      updatedElements[activeModuleIndex][activeLessonIndex] = [];
    }
    
    const currentElements = updatedElements[activeModuleIndex][activeLessonIndex];
    const newOrder = currentElements.length;
    
    let content = null;
    if (type === 'title') content = 'New Section Title';
    if (type === 'text') content = 'Add your text content here...';
    if (type === 'separator') content = 'Separator';
    
    const newElement: CourseElement = {
      id: `element-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      type,
      content,
      order: newOrder,
    };
    
    updatedElements[activeModuleIndex][activeLessonIndex].push(newElement);
    setLessonElements(updatedElements);
  };

  const removeElement = (elementId: string) => {
    const updatedElements = { ...lessonElements };
    const currentElements = [...(updatedElements[activeModuleIndex]?.[activeLessonIndex] || [])];
    
    const elementIndex = currentElements.findIndex(el => el.id === elementId);
    if (elementIndex >= 0) {
      currentElements.splice(elementIndex, 1);
      
      // Update orders
      currentElements.forEach((element, index) => {
        element.order = index;
      });
      
      // Save back to state
      updatedElements[activeModuleIndex][activeLessonIndex] = currentElements;
      setLessonElements(updatedElements);
    }
  };

  const duplicateElement = (elementId: string) => {
    const updatedElements = { ...lessonElements };
    const currentElements = [...(updatedElements[activeModuleIndex]?.[activeLessonIndex] || [])];
    
    const elementToDuplicate = currentElements.find(el => el.id === elementId);
    if (elementToDuplicate) {
      const newElement = {
        ...elementToDuplicate,
        id: `element-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        order: currentElements.length,
      };
      
      currentElements.push(newElement);
      
      // Save back to state
      updatedElements[activeModuleIndex][activeLessonIndex] = currentElements;
      setLessonElements(updatedElements);
    }
  };

  const getMediaItem = (mediaId?: number) => {
    if (!mediaId) return null;
    return mediaLibrary.find(item => item.id === mediaId);
  };
  
  // Open media selection dialog for a specific element
  const openMediaSelectionForElement = (elementId: string, elementType: string) => {
    setElementForMedia({ id: elementId, type: elementType });
    setShowMediaDialog(true);
  };
  
  // Handle selection of media from the dialog
  const handleMediaSelection = (media: Media) => {
    if (!elementForMedia) return;
    
    // Determine the appropriate element type based on media type
    let elementType: 'image' | 'video' | 'audio' | 'file' = 'file';
    if (media.type === 'image') elementType = 'image';
    else if (media.type === 'video') elementType = 'video';
    else if (media.type === 'audio') elementType = 'audio';
    
    // Update the element with the new media
    const updatedElements = { ...lessonElements };
    const currentElements = [...(updatedElements[activeModuleIndex]?.[activeLessonIndex] || [])];
    
    const elementIndex = currentElements.findIndex(el => el.id === elementForMedia.id);
    if (elementIndex >= 0) {
      currentElements[elementIndex] = {
        ...currentElements[elementIndex],
        type: elementType,
        mediaId: media.id,
        mediaUrl: media.url,
        duration: media.duration || undefined,
        content: media.name,
      };
      
      // Save back to state
      updatedElements[activeModuleIndex][activeLessonIndex] = currentElements;
      setLessonElements(updatedElements);
      
      toast({
        title: "Media Added",
        description: `"${media.name}" added to course element`,
      });
    }
    
    // Close the dialog
    setShowMediaDialog(false);
    setElementForMedia(null);
  };

  const renderElementContent = (element: CourseElement) => {
    switch (element.type) {
      case 'title':
        return (
          <div className="font-semibold text-lg border-l-4 border-primary pl-3 py-1">
            {element.content}
          </div>
        );
      case 'text':
        return (
          <div className="prose prose-sm max-w-none">
            {element.content}
          </div>
        );
      case 'image':
        return (
          <div className="relative">
            <img 
              src={element.mediaUrl} 
              alt={element.content || 'Image'} 
              className="max-h-48 object-contain rounded-md"
            />
            <div className="mt-1 text-sm text-slate-500">{element.content}</div>
          </div>
        );
      case 'video':
        return (
          <div className="relative">
            <video 
              src={element.mediaUrl} 
              controls
              className="max-h-48 w-full object-contain rounded-md"
            />
            <div className="mt-1 text-sm text-slate-500">{element.content}</div>
          </div>
        );
      case 'audio':
        return (
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => {
                if (playingAudio === element.mediaUrl) {
                  setPlayingAudio(null);
                } else {
                  setPlayingAudio(element.mediaUrl || null);
                }
              }}
            >
              {playingAudio === element.mediaUrl ? (
                <Pause className="h-5 w-5" />
              ) : (
                <Play className="h-5 w-5" />
              )}
            </Button>
            <div>
              <div className="font-medium">{element.content}</div>
              {element.duration && (
                <div className="text-sm text-slate-500">
                  {Math.floor(element.duration / 60)}:{String(Math.floor(element.duration % 60)).padStart(2, "0")}
                </div>
              )}
            </div>
          </div>
        );
      case 'file':
        return (
          <div className="flex items-center space-x-2">
            <FileText className="h-5 w-5 text-primary" />
            <div>
              <div className="font-medium">{element.content}</div>
              <a 
                href={element.mediaUrl} 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-sm text-primary hover:underline"
              >
                Download File
              </a>
            </div>
          </div>
        );
      case 'separator':
        return (
          <div className="border-t-2 border-slate-200 my-2" />
        );
      default:
        return null;
    }
  };

  // Template options for quick assembly
  const lessonTemplates = [
    {
      name: "Standard Lesson",
      description: "Basic lesson with title, introduction, content, and conclusion",
      elements: [
        { type: 'title', content: 'Lesson Title', order: 0 },
        { type: 'text', content: 'Introduction to the lesson topic...', order: 1 },
        { type: 'separator', content: 'Separator', order: 2 },
        { type: 'title', content: 'Key Concepts', order: 3 },
        { type: 'text', content: 'Main content explaining key concepts...', order: 4 },
        { type: 'separator', content: 'Separator', order: 5 },
        { type: 'title', content: 'Conclusion', order: 6 },
        { type: 'text', content: 'Summary of what was learned...', order: 7 },
      ]
    },
    {
      name: "Media-Rich Lesson",
      description: "Lesson with multiple media elements to enhance engagement",
      elements: [
        { type: 'title', content: 'Lesson Title', order: 0 },
        { type: 'text', content: 'Brief introduction...', order: 1 },
        { type: 'title', content: 'Visual Explanation', order: 2 },
        { type: 'text', content: 'Explanation text...', order: 3 },
        { type: 'separator', content: 'Separator', order: 4 },
        { type: 'title', content: 'Audio Explanation', order: 5 },
        { type: 'text', content: 'This concept is explained in the audio clip below:', order: 6 },
        { type: 'text', content: 'Additional details and conclusion...', order: 7 },
      ]
    },
    {
      name: "Tutorial Lesson",
      description: "Step-by-step guide with clear instructions",
      elements: [
        { type: 'title', content: 'Tutorial: How to...', order: 0 },
        { type: 'text', content: 'In this tutorial, you will learn...', order: 1 },
        { type: 'title', content: 'Step 1: Getting Started', order: 2 },
        { type: 'text', content: 'Instructions for step 1...', order: 3 },
        { type: 'title', content: 'Step 2: Next Actions', order: 4 },
        { type: 'text', content: 'Instructions for step 2...', order: 5 },
        { type: 'title', content: 'Step 3: Finishing Up', order: 6 },
        { type: 'text', content: 'Instructions for step 3...', order: 7 },
        { type: 'title', content: 'What You Learned', order: 8 },
        { type: 'text', content: 'Summary of skills acquired...', order: 9 },
      ]
    }
  ];

  const applyTemplate = (templateIndex: number) => {
    const template = lessonTemplates[templateIndex];
    if (template) {
      const updatedElements = { ...lessonElements };
      if (!updatedElements[activeModuleIndex]) {
        updatedElements[activeModuleIndex] = {};
      }
      
      // Create new elements from template, preserve title if exists
      const existingTitle = updatedElements[activeModuleIndex][activeLessonIndex]?.[0]?.content || 
                           courseStructure.modules[activeModuleIndex].lessons[activeLessonIndex].title;
      
      const newElements = template.elements.map((el, index) => {
        // If it's the first element and it's a title, use the existing title
        const content = (index === 0 && el.type === 'title') ? existingTitle : el.content;
        
        return {
          id: `element-${Date.now()}-${Math.random().toString(36).substr(2, 9)}-${index}`,
          type: el.type as CourseElement['type'],
          content,
          order: index,
        };
      });
      
      updatedElements[activeModuleIndex][activeLessonIndex] = newElements;
      setLessonElements(updatedElements);
      setShowTemplates(false);
      
      toast({
        title: "Template Applied",
        description: `Applied the "${template.name}" template to this lesson.`,
      });
    }
  };

  // Media selection dialog for course elements
  const MediaSelectionDialog = () => (
    <Dialog open={showMediaDialog} onOpenChange={setShowMediaDialog}>
      <DialogContent className="sm:max-w-[800px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Select Media</DialogTitle>
          <DialogDescription>
            Choose a media item to add to your course element
          </DialogDescription>
        </DialogHeader>
        
        <Tabs defaultValue="my-media" className="w-full">
          <TabsList className="grid grid-cols-3 mb-4">
            <TabsTrigger value="my-media">My Media</TabsTrigger>
            <TabsTrigger value="pexels">Pexels</TabsTrigger>
            <TabsTrigger value="pixabay">Pixabay</TabsTrigger>
          </TabsList>
          
          {/* My Media Tab */}
          <TabsContent value="my-media" className="space-y-4">
            {isLoadingMedia ? (
              <div className="space-y-3">
                {Array(5).fill(0).map((_, index) => (
                  <div key={index} className="flex items-center space-x-3">
                    <Skeleton className="h-12 w-12 rounded" />
                    <div className="space-y-2">
                      <Skeleton className="h-4 w-32" />
                      <Skeleton className="h-3 w-24" />
                    </div>
                  </div>
                ))}
              </div>
            ) : mediaLibrary.length === 0 ? (
              <div className="text-center py-6">
                <FilePlus className="h-10 w-10 text-slate-300 mx-auto mb-2" />
                <h4 className="text-sm font-medium mb-1">No media files</h4>
                <p className="text-xs text-slate-500">
                  Upload media in the Media Creation step
                </p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                {mediaLibrary.map((media) => (
                  <div
                    key={media.id}
                    className="p-3 border border-slate-200 rounded-md hover:border-primary cursor-pointer"
                    onClick={() => handleMediaSelection(media)}
                  >
                    <div className="h-24 w-full mb-2 bg-slate-100 rounded flex items-center justify-center overflow-hidden">
                      {media.type === 'image' ? (
                        <img src={media.url} alt={media.name} className="max-h-full max-w-full object-contain" />
                      ) : media.type === 'video' ? (
                        <div className="relative w-full h-full">
                          <video src={media.url} className="w-full h-full object-cover" />
                          <div className="absolute inset-0 flex items-center justify-center bg-black/20">
                            <Play className="h-8 w-8 text-white" />
                          </div>
                        </div>
                      ) : media.type === 'audio' ? (
                        <FileAudio className="h-10 w-10 text-slate-500" />
                      ) : (
                        <FileText className="h-10 w-10 text-slate-500" />
                      )}
                    </div>
                    <div className="font-medium text-sm truncate">{media.name}</div>
                    <div className="text-xs text-slate-500 flex justify-between">
                      <span>{media.type}</span>
                      <span>{formatFileSize(media.fileSize)}</span>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </TabsContent>
          
          {/* Pexels Tab */}
          <TabsContent value="pexels" className="space-y-4">
            <div className="flex items-center space-x-2">
              <Input 
                placeholder="Search for free images & videos..." 
                value={mediaSearchQuery}
                onChange={(e) => setMediaSearchQuery(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    searchMedia(mediaSearchQuery, 'pexels');
                  }
                }}
              />
              <Button 
                onClick={() => searchMedia(mediaSearchQuery, 'pexels')}
                disabled={isSearching || !mediaSearchQuery.trim()}
              >
                {isSearching ? (
                  <div className="h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent" />
                ) : "Search"}
              </Button>
            </div>
            
            {isSearching ? (
              <div className="text-center py-6">
                <div className="h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent mx-auto mb-2" />
                <p className="text-sm text-slate-500">Searching Pexels...</p>
              </div>
            ) : pexelsResults.length === 0 ? (
              <div className="text-center py-6">
                <Image className="h-10 w-10 text-slate-300 mx-auto mb-2" />
                <h4 className="text-sm font-medium mb-1">No media found</h4>
                <p className="text-xs text-slate-500">
                  Search for images and videos on Pexels
                </p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                {pexelsResults.map((item: any) => {
                  const isVideo = !!item.video_files;
                  const thumbnailUrl = isVideo 
                    ? (item.image || item.video_pictures?.[0]?.picture) 
                    : item.src?.medium;
                  const title = isVideo 
                    ? `Pexels Video ${item.id}` 
                    : (item.alt || `Pexels Photo ${item.id}`);
                  
                  return (
                    <div
                      key={`pexels-${item.id}`}
                      className="p-3 border border-slate-200 rounded-md hover:border-primary cursor-pointer"
                      onClick={() => importExternalMedia(item, 'pexels')}
                    >
                      <div className="h-24 w-full mb-2 bg-slate-100 rounded flex items-center justify-center overflow-hidden">
                        {thumbnailUrl ? (
                          <img src={thumbnailUrl} alt={title} className="w-full h-full object-cover" />
                        ) : isVideo ? (
                          <Video className="h-10 w-10 text-slate-500" />
                        ) : (
                          <Image className="h-10 w-10 text-slate-500" />
                        )}
                        {isVideo && (
                          <div className="absolute inset-0 flex items-center justify-center bg-black/20">
                            <Play className="h-8 w-8 text-white" />
                          </div>
                        )}
                      </div>
                      <div className="font-medium text-sm truncate">{title}</div>
                      <div className="text-xs text-slate-500 flex justify-between">
                        <span>{isVideo ? 'Video' : 'Image'}</span>
                        <span>Pexels</span>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </TabsContent>
          
          {/* Pixabay Tab */}
          <TabsContent value="pixabay" className="space-y-4">
            <div className="flex items-center space-x-2">
              <Input 
                placeholder="Search for free images & videos..." 
                value={mediaSearchQuery}
                onChange={(e) => setMediaSearchQuery(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    searchMedia(mediaSearchQuery, 'pixabay');
                  }
                }}
              />
              <Button 
                onClick={() => searchMedia(mediaSearchQuery, 'pixabay')}
                disabled={isSearching || !mediaSearchQuery.trim()}
              >
                {isSearching ? (
                  <div className="h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent" />
                ) : "Search"}
              </Button>
            </div>
            
            {isSearching ? (
              <div className="text-center py-6">
                <div className="h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent mx-auto mb-2" />
                <p className="text-sm text-slate-500">Searching Pixabay...</p>
              </div>
            ) : pixabayResults.length === 0 ? (
              <div className="text-center py-6">
                <Image className="h-10 w-10 text-slate-300 mx-auto mb-2" />
                <h4 className="text-sm font-medium mb-1">No media found</h4>
                <p className="text-xs text-slate-500">
                  Search for images and videos on Pixabay
                </p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                {pixabayResults.map((item: any) => {
                  const isVideo = !!item.videos;
                  const thumbnailUrl = isVideo 
                    ? item.userImageURL || item.previewURL  
                    : item.webformatURL || item.previewURL;
                  const title = isVideo 
                    ? `Pixabay Video ${item.id}` 
                    : item.tags?.split(',')[0] || `Pixabay Image ${item.id}`;
                  
                  return (
                    <div
                      key={`pixabay-${item.id}`}
                      className="p-3 border border-slate-200 rounded-md hover:border-primary cursor-pointer"
                      onClick={() => importExternalMedia(item, 'pixabay')}
                    >
                      <div className="relative h-24 w-full mb-2 bg-slate-100 rounded flex items-center justify-center overflow-hidden">
                        {thumbnailUrl ? (
                          <img src={thumbnailUrl} alt={title} className="w-full h-full object-cover" />
                        ) : isVideo ? (
                          <Video className="h-10 w-10 text-slate-500" />
                        ) : (
                          <Image className="h-10 w-10 text-slate-500" />
                        )}
                        {isVideo && (
                          <div className="absolute inset-0 flex items-center justify-center bg-black/20">
                            <Play className="h-8 w-8 text-white" />
                          </div>
                        )}
                      </div>
                      <div className="font-medium text-sm truncate">{title}</div>
                      <div className="text-xs text-slate-500 flex justify-between">
                        <span>{isVideo ? 'Video' : 'Image'}</span>
                        <span>Pixabay</span>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
  
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-xl font-semibold">Course Assembly</h3>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={() => {
            // Save export settings before going back
            if (onUpdateExportSettings) {
              onUpdateExportSettings({
                format: exportFormat,
                quality: exportFormat === 'video' ? 'hd' : 'high',
              });
            }
            onPrevious();
          }}>Back</Button>
          <Button onClick={() => {
            // Save the course assembly data before continuing
            // In a real implementation, this would save to the database via API
            console.log('Course assembly complete with export format:', exportFormat);
            
            // Save export settings to parent component state
            if (onUpdateExportSettings) {
              onUpdateExportSettings({
                format: exportFormat,
                quality: exportFormat === 'video' ? 'hd' : 'high',
              });
            }
            
            // Show a confirmation toast
            toast({
              title: "Course assembly saved",
              description: `Course will be exported as ${exportFormat.toUpperCase()}`,
              duration: 3000,
            });
            
            // Pass the assembled course data to the next step
            onNext();
          }}>Continue</Button>
        </div>
      </div>
      
      <div className="bg-slate-50 p-4 rounded-md border border-slate-200">
        <p className="text-sm text-slate-600">
          Assemble your course content by dragging media and arranging lesson elements. 
          Create a visual sequence for each lesson before previewing and publishing.
        </p>
      </div>
      
      {/* Hidden audio element for audio playback */}
      <audio ref={audioRef} onEnded={() => setPlayingAudio(null)} />
      
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Left sidebar - Course structure navigation */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-md border border-slate-200 overflow-hidden">
            <div className="p-3 border-b border-slate-200 bg-slate-50">
              <h4 className="font-medium">Course Structure</h4>
            </div>
            <ScrollArea className="h-[600px]">
              <div className="p-3 space-y-2">
                {courseStructure.modules.map((module, moduleIndex) => (
                  <div key={moduleIndex} className="space-y-1">
                    <div 
                      className="flex items-center justify-between cursor-pointer hover:bg-slate-50 p-2 rounded"
                      onClick={() => toggleModuleExpanded(moduleIndex)}
                    >
                      <div className="flex items-center space-x-2">
                        {expandedModules[moduleIndex] ? (
                          <ChevronDown className="h-4 w-4 text-slate-400" />
                        ) : (
                          <ChevronRight className="h-4 w-4 text-slate-400" />
                        )}
                        <span className="font-medium">{module.title}</span>
                      </div>
                    </div>
                    
                    {expandedModules[moduleIndex] && (
                      <div className="ml-4 pl-3 border-l border-slate-200 space-y-1">
                        {module.lessons.map((lesson, lessonIndex) => (
                          <div 
                            key={lessonIndex}
                            className={`p-2 rounded cursor-pointer text-sm transition-colors ${
                              activeModuleIndex === moduleIndex && activeLessonIndex === lessonIndex
                                ? "bg-primary text-white"
                                : "hover:bg-slate-100"
                            }`}
                            onClick={() => selectLessonForEditing(moduleIndex, lessonIndex)}
                          >
                            {lesson.title}
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </ScrollArea>
          </div>
        </div>

        {/* Main content area - Drag and drop assembly */}
        <div className="lg:col-span-2">
          <DragDropContext onDragEnd={handleDragEnd}>
            <Card className="h-full">
              <CardHeader className="pb-3 pt-4">
                <div className="flex justify-between items-center">
                  <CardTitle className="text-lg">
                    {courseStructure.modules[activeModuleIndex]?.lessons[activeLessonIndex]?.title || "Select a lesson"}
                  </CardTitle>
                  <div className="flex space-x-2">
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => setShowTemplates(!showTemplates)}
                          >
                            <LayoutTemplate className="h-4 w-4 mr-1" />
                            Templates
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Apply a pre-designed lesson template</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                    
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => {
                              const previewUrl = `/preview/lesson/${activeModuleIndex}/${activeLessonIndex}`;
                              window.open(previewUrl, '_blank');
                            }}
                          >
                            <Eye className="h-4 w-4 mr-1" />
                            Preview
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Preview this lesson</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                </div>
                
                {/* Element type buttons */}
                <div className="flex flex-wrap gap-2 mt-2">
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => addNewElement('title')}
                  >
                    <PlusCircle className="h-3.5 w-3.5 mr-1" />
                    Section Title
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => addNewElement('text')}
                  >
                    <PlusCircle className="h-3.5 w-3.5 mr-1" />
                    Text
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => addNewElement('separator')}
                  >
                    <PlusCircle className="h-3.5 w-3.5 mr-1" />
                    Separator
                  </Button>
                </div>
              </CardHeader>
              
              {/* Template selection dialog */}
              {showTemplates && (
                <div className="mx-4 mb-4 p-4 border border-slate-200 rounded-md bg-slate-50">
                  <div className="flex justify-between items-center mb-2">
                    <h4 className="font-medium">Lesson Templates</h4>
                    <Button variant="ghost" size="icon" onClick={() => setShowTemplates(false)}>
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                    {lessonTemplates.map((template, index) => (
                      <div 
                        key={index}
                        className="border border-slate-200 rounded p-3 bg-white cursor-pointer hover:border-primary transition-colors"
                        onClick={() => applyTemplate(index)}
                      >
                        <h5 className="font-medium text-sm">{template.name}</h5>
                        <p className="text-xs text-slate-500 mt-1">{template.description}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}
              
              <CardContent className="pt-0">
                <ScrollArea className="h-[500px] rounded border border-slate-100">
                  <Droppable droppableId="lesson-elements">
                    {(provided) => (
                      <div
                        ref={provided.innerRef}
                        {...provided.droppableProps}
                        className="min-h-[500px] p-4 space-y-4"
                      >
                        {courseStructure.modules[activeModuleIndex] && 
                         courseStructure.modules[activeModuleIndex].lessons[activeLessonIndex] ? (
                          <>
                            {(lessonElements[activeModuleIndex]?.[activeLessonIndex] || [])
                              .sort((a, b) => a.order - b.order)
                              .map((element, index) => (
                                <Draggable
                                  key={element.id}
                                  draggableId={element.id}
                                  index={index}
                                >
                                  {(provided) => (
                                    <div
                                      ref={provided.innerRef}
                                      {...provided.draggableProps}
                                      className="border border-slate-200 rounded-md bg-white p-3 group"
                                    >
                                      <div className="flex justify-between items-start">
                                        <div className="flex-1">
                                          {renderElementContent(element)}
                                        </div>
                                        <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                                          <TooltipProvider>
                                            <Tooltip>
                                              <TooltipTrigger asChild>
                                                <Button 
                                                  variant="ghost" 
                                                  size="icon" 
                                                  className="h-8 w-8 text-primary"
                                                  onClick={() => openMediaSelectionForElement(element.id, element.type)}
                                                >
                                                  <ImagePlus className="h-4 w-4" />
                                                </Button>
                                              </TooltipTrigger>
                                              <TooltipContent>
                                                <p>Select Media</p>
                                              </TooltipContent>
                                            </Tooltip>
                                          </TooltipProvider>
                                          <Button 
                                            variant="ghost" 
                                            size="icon" 
                                            className="h-8 w-8"
                                            {...provided.dragHandleProps}
                                          >
                                            <MoveVertical className="h-4 w-4" />
                                          </Button>
                                          <Button 
                                            variant="ghost" 
                                            size="icon" 
                                            className="h-8 w-8"
                                            onClick={() => duplicateElement(element.id)}
                                          >
                                            <Copy className="h-4 w-4" />
                                          </Button>
                                          <Button 
                                            variant="ghost" 
                                            size="icon" 
                                            className="h-8 w-8 text-red-500 hover:text-red-700"
                                            onClick={() => removeElement(element.id)}
                                          >
                                            <Trash className="h-4 w-4" />
                                          </Button>
                                          <Badge variant="outline" className="text-xs">
                                            {element.type}
                                          </Badge>
                                        </div>
                                      </div>
                                    </div>
                                  )}
                                </Draggable>
                              ))}
                            {(lessonElements[activeModuleIndex]?.[activeLessonIndex] || []).length === 0 && (
                              <div className="flex flex-col items-center justify-center h-[300px] text-center">
                                <FilePlus className="h-12 w-12 text-slate-300 mb-3" />
                                <h4 className="text-base font-medium mb-1">Add elements to this lesson</h4>
                                <p className="text-sm text-slate-500 mb-4 max-w-md">
                                  Use the buttons above to add text sections or drag media from the right panel
                                </p>
                              </div>
                            )}
                          </>
                        ) : (
                          <div className="flex flex-col items-center justify-center h-[300px] text-center">
                            <Info className="h-12 w-12 text-slate-300 mb-3" />
                            <h4 className="text-base font-medium mb-1">Select a lesson to edit</h4>
                            <p className="text-sm text-slate-500 mb-4 max-w-md">
                              Choose a lesson from the left sidebar to start assembling content
                            </p>
                          </div>
                        )}
                        {provided.placeholder}
                      </div>
                    )}
                  </Droppable>
                </ScrollArea>
              </CardContent>
            </Card>
          </DragDropContext>
        </div>
        
        {/* Right sidebar - Media elements */}
        <div className="lg:col-span-1">
          <Card className="h-full">
            <CardHeader className="pb-2">
              <CardTitle className="text-base">Media Sources</CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <Tabs value={activeMediaTab} onValueChange={setActiveMediaTab} className="w-full">
                <TabsList className="grid grid-cols-3 mb-4">
                  <TabsTrigger value="my-media">My Media</TabsTrigger>
                  <TabsTrigger value="pexels">Pexels</TabsTrigger>
                  <TabsTrigger value="pixabay">Pixabay</TabsTrigger>
                </TabsList>
                
                {/* My Media Tab */}
                <TabsContent value="my-media" className="space-y-3">
                  <div className="text-center mb-3">
                    <p className="text-xs text-slate-500 mb-2">Drag items from your media library to the content area</p>
                  </div>
                  
                  <ScrollArea className="h-[500px]">
                    <DragDropContext onDragEnd={handleDragEnd}>
                      <Droppable droppableId="media-library">
                        {(provided) => (
                          <div
                            ref={provided.innerRef}
                            {...provided.droppableProps}
                            className="space-y-3"
                          >
                            {isLoadingMedia ? (
                              <div className="space-y-3">
                                {Array(5).fill(0).map((_, index) => (
                                  <div key={index} className="flex items-center space-x-3">
                                    <Skeleton className="h-12 w-12 rounded" />
                                    <div className="space-y-2">
                                      <Skeleton className="h-4 w-32" />
                                      <Skeleton className="h-3 w-24" />
                                    </div>
                                  </div>
                                ))}
                              </div>
                            ) : mediaLibrary.length === 0 ? (
                              <div className="text-center py-6">
                                <FilePlus className="h-10 w-10 text-slate-300 mx-auto mb-2" />
                                <h4 className="text-sm font-medium mb-1">No media files</h4>
                                <p className="text-xs text-slate-500">
                                  Upload media in the Media Creation step
                                </p>
                              </div>
                            ) : (
                              mediaLibrary.map((media, index) => (
                                <Draggable
                                  key={`media-${media.id}`}
                                  draggableId={`media-${media.id}`}
                                  index={index}
                                >
                                  {(provided) => (
                                    <div
                                      ref={provided.innerRef}
                                      {...provided.draggableProps}
                                      {...provided.dragHandleProps}
                                      className="flex items-start space-x-3 p-2 border border-slate-200 rounded-md cursor-move hover:bg-slate-50"
                                    >
                                      <div className="h-12 w-12 flex items-center justify-center bg-slate-100 rounded">
                                        {media.type === 'image' ? (
                                          <Image className="h-6 w-6 text-slate-500" />
                                        ) : media.type === 'video' ? (
                                          <Video className="h-6 w-6 text-slate-500" />
                                        ) : media.type === 'audio' ? (
                                          <Music className="h-6 w-6 text-slate-500" />
                                        ) : (
                                          <FileText className="h-6 w-6 text-slate-500" />
                                        )}
                                      </div>
                                      <div className="flex-1 min-w-0">
                                        <div className="font-medium text-sm truncate">
                                          {media.name}
                                        </div>
                                        <div className="text-xs text-slate-500">
                                          {media.type} • {formatFileSize(media.fileSize)}
                                        </div>
                                      </div>
                                    </div>
                                  )}
                                </Draggable>
                              ))
                            )}
                            {provided.placeholder}
                          </div>
                        )}
                      </Droppable>
                    </DragDropContext>
                  </ScrollArea>
                </TabsContent>
                
                {/* Pexels Tab */}
                <TabsContent value="pexels" className="space-y-3">
                  <div className="flex items-center space-x-2 mb-3">
                    <Input 
                      placeholder="Search for free images & videos..." 
                      value={mediaSearchQuery}
                      onChange={(e) => setMediaSearchQuery(e.target.value)}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          searchMedia(mediaSearchQuery, 'pexels');
                        }
                      }}
                    />
                    <Button 
                      size="sm" 
                      onClick={() => searchMedia(mediaSearchQuery, 'pexels')}
                      disabled={isSearching || !mediaSearchQuery.trim()}
                    >
                      {isSearching ? (
                        <div className="h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent" />
                      ) : "Search"}
                    </Button>
                  </div>
                  
                  <ScrollArea className="h-[450px]">
                    {isSearching ? (
                      <div className="text-center py-6">
                        <div className="h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent mx-auto mb-2" />
                        <p className="text-sm text-slate-500">Searching Pexels...</p>
                      </div>
                    ) : pexelsResults.length === 0 ? (
                      <div className="text-center py-6">
                        <Image className="h-10 w-10 text-slate-300 mx-auto mb-2" />
                        <h4 className="text-sm font-medium mb-1">No media found</h4>
                        <p className="text-xs text-slate-500">
                          Search for images and videos on Pexels
                        </p>
                      </div>
                    ) : (
                      <div className="grid grid-cols-2 gap-3">
                        {pexelsResults.map((item) => (
                          <div 
                            key={item.id} 
                            className="border border-slate-200 rounded-md overflow-hidden"
                          >
                            <div className="relative h-32 bg-slate-100">
                              {item.src ? (
                                <img 
                                  src={item.src.medium || item.src.small} 
                                  alt={item.alt || 'Pexels image'} 
                                  className="h-full w-full object-cover"
                                />
                              ) : item.video_files && (
                                <div className="relative h-full w-full bg-slate-800 flex items-center justify-center">
                                  <Video className="h-8 w-8 text-white opacity-70" />
                                </div>
                              )}
                            </div>
                            <div className="p-2">
                              <p className="text-xs text-slate-500 truncate">
                                {item.alt || item.user?.name || 'Pexels ' + (item.video_files ? 'video' : 'image')}
                              </p>
                              <Button 
                                variant="secondary" 
                                size="sm" 
                                className="w-full mt-1"
                                onClick={() => importExternalMedia(item, 'pexels')}
                              >
                                Import to Library
                              </Button>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </ScrollArea>
                </TabsContent>
                
                {/* Pixabay Tab */}
                <TabsContent value="pixabay" className="space-y-3">
                  <div className="flex items-center space-x-2 mb-3">
                    <Input 
                      placeholder="Search for free images & videos..." 
                      value={mediaSearchQuery}
                      onChange={(e) => setMediaSearchQuery(e.target.value)}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          searchMedia(mediaSearchQuery, 'pixabay');
                        }
                      }}
                    />
                    <Button 
                      size="sm" 
                      onClick={() => searchMedia(mediaSearchQuery, 'pixabay')}
                      disabled={isSearching || !mediaSearchQuery.trim()}
                    >
                      {isSearching ? (
                        <div className="h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent" />
                      ) : "Search"}
                    </Button>
                  </div>
                  
                  <ScrollArea className="h-[450px]">
                    {isSearching ? (
                      <div className="text-center py-6">
                        <div className="h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent mx-auto mb-2" />
                        <p className="text-sm text-slate-500">Searching Pixabay...</p>
                      </div>
                    ) : pixabayResults.length === 0 ? (
                      <div className="text-center py-6">
                        <Image className="h-10 w-10 text-slate-300 mx-auto mb-2" />
                        <h4 className="text-sm font-medium mb-1">No media found</h4>
                        <p className="text-xs text-slate-500">
                          Search for images and videos on Pixabay
                        </p>
                      </div>
                    ) : (
                      <div className="grid grid-cols-2 gap-3">
                        {pixabayResults.map((item) => (
                          <div 
                            key={item.id} 
                            className="border border-slate-200 rounded-md overflow-hidden"
                          >
                            <div className="relative h-32 bg-slate-100">
                              {item.previewURL || item.webformatURL ? (
                                <img 
                                  src={item.webformatURL || item.previewURL} 
                                  alt={item.tags || 'Pixabay image'} 
                                  className="h-full w-full object-cover"
                                />
                              ) : item.videos && (
                                <div className="relative h-full w-full bg-slate-800 flex items-center justify-center">
                                  <Video className="h-8 w-8 text-white opacity-70" />
                                </div>
                              )}
                            </div>
                            <div className="p-2">
                              <p className="text-xs text-slate-500 truncate">
                                {item.tags || item.user || 'Pixabay ' + (item.videos ? 'video' : 'image')}
                              </p>
                              <Button 
                                variant="secondary" 
                                size="sm" 
                                className="w-full mt-1"
                                onClick={() => importExternalMedia(item, 'pixabay')}
                              >
                                Import to Library
                              </Button>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </ScrollArea>
                </TabsContent>
              </Tabs>
              
              {/* Export Format Options */}
              <div className="mt-4 border-t border-slate-200 pt-4">
                <h4 className="text-sm font-medium mb-2">Export Settings</h4>
                <div className="space-y-3">
                  <div>
                    <Label className="text-xs mb-1 block">Export Format</Label>
                    <Select value={exportFormat} onValueChange={setExportFormat}>
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="Select format" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="video">Video</SelectItem>
                        <SelectItem value="slides">Slides</SelectItem>
                        <SelectItem value="pdf">PDF</SelectItem>
                        <SelectItem value="scorm">SCORM Package</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  {exportFormat === 'video' && (
                    <div>
                      <Label className="text-xs mb-1 block">Video Quality</Label>
                      <RadioGroup defaultValue="hd" className="flex space-x-2">
                        <div className="flex items-center space-x-1">
                          <RadioGroupItem value="sd" id="sd" />
                          <Label htmlFor="sd" className="text-xs">SD</Label>
                        </div>
                        <div className="flex items-center space-x-1">
                          <RadioGroupItem value="hd" id="hd" />
                          <Label htmlFor="hd" className="text-xs">HD</Label>
                        </div>
                        <div className="flex items-center space-x-1">
                          <RadioGroupItem value="4k" id="4k" />
                          <Label htmlFor="4k" className="text-xs">4K</Label>
                        </div>
                      </RadioGroup>
                    </div>
                  )}
                  
                  {exportFormat === 'slides' && (
                    <div>
                      <Label className="text-xs mb-1 block">Slide Format</Label>
                      <Select defaultValue="pptx">
                        <SelectTrigger className="w-full">
                          <SelectValue placeholder="Select format" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="pptx">PowerPoint (PPTX)</SelectItem>
                          <SelectItem value="keynote">Keynote</SelectItem>
                          <SelectItem value="google">Google Slides</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
      
      {/* Render the media selection dialog */}
      <MediaSelectionDialog />
    </div>
  );
}

// Helper function to format file size
function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
}