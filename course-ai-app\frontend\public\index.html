<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#667eea" />
    <meta
      name="description"
      content="AI-powered course creation platform with GPU-accelerated content generation"
    />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    <title>AI Course Builder - GPU-Accelerated Content Creation</title>
    
    <!-- Preload critical fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- Meta tags for SEO -->
    <meta property="og:title" content="AI Course Builder" />
    <meta property="og:description" content="Create professional courses with AI-generated images, videos, audio, and slides using A100 GPU acceleration" />
    <meta property="og:type" content="website" />
    
    <!-- Performance optimization -->
    <link rel="dns-prefetch" href="//fonts.googleapis.com">
    <link rel="dns-prefetch" href="//fonts.gstatic.com">
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    
    <!-- Loading fallback -->
    <style>
      .initial-loader {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
      }
      
      .loader-content {
        text-align: center;
        color: white;
      }
      
      .spinner {
        width: 40px;
        height: 40px;
        border: 4px solid rgba(255,255,255,0.3);
        border-top: 4px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 20px;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    </style>
    
    <script>
      // Remove loader when React app loads
      window.addEventListener('load', function() {
        const loader = document.querySelector('.initial-loader');
        if (loader) {
          setTimeout(() => {
            loader.style.opacity = '0';
            setTimeout(() => loader.remove(), 300);
          }, 1000);
        }
      });
    </script>
  </body>
</html>