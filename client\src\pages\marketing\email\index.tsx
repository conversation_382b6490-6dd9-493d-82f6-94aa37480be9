import React, { useState } from 'react';
import EmailLayout from './layout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { PaginationButton } from '@/components/ui/pagination-button';
import {
  Download,
  Filter,
  MoreVertical,
  Plus,
  Trash2,
  Upload,
  Users,
} from 'lucide-react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useForm } from 'react-hook-form';
import { useMediaQuery } from '@/hooks/use-media-query';

const SubscribersPage = () => {
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [activeFilter, setActiveFilter] = useState<string | null>(null);
  const [listFilter, setListFilter] = useState<string | null>(null);
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { isMobile } = useMediaQuery();

  // Fetch subscribers with pagination and filtering
  const { data: subscribersData, isLoading: subscribersLoading } = useQuery({
    queryKey: ['/api/subscribers', page, limit, activeFilter, listFilter],
    queryFn: async () => {
      let url = `/api/subscribers?page=${page}&limit=${limit}`;
      if (activeFilter) url += `&status=${activeFilter}`;
      if (listFilter) url += `&listId=${listFilter}`;
      const res = await apiRequest('GET', url);
      return res.json();
    },
  });

  // Fetch subscriber statistics
  const { data: statsData, isLoading: statsLoading } = useQuery({
    queryKey: ['/api/subscribers/stats'],
    queryFn: async () => {
      const res = await apiRequest('GET', `/api/subscribers/stats`);
      return res.json();
    },
  });

  // Fetch subscriber lists
  const { data: listsData, isLoading: listsLoading } = useQuery({
    queryKey: ['/api/subscribers/lists'],
    queryFn: async () => {
      const res = await apiRequest('GET', `/api/subscribers/lists`);
      return res.json();
    },
  });

  // Delete subscriber mutation
  const deleteMutation = useMutation({
    mutationFn: async (id: number) => {
      await apiRequest('DELETE', `/api/subscribers/${id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/subscribers'] });
      queryClient.invalidateQueries({ queryKey: ['/api/subscribers/stats'] });
      toast({
        title: 'Subscriber deleted',
        description: 'The subscriber has been deleted successfully.',
        variant: 'default',
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: `Failed to delete subscriber: ${error.message}`,
        variant: 'destructive',
      });
    },
  });

  const [importDialogOpen, setImportDialogOpen] = useState(false);
  const [addSubscriberDialogOpen, setAddSubscriberDialogOpen] = useState(false);
  const [newListDialogOpen, setNewListDialogOpen] = useState(false);

  // Add subscriber form schema
  const subscriberSchema = z.object({
    email: z.string().email({ message: "Please enter a valid email address" }),
    name: z.string().optional(),
    status: z.enum(['active', 'inactive', 'unsubscribed']).default('active'),
    listId: z.number().optional(),
  });

  // Create subscriber form
  const subscriberForm = useForm<z.infer<typeof subscriberSchema>>({
    resolver: zodResolver(subscriberSchema),
    defaultValues: {
      email: '',
      name: '',
      status: 'active',
    },
  });

  // Add subscriber mutation
  const addSubscriberMutation = useMutation({
    mutationFn: async (values: z.infer<typeof subscriberSchema>) => {
      const res = await apiRequest('POST', '/api/subscribers', values);
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/subscribers'] });
      queryClient.invalidateQueries({ queryKey: ['/api/subscribers/stats'] });
      setAddSubscriberDialogOpen(false);
      subscriberForm.reset();
      toast({
        title: 'Subscriber added',
        description: 'New subscriber has been added successfully.',
        variant: 'default',
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: `Failed to add subscriber: ${error.message}`,
        variant: 'destructive',
      });
    },
  });

  // List form schema
  const listSchema = z.object({
    name: z.string().min(1, { message: "List name is required" }),
    description: z.string().optional(),
  });

  // Create list form
  const listForm = useForm<z.infer<typeof listSchema>>({
    resolver: zodResolver(listSchema),
    defaultValues: {
      name: '',
      description: '',
    },
  });

  // Add list mutation
  const addListMutation = useMutation({
    mutationFn: async (values: z.infer<typeof listSchema>) => {
      const res = await apiRequest('POST', '/api/subscribers/lists', values);
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/subscribers/lists'] });
      setNewListDialogOpen(false);
      listForm.reset();
      toast({
        title: 'List created',
        description: 'New subscriber list has been created successfully.',
        variant: 'default',
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: `Failed to create list: ${error.message}`,
        variant: 'destructive',
      });
    },
  });

  // Handle file import
  const [importFile, setImportFile] = useState<File | null>(null);
  const [importListId, setImportListId] = useState<number | null>(null);

  const importMutation = useMutation({
    mutationFn: async () => {
      if (!importFile) return null;
      
      const formData = new FormData();
      formData.append('file', importFile);
      if (importListId) formData.append('listId', String(importListId));
      
      // Pass formData directly as the third parameter
      const res = await apiRequest('POST', '/api/subscribers/import', formData);
      return res.json();
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['/api/subscribers'] });
      queryClient.invalidateQueries({ queryKey: ['/api/subscribers/stats'] });
      setImportDialogOpen(false);
      setImportFile(null);
      setImportListId(null);
      toast({
        title: 'Import complete',
        description: `${data.imported} subscribers imported successfully.`,
        variant: 'default',
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'Import failed',
        description: `Failed to import subscribers: ${error.message}`,
        variant: 'destructive',
      });
    },
  });

  // Export subscribers
  const handleExport = async () => {
    try {
      let url = '/api/subscribers/export';
      const params = new URLSearchParams();
      if (activeFilter) params.append('status', activeFilter);
      if (listFilter) params.append('listId', listFilter);
      
      if (params.toString()) {
        url += `?${params.toString()}`;
      }
      
      // Trigger file download
      window.location.href = url;
      
      toast({
        title: 'Export started',
        description: 'Your subscriber export has started downloading.',
        variant: 'default',
      });
    } catch (error) {
      toast({
        title: 'Export failed',
        description: 'Failed to export subscribers.',
        variant: 'destructive',
      });
    }
  };
  
  // Render subscriber stats
  const renderStats = () => {
    if (statsLoading) {
      return (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          {Array.from({ length: 3 }).map((_, index) => (
            <Card key={index}>
              <CardHeader className="pb-2">
                <Skeleton className="h-5 w-1/2" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-16" />
              </CardContent>
            </Card>
          ))}
        </div>
      );
    }

    if (!statsData) return null;

    return (
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Total Subscribers</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-2xl font-bold">{statsData.total}</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Active Subscribers</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-2xl font-bold">{statsData.active}</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Unsubscribed</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-2xl font-bold">{statsData.unsubscribed}</p>
          </CardContent>
        </Card>
      </div>
    );
  };

  // Render subscriber table and filters
  return (
    <EmailLayout>
      {/* Stats Cards */}
      {renderStats()}

      {/* Action Buttons Row */}
      <div className="flex flex-wrap gap-2 mb-4">
        <Dialog open={addSubscriberDialogOpen} onOpenChange={setAddSubscriberDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Add Subscriber
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Add New Subscriber</DialogTitle>
              <DialogDescription>
                Enter the details of the new subscriber below.
              </DialogDescription>
            </DialogHeader>
            
            <Form {...subscriberForm}>
              <form onSubmit={subscriberForm.handleSubmit((values) => addSubscriberMutation.mutate(values))} className="space-y-4">
                <FormField
                  control={subscriberForm.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input placeholder="<EMAIL>" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={subscriberForm.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Name (Optional)</FormLabel>
                      <FormControl>
                        <Input placeholder="John Doe" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={subscriberForm.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Status</FormLabel>
                      <Select 
                        value={field.value} 
                        onValueChange={(value) => field.onChange(value)}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select status" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="active">Active</SelectItem>
                          <SelectItem value="inactive">Inactive</SelectItem>
                          <SelectItem value="unsubscribed">Unsubscribed</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={subscriberForm.control}
                  name="listId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Subscriber List (Optional)</FormLabel>
                      <Select 
                        value={field.value?.toString()} 
                        onValueChange={(value) => field.onChange(value ? parseInt(value) : undefined)}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select a list" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {listsData?.map((list: any) => (
                            <SelectItem key={list.id} value={list.id.toString()}>
                              {list.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <DialogFooter>
                  <Button 
                    type="submit"
                    disabled={addSubscriberMutation.isPending}
                  >
                    {addSubscriberMutation.isPending ? 'Adding...' : 'Add Subscriber'}
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
        
        <Dialog open={importDialogOpen} onOpenChange={setImportDialogOpen}>
          <DialogTrigger asChild>
            <Button variant="outline">
              <Upload className="mr-2 h-4 w-4" />
              Import
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Import Subscribers</DialogTitle>
              <DialogDescription>
                Upload a CSV file with subscriber data. The CSV file should have the following columns: email (required), name, status.
              </DialogDescription>
            </DialogHeader>
            
            <div className="space-y-4">
              <div className="grid w-full max-w-sm items-center gap-1.5">
                <FormLabel>CSV File</FormLabel>
                <Input 
                  type="file" 
                  accept=".csv" 
                  onChange={(e) => {
                    if (e.target.files && e.target.files.length > 0) {
                      setImportFile(e.target.files[0]);
                    }
                  }}
                />
                <FormDescription>
                  Only CSV files are accepted
                </FormDescription>
              </div>
              
              <div className="grid w-full max-w-sm items-center gap-1.5">
                <FormLabel>Add to List (Optional)</FormLabel>
                <Select 
                  value={importListId?.toString() || ''} 
                  onValueChange={(value) => setImportListId(value ? parseInt(value) : null)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a list" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">No list</SelectItem>
                    {listsData?.map((list: any) => (
                      <SelectItem key={list.id} value={list.id.toString()}>
                        {list.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <DialogFooter>
              <Button 
                onClick={() => importMutation.mutate()}
                disabled={!importFile || importMutation.isPending}
              >
                {importMutation.isPending ? 'Importing...' : 'Import Subscribers'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
        
        <Button variant="outline" onClick={handleExport}>
          <Download className="mr-2 h-4 w-4" />
          Export
        </Button>
        
        <Dialog open={newListDialogOpen} onOpenChange={setNewListDialogOpen}>
          <DialogTrigger asChild>
            <Button variant="outline">
              <Users className="mr-2 h-4 w-4" />
              New List
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Create Subscriber List</DialogTitle>
              <DialogDescription>
                Create a new list to organize your subscribers.
              </DialogDescription>
            </DialogHeader>
            
            <Form {...listForm}>
              <form onSubmit={listForm.handleSubmit((values) => addListMutation.mutate(values))} className="space-y-4">
                <FormField
                  control={listForm.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>List Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Newsletter Subscribers" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={listForm.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description (Optional)</FormLabel>
                      <FormControl>
                        <Input placeholder="Subscribers to our monthly newsletter" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <DialogFooter>
                  <Button 
                    type="submit"
                    disabled={addListMutation.isPending}
                  >
                    {addListMutation.isPending ? 'Creating...' : 'Create List'}
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      </div>
      
      {/* Filters Row */}
      <div className="flex flex-wrap gap-2 mb-4">
        <div className="flex items-center">
          <Filter className="mr-2 h-4 w-4 text-muted-foreground" />
          <span className="text-sm mr-2">Status:</span>
          <Select 
            value={activeFilter || 'all'} 
            onValueChange={(value) => setActiveFilter(value === 'all' ? null : value)}
          >
            <SelectTrigger className="h-8 w-[130px]">
              <SelectValue placeholder="Select status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Statuses</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="inactive">Inactive</SelectItem>
              <SelectItem value="unsubscribed">Unsubscribed</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <div className="flex items-center">
          <Users className="mr-2 h-4 w-4 text-muted-foreground" />
          <span className="text-sm mr-2">List:</span>
          <Select 
            value={listFilter || 'all'} 
            onValueChange={(value) => setListFilter(value === 'all' ? null : value)}
          >
            <SelectTrigger className="h-8 w-[150px]">
              <SelectValue placeholder="Select list" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Lists</SelectItem>
              {listsData?.map((list: any) => (
                <SelectItem key={list.id} value={list.id.toString()}>
                  {list.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>
      
      {/* Subscribers Table */}
      <Card>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Email</TableHead>
                <TableHead>Name</TableHead>
                {!isMobile && <TableHead>Status</TableHead>}
                {!isMobile && <TableHead>List</TableHead>}
                {!isMobile && <TableHead>Joined</TableHead>}
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {subscribersLoading ? (
                // Loading state
                Array.from({ length: 5 }).map((_, index) => (
                  <TableRow key={index}>
                    <TableCell colSpan={isMobile ? 3 : 6}>
                      <div className="flex items-center space-x-4">
                        <Skeleton className="h-4 w-[250px]" />
                        <Skeleton className="h-4 w-[100px]" />
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              ) : subscribersData?.data?.length > 0 ? (
                // Subscribers data
                subscribersData.data.map((subscriber: any) => (
                  <TableRow key={subscriber.id}>
                    <TableCell className="font-medium">{subscriber.email}</TableCell>
                    <TableCell>{subscriber.name || '-'}</TableCell>
                    {!isMobile && (
                      <TableCell>
                        <Badge variant={
                          subscriber.status === 'active' ? 'default' : 
                          subscriber.status === 'inactive' ? 'secondary' : 'destructive'
                        }>
                          {subscriber.status}
                        </Badge>
                      </TableCell>
                    )}
                    {!isMobile && (
                      <TableCell>
                        {subscriber.list?.name || '-'}
                      </TableCell>
                    )}
                    {!isMobile && (
                      <TableCell>
                        {new Date(subscriber.createdAt).toLocaleDateString()}
                      </TableCell>
                    )}
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <span className="sr-only">Open menu</span>
                            <MoreVertical className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem
                            onClick={() => {
                              /* Set up to edit subscriber */
                            }}
                          >
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            className="text-destructive focus:text-destructive"
                            onClick={() => {
                              if (window.confirm('Are you sure you want to delete this subscriber?')) {
                                deleteMutation.mutate(subscriber.id);
                              }
                            }}
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                // Empty state
                <TableRow>
                  <TableCell colSpan={isMobile ? 3 : 6} className="text-center py-4 text-muted-foreground">
                    No subscribers found. Add your first subscriber to get started.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>
        
        {/* Pagination */}
        {subscribersData?.pagination?.totalPages > 1 && (
          <CardFooter className="flex justify-center py-4">
            <PaginationButton
              currentPage={page}
              totalPages={subscribersData.pagination.totalPages}
              onPageChange={setPage}
            />
          </CardFooter>
        )}
      </Card>
    </EmailLayout>
  );
};

export default SubscribersPage;