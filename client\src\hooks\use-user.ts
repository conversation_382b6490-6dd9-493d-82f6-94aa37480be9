import { useQuery } from "@tanstack/react-query";
import { User } from '@/types';

export function useUser() {
  const { 
    data: user, 
    error, 
    isLoading,
    refetch 
  } = useQuery<User>({
    queryKey: ['/api/auth/me'],
    retry: false,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });

  return { 
    user: user || null, 
    error, 
    isLoading,
    refetch
  };
}