/* Accessibility Styles for Color Contrast Enhancement */

/* Enhanced Contrast Mode */
.color-contrast-enhanced {
  /* Increase text contrast */
  --color-text-base: #000000;
  --color-text-muted: #333333;
  --color-background: #ffffff;
  
  /* Increase border contrast */
  --color-border: #555555;
  
  /* Adjust primary colors for better contrast */
  --color-primary-lighter: color-mix(in srgb, var(--color-primary), #000000 20%);
  --color-primary-darker: color-mix(in srgb, var(--color-primary), #000000 30%);
  
  /* Adjustment to UI elements */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.15);
  --shadow-md: 0 4px 8px -2px rgba(0, 0, 0, 0.2), 0 2px 4px -2px rgba(0, 0, 0, 0.2);
  --shadow-lg: 0 12px 24px -4px rgba(0, 0, 0, 0.25), 0 8px 16px -4px rgba(0, 0, 0, 0.25);
}

/* High Contrast Mode */
.color-contrast-high {
  /* Maximum contrast for text */
  --color-text-base: #000000;
  --color-text-muted: #222222;
  --color-background: #ffffff;
  
  /* Bold borders for better definition */
  --color-border: #000000;
  
  /* Much darker primary colors for strongest contrast */
  --color-primary-lighter: color-mix(in srgb, var(--color-primary), #000000 30%);
  --color-primary-darker: color-mix(in srgb, var(--color-primary), #000000 50%);
  
  /* More defined shadows */
  --shadow-sm: 0 2px 3px 0 rgba(0, 0, 0, 0.25);
  --shadow-md: 0 5px 10px -3px rgba(0, 0, 0, 0.3), 0 3px 6px -3px rgba(0, 0, 0, 0.3);
  --shadow-lg: 0 15px 30px -5px rgba(0, 0, 0, 0.35), 0 10px 20px -5px rgba(0, 0, 0, 0.35);
  
  /* Additional high contrast overrides */
  --tw-ring-offset-color: #ffffff;
  --tw-ring-color: #000000;
  
  /* Ensure all buttons have strong borders */
  .btn, button:not(.unstyled) {
    border: 2px solid #000000 !important;
    outline: 1px solid white;
  }
  
  /* Increase focus indicators */
  *:focus-visible {
    outline: 3px solid black !important;
    outline-offset: 2px;
  }
  
  /* Ensure links are always underlined */
  a:not(.btn):not(button) {
    text-decoration: underline !important;
  }
  
  /* Ensure icons have sufficient contrast */
  svg {
    stroke-width: 2.5px;
  }
}

/* Font Size Adjustments */
.font-size-large {
  font-size: 112.5%; /* 1.125 times default size */
  
  h1 { font-size: 2.25rem !important; }
  h2 { font-size: 1.875rem !important; }
  h3 { font-size: 1.5rem !important; }
  h4 { font-size: 1.25rem !important; }
  p, li, button, input, select, textarea { font-size: 1.125rem !important; }
  .text-sm { font-size: 1rem !important; }
  .text-xs { font-size: 0.875rem !important; }
  
  /* Adjust line heights */
  line-height: 1.6;
}

.font-size-x-large {
  font-size: 125%; /* 1.25 times default size */
  
  h1 { font-size: 2.5rem !important; }
  h2 { font-size: 2rem !important; }
  h3 { font-size: 1.75rem !important; }
  h4 { font-size: 1.5rem !important; }
  p, li, button, input, select, textarea { font-size: 1.25rem !important; }
  .text-sm { font-size: 1.125rem !important; }
  .text-xs { font-size: 1rem !important; }
  
  /* Adjust line heights further */
  line-height: 1.7;
  
  /* Increase spacing */
  .space-y-1 { margin-top: 0.375rem !important; }
  .space-y-2 { margin-top: 0.625rem !important; }
  .space-y-4 { margin-top: 1.25rem !important; }
  
  /* Ensure buttons are big enough to tap */
  button, .btn, a.btn {
    min-height: 2.75rem;
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
  }
}

/* Motion Reduction */
.motion-reduced {
  * {
    animation-duration: 0.001ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.001ms !important;
    scroll-behavior: auto !important;
  }
  
  .animate-spin,
  .animate-pulse,
  .animate-bounce,
  .animate-ping {
    animation: none !important;
  }
  
  /* Replace animations with static elements */
  .animate-spin {
    opacity: 0.7;
  }
  
  /* Ensure no parallax effects */
  [style*="transform:"] {
    transform: none !important;
  }
}