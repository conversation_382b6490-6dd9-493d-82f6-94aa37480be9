import React, { useEffect, useRef, useState } from 'react';
import { 
  Pencil, 
  Square, 
  Circle, 
  Type, 
  Image as ImageIcon, 
  Trash2, 
  Download, 
  Undo, 
  Redo, 
  StickyNote, 
  Hand
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { useToast } from '@/hooks/use-toast';
import WhiteboardTemplates from './WhiteboardTemplates';

// Define tool types
type Tool = 'pen' | 'eraser' | 'rectangle' | 'circle' | 'text' | 'select' | 'image';

interface Point {
  x: number;
  y: number;
}

export interface Action {
  tool: Tool;
  points?: Point[];
  color?: string;
  size?: number;
  x?: number;
  y?: number;
  width?: number;
  height?: number;
  text?: string;
}

interface SimpleWhiteboardProps {
  readOnly?: boolean;
  onSave?: (data: string) => void;
}

const SimpleWhiteboard: React.FC<SimpleWhiteboardProps> = ({ 
  readOnly = false,
  onSave 
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [ctx, setCtx] = useState<CanvasRenderingContext2D | null>(null);
  const [isDrawing, setIsDrawing] = useState(false);
  const [tool, setTool] = useState<Tool>('pen');
  const [color, setColor] = useState('#000000');
  const [size, setSize] = useState(5);
  const [lastPoint, setLastPoint] = useState<Point | null>(null);
  const [actions, setActions] = useState<Action[]>([]);
  const [redoStack, setRedoStack] = useState<Action[]>([]);
  const [text, setText] = useState('');
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  // Initialize canvas
  useEffect(() => {
    if (canvasRef.current) {
      const canvas = canvasRef.current;
      canvas.width = canvas.clientWidth;
      canvas.height = canvas.clientHeight;
      
      const context = canvas.getContext('2d');
      if (context) {
        context.lineCap = 'round';
        context.lineJoin = 'round';
        setCtx(context);
      }
    }
  }, []);

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      if (canvasRef.current && ctx) {
        // Create a temporary canvas to store the current drawing
        const tempCanvas = document.createElement('canvas');
        tempCanvas.width = canvasRef.current.width;
        tempCanvas.height = canvasRef.current.height;
        const tempCtx = tempCanvas.getContext('2d');
        if (tempCtx) {
          tempCtx.drawImage(canvasRef.current, 0, 0);
        }
        
        // Resize the main canvas
        canvasRef.current.width = canvasRef.current.clientWidth;
        canvasRef.current.height = canvasRef.current.clientHeight;
        
        // Restore the drawing
        ctx.drawImage(tempCanvas, 0, 0);
      }
    };
    
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [ctx]);

  // Clear canvas and redraw all actions
  const redrawCanvas = () => {
    if (!ctx || !canvasRef.current) return;
    
    ctx.clearRect(0, 0, canvasRef.current.width, canvasRef.current.height);
    
    actions.forEach(action => {
      if (action.tool === 'pen' && action.points && action.points.length > 1) {
        ctx.beginPath();
        ctx.strokeStyle = action.color || '#000000';
        ctx.lineWidth = action.size || 5;
        
        ctx.moveTo(action.points[0].x, action.points[0].y);
        for (let i = 1; i < action.points.length; i++) {
          ctx.lineTo(action.points[i].x, action.points[i].y);
        }
        
        ctx.stroke();
      } else if (action.tool === 'eraser' && action.points && action.points.length > 1) {
        ctx.beginPath();
        ctx.strokeStyle = '#ffffff';
        ctx.lineWidth = action.size || 20;
        
        ctx.moveTo(action.points[0].x, action.points[0].y);
        for (let i = 1; i < action.points.length; i++) {
          ctx.lineTo(action.points[i].x, action.points[i].y);
        }
        
        ctx.stroke();
      } else if (action.tool === 'rectangle' && action.x !== undefined && action.y !== undefined && action.width !== undefined && action.height !== undefined) {
        ctx.beginPath();
        ctx.strokeStyle = action.color || '#000000';
        ctx.lineWidth = action.size || 2;
        ctx.strokeRect(action.x, action.y, action.width, action.height);
      } else if (action.tool === 'circle' && action.x !== undefined && action.y !== undefined && action.width !== undefined) {
        const radius = action.width / 2;
        ctx.beginPath();
        ctx.strokeStyle = action.color || '#000000';
        ctx.lineWidth = action.size || 2;
        ctx.arc(action.x + radius, action.y + radius, radius, 0, Math.PI * 2);
        ctx.stroke();
      } else if (action.tool === 'text' && action.x !== undefined && action.y !== undefined && action.text) {
        ctx.font = `${action.size || 16}px Arial`;
        ctx.fillStyle = action.color || '#000000';
        ctx.fillText(action.text, action.x, action.y);
      } else if (action.tool === 'image' && action.x !== undefined && action.y !== undefined && action.width !== undefined && action.height !== undefined && action.text) {
        // For images, load from the stored data URL
        const img = new Image();
        img.onload = () => {
          if (action.x !== undefined && action.y !== undefined && action.width !== undefined && action.height !== undefined) {
            ctx.drawImage(img, action.x, action.y, action.width, action.height);
          }
        };
        img.src = action.text;
      }
    });
  };

  // Handle mouse down
  const handleMouseDown = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (readOnly) return;
    
    const rect = canvasRef.current?.getBoundingClientRect();
    if (!rect) return;
    
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    
    setIsDrawing(true);
    setLastPoint({ x, y });
    
    if (tool === 'text') {
      if (!text) {
        // Show a prompt if text is empty
        const promptText = window.prompt('Enter text to add to the whiteboard:', 'Sample Text');
        if (promptText) {
          const newAction: Action = {
            tool: 'text',
            x,
            y,
            color,
            size,
            text: promptText
          };
          
          setActions(prev => [...prev, newAction]);
          setRedoStack([]);
          
          // Add the text to the canvas immediately
          if (ctx) {
            ctx.font = `${size}px Arial`;
            ctx.fillStyle = color;
            ctx.fillText(promptText, x, y);
          }
        }
      } else {
        const newAction: Action = {
          tool: 'text',
          x,
          y,
          color,
          size,
          text
        };
        
        setActions(prev => [...prev, newAction]);
        setRedoStack([]);
        setText('');
        
        // Add the text to the canvas immediately
        if (ctx) {
          ctx.font = `${size}px Arial`;
          ctx.fillStyle = color;
          ctx.fillText(text, x, y);
        }
      }
    }
  };

  // Handle mouse move
  const handleMouseMove = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (readOnly || !isDrawing || !lastPoint || !ctx || !canvasRef.current) return;
    
    const rect = canvasRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    
    if (tool === 'pen' || tool === 'eraser') {
      ctx.beginPath();
      ctx.strokeStyle = tool === 'pen' ? color : '#ffffff';
      ctx.lineWidth = tool === 'pen' ? size : size * 4; // Make eraser larger
      
      ctx.moveTo(lastPoint.x, lastPoint.y);
      ctx.lineTo(x, y);
      ctx.stroke();
      
      setLastPoint({ x, y });
      
      // Add to current action's points or create a new action
      setActions(prev => {
        const lastAction = prev[prev.length - 1];
        
        if (lastAction && lastAction.tool === tool && isDrawing) {
          return [
            ...prev.slice(0, -1),
            {
              ...lastAction,
              points: [...(lastAction.points || []), { x, y }]
            }
          ];
        } else {
          return [
            ...prev,
            {
              tool,
              points: [lastPoint, { x, y }],
              color: tool === 'pen' ? color : '#ffffff',
              size: tool === 'pen' ? size : size * 4
            }
          ];
        }
      });
    } else if (tool === 'rectangle' || tool === 'circle') {
      // Preview drawing
      redrawCanvas();
      
      ctx.beginPath();
      ctx.strokeStyle = color;
      ctx.lineWidth = size;
      
      if (tool === 'rectangle') {
        ctx.strokeRect(lastPoint.x, lastPoint.y, x - lastPoint.x, y - lastPoint.y);
      } else {
        const width = Math.abs(x - lastPoint.x);
        const height = Math.abs(y - lastPoint.y);
        const radius = Math.max(width, height) / 2;
        
        ctx.arc(
          lastPoint.x + (x > lastPoint.x ? radius : -radius),
          lastPoint.y + (y > lastPoint.y ? radius : -radius),
          radius,
          0,
          Math.PI * 2
        );
        ctx.stroke();
      }
    }
  };

  // Handle mouse up
  const handleMouseUp = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (readOnly || !isDrawing || !lastPoint || !canvasRef.current) return;
    
    const rect = canvasRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    
    if (tool === 'rectangle') {
      const newAction: Action = {
        tool: 'rectangle',
        x: lastPoint.x,
        y: lastPoint.y,
        width: x - lastPoint.x,
        height: y - lastPoint.y,
        color,
        size
      };
      
      setActions(prev => [...prev, newAction]);
      setRedoStack([]);
    } else if (tool === 'circle') {
      const width = Math.abs(x - lastPoint.x);
      const height = Math.abs(y - lastPoint.y);
      const radius = Math.max(width, height);
      
      const newAction: Action = {
        tool: 'circle',
        x: lastPoint.x - (x < lastPoint.x ? radius : 0),
        y: lastPoint.y - (y < lastPoint.y ? radius : 0),
        width: radius * 2,
        height: radius * 2,
        color,
        size
      };
      
      setActions(prev => [...prev, newAction]);
      setRedoStack([]);
    }
    
    setIsDrawing(false);
    setLastPoint(null);
    redrawCanvas();
  };

  // Handle undo
  const handleUndo = () => {
    if (actions.length === 0) return;
    
    const lastAction = actions[actions.length - 1];
    setRedoStack(prev => [...prev, lastAction]);
    setActions(prev => prev.slice(0, -1));
  };

  // Handle redo
  const handleRedo = () => {
    if (redoStack.length === 0) return;
    
    const nextAction = redoStack[redoStack.length - 1];
    setActions(prev => [...prev, nextAction]);
    setRedoStack(prev => prev.slice(0, -1));
  };

  // Handle clear
  const handleClear = () => {
    if (actions.length === 0) return;
    
    // Add all current actions to redo stack
    setRedoStack(prev => [...prev, ...actions]);
    setActions([]);
  };

  // Handle export as PNG
  const handleExportPNG = () => {
    if (!canvasRef.current) return;
    
    const link = document.createElement('a');
    link.download = `whiteboard-${new Date().toISOString()}.png`;
    link.href = canvasRef.current.toDataURL('image/png');
    link.click();
    
    toast({
      title: "Export Successful",
      description: "Your whiteboard has been exported as a PNG file.",
    });
  };

  // Handle save
  const handleSave = () => {
    if (!onSave) return;
    
    onSave(JSON.stringify({ actions }));
    
    toast({
      title: "Save Successful",
      description: "Your whiteboard has been saved.",
    });
  };

  // Upload image
  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files || e.target.files.length === 0 || !ctx || !canvasRef.current) return;
    
    const file = e.target.files[0];
    const reader = new FileReader();
    
    reader.onload = (event) => {
      if (!event.target) return;
      const result = event.target.result;
      if (!result) return;
      
      const img = new Image();
      img.onload = () => {
        // Scale the image to fit the canvas
        const maxWidth = canvasRef.current!.width * 0.8;
        const maxHeight = canvasRef.current!.height * 0.8;
        
        let width = img.width;
        let height = img.height;
        
        if (width > maxWidth) {
          const ratio = maxWidth / width;
          width *= ratio;
          height *= ratio;
        }
        
        if (height > maxHeight) {
          const ratio = maxHeight / height;
          width *= ratio;
          height *= ratio;
        }
        
        // Center the image
        const x = (canvasRef.current!.width - width) / 2;
        const y = (canvasRef.current!.height - height) / 2;
        
        // Draw the image
        ctx.drawImage(img, x, y, width, height);
        
        // Store the image data URL for redrawing
        const imageDataUrl = result as string;
        
        // Add as a custom image action
        setActions(prev => [
          ...prev,
          {
            tool: 'image',
            x,
            y,
            width,
            height,
            text: imageDataUrl // Store the image data URL in the text field
          }
        ]);
        
        setRedoStack([]);
        
        toast({
          title: "Image Uploaded",
          description: "Image has been added to the whiteboard.",
        });
      };
      
      img.src = result as string;
    };
    
    reader.readAsDataURL(file);
    e.target.value = '';
  };

  // Handle template selection
  const handleTemplateSelect = (templateActions: Action[]) => {
    // Clear current actions
    setActions([]);
    setRedoStack([]);
    
    // Add template actions
    setActions(templateActions);
    
    toast({
      title: "Template Applied",
      description: "The selected template has been applied to your whiteboard.",
    });
  };

  // Redraw canvas when actions change
  useEffect(() => {
    redrawCanvas();
  }, [actions]);

  return (
    <div className="flex flex-col w-full h-full gap-4">
      <div className="flex flex-wrap items-center gap-2 p-2 border rounded-md">
        <Button
          variant={tool === 'select' ? 'default' : 'outline'}
          size="sm"
          onClick={() => setTool('select')}
          disabled={readOnly}
          title="Select"
        >
          <Hand size={16} />
        </Button>
        
        <Button
          variant={tool === 'pen' ? 'default' : 'outline'}
          size="sm"
          onClick={() => setTool('pen')}
          disabled={readOnly}
          title="Pen"
        >
          <Pencil size={16} />
        </Button>
        
        <Button
          variant={tool === 'eraser' ? 'default' : 'outline'}
          size="sm"
          onClick={() => setTool('eraser')}
          disabled={readOnly}
          title="Eraser"
        >
          <Trash2 size={16} />
        </Button>
        
        <Button
          variant={tool === 'rectangle' ? 'default' : 'outline'}
          size="sm"
          onClick={() => setTool('rectangle')}
          disabled={readOnly}
          title="Rectangle"
        >
          <Square size={16} />
        </Button>
        
        <Button
          variant={tool === 'circle' ? 'default' : 'outline'}
          size="sm"
          onClick={() => setTool('circle')}
          disabled={readOnly}
          title="Circle"
        >
          <Circle size={16} />
        </Button>
        
        <Button
          variant={tool === 'text' ? 'default' : 'outline'}
          size="sm"
          onClick={() => setTool('text')}
          disabled={readOnly}
          title="Text"
        >
          <Type size={16} />
        </Button>
        
        <input
          type="color"
          value={color}
          onChange={(e) => setColor(e.target.value)}
          className="h-9 w-9 rounded-md border border-input cursor-pointer"
          disabled={readOnly}
          title="Color"
        />
        
        <div className="flex items-center gap-2">
          <Label htmlFor="size-slider" className="text-xs">Size:</Label>
          <Slider
            id="size-slider"
            min={1}
            max={30}
            step={1}
            value={[size]}
            onValueChange={(value) => setSize(value[0])}
            className="w-24"
            disabled={readOnly}
          />
        </div>
        
        <div className="flex-grow"></div>
        
        <div className="flex items-center gap-2">
          {!readOnly && (
            <WhiteboardTemplates onSelectTemplate={handleTemplateSelect} />
          )}
          
          <Button
            variant="outline"
            size="sm"
            onClick={handleUndo}
            disabled={readOnly || actions.length === 0}
            title="Undo"
          >
            <Undo size={16} />
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={handleRedo}
            disabled={readOnly || redoStack.length === 0}
            title="Redo"
          >
            <Redo size={16} />
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={handleClear}
            disabled={readOnly || actions.length === 0}
            title="Clear All"
          >
            <Trash2 size={16} />
          </Button>
          
          <input
            type="file"
            ref={fileInputRef}
            className="hidden"
            accept="image/*"
            onChange={handleImageUpload}
            disabled={readOnly}
          />
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => fileInputRef.current?.click()}
            disabled={readOnly}
            title="Upload Image"
          >
            <ImageIcon size={16} />
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={handleExportPNG}
            title="Export as PNG"
          >
            <Download size={16} />
          </Button>
          
          {onSave && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleSave}
              disabled={readOnly}
              title="Save"
            >
              Save
            </Button>
          )}
        </div>
      </div>
      
      {tool === 'text' && (
        <div className="flex items-center gap-2 p-2 border rounded-md">
          <Label htmlFor="text-input">Text:</Label>
          <Input
            id="text-input"
            value={text}
            onChange={(e) => setText(e.target.value)}
            placeholder="Click on canvas to add text"
            className="max-w-md"
          />
        </div>
      )}
      
      <div className="flex-grow relative border rounded-md overflow-hidden">
        <canvas
          ref={canvasRef}
          className="absolute top-0 left-0 w-full h-full cursor-crosshair"
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseUp}
        />
      </div>
    </div>
  );
};

export default SimpleWhiteboard;