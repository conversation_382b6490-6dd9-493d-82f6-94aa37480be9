#!/usr/bin/env python3
"""
Modal Example Runner
This script properly handles the Modal import and execution in the Replit environment.
"""

import sys
import os
import subprocess

def find_python_with_modal():
    """Find a Python interpreter that has Modal available."""
    
    # Try different Python executables
    python_candidates = [
        'python3.11',
        'python3',
        'python',
    ]
    
    # Also check Nix store paths
    nix_pythons = []
    if os.path.exists('/nix/store'):
        try:
            # Look for Python in Nix store (limited search to avoid timeout)
            import glob
            nix_pythons = glob.glob('/nix/store/*/bin/python3*')[:5]
        except:
            pass
    
    all_candidates = python_candidates + nix_pythons
    
    for python_cmd in all_candidates:
        try:
            # Test if this Python can import modal
            result = subprocess.run([python_cmd, '-c', 'import modal; print("Modal found")'], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                print(f"Found working Python with Modal: {python_cmd}")
                return python_cmd
        except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.SubprocessError):
            continue
    
    return None

def install_modal_if_needed(python_cmd):
    """Install Modal using the given Python interpreter."""
    try:
        print(f"Installing Modal using {python_cmd}...")
        result = subprocess.run([python_cmd, '-m', 'pip', 'install', 'modal', '--user'], 
                              capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            print("Modal installed successfully")
            return True
        else:
            print(f"Modal installation failed: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print("Modal installation timed out")
        return False
    except Exception as e:
        print(f"Modal installation error: {e}")
        return False

def run_modal_example(python_cmd):
    """Run the modal example using the specified Python interpreter."""
    try:
        print(f"Running modal_example.py with {python_cmd}...")
        result = subprocess.run([python_cmd, 'modal_example.py'], 
                              capture_output=False, timeout=60)
        return result.returncode == 0
    except subprocess.TimeoutExpired:
        print("Modal example execution timed out")
        return False
    except Exception as e:
        print(f"Error running modal example: {e}")
        return False

def main():
    print("Modal Import Resolver")
    print("=" * 30)
    
    # Check if modal_example.py exists
    if not os.path.exists('modal_example.py'):
        print("Error: modal_example.py not found")
        return False
    
    # Try to find a working Python with Modal
    python_cmd = find_python_with_modal()
    
    if python_cmd:
        print(f"Using Python: {python_cmd}")
        success = run_modal_example(python_cmd)
        if success:
            print("Modal example executed successfully!")
            return True
        else:
            print("Modal example execution failed")
            return False
    
    # If no Python with Modal found, try to install it
    print("No Python with Modal found. Attempting installation...")
    
    # Try system Python first
    for python_cmd in ['python3', 'python']:
        try:
            result = subprocess.run([python_cmd, '--version'], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                print(f"Found system Python: {python_cmd}")
                if install_modal_if_needed(python_cmd):
                    success = run_modal_example(python_cmd)
                    if success:
                        print("Modal example executed successfully!")
                        return True
        except:
            continue
    
    print("Could not resolve Modal import issue")
    print("Please ensure Python and Modal are properly installed in your environment")
    return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)