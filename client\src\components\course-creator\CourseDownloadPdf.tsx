import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { 
  FileDown, 
  CheckCircle, 
  AlertCircle,
  FileText
} from 'lucide-react';
import { downloadCoursePdf } from '@/services/pdf-generator';
import { useToast } from '@/hooks/use-toast';
import { useHints } from '@/components/hints/HintProvider';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from "@/components/ui/card";

interface CourseDownloadPdfProps {
  courseId: number;
  courseTitle?: string;
}

/**
 * A component for downloading a course as a PDF file
 */
export default function CourseDownloadPdf({ courseId, courseTitle }: CourseDownloadPdfProps) {
  const { toast } = useToast();
  const [isGenerating, setIsGenerating] = useState(false);
  const { showHint } = useHints();
  
  useEffect(() => {
    // Show PDF export hint when component mounts
    showHint("pdfDownload");
  }, [showHint]);
  
  const handleDownload = async () => {
    try {
      setIsGenerating(true);
      
      toast({
        title: 'Generating PDF',
        description: 'Please wait while we prepare your course PDF...',
      });
      
      await downloadCoursePdf(courseId);
      
      toast({
        title: 'PDF Generated Successfully',
        description: 'Your course PDF has been downloaded.',
      });
      
    } catch (error) {
      console.error('Error downloading PDF:', error);
      
      toast({
        title: 'PDF Generation Failed',
        description: 'There was an error generating your course PDF. Please try again later.',
        variant: 'destructive',
      });
      
    } finally {
      setIsGenerating(false);
    }
  };
  
  return (
    <Card className="border-2 border-dashed">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileText className="h-5 w-5 text-primary" />
          Download Course PDF
        </CardTitle>
        <CardDescription>
          Export and share your course content as a PDF document
        </CardDescription>
      </CardHeader>
      <CardContent>
        <p className="text-sm text-muted-foreground mb-4">
          Create a downloadable PDF version of your course including all modules, 
          lessons, and scripts. Perfect for offline reference or sharing with students 
          who prefer reading materials.
        </p>
        
        <div className="space-y-2">
          <div className="flex items-start gap-2">
            <CheckCircle className="h-4 w-4 text-green-500 mt-0.5" />
            <p className="text-sm">Includes all module and lesson content</p>
          </div>
          <div className="flex items-start gap-2">
            <CheckCircle className="h-4 w-4 text-green-500 mt-0.5" />
            <p className="text-sm">Professionally formatted for easy reading</p>
          </div>
          <div className="flex items-start gap-2">
            <CheckCircle className="h-4 w-4 text-green-500 mt-0.5" />
            <p className="text-sm">Lesson scripts and descriptions included</p>
          </div>
          <div className="flex items-start gap-2">
            <AlertCircle className="h-4 w-4 text-amber-500 mt-0.5" />
            <p className="text-sm">Note: Videos and interactive elements are not included in the PDF</p>
          </div>
        </div>
      </CardContent>
      <CardFooter>
        <Button 
          onClick={handleDownload} 
          disabled={isGenerating}
          className="w-full"
        >
          <FileDown className="mr-2 h-4 w-4" />
          {isGenerating ? 'Generating PDF...' : 'Download Course PDF'}
        </Button>
      </CardFooter>
    </Card>
  );
}