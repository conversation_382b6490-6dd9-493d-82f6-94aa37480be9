#!/usr/bin/env python3
"""
Proper Modal installation script for Replit environment
"""

import sys
import subprocess
import os
import tempfile

def install_modal_with_constraints():
    """Install Modal with proper dependency management"""
    try:
        # Create a temporary requirements file to handle conflicts
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            # Write Modal requirement with specific constraints
            f.write("modal>=0.57.0\n")
            f.write("# Override conflicting TTS dependencies\n")
            f.write("tts==0.21.3\n")  # Use older compatible version
            temp_file = f.name
        
        # Try installing with the constraints file
        cmd = [
            sys.executable, "-m", "pip", "install", 
            "-r", temp_file,
            "--no-deps",  # Skip automatic dependency resolution
            "--force-reinstall"
        ]
        
        print(f"Running: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        # Clean up temp file
        os.unlink(temp_file)
        
        if result.returncode == 0:
            print("Modal installation successful")
            return True
        else:
            print(f"Installation failed: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"Installation error: {e}")
        return False

def install_modal_minimal():
    """Install just the core Modal package"""
    try:
        cmd = [
            sys.executable, "-m", "pip", "install", 
            "modal-client",  # Try the client-only package
            "--no-deps"
        ]
        
        print(f"Trying minimal install: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("Modal client installation successful")
            return True
        else:
            # Try the full package with no deps
            cmd = [
                sys.executable, "-m", "pip", "install", 
                "modal",
                "--no-deps"
            ]
            
            print(f"Trying full package with no deps: {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("Modal full package installation successful")
                return True
            else:
                print(f"Both installations failed: {result.stderr}")
                return False
                
    except Exception as e:
        print(f"Minimal installation error: {e}")
        return False

def test_modal():
    """Test if Modal can be imported"""
    try:
        import modal
        print(f"Modal imported successfully! Version: {getattr(modal, '__version__', 'unknown')}")
        
        # Test basic functionality
        try:
            app = modal.App("test-app")
            print("Modal App creation test passed")
            return True
        except Exception as e:
            print(f"Modal functionality test failed: {e}")
            return False
            
    except ImportError as e:
        print(f"Modal import failed: {e}")
        return False

def main():
    print("Installing Modal for Replit environment...")
    print("=" * 50)
    
    # First check if it's already installed
    if test_modal():
        print("Modal is already working!")
        return True
    
    # Try minimal installation first
    print("\nAttempting minimal Modal installation...")
    if install_modal_minimal():
        if test_modal():
            print("Success! Modal is now working.")
            return True
    
    # Try with constraints
    print("\nAttempting Modal installation with dependency constraints...")
    if install_modal_with_constraints():
        if test_modal():
            print("Success! Modal is now working.")
            return True
    
    print("\nModal installation failed. Manual intervention may be required.")
    return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)