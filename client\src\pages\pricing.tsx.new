import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ader2, <PERSON><PERSON>ard, CheckCircle } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useUser } from "@/hooks/use-user";
import { useLocation } from 'wouter';
import { useToast } from "@/hooks/use-toast";
import { loadStripe } from "@stripe/stripe-js";
import { Elements } from "@stripe/react-stripe-js";
import { apiRequest } from "@/lib/queryClient";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog";
import { useQueryClient } from "@tanstack/react-query";
import { CheckoutForm } from "@/components/ui/checkout-form";

// Initialize Stripe
const stripePromise = loadStripe(import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY || '');

interface PlanFeature {
  name: string;
  starter: React.ReactNode;
  pro: React.ReactNode;
  business: React.ReactNode;
  enterprise: React.ReactNode;
}

const PricingPage = () => {
  const { user, isLoading } = useUser();
  const [billingInterval, setBillingInterval] = useState<'monthly' | 'yearly'>('monthly');
  const [checkoutSessionId, setCheckoutSessionId] = useState<string | null>(null);
  const [showCheckoutModal, setShowCheckoutModal] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState<any>(null);
  const [checkoutStatus, setCheckoutStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');
  const [, navigate] = useLocation();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const features: PlanFeature[] = [
    { 
      name: 'Courses', 
      starter: '1', 
      pro: '3', 
      business: '10', 
      enterprise: 'Unlimited' 
    },
    { 
      name: 'AI Videos', 
      starter: '2', 
      pro: '7', 
      business: '20', 
      enterprise: 'Unlimited' 
    },
    { 
      name: 'Voiceovers', 
      starter: '5', 
      pro: '15', 
      business: '50', 
      enterprise: 'Unlimited' 
    },
    { 
      name: 'AI Images', 
      starter: '10', 
      pro: '30', 
      business: '100', 
      enterprise: 'Unlimited' 
    },
    { 
      name: 'Course Templates', 
      starter: <Check className="h-5 w-5 text-green-500" />, 
      pro: <Check className="h-5 w-5 text-green-500" />, 
      business: <Check className="h-5 w-5 text-green-500" />, 
      enterprise: <Check className="h-5 w-5 text-green-500" /> 
    },
    { 
      name: 'AI Script Generation', 
      starter: <Check className="h-5 w-5 text-green-500" />, 
      pro: <Check className="h-5 w-5 text-green-500" />, 
      business: <Check className="h-5 w-5 text-green-500" />, 
      enterprise: <Check className="h-5 w-5 text-green-500" /> 
    },
    { 
      name: 'Micro-learning Mode', 
      starter: <Minus className="h-5 w-5 text-gray-300" />, 
      pro: <Check className="h-5 w-5 text-green-500" />, 
      business: <Check className="h-5 w-5 text-green-500" />, 
      enterprise: <Check className="h-5 w-5 text-green-500" /> 
    },
    { 
      name: 'Team Collaboration', 
      starter: <Minus className="h-5 w-5 text-gray-300" />, 
      pro: <Check className="h-5 w-5 text-green-500" />, 
      business: <Check className="h-5 w-5 text-green-500" />, 
      enterprise: <Check className="h-5 w-5 text-green-500" /> 
    },
    { 
      name: 'Advanced Analytics', 
      starter: <Minus className="h-5 w-5 text-gray-300" />, 
      pro: <Minus className="h-5 w-5 text-gray-300" />, 
      business: <Check className="h-5 w-5 text-green-500" />, 
      enterprise: <Check className="h-5 w-5 text-green-500" /> 
    },
    { 
      name: 'API Access', 
      starter: <Minus className="h-5 w-5 text-gray-300" />, 
      pro: <Minus className="h-5 w-5 text-gray-300" />, 
      business: <Check className="h-5 w-5 text-green-500" />, 
      enterprise: <Check className="h-5 w-5 text-green-500" /> 
    },
    { 
      name: 'Priority Support', 
      starter: <Minus className="h-5 w-5 text-gray-300" />, 
      pro: <Minus className="h-5 w-5 text-gray-300" />, 
      business: <Check className="h-5 w-5 text-green-500" />, 
      enterprise: <Check className="h-5 w-5 text-green-500" /> 
    },
    { 
      name: 'Dedicated Success Manager', 
      starter: <Minus className="h-5 w-5 text-gray-300" />, 
      pro: <Minus className="h-5 w-5 text-gray-300" />, 
      business: <Minus className="h-5 w-5 text-gray-300" />, 
      enterprise: <Check className="h-5 w-5 text-green-500" /> 
    },
  ];

  const plans = [
    {
      id: 'starter',
      name: 'Starter',
      description: 'Essential features for creating basic courses',
      monthlyPrice: 19,
      yearlyPrice: 190,
      features: features.map(f => ({ name: f.name, value: f.starter })),
      popular: false,
      gradient: 'from-blue-400 to-blue-500'
    },
    {
      id: 'pro',
      name: 'Pro',
      description: 'Advanced features for professional educators',
      monthlyPrice: 49,
      yearlyPrice: 490,
      features: features.map(f => ({ name: f.name, value: f.pro })),
      popular: true,
      gradient: 'from-primary to-indigo-600'
    },
    {
      id: 'business',
      name: 'Business',
      description: 'Comprehensive solution for growing businesses',
      monthlyPrice: 99,
      yearlyPrice: 990,
      features: features.map(f => ({ name: f.name, value: f.business })),
      popular: false,
      gradient: 'from-purple-500 to-purple-600'
    },
    {
      id: 'enterprise',
      name: 'Enterprise',
      description: 'Custom solutions for large organizations',
      monthlyPrice: 299,
      yearlyPrice: 2990,
      features: features.map(f => ({ name: f.name, value: f.enterprise })),
      popular: false,
      gradient: 'from-gray-700 to-gray-800'
    },
  ];

  // Handle subscribe button click
  const handleSubscribe = async (planId: string) => {
    if (!user) {
      // If not logged in, redirect to login with returnTo
      navigate(`/login?returnTo=${encodeURIComponent('/pricing')}`);
      return;
    }
    
    setSelectedPlan(plans.find(p => p.id === planId));
    setCheckoutStatus('loading');
    setShowCheckoutModal(true);
    
    try {
      // Create a payment intent with the API for embedded checkout
      const response = await apiRequest('POST', '/api/payments/create-payment-intent', {
        planId,
        billingInterval
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to create payment intent');
      }
      
      // Get client secret for Stripe Elements
      const data = await response.json();
      setCheckoutSessionId(data.clientSecret);
      
    } catch (error: any) {
      console.error("Error creating payment intent:", error);
      toast({
        title: "Error",
        description: error.message || "Failed to initialize payment process",
        variant: "destructive",
      });
      setCheckoutStatus('error');
      setShowCheckoutModal(false);
    }
  };

  // Handle checkout completion
  const handleCheckoutComplete = () => {
    setCheckoutStatus('success');
    
    // After payment is successful, redirect to dashboard
    setTimeout(() => {
      queryClient.invalidateQueries({ queryKey: ['/api/auth/me'] });
      navigate('/dashboard');
    }, 2000);
  };

  return (
    <div className="container mx-auto px-4 py-16">
      <div className="text-center">
        <h1 className="text-4xl font-extrabold tracking-tight lg:text-5xl bg-gradient-to-r from-primary to-blue-600 bg-clip-text text-transparent pb-2">
          Choose Your Plan
        </h1>
        <p className="mx-auto mt-4 max-w-3xl text-xl text-muted-foreground">
          Flexible pricing options to match your course creation needs
        </p>
      </div>
      
      {/* Stripe Checkout Modal */}
      <Dialog open={showCheckoutModal} onOpenChange={setShowCheckoutModal}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle className="text-2xl font-bold">
              {checkoutStatus === 'success' ? (
                <span className="flex items-center gap-2 text-green-600">
                  <CheckCircle className="h-6 w-6" />
                  Payment Successful
                </span>
              ) : (
                <span>Complete Your Subscription</span>
              )}
            </DialogTitle>
            <DialogDescription>
              {checkoutStatus === 'success' 
                ? "Thank you for your subscription! You'll be redirected to your dashboard shortly."
                : `Please enter your payment details to subscribe to the ${selectedPlan?.name} plan.`}
            </DialogDescription>
          </DialogHeader>
          
          {checkoutStatus === 'loading' && checkoutSessionId && (
            <div className="py-4">
              {stripePromise && checkoutSessionId && (
                <Elements 
                  stripe={stripePromise} 
                  options={{
                    clientSecret: checkoutSessionId,
                    appearance: {
                      theme: 'stripe',
                      variables: {
                        colorPrimary: '#4f46e5',
                        colorBackground: '#ffffff',
                        colorText: '#1f2937',
                        colorDanger: '#ef4444',
                        fontFamily: 'system-ui, sans-serif',
                        borderRadius: '6px',
                      },
                    }
                  }}
                >
                  <CheckoutForm 
                    sessionId={checkoutSessionId} 
                    onSuccess={handleCheckoutComplete}
                    selectedPlan={selectedPlan}
                  />
                </Elements>
              )}
            </div>
          )}
          
          {checkoutStatus === 'success' && (
            <div className="flex flex-col items-center justify-center py-6">
              <CheckCircle className="h-16 w-16 text-green-500 mb-4" />
              <p className="text-center">Your subscription has been processed successfully. You'll be redirected to your dashboard momentarily.</p>
            </div>
          )}
          
          <DialogFooter>
            {checkoutStatus !== 'success' && (
              <Button 
                variant="outline" 
                onClick={() => setShowCheckoutModal(false)}
                disabled={checkoutStatus === 'loading'}
              >
                Cancel
              </Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <div className="mt-8 flex justify-center">
        <Tabs 
          defaultValue="monthly" 
          className="w-full max-w-4xl"
          onValueChange={(value) => setBillingInterval(value as 'monthly' | 'yearly')}
        >
          <TabsList className="grid w-[300px] mx-auto grid-cols-2">
            <TabsTrigger value="monthly">Monthly</TabsTrigger>
            <TabsTrigger value="yearly">
              Yearly
              <Badge className="ml-2 bg-green-500 hover:bg-green-600">Save 20%</Badge>
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="monthly" className="mt-8">
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
              {plans.map((plan) => (
                <Card 
                  key={plan.id}
                  className={`flex flex-col border-2 ${plan.popular ? 'border-primary shadow-lg' : 'border-border'}`}
                >
                  {plan.popular && (
                    <div className="absolute top-0 right-0 -mt-2 -mr-2">
                      <Badge className="bg-primary hover:bg-primary px-3 py-1 text-white">
                        <Sparkles className="h-3.5 w-3.5 mr-1" />
                        Popular
                      </Badge>
                    </div>
                  )}
                  
                  <CardHeader>
                    <CardTitle className={`text-2xl font-bold bg-gradient-to-r ${plan.gradient} bg-clip-text text-transparent`}>
                      {plan.name}
                    </CardTitle>
                    <CardDescription>{plan.description}</CardDescription>
                  </CardHeader>
                  
                  <CardContent className="flex-grow">
                    <div className="mb-6">
                      <span className="text-4xl font-bold">${plan.monthlyPrice}</span>
                      <span className="text-muted-foreground">/month</span>
                    </div>
                    
                    <ul className="space-y-3">
                      {plan.features.map((feature, index) => (
                        <li key={index} className="flex items-center">
                          <div className="w-6 h-6 flex-shrink-0">
                            {typeof feature.value === 'object' ? (
                              feature.value
                            ) : (
                              <span className="font-medium">{feature.value}</span>
                            )}
                          </div>
                          <span className="ml-3 text-sm text-muted-foreground">{feature.name}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                  
                  <CardFooter>
                    <Button 
                      className={`w-full ${plan.popular ? 'bg-primary hover:bg-primary/90' : ''}`}
                      onClick={() => handleSubscribe(plan.id)}
                      disabled={isLoading}
                    >
                      {isLoading ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <>
                          <CreditCard className="h-4 w-4 mr-2" />
                          Subscribe
                        </>
                      )}
                    </Button>
                  </CardFooter>
                </Card>
              ))}
            </div>
          </TabsContent>
          
          <TabsContent value="yearly" className="mt-8">
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
              {plans.map((plan) => (
                <Card 
                  key={plan.id}
                  className={`flex flex-col border-2 ${plan.popular ? 'border-primary shadow-lg' : 'border-border'}`}
                >
                  {plan.popular && (
                    <div className="absolute top-0 right-0 -mt-2 -mr-2">
                      <Badge className="bg-primary hover:bg-primary px-3 py-1 text-white">
                        <Sparkles className="h-3.5 w-3.5 mr-1" />
                        Popular
                      </Badge>
                    </div>
                  )}
                  
                  <CardHeader>
                    <CardTitle className={`text-2xl font-bold bg-gradient-to-r ${plan.gradient} bg-clip-text text-transparent`}>
                      {plan.name}
                    </CardTitle>
                    <CardDescription>{plan.description}</CardDescription>
                  </CardHeader>
                  
                  <CardContent className="flex-grow">
                    <div className="mb-6">
                      <span className="text-4xl font-bold">${plan.yearlyPrice}</span>
                      <span className="text-muted-foreground">/year</span>
                    </div>
                    
                    <ul className="space-y-3">
                      {plan.features.map((feature, index) => (
                        <li key={index} className="flex items-center">
                          <div className="w-6 h-6 flex-shrink-0">
                            {typeof feature.value === 'object' ? (
                              feature.value
                            ) : (
                              <span className="font-medium">{feature.value}</span>
                            )}
                          </div>
                          <span className="ml-3 text-sm text-muted-foreground">{feature.name}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                  
                  <CardFooter>
                    <Button 
                      className={`w-full ${plan.popular ? 'bg-primary hover:bg-primary/90' : ''}`}
                      onClick={() => handleSubscribe(plan.id)}
                      disabled={isLoading}
                    >
                      {isLoading ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <>
                          <CreditCard className="h-4 w-4 mr-2" />
                          Subscribe
                        </>
                      )}
                    </Button>
                  </CardFooter>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </div>
      
      <div className="mt-16 text-center">
        <h3 className="text-2xl font-bold mb-4">Need a custom plan?</h3>
        <p className="text-muted-foreground mb-6">Contact our sales team for enterprise solutions tailored to your needs.</p>
        <Button variant="outline" size="lg">
          Contact Sales
        </Button>
      </div>
    </div>
  );
};

export default PricingPage;