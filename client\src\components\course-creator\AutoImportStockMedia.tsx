import React, { useState, useEffect, useCallback, useRef } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Progress } from '@/components/ui/progress';
import { 
  Search, 
  Download, 
  Grid3X3, 
  List, 
  CheckCircle2, 
  Loader2, 
  ImageIcon, 
  PlayCircle, 
  ZoomIn, 
  Star,
  Heart,
  Eye,
  X,
  Filter,
  RefreshCw,
  Check,
  Plus
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useQueryClient } from '@tanstack/react-query';

interface StockItem {
  id: string;
  type: 'photo' | 'video';
  url: string;
  thumbnailUrl: string;
  title: string;
  tags: string;
  photographer?: string;
  user?: string;
  source: 'pexels' | 'pixabay';
  width: number;
  height: number;
  duration?: number;
  views?: number;
  likes?: number;
  downloads?: number;
}

interface AutoImportStockMediaProps {
  onMediaImported?: (count: number) => void;
}

const AutoImportStockMedia: React.FC<AutoImportStockMediaProps> = ({ onMediaImported }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [items, setItems] = useState<StockItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [previewItem, setPreviewItem] = useState<StockItem | null>(null);
  const [importing, setImporting] = useState<Set<string>>(new Set());
  const [imported, setImported] = useState<Set<string>>(new Set());
  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(1);
  const [mediaType, setMediaType] = useState<'all' | 'photo' | 'video'>('all');
  
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const searchTimeoutRef = useRef<NodeJS.Timeout>();
  const loadMoreRef = useRef<HTMLDivElement>(null);

  const SUGGESTED_SEARCHES = [
    'technology', 'education', 'business meeting', 'nature landscape',
    'city skyline', 'people working', 'abstract background', 'office space'
  ];

  // Auto-import function that saves media to user's library
  const autoImportMedia = useCallback(async (item: StockItem): Promise<boolean> => {
    const itemId = item.id;
    
    // Skip if already importing or imported
    if (importing.has(itemId) || imported.has(itemId)) {
      return true;
    }

    setImporting(prev => new Set([...prev, itemId]));

    try {
      // Extract proper ID from composite ID
      const actualId = itemId.replace(/^(pexels|pixabay)-(photo|video)-/, '');
      
      const response = await fetch('/api/stock/import', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          id: actualId,
          type: item.type,
          url: item.url,
          thumbnailUrl: item.thumbnailUrl,
          title: item.title,
          tags: item.tags,
          photographer: item.photographer || item.user,
          source: item.source,
          width: item.width,
          height: item.height,
          duration: item.duration,
          metadata: {
            views: item.views,
            likes: item.likes,
            downloads: item.downloads,
            author: item.photographer || item.user,
            size: 0
          }
        })
      });
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `Import failed: ${response.status}`);
      }

      const importedMedia = await response.json();
      console.log('Auto-imported media:', importedMedia);
      
      // Mark as imported
      setImported(prev => new Set([...prev, itemId]));
      
      // Invalidate media queries to refresh the library
      queryClient.invalidateQueries({ queryKey: ['/api/media'] });
      
      // Notify parent component
      onMediaImported?.(1);
      
      // Show discrete success indicator
      toast({
        title: "Added to Library",
        description: `"${item.title}" is now available in your Media Library.`,
        duration: 2000
      });
      
      return true;
      
    } catch (error: any) {
      console.error('Auto-import failed:', error);
      
      toast({
        title: "Import Failed",
        description: `Failed to add "${item.title}" to your library. Try again later.`,
        variant: "destructive",
        duration: 3000
      });
      
      return false;
    } finally {
      setImporting(prev => {
        const newSet = new Set(prev);
        newSet.delete(itemId);
        return newSet;
      });
    }
  }, [importing, imported, toast, queryClient, onMediaImported]);

  // Debounced search function
  const debouncedSearch = useCallback((query: string, resetPage = true) => {
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    searchTimeoutRef.current = setTimeout(() => {
      if (query.trim()) {
        performSearch(query, resetPage ? 1 : page);
      } else {
        setItems([]);
        setHasMore(true);
      }
    }, 500);
  }, [page]);

  const performSearch = async (query: string, searchPage: number = 1) => {
    if (!query.trim()) return;

    setLoading(true);
    try {
      const params = new URLSearchParams({
        query: query.trim(),
        page: searchPage.toString(),
        perPage: '20'
      });

      const results: StockItem[] = [];

      // Search photos if needed
      if (mediaType === 'photo' || mediaType === 'all') {
        try {
          const [pexelsPhotos, pixabayPhotos] = await Promise.all([
            fetch(`/api/pexels/photos?${params}`).then(r => r.ok ? r.json() : { photos: [] }),
            fetch(`/api/pixabay/photos?${params}`).then(r => r.ok ? r.json() : [])
          ]);

          // Process Pexels photos
          if (pexelsPhotos.photos) {
            results.push(...pexelsPhotos.photos.map((photo: any) => ({
              id: `pexels-photo-${photo.id}`,
              type: 'photo' as const,
              url: photo.src.large,
              thumbnailUrl: photo.src.medium,
              title: photo.alt || `Photo by ${photo.photographer}`,
              tags: photo.alt || '',
              photographer: photo.photographer,
              source: 'pexels' as const,
              width: photo.width,
              height: photo.height
            })));
          }

          // Process Pixabay photos
          if (Array.isArray(pixabayPhotos)) {
            results.push(...pixabayPhotos.map((photo: any) => ({
              id: `pixabay-photo-${photo.id}`,
              type: 'photo' as const,
              url: photo.largeImageURL || photo.url,
              thumbnailUrl: photo.webformatURL || photo.thumbnail,
              title: photo.tags || `Photo by ${photo.user || photo.photographer}`,
              tags: photo.tags || '',
              user: photo.user || photo.photographer,
              source: 'pixabay' as const,
              width: photo.imageWidth || photo.width,
              height: photo.imageHeight || photo.height,
              views: photo.views,
              likes: photo.likes,
              downloads: photo.downloads
            })));
          }
        } catch (error) {
          console.error('Error fetching photos:', error);
        }
      }

      // Search videos if needed
      if (mediaType === 'video' || mediaType === 'all') {
        try {
          const [pexelsVideos, pixabayVideos] = await Promise.all([
            fetch(`/api/pexels/videos?${params}`).then(r => r.ok ? r.json() : []),
            fetch(`/api/pixabay/videos?${params}`).then(r => r.ok ? r.json() : [])
          ]);

          // Process Pexels videos
          if (Array.isArray(pexelsVideos)) {
            results.push(...pexelsVideos.map((video: any) => ({
              id: `pexels-video-${video.id}`,
              type: 'video' as const,
              url: video.video_files?.[0]?.link || video.url,
              thumbnailUrl: video.image || video.thumbnail,
              title: `Video by ${video.user?.name || video.photographer}`,
              tags: video.tags || '',
              photographer: video.user?.name || video.photographer,
              source: 'pexels' as const,
              width: video.width,
              height: video.height,
              duration: video.duration
            })));
          }

          // Process Pixabay videos
          if (Array.isArray(pixabayVideos)) {
            results.push(...pixabayVideos.map((video: any) => ({
              id: `pixabay-video-${video.id}`,
              type: 'video' as const,
              url: video.videos?.large?.url || video.url,
              thumbnailUrl: video.picture_id || video.thumbnail,
              title: video.tags || `Video by ${video.user}`,
              tags: video.tags || '',
              user: video.user,
              source: 'pixabay' as const,
              width: video.videos?.large?.width || video.width,
              height: video.videos?.large?.height || video.height,
              duration: video.duration,
              views: video.views,
              likes: video.likes
            })));
          }
        } catch (error) {
          console.error('Error fetching videos:', error);
        }
      }

      // Shuffle results for variety
      const shuffledResults = results.sort(() => Math.random() - 0.5);

      if (searchPage === 1) {
        setItems(shuffledResults);
        setPage(1);
      } else {
        setItems(prev => [...prev, ...shuffledResults]);
      }

      setHasMore(shuffledResults.length >= 20);
      setPage(searchPage);
    } catch (error) {
      console.error('Search error:', error);
      toast({
        title: "Search Error",
        description: "Failed to search stock media. Please try again.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleItemClick = async (item: StockItem) => {
    // Auto-import on click/interaction
    await autoImportMedia(item);
  };

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // Search effect
  useEffect(() => {
    if (searchQuery.trim()) {
      debouncedSearch(searchQuery, true);
    }
  }, [searchQuery, mediaType, debouncedSearch]);

  // Intersection observer for infinite scroll
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && hasMore && !loading && searchQuery.trim()) {
          debouncedSearch(searchQuery, false);
          setPage(prev => prev + 1);
        }
      },
      { threshold: 0.1 }
    );

    if (loadMoreRef.current) {
      observer.observe(loadMoreRef.current);
    }

    return () => observer.disconnect();
  }, [hasMore, loading, searchQuery, debouncedSearch]);

  const renderItem = (item: StockItem) => {
    const isImporting = importing.has(item.id);
    const isImported = imported.has(item.id);
    
    return (
      <Card 
        key={item.id} 
        className={`group relative overflow-hidden hover:shadow-lg transition-all cursor-pointer ${
          isImported ? 'ring-2 ring-green-500' : ''
        }`}
        onClick={() => handleItemClick(item)}
      >
        <div className="relative">
          <img
            src={item.thumbnailUrl}
            alt={item.title}
            className="w-full h-48 object-cover"
            loading="lazy"
          />
          
          {item.type === 'video' && (
            <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-20">
              <PlayCircle className="h-12 w-12 text-white" />
            </div>
          )}

          {item.duration && (
            <Badge variant="secondary" className="absolute bottom-2 right-2">
              {formatDuration(item.duration)}
            </Badge>
          )}

          {/* Import status overlay */}
          <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all flex items-center justify-center">
            {isImporting ? (
              <div className="bg-white rounded-full p-3">
                <Loader2 className="h-6 w-6 animate-spin text-primary" />
              </div>
            ) : isImported ? (
              <div className="bg-green-500 rounded-full p-3 opacity-0 group-hover:opacity-100 transition-opacity">
                <Check className="h-6 w-6 text-white" />
              </div>
            ) : (
              <div className="bg-primary rounded-full p-3 opacity-0 group-hover:opacity-100 transition-opacity">
                <Plus className="h-6 w-6 text-white" />
              </div>
            )}
          </div>

          {/* Status indicator */}
          {isImported && (
            <div className="absolute top-2 left-2">
              <Badge variant="default" className="bg-green-500">
                <Check className="h-3 w-3 mr-1" />
                Added
              </Badge>
            </div>
          )}

          {/* Source badge */}
          <Badge variant="outline" className="absolute top-2 right-2 capitalize">
            {item.source}
          </Badge>
        </div>

        <CardContent className="p-3">
          <h4 className="font-medium text-sm line-clamp-2 mb-1">{item.title}</h4>
          <p className="text-xs text-muted-foreground mb-2">
            {item.photographer || item.user}
          </p>
          
          <div className="flex items-center justify-between text-xs text-muted-foreground">
            <span>{item.width} × {item.height}</span>
            <div className="flex items-center gap-2">
              {item.views && (
                <span className="flex items-center gap-1">
                  <Eye className="h-3 w-3" />
                  {item.views.toLocaleString()}
                </span>
              )}
              {item.likes && (
                <span className="flex items-center gap-1">
                  <Heart className="h-3 w-3" />
                  {item.likes.toLocaleString()}
                </span>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="space-y-6">
      {/* Search and Filters */}
      <div className="space-y-4">
        <div className="flex gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search for photos and videos..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          <Button
            variant="outline"
            onClick={() => setSearchQuery('')}
            disabled={!searchQuery}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Clear
          </Button>
        </div>

        {/* Media Type Tabs */}
        <Tabs value={mediaType} onValueChange={(value) => setMediaType(value as any)}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="all">All Media</TabsTrigger>
            <TabsTrigger value="photo">Photos</TabsTrigger>
            <TabsTrigger value="video">Videos</TabsTrigger>
          </TabsList>
        </Tabs>

        {/* Quick suggestions when no search */}
        {!searchQuery && (
          <div className="space-y-2">
            <p className="text-sm font-medium">Suggested searches:</p>
            <div className="flex flex-wrap gap-2">
              {SUGGESTED_SEARCHES.map(suggestion => (
                <Button
                  key={suggestion}
                  variant="outline"
                  size="sm"
                  onClick={() => setSearchQuery(suggestion)}
                >
                  {suggestion}
                </Button>
              ))}
            </div>
          </div>
        )}

        {/* Auto-import info */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
          <p className="text-sm text-blue-800">
            <ImageIcon className="h-4 w-4 inline mr-2" />
            Click any image or video to automatically add it to your Media Library for use in course creation.
          </p>
        </div>
      </div>

      {/* Results */}
      {items.length > 0 && (
        <div className="space-y-4">
          {/* Results header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <p className="text-sm text-muted-foreground">
                {items.length} results found • {imported.size} added to library
              </p>
            </div>

            <div className="flex items-center gap-2">
              <Button
                size="sm"
                variant={viewMode === 'grid' ? 'default' : 'outline'}
                onClick={() => setViewMode('grid')}
              >
                <Grid3X3 className="h-4 w-4" />
              </Button>
              <Button
                size="sm"
                variant={viewMode === 'list' ? 'default' : 'outline'}
                onClick={() => setViewMode('list')}
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Items grid */}
          <div className={viewMode === 'grid' 
            ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4"
            : "space-y-4"
          }>
            {items.map(renderItem)}
          </div>

          {/* Load more trigger */}
          {hasMore && (
            <div ref={loadMoreRef} className="flex justify-center py-8">
              {loading && <Loader2 className="h-6 w-6 animate-spin" />}
            </div>
          )}
        </div>
      )}

      {/* Empty state */}
      {!loading && searchQuery && items.length === 0 && (
        <div className="text-center py-12">
          <ImageIcon className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="font-medium mb-2">No results found</h3>
          <p className="text-muted-foreground mb-4">
            Try adjusting your search terms
          </p>
          <Button variant="outline" onClick={() => setSearchQuery('')}>
            Clear search
          </Button>
        </div>
      )}

      {/* Loading state */}
      {loading && items.length === 0 && (
        <div className="text-center py-12">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-muted-foreground">Searching stock media...</p>
        </div>
      )}

      {/* Preview Dialog */}
      <Dialog open={!!previewItem} onOpenChange={() => setPreviewItem(null)}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle className="flex items-center justify-between">
              <span>{previewItem?.title}</span>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setPreviewItem(null)}
              >
                <X className="h-4 w-4" />
              </Button>
            </DialogTitle>
          </DialogHeader>
          
          {previewItem && (
            <div className="space-y-4">
              <div className="relative">
                {previewItem.type === 'video' ? (
                  <video
                    src={previewItem.url}
                    controls
                    className="w-full max-h-96 object-contain"
                    poster={previewItem.thumbnailUrl}
                  />
                ) : (
                  <img
                    src={previewItem.url}
                    alt={previewItem.title}
                    className="w-full max-h-96 object-contain"
                  />
                )}
              </div>
              
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <p><strong>Source:</strong> {previewItem.source}</p>
                  <p><strong>Creator:</strong> {previewItem.photographer || previewItem.user}</p>
                  <p><strong>Dimensions:</strong> {previewItem.width} × {previewItem.height}</p>
                  {previewItem.duration && (
                    <p><strong>Duration:</strong> {formatDuration(previewItem.duration)}</p>
                  )}
                </div>
                <div>
                  {previewItem.views && <p><strong>Views:</strong> {previewItem.views.toLocaleString()}</p>}
                  {previewItem.likes && <p><strong>Likes:</strong> {previewItem.likes.toLocaleString()}</p>}
                  {previewItem.downloads && <p><strong>Downloads:</strong> {previewItem.downloads.toLocaleString()}</p>}
                  <p><strong>Tags:</strong> {previewItem.tags}</p>
                </div>
              </div>
              
              <div className="flex justify-end gap-2">
                <Button
                  onClick={() => {
                    handleItemClick(previewItem);
                    setPreviewItem(null);
                  }}
                  disabled={importing.has(previewItem.id) || imported.has(previewItem.id)}
                >
                  {importing.has(previewItem.id) ? (
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  ) : imported.has(previewItem.id) ? (
                    <Check className="h-4 w-4 mr-2" />
                  ) : (
                    <Plus className="h-4 w-4 mr-2" />
                  )}
                  {imported.has(previewItem.id) ? 'Added to Library' : 'Add to Library'}
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AutoImportStockMedia;