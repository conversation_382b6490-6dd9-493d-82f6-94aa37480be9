import express from 'express';
import { z } from 'zod';
import { Resend } from 'resend';
import { db } from '../db';
import { emailSettings } from '@shared/schema';
import { eq } from 'drizzle-orm';

// Check if Resend API key is set
if (!process.env.RESEND_API_KEY) {
  console.warn('RESEND_API_KEY environment variable is not set. Email functionality will be limited.');
}

// Initialize Resend client
const resend = new Resend(process.env.RESEND_API_KEY);

const router = express.Router();

// Schema for test email request
const testEmailSchema = z.object({
  email: z.string().email({ message: 'Invalid email address' }),
});

// Schema for sending a campaign email
const sendCampaignEmailSchema = z.object({
  from: z.string(),
  to: z.array(z.string().email()),
  subject: z.string(),
  text: z.string().optional(),
  html: z.string(),
  replyTo: z.string().email().optional(),
  cc: z.array(z.string().email()).optional(),
  bcc: z.array(z.string().email()).optional(),
  attachments: z.array(
    z.object({
      filename: z.string(),
      content: z.string() // Base64 encoded content
    })
  ).optional(),
  tags: z.array(
    z.object({
      name: z.string(),
      value: z.string()
    })
  ).optional(),
});

// Send a test email
router.post('/test', async (req, res) => {
  try {
    const { email } = testEmailSchema.parse(req.body);
    
    // For test emails, we'll allow without authentication for easier testing
    // In a production environment, you'd typically want to authenticate this endpoint

        console.log('Attempting to send test email to:', email);

    // Get user's email settings if they're authenticated
    let fromName = 'Course Creator';
    let fromEmail = '<EMAIL>'; // Default Resend sender
    let customApiKey = null;

    const user = req.user;
    if (user) {
      // User is authenticated, try to get their email settings
      try {
        const settings = await db.select().from(emailSettings).where(eq(emailSettings.userId, user.id));
        if (settings.length > 0) {
          fromName = settings[0].fromName;
          fromEmail = settings[0].fromEmail;
          customApiKey = settings[0].resendApiKey;
        }
      } catch (settingsError) {
        console.error('Error fetching email settings:', settingsError);
        // Continue with defaults if there's an error
      }
    }

    // Use user's custom API key if available
    let emailClient = resend;
    if (customApiKey) {
      emailClient = new Resend(customApiKey);
    }

    // Try to send the email with detailed error handling
    try {
      console.log(`Sending email from: ${fromName} <${fromEmail}>`);
      
      // Check if it's a test email address (example.com domains aren't allowed in Resend)
      let testMode = false;
      if (email.includes('example.com')) {
        console.log('Using test mode because of example.com domain');
        testMode = true;
        
        // In test mode, we don't actually send the email but simulate success
        return res.status(200).json({
          success: true,
          message: 'Test completed successfully. Note: Emails to example.com domains are not actually sent by Resend.',
          testMode: true,
          email: email,
          fromName: fromName,
          fromEmail: fromEmail
        });
      }
      
      // If not testing, proceed with actual email sending
      const data = await emailClient.emails.send({
        from: `${fromName} <${fromEmail}>`,
        to: [email],
        subject: 'Test Email from Course Creator',
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #eaeaea; border-radius: 5px;">
            <h2 style="color: #333;">Test Email</h2>
            <p style="color: #666; line-height: 1.5;">
              This is a test email from Course Creator to verify your email settings.
            </p>
            <p style="color: #666; line-height: 1.5;">
              If you received this email, your email configurations are working correctly!
            </p>
            <div style="margin-top: 20px; padding-top: 20px; border-top: 1px solid #eaeaea; color: #999; font-size: 12px;">
              <p>This is an automated message from Course Creator. Please do not reply to this email.</p>
            </div>
          </div>
        `,
        text: 'This is a test email from Course Creator to verify your email settings. If you received this email, your email configurations are working correctly!',
        // Enable email tracking if settings allow
        tags: [{ name: 'email_type', value: 'test_email' }]
      });

      return res.status(200).json({ 
        success: true, 
        message: 'Test email sent successfully',
        data
      });
    } catch (emailError) {
      console.error('Error sending email through Resend:', emailError);
      throw emailError; // Re-throw to be caught by the outer catch
    }
  } catch (error) {
    console.error('Error sending test email:', error);
    
    // Provide detailed error information
    if (error instanceof z.ZodError) {
      return res.status(400).json({ 
        error: 'Validation error', 
        details: error.errors 
      });
    }
    
    // Handle Resend API errors
    return res.status(500).json({ 
      error: 'Failed to send test email',
      message: error.message || 'Unknown error occurred'
    });
  }
});

// Send an email campaign
router.post('/campaign', async (req, res) => {
  try {
    const campaignData = sendCampaignEmailSchema.parse(req.body);
    
    // Get the authenticated user
    const user = req.user;
    if (!user) {
      return res.status(401).json({ error: 'Authentication required' });
    }

        // Get user's email settings to check for custom API key
    let customApiKey = null;
    try {
      const settings = await db.select().from(emailSettings).where(eq(emailSettings.userId, user.id));
      if (settings.length > 0) {
        customApiKey = settings[0].resendApiKey;
      }
    } catch (settingsError) {
      console.error('Error fetching email settings:', settingsError);
      // Continue with default API key if there's an error
    }

    // Use user's custom API key if available
    let emailClient = resend;
    if (customApiKey) {
      emailClient = new Resend(customApiKey);
      console.log('Using custom Resend API key for campaign email');
    } else {
      console.log('Using default Resend API key for campaign email');
    }

    console.log(`Sending campaign email from: ${campaignData.from}`);
    
    // Send campaign email using Resend
    const data = await emailClient.emails.send({
      from: campaignData.from,
      to: campaignData.to,
      subject: campaignData.subject,
      html: campaignData.html,
      text: campaignData.text,
      replyTo: campaignData.replyTo,
      cc: campaignData.cc,
      bcc: campaignData.bcc,
      attachments: campaignData.attachments,
      tags: campaignData.tags,
    });

    return res.status(200).json({ 
      success: true, 
      message: 'Campaign email sent successfully',
      data
    });
  } catch (error) {
    console.error('Error sending campaign email:', error);
    
    // Provide detailed error information
    if (error instanceof z.ZodError) {
      return res.status(400).json({ 
        error: 'Validation error', 
        details: error.errors 
      });
    }
    
    // Handle Resend API errors
    return res.status(500).json({ 
      error: 'Failed to send campaign email',
      message: error.message || 'Unknown error occurred'
    });
  }
});

// Get email metrics (opens, clicks, etc.)
router.get('/metrics', async (req, res) => {
  try {
    // Get the authenticated user
    const user = req.user;
    if (!user) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    // Get date range from query params
    const startDate = req.query.startDate ? new Date(req.query.startDate as string) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000); // Default to 30 days ago
    const endDate = req.query.endDate ? new Date(req.query.endDate as string) : new Date();

    // In a production app, we would integrate with Resend's APIs to get metrics
    // For now, we'll return mock data
    return res.status(200).json({ 
      success: true, 
      message: 'Email metrics retrieved',
      data: {
        sent: 0,
        delivered: 0,
        opens: 0,
        clicks: 0,
        bounces: 0,
        complaints: 0,
        unsubscribes: 0,
      }
    });
  } catch (error) {
    console.error('Error getting email metrics:', error);
    
    return res.status(500).json({ 
      error: 'Failed to get email metrics',
      message: error.message || 'Unknown error occurred'
    });
  }
});

// Check Resend API status
router.get('/status', async (req, res) => {
  try {
    if (!process.env.RESEND_API_KEY) {
      return res.status(200).json({ 
        configured: false,
        message: 'Resend API key is not configured. Email functionality will be limited.'
      });
    }

    // Simple API call to check if credentials are valid
    const domains = await resend.domains.list();
    
    return res.status(200).json({ 
      configured: true,
      message: 'Resend API is properly configured',
      domains: domains.data || []
    });
  } catch (error) {
    console.error('Error checking Resend API status:', error);
    
    return res.status(200).json({ 
      configured: false,
      error: error.message || 'Unknown error occurred',
      message: 'Failed to validate Resend API credentials'
    });
  }
});

export default router;