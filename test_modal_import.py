#!/usr/bin/env python3
"""
Test script to check Modal installation and import
"""

import sys
import subprocess

def test_modal_import():
    """Test if Modal can be imported"""
    try:
        import modal
        print("✓ Modal imported successfully")
        print(f"Modal version: {modal.__version__}")
        return True
    except ImportError as e:
        print(f"✗ Modal import failed: {e}")
        return False

def install_modal_direct():
    """Try to install Modal directly"""
    try:
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "modal", "--user"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ Modal installed successfully")
            return True
        else:
            print(f"✗ Modal installation failed: {result.stderr}")
            return False
    except Exception as e:
        print(f"✗ Installation error: {e}")
        return False

def main():
    print("Testing Modal installation...")
    
    # First try to import
    if test_modal_import():
        print("Modal is already available")
        return
    
    # Try to install
    print("Attempting to install Modal...")
    if install_modal_direct():
        # Test import again after installation
        if test_modal_import():
            print("Modal successfully installed and imported")
        else:
            print("Modal installed but import still fails")
    else:
        print("Failed to install Modal")

if __name__ == "__main__":
    main()