/**
 * Avatar Course Generation Service
 * Generates individual lesson videos with Marp slides and assembles them into a continuous course video
 */

import { spawn } from 'child_process';
import { promises as fs } from 'fs';
import { join } from 'path';
import { v4 as uuidv4 } from 'uuid';

export interface LessonData {
  id: string;
  title: string;
  content: string;
  script: string;
  duration?: number;
  slides?: string[];
}

export interface ModuleData {
  id: string;
  title: string;
  lessons: LessonData[];
}

export interface CourseData {
  id: string;
  title: string;
  description: string;
  modules: ModuleData[];
}

export interface AvatarSettings {
  avatarImage: string; // base64 encoded
  voiceSettings: {
    service: string;
    voiceId: string;
    speed: number;
    pitch: number;
    temperature?: number;
    stability?: number;
  };
}

export interface GeneratedLesson {
  lessonId: string;
  title: string;
  videoPath: string;
  audioPath: string;
  slidesPath: string;
  subtitlesPath: string;
  duration: number;
  status: 'completed' | 'failed' | 'processing';
  error?: string;
}

export interface GeneratedCourse {
  courseId: string;
  title: string;
  finalVideoPath: string;
  lessons: GeneratedLesson[];
  totalDuration: number;
  status: 'completed' | 'failed' | 'processing';
  error?: string;
}

export class AvatarCourseGenerationService {
  private workingDir: string;

  constructor() {
    this.workingDir = join(process.cwd(), 'temp', 'avatar-courses');
  }

  async generateCourse(
    courseData: CourseData,
    avatarSettings: AvatarSettings
  ): Promise<GeneratedCourse> {
    const courseId = uuidv4();
    const coursePath = join(this.workingDir, courseId);
    
    try {
      // Create working directory
      await fs.mkdir(coursePath, { recursive: true });
      
      console.log(`Starting avatar course generation for: ${courseData.title}`);
      
      const generatedLessons: GeneratedLesson[] = [];
      let totalDuration = 0;

      // Process each module and lesson
      for (const module of courseData.modules) {
        console.log(`Processing module: ${module.title}`);
        
        for (const lesson of module.lessons) {
          console.log(`Generating lesson: ${lesson.title}`);
          
          const generatedLesson = await this.generateLessonVideo(
            lesson,
            avatarSettings,
            coursePath
          );
          
          generatedLessons.push(generatedLesson);
          
          if (generatedLesson.status === 'completed') {
            totalDuration += generatedLesson.duration;
          }
        }
      }

      // Assemble final course video
      console.log('Assembling final course video...');
      const finalVideoPath = await this.assembleFinalVideo(
        generatedLessons.filter(l => l.status === 'completed'),
        coursePath,
        courseData.title
      );

      return {
        courseId,
        title: courseData.title,
        finalVideoPath,
        lessons: generatedLessons,
        totalDuration,
        status: generatedLessons.every(l => l.status === 'completed') ? 'completed' : 'failed'
      };

    } catch (error) {
      console.error('Avatar course generation failed:', error);
      return {
        courseId,
        title: courseData.title,
        finalVideoPath: '',
        lessons: [],
        totalDuration: 0,
        status: 'failed',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  private async generateLessonVideo(
    lesson: LessonData,
    avatarSettings: AvatarSettings,
    coursePath: string
  ): Promise<GeneratedLesson> {
    const lessonPath = join(coursePath, lesson.id);
    await fs.mkdir(lessonPath, { recursive: true });

    try {
      // Step 1: Generate Marp slides
      const slidesPath = await this.generateMarpSlides(lesson, lessonPath);
      
      // Step 2: Generate TTS audio
      const audioPath = await this.generateTTSAudio(lesson, avatarSettings.voiceSettings, lessonPath);
      
      // Step 3: Generate subtitles
      const subtitlesPath = await this.generateSubtitles(lesson, lessonPath);
      
      // Step 4: Create avatar video with SadTalker
      const videoPath = await this.generateAvatarVideo(
        lesson,
        avatarSettings.avatarImage,
        audioPath,
        lessonPath
      );
      
      // Step 5: Composite final lesson video with slides and subtitles
      const finalVideoPath = await this.compositeLessonVideo(
        videoPath,
        slidesPath,
        subtitlesPath,
        lessonPath
      );

      // Get video duration
      const duration = await this.getVideoDuration(finalVideoPath);

      return {
        lessonId: lesson.id,
        title: lesson.title,
        videoPath: finalVideoPath,
        audioPath,
        slidesPath,
        subtitlesPath,
        duration,
        status: 'completed'
      };

    } catch (error) {
      console.error(`Failed to generate lesson ${lesson.title}:`, error);
      return {
        lessonId: lesson.id,
        title: lesson.title,
        videoPath: '',
        audioPath: '',
        slidesPath: '',
        subtitlesPath: '',
        duration: 0,
        status: 'failed',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  private async generateMarpSlides(lesson: LessonData, lessonPath: string): Promise<string> {
    const markdownPath = join(lessonPath, 'slides.md');
    const outputPath = join(lessonPath, 'slides.html');
    
    // Create Marp markdown content
    const marpContent = this.createMarpContent(lesson);
    await fs.writeFile(markdownPath, marpContent);

    // Generate slides using Marp CLI
    return new Promise((resolve, reject) => {
      const marpProcess = spawn('npx', [
        '@marp-team/marp-cli',
        markdownPath,
        '--html',
        '--output', outputPath,
        '--theme', 'default'
      ]);

      marpProcess.on('close', (code) => {
        if (code === 0) {
          resolve(outputPath);
        } else {
          reject(new Error(`Marp generation failed with code ${code}`));
        }
      });

      marpProcess.on('error', reject);
    });
  }

  private createMarpContent(lesson: LessonData): string {
    const slides = lesson.slides || this.generateSlidesFromContent(lesson.content);
    
    let marpContent = `---
marp: true
theme: default
class: invert
paginate: true
backgroundColor: #1a1a1a
color: #ffffff
---

# ${lesson.title}

---

`;

    slides.forEach((slide, index) => {
      marpContent += `## Slide ${index + 1}

${slide}

---

`;
    });

    return marpContent;
  }

  private generateSlidesFromContent(content: string): string[] {
    // Split content into logical slides
    const paragraphs = content.split('\n\n').filter(p => p.trim());
    const slides: string[] = [];
    
    // Group paragraphs into slides (max 2-3 paragraphs per slide)
    for (let i = 0; i < paragraphs.length; i += 2) {
      const slideContent = paragraphs.slice(i, i + 2).join('\n\n');
      slides.push(slideContent);
    }
    
    return slides.length > 0 ? slides : [content];
  }

  private async generateTTSAudio(
    lesson: LessonData,
    voiceSettings: any,
    lessonPath: string
  ): Promise<string> {
    const audioPath = join(lessonPath, 'audio.wav');
    
    // Use the existing voice service to generate audio
    const response = await fetch('/api/voice/generate', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        text: lesson.script,
        service: voiceSettings.service,
        voiceId: voiceSettings.voiceId,
        speed: voiceSettings.speed,
        pitch: voiceSettings.pitch,
        temperature: voiceSettings.temperature,
        stability: voiceSettings.stability
      })
    });

    if (!response.ok) {
      throw new Error('TTS generation failed');
    }

    const audioBuffer = await response.arrayBuffer();
    await fs.writeFile(audioPath, Buffer.from(audioBuffer));
    
    return audioPath;
  }

  private async generateSubtitles(lesson: LessonData, lessonPath: string): Promise<string> {
    const subtitlesPath = join(lessonPath, 'subtitles.srt');
    
    // Generate SRT subtitles from script
    const srtContent = this.generateSRTContent(lesson.script);
    await fs.writeFile(subtitlesPath, srtContent);
    
    return subtitlesPath;
  }

  private generateSRTContent(script: string): string {
    const sentences = script.split('.').filter(s => s.trim());
    let srtContent = '';
    let startTime = 0;
    
    sentences.forEach((sentence, index) => {
      const duration = Math.max(3, sentence.length * 0.1); // Estimate duration
      const endTime = startTime + duration;
      
      srtContent += `${index + 1}\n`;
      srtContent += `${this.formatSRTTime(startTime)} --> ${this.formatSRTTime(endTime)}\n`;
      srtContent += `${sentence.trim()}.\n\n`;
      
      startTime = endTime;
    });
    
    return srtContent;
  }

  private formatSRTTime(seconds: number): string {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    const ms = Math.floor((seconds % 1) * 1000);
    
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')},${ms.toString().padStart(3, '0')}`;
  }

  private async generateAvatarVideo(
    lesson: LessonData,
    avatarImage: string,
    audioPath: string,
    lessonPath: string
  ): Promise<string> {
    const videoPath = join(lessonPath, 'avatar.mp4');
    
    // Try SadTalker A100 service first
    try {
      const response = await fetch('/api/sadtalker/generate-video', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          avatarImage,
          audioPath,
          settings: {
            pose_style: 0,
            expression_scale: 1.0,
            still_mode: false,
            preprocess: 'crop',
            size: 512,
            enhancer: 'gfpgan'
          }
        })
      });

      if (response.ok) {
        const videoBuffer = await response.arrayBuffer();
        await fs.writeFile(videoPath, Buffer.from(videoBuffer));
        return videoPath;
      }
    } catch (error) {
      console.log('SadTalker service unavailable, using fallback');
    }

    // Fallback: Create simple avatar video with static image and audio
    return this.createStaticAvatarVideo(avatarImage, audioPath, videoPath);
  }

  private async createStaticAvatarVideo(
    avatarImage: string,
    audioPath: string,
    outputPath: string
  ): Promise<string> {
    const imagePath = outputPath.replace('.mp4', '.jpg');
    
    // Save avatar image
    const imageBuffer = Buffer.from(avatarImage, 'base64');
    await fs.writeFile(imagePath, imageBuffer);

    // Create video with FFmpeg
    return new Promise((resolve, reject) => {
      const ffmpegProcess = spawn('ffmpeg', [
        '-loop', '1',
        '-i', imagePath,
        '-i', audioPath,
        '-c:v', 'libx264',
        '-tune', 'stillimage',
        '-c:a', 'aac',
        '-b:a', '192k',
        '-pix_fmt', 'yuv420p',
        '-shortest',
        '-y',
        outputPath
      ]);

      ffmpegProcess.on('close', (code) => {
        if (code === 0) {
          resolve(outputPath);
        } else {
          reject(new Error(`FFmpeg failed with code ${code}`));
        }
      });

      ffmpegProcess.on('error', reject);
    });
  }

  private async compositeLessonVideo(
    avatarVideoPath: string,
    slidesPath: string,
    subtitlesPath: string,
    lessonPath: string
  ): Promise<string> {
    const outputPath = join(lessonPath, 'final_lesson.mp4');
    
    // Convert HTML slides to video frames
    const slidesVideoPath = await this.convertSlidesToVideo(slidesPath, lessonPath);
    
    // Composite avatar and slides side by side with subtitles
    return new Promise((resolve, reject) => {
      const ffmpegProcess = spawn('ffmpeg', [
        '-i', avatarVideoPath,
        '-i', slidesVideoPath,
        '-filter_complex', 
        `[0:v]scale=640:480[avatar];[1:v]scale=640:480[slides];[avatar][slides]hstack=inputs=2[v];[v]subtitles=${subtitlesPath}[final]`,
        '-map', '[final]',
        '-map', '0:a',
        '-c:v', 'libx264',
        '-c:a', 'aac',
        '-preset', 'fast',
        '-crf', '23',
        '-y',
        outputPath
      ]);

      ffmpegProcess.on('close', (code) => {
        if (code === 0) {
          resolve(outputPath);
        } else {
          reject(new Error(`Video composition failed with code ${code}`));
        }
      });

      ffmpegProcess.on('error', reject);
    });
  }

  private async convertSlidesToVideo(slidesPath: string, lessonPath: string): Promise<string> {
    const videoPath = join(lessonPath, 'slides.mp4');
    
    // Use puppeteer to convert HTML slides to video frames
    // For now, create a simple background video
    return new Promise((resolve, reject) => {
      const ffmpegProcess = spawn('ffmpeg', [
        '-f', 'lavfi',
        '-i', 'color=c=white:s=640x480:d=30',
        '-c:v', 'libx264',
        '-t', '30',
        '-pix_fmt', 'yuv420p',
        '-y',
        videoPath
      ]);

      ffmpegProcess.on('close', (code) => {
        if (code === 0) {
          resolve(videoPath);
        } else {
          reject(new Error(`Slides video creation failed with code ${code}`));
        }
      });

      ffmpegProcess.on('error', reject);
    });
  }

  private async assembleFinalVideo(
    lessons: GeneratedLesson[],
    coursePath: string,
    courseTitle: string
  ): Promise<string> {
    const finalVideoPath = join(coursePath, 'final_course.mp4');
    const fileListPath = join(coursePath, 'filelist.txt');
    
    // Create file list for FFmpeg concat
    const fileList = lessons.map(lesson => `file '${lesson.videoPath}'`).join('\n');
    await fs.writeFile(fileListPath, fileList);
    
    // Add title card and transitions
    const titleCardPath = await this.createTitleCard(courseTitle, coursePath);
    
    // Concatenate all videos with transitions
    return new Promise((resolve, reject) => {
      const ffmpegProcess = spawn('ffmpeg', [
        '-f', 'concat',
        '-safe', '0',
        '-i', fileListPath,
        '-c', 'copy',
        '-y',
        finalVideoPath
      ]);

      ffmpegProcess.on('close', (code) => {
        if (code === 0) {
          resolve(finalVideoPath);
        } else {
          reject(new Error(`Final video assembly failed with code ${code}`));
        }
      });

      ffmpegProcess.on('error', reject);
    });
  }

  private async createTitleCard(title: string, coursePath: string): Promise<string> {
    const titleCardPath = join(coursePath, 'title_card.mp4');
    
    return new Promise((resolve, reject) => {
      const ffmpegProcess = spawn('ffmpeg', [
        '-f', 'lavfi',
        '-i', `color=c=black:s=1280x720:d=3`,
        '-vf', `drawtext=text='${title}':fontcolor=white:fontsize=48:x=(w-text_w)/2:y=(h-text_h)/2`,
        '-c:v', 'libx264',
        '-t', '3',
        '-pix_fmt', 'yuv420p',
        '-y',
        titleCardPath
      ]);

      ffmpegProcess.on('close', (code) => {
        if (code === 0) {
          resolve(titleCardPath);
        } else {
          reject(new Error(`Title card creation failed with code ${code}`));
        }
      });

      ffmpegProcess.on('error', reject);
    });
  }

  private async getVideoDuration(videoPath: string): Promise<number> {
    return new Promise((resolve, reject) => {
      const ffprobeProcess = spawn('ffprobe', [
        '-v', 'quiet',
        '-print_format', 'json',
        '-show_format',
        videoPath
      ]);

      let output = '';
      ffprobeProcess.stdout.on('data', (data) => {
        output += data.toString();
      });

      ffprobeProcess.on('close', (code) => {
        if (code === 0) {
          try {
            const info = JSON.parse(output);
            resolve(parseFloat(info.format.duration) || 0);
          } catch (error) {
            resolve(0);
          }
        } else {
          resolve(0);
        }
      });

      ffprobeProcess.on('error', () => resolve(0));
    });
  }

  async cleanupCourse(courseId: string): Promise<void> {
    const coursePath = join(this.workingDir, courseId);
    try {
      await fs.rm(coursePath, { recursive: true, force: true });
    } catch (error) {
      console.error('Failed to cleanup course files:', error);
    }
  }
}

export const avatarCourseGenerationService = new AvatarCourseGenerationService();