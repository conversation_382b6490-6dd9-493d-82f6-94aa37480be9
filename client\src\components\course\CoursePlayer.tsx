import React, { useState, useEffect, useRef } from 'react';
import { Lesson } from '@shared/schema';
import { 
  Card, 
  CardContent, 
  CardFooter, 
  CardHeader, 
  CardTitle, 
  CardDescription 
} from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Play, 
  Pause, 
  SkipForward, 
  SkipBack, 
  Volume2, 
  VolumeX,
  SettingsIcon,
  Brain 
} from 'lucide-react';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from '@/components/ui/dialog';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import { MicroLearningPlayer } from '../micro-learning/MicroLearningPlayer';

interface CoursePlayerProps {
  lesson: Lesson;
  onNextLesson?: () => void;
  onPreviousLesson?: () => void;
  hasNextLesson?: boolean;
  hasPreviousLesson?: boolean;
}

export function CoursePlayer({ 
  lesson, 
  onNextLesson, 
  onPreviousLesson,
  hasNextLesson = false,
  hasPreviousLesson = false
}: CoursePlayerProps) {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [progress, setProgress] = useState(0);
  const [volume, setVolume] = useState(1);
  const [isMuted, setIsMuted] = useState(false);
  const [showTranscript, setShowTranscript] = useState(false);
  const [isMicroLearningDialogOpen, setIsMicroLearningDialogOpen] = useState(false);
  const { toast } = useToast();

  // Micro-learning settings
  const [microLearningSettings, setMicroLearningSettings] = useState({
    enabled: lesson.microLearningEnabled || false,
    segmentCount: lesson.microLearningSegmentCount || 3,
    breakInterval: Math.floor((lesson.microLearningBreakInterval || 300) / 60), // Convert seconds to minutes for UI
    breakDuration: lesson.microLearningBreakDuration || 60
  });

  useEffect(() => {
    // Update settings when lesson changes
    setMicroLearningSettings({
      enabled: lesson.microLearningEnabled || false,
      segmentCount: lesson.microLearningSegmentCount || 3,
      breakInterval: Math.floor((lesson.microLearningBreakInterval || 300) / 60),
      breakDuration: lesson.microLearningBreakDuration || 60
    });
  }, [lesson]);

  // Handle video events
  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const handleLoadedMetadata = () => {
      setDuration(video.duration);
    };

    const handleTimeUpdate = () => {
      setCurrentTime(video.currentTime);
      setProgress((video.currentTime / video.duration) * 100);
    };

    const handlePlay = () => {
      setIsPlaying(true);
    };

    const handlePause = () => {
      setIsPlaying(false);
    };

    const handleEnded = () => {
      setIsPlaying(false);
      if (onNextLesson && hasNextLesson) {
        // Automatically go to next lesson if available
        setTimeout(() => {
          onNextLesson();
        }, 1500);
      }
    };

    // Add event listeners
    video.addEventListener('loadedmetadata', handleLoadedMetadata);
    video.addEventListener('timeupdate', handleTimeUpdate);
    video.addEventListener('play', handlePlay);
    video.addEventListener('pause', handlePause);
    video.addEventListener('ended', handleEnded);

    // Cleanup
    return () => {
      video.removeEventListener('loadedmetadata', handleLoadedMetadata);
      video.removeEventListener('timeupdate', handleTimeUpdate);
      video.removeEventListener('play', handlePlay);
      video.removeEventListener('pause', handlePause);
      video.removeEventListener('ended', handleEnded);
    };
  }, [hasNextLesson, onNextLesson]);

  // Toggle play/pause
  const togglePlayPause = () => {
    const video = videoRef.current;
    if (!video) return;

    if (isPlaying) {
      video.pause();
    } else {
      video.play().catch((error) => {
        console.error('Error playing video:', error);
        toast({
          title: 'Playback Error',
          description: 'Could not play video. Please try again.',
          variant: 'destructive',
        });
      });
    }
  };

  // Toggle mute
  const toggleMute = () => {
    const video = videoRef.current;
    if (!video) return;

    if (isMuted) {
      video.muted = false;
      setIsMuted(false);
    } else {
      video.muted = true;
      setIsMuted(true);
    }
  };

  // Update volume
  const handleVolumeChange = (value: number[]) => {
    const newVolume = value[0];
    const video = videoRef.current;
    if (!video) return;

    setVolume(newVolume);
    video.volume = newVolume;
    if (newVolume === 0) {
      setIsMuted(true);
      video.muted = true;
    } else if (isMuted) {
      setIsMuted(false);
      video.muted = false;
    }
  };

  // Handle seeking
  const handleSeek = (value: number[]) => {
    const newTime = (value[0] / 100) * duration;
    const video = videoRef.current;
    if (!video) return;

    video.currentTime = newTime;
    setCurrentTime(newTime);
  };

  // Format time (seconds to MM:SS)
  const formatTime = (timeInSeconds: number) => {
    const minutes = Math.floor(timeInSeconds / 60);
    const seconds = Math.floor(timeInSeconds % 60);
    return `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
  };

  // Save micro-learning settings
  const saveMicroLearningSettings = async () => {
    try {
      // Convert minutes back to seconds for the API
      const settings = {
        microLearningEnabled: microLearningSettings.enabled,
        microLearningSegmentCount: microLearningSettings.segmentCount,
        microLearningBreakInterval: microLearningSettings.breakInterval * 60, // Convert minutes to seconds
        microLearningBreakDuration: microLearningSettings.breakDuration
      };

      const response = await apiRequest('PATCH', `/api/lessons/${lesson.id}/micro-learning`, settings);
      
      if (response.ok) {
        const updatedLesson = await response.json();
        
        // Update local settings with server response
        setMicroLearningSettings({
          enabled: updatedLesson.microLearningEnabled,
          segmentCount: updatedLesson.microLearningSegmentCount,
          breakInterval: Math.floor((updatedLesson.microLearningBreakInterval || 300) / 60),
          breakDuration: updatedLesson.microLearningBreakDuration || 60
        });

        toast({
          title: 'Settings Updated',
          description: 'Micro-learning settings have been saved.',
        });

        // Close the dialog
        setIsMicroLearningDialogOpen(false);
      } else {
        throw new Error('Failed to update settings');
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to save micro-learning settings. Please try again.',
        variant: 'destructive',
      });
    }
  };

  return (
    <div className="w-full space-y-4">
      <Card>
        <CardHeader className="pb-2">
          <CardTitle>{lesson.title}</CardTitle>
          <CardDescription>{lesson.description}</CardDescription>
        </CardHeader>

        <CardContent className="pb-0">
          <div className="relative">
            {/* Video Element */}
            <div className="relative rounded-md overflow-hidden bg-black aspect-video">
              <video
                ref={videoRef}
                className="w-full h-full"
                src={lesson.videoUrl || ''}
                playsInline
                controls={false}
              />
              
              {/* Play Button Overlay (when paused) */}
              {!isPlaying && (
                <div 
                  className="absolute inset-0 flex items-center justify-center cursor-pointer"
                  onClick={togglePlayPause}
                >
                  <div className="rounded-full bg-primary/90 p-4">
                    <Play className="h-8 w-8 text-white" />
                  </div>
                </div>
              )}
              
              {/* Micro-learning status indicator */}
              {lesson.microLearningEnabled && (
                <div className="absolute top-4 left-4 rounded-full bg-black/70 text-white px-3 py-1 text-sm flex items-center">
                  <Brain className="h-4 w-4 mr-1.5" />
                  Micro-Learning Enabled
                </div>
              )}
            </div>
            
            {/* Micro-Learning Player */}
            {lesson.microLearningEnabled && (
              <MicroLearningPlayer 
                lessonId={lesson.id}
                videoUrl={lesson.videoUrl || ''}
                // Using the lesson script as transcript
                transcript={lesson.script || ''}
                segmentCount={lesson.microLearningSegmentCount || 4}
                breakInterval={lesson.microLearningBreakInterval || 300}
                breakDuration={lesson.microLearningBreakDuration || 30}
                onComplete={() => {
                  toast({
                    title: 'Learning Complete',
                    description: 'You have completed this micro-learning session!',
                  });
                }}
              />
            )}
          </div>
        </CardContent>
        
        <CardFooter className="flex flex-col pt-2 pb-4 px-6">
          {/* Progress Bar */}
          <div className="w-full space-y-2">
            <Slider
              value={[progress]}
              max={100}
              step={0.1}
              onValueChange={handleSeek}
              className="cursor-pointer"
            />
            
            <div className="flex items-center justify-between text-xs text-muted-foreground">
              <span>{formatTime(currentTime)}</span>
              <span>{formatTime(duration)}</span>
            </div>
          </div>
          
          {/* Controls */}
          <div className="flex items-center justify-between w-full mt-2">
            <div className="flex items-center space-x-2">
              {/* Play/Pause Button */}
              <Button
                variant="ghost"
                size="icon"
                onClick={togglePlayPause}
              >
                {isPlaying ? <Pause className="h-5 w-5" /> : <Play className="h-5 w-5" />}
              </Button>
              
              {/* Previous/Next Lesson Buttons */}
              <Button
                variant="ghost"
                size="icon"
                disabled={!hasPreviousLesson}
                onClick={onPreviousLesson}
              >
                <SkipBack className="h-5 w-5" />
              </Button>
              
              <Button
                variant="ghost"
                size="icon"
                disabled={!hasNextLesson}
                onClick={onNextLesson}
              >
                <SkipForward className="h-5 w-5" />
              </Button>
              
              {/* Volume Controls */}
              <div className="flex items-center">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={toggleMute}
                >
                  {isMuted ? <VolumeX className="h-5 w-5" /> : <Volume2 className="h-5 w-5" />}
                </Button>
                
                <div className="w-20 hidden sm:block">
                  <Slider
                    value={[isMuted ? 0 : volume]}
                    min={0}
                    max={1}
                    step={0.01}
                    onValueChange={handleVolumeChange}
                  />
                </div>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              {/* Micro-Learning Settings Dialog */}
              <Dialog open={isMicroLearningDialogOpen} onOpenChange={setIsMicroLearningDialogOpen}>
                <DialogTrigger asChild>
                  <Button variant="outline" size="sm" className="flex items-center gap-1">
                    <Brain className="h-4 w-4" />
                    <span className="hidden sm:inline">Micro-Learning</span>
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Micro-Learning Settings</DialogTitle>
                    <DialogDescription>
                      Configure bite-sized learning with strategic breaks to improve retention and reduce cognitive load.
                    </DialogDescription>
                  </DialogHeader>
                  
                  <div className="grid gap-6 py-4">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="micro-learning-toggle" className="text-base font-medium">
                        Enable Micro-Learning
                      </Label>
                      <Switch
                        id="micro-learning-toggle"
                        checked={microLearningSettings.enabled}
                        onCheckedChange={(checked) => {
                          setMicroLearningSettings({
                            ...microLearningSettings,
                            enabled: checked
                          });
                        }}
                      />
                    </div>
                    
                    <div className="space-y-4">
                      <div>
                        <div className="flex justify-between">
                          <Label htmlFor="segment-count">Number of Segments</Label>
                          <span className="text-sm text-muted-foreground">
                            {microLearningSettings.segmentCount}
                          </span>
                        </div>
                        <Slider
                          id="segment-count"
                          disabled={!microLearningSettings.enabled}
                          value={[microLearningSettings.segmentCount]}
                          min={2}
                          max={10}
                          step={1}
                          onValueChange={(value) => {
                            setMicroLearningSettings({
                              ...microLearningSettings,
                              segmentCount: value[0]
                            });
                          }}
                        />
                        <p className="text-xs text-muted-foreground mt-1">
                          Divide your content into smaller segments for better retention
                        </p>
                      </div>
                      
                      <div>
                        <div className="flex justify-between">
                          <Label htmlFor="break-interval">Break Interval (minutes)</Label>
                          <span className="text-sm text-muted-foreground">
                            {microLearningSettings.breakInterval}
                          </span>
                        </div>
                        <Slider
                          id="break-interval"
                          disabled={!microLearningSettings.enabled}
                          value={[microLearningSettings.breakInterval]}
                          min={1}
                          max={10}
                          step={1}
                          onValueChange={(value) => {
                            setMicroLearningSettings({
                              ...microLearningSettings,
                              breakInterval: value[0]
                            });
                          }}
                        />
                        <p className="text-xs text-muted-foreground mt-1">
                          Time between breaks in minutes
                        </p>
                      </div>
                      
                      <div>
                        <div className="flex justify-between">
                          <Label htmlFor="break-duration">Break Duration (seconds)</Label>
                          <span className="text-sm text-muted-foreground">
                            {microLearningSettings.breakDuration}
                          </span>
                        </div>
                        <Slider
                          id="break-duration"
                          disabled={!microLearningSettings.enabled}
                          value={[microLearningSettings.breakDuration]}
                          min={15}
                          max={120}
                          step={5}
                          onValueChange={(value) => {
                            setMicroLearningSettings({
                              ...microLearningSettings,
                              breakDuration: value[0]
                            });
                          }}
                        />
                        <p className="text-xs text-muted-foreground mt-1">
                          Length of each break in seconds
                        </p>
                      </div>
                    </div>
                    
                    <div className="bg-secondary/30 p-3 rounded text-sm">
                      <p className="font-medium mb-1">Benefits of Micro-Learning:</p>
                      <ul className="list-disc pl-5 space-y-1 text-xs">
                        <li>Improved information retention</li>
                        <li>Reduced cognitive overload</li>
                        <li>Better focus and engagement</li>
                        <li>Enhanced knowledge retention</li>
                      </ul>
                    </div>
                  </div>
                  
                  <DialogFooter>
                    <Button
                      variant="outline"
                      onClick={() => setIsMicroLearningDialogOpen(false)}
                    >
                      Cancel
                    </Button>
                    <Button onClick={saveMicroLearningSettings}>
                      Save Settings
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
              
              {/* Transcript Toggle Button */}
              <Button 
                variant={showTranscript ? "default" : "outline"} 
                size="sm"
                onClick={() => setShowTranscript(!showTranscript)}
              >
                Transcript
              </Button>
            </div>
          </div>
        </CardFooter>
      </Card>
      
      {/* Transcript and Notes Section */}
      {showTranscript && (
        <Card>
          <CardHeader>
            <CardTitle>Transcript</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="prose prose-sm max-w-none">
              {lesson.script ? (
                <div dangerouslySetInnerHTML={{ __html: lesson.script }} />
              ) : (
                <p className="text-muted-foreground">No transcript available for this lesson.</p>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}