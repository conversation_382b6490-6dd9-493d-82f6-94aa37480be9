import { createContext, ReactNode, useContext } from 'react';
import {
  useQuery,
  useMutation,
  UseQueryResult,
  UseMutationResult,
} from '@tanstack/react-query';
import { Notification, NotificationType, NotificationPreference } from '@/types/notifications';
import { apiRequest } from '@/lib/queryClient';
import { queryClient } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';

// Define the context type for notifications
interface NotificationsContextType {
  notifications: Notification[] | undefined;
  unreadNotifications: Notification[] | undefined;
  notificationCount: number;
  isLoading: boolean;
  notificationTypes: NotificationType[] | undefined;
  preferences: NotificationPreference[] | undefined;
  markAsReadMutation: UseMutationResult<any, Error, number, unknown>;
  markAllAsReadMutation: UseMutationResult<any, Error, void, unknown>;
  deleteNotificationMutation: UseMutationResult<any, Error, number, unknown>;
  updatePreferenceMutation: UseMutationResult<any, Error, { typeId: number, preference: Partial<NotificationPreference> }, unknown>;
}

// Create context for notifications
const NotificationsContext = createContext<NotificationsContextType | null>(null);

// Provider component for notifications
export function NotificationsProvider({ children }: { children: ReactNode }) {
  const { toast } = useToast();
  
  // Fetch notifications
  const { 
    data: notifications = [],
    isLoading: isLoadingNotifications,
    error: notificationsError,
  } = useQuery<Notification[]>({
    queryKey: ['/api/notifications'],
    refetchInterval: 60000, // Refetch every minute
  });
  
  // Fetch unread notifications 
  const { 
    data: unreadNotifications = [],
    isLoading: isLoadingUnread,
  } = useQuery<Notification[]>({
    queryKey: ['/api/notifications/unread'],
    refetchInterval: 30000, // Refetch every 30 seconds
  });
  
  // Fetch notification count
  const { 
    data: notificationCountData = { count: 0 },
    isLoading: isLoadingCount,
  } = useQuery<{ count: number }>({
    queryKey: ['/api/notifications/count'],
    refetchInterval: 30000, // Refetch every 30 seconds
  });
  
  // Fetch notification types
  const { 
    data: notificationTypes,
    isLoading: isLoadingTypes,
  } = useQuery<NotificationType[]>({
    queryKey: ['/api/notifications/types'],
  });
  
  // Fetch notification preferences
  const { 
    data: preferences,
    isLoading: isLoadingPreferences,
  } = useQuery<NotificationPreference[]>({
    queryKey: ['/api/notifications/preferences'],
  });
  
  // Mutation for marking a notification as read
  const markAsReadMutation = useMutation({
    mutationFn: async (id: number) => {
      const res = await apiRequest('PATCH', `/api/notifications/${id}/read`);
      if (!res.ok) {
        throw new Error('Failed to mark notification as read');
      }
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/notifications'] });
      queryClient.invalidateQueries({ queryKey: ['/api/notifications/unread'] });
      queryClient.invalidateQueries({ queryKey: ['/api/notifications/count'] });
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
    },
  });
  
  // Mutation for marking all notifications as read
  const markAllAsReadMutation = useMutation({
    mutationFn: async () => {
      const res = await apiRequest('POST', '/api/notifications/mark-all-read');
      if (!res.ok) {
        throw new Error('Failed to mark all notifications as read');
      }
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/notifications'] });
      queryClient.invalidateQueries({ queryKey: ['/api/notifications/unread'] });
      queryClient.invalidateQueries({ queryKey: ['/api/notifications/count'] });
      toast({
        title: 'Success',
        description: 'All notifications marked as read',
      });
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
    },
  });
  
  // Mutation for deleting a notification
  const deleteNotificationMutation = useMutation({
    mutationFn: async (id: number) => {
      const res = await apiRequest('DELETE', `/api/notifications/${id}`);
      if (!res.ok) {
        throw new Error('Failed to delete notification');
      }
      return id;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/notifications'] });
      queryClient.invalidateQueries({ queryKey: ['/api/notifications/unread'] });
      queryClient.invalidateQueries({ queryKey: ['/api/notifications/count'] });
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
    },
  });
  
  // Mutation for updating notification preferences
  const updatePreferenceMutation = useMutation({
    mutationFn: async ({ typeId, preference }: { typeId: number, preference: Partial<NotificationPreference> }) => {
      const res = await apiRequest('PATCH', `/api/notifications/preferences/${typeId}`, preference);
      if (!res.ok) {
        throw new Error('Failed to update notification preference');
      }
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/notifications/preferences'] });
      toast({
        title: 'Success',
        description: 'Notification preferences updated',
      });
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
    },
  });
  
  // Check for loading states
  const isLoading = 
    isLoadingNotifications || 
    isLoadingUnread || 
    isLoadingCount || 
    isLoadingTypes || 
    isLoadingPreferences;
  
  // Handle any errors in initial data loading
  if (notificationsError) {
    console.error('Error loading notifications:', notificationsError);
  }
  
  // Get the notification count from the data
  const notificationCount = notificationCountData.count || 0;
  
  // Create the context value
  const contextValue = {
    notifications,
    unreadNotifications,
    notificationCount,
    isLoading,
    notificationTypes,
    preferences,
    markAsReadMutation,
    markAllAsReadMutation,
    deleteNotificationMutation,
    updatePreferenceMutation,
  };
  
  return (
    <NotificationsContext.Provider value={contextValue}>
      {children}
    </NotificationsContext.Provider>
  );
}

// Custom hook for using notifications
export function useNotifications() {
  const context = useContext(NotificationsContext);
  if (!context) {
    throw new Error('useNotifications must be used within a NotificationsProvider');
  }
  return context;
}
