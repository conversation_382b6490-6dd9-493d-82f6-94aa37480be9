"""
Final Modal A100 80G GPU Production Deployment
Comprehensive Avatar Course Creation with SadTalker, Chatterbox TTS, and Marp
"""

import modal
import os
import base64
import io
import tempfile
import subprocess
import json
import time
from typing import Dict, List, Any, Optional
from pathlib import Path

# Modal App Configuration
app = modal.App("courseai-a100-production")

# A100 80G GPU Image with all dependencies
gpu_image = (
    modal.Image.debian_slim(python_version="3.10")
    .apt_install([
        "git", "wget", "curl", "ffmpeg", "libsm6", "libxext6", 
        "libxrender-dev", "libglib2.0-0", "libgl1-mesa-glx",
        "libsndfile1", "espeak", "espeak-data", "libespeak1",
        "libespeak-dev", "festival", "festvox-kallpc16k"
    ])
    .pip_install([
        "torch==2.0.1",
        "torchvision==0.15.2",
        "torchaudio==2.0.2",
        "diffusers==0.21.4",
        "transformers==4.35.0",
        "accelerate==0.24.1",
        "xformers==0.0.22",
        "opencv-python==********",
        "pillow==10.0.1",
        "numpy==1.24.3",
        "scipy==1.11.4",
        "scikit-image==0.21.0",
        "face-alignment==1.4.1",
        "imageio==2.31.5",
        "imageio-ffmpeg==0.4.9",
        "librosa==0.10.1",
        "soundfile==0.12.1",
        "resampy==0.4.2",
        "pydub==0.25.1",
        "TTS==0.22.0",
        "coqui-tts==0.13.3",
        "gfpgan==1.3.8",
        "basicsr==1.4.2",
        "facexlib==0.3.0",
        "dlib==19.24.2",
        "matplotlib==3.7.2",
        "seaborn==0.12.2",
        "requests==2.31.0",
        "flask==2.3.3",
        "fastapi==0.104.1",
        "uvicorn==0.24.0"
    ])
    .run_commands([
        "mkdir -p /app/models /app/checkpoints /app/temp",
        "cd /app && git clone https://github.com/OpenTalker/SadTalker.git",
        "cd /app/SadTalker && pip install -r requirements.txt",
        "mkdir -p /app/SadTalker/checkpoints",
        "wget -O /app/SadTalker/checkpoints/mapping_00109-model.pth.tar https://github.com/OpenTalker/SadTalker/releases/download/v0.0.2-rc/mapping_00109-model.pth.tar",
        "wget -O /app/SadTalker/checkpoints/mapping_00229-model.pth.tar https://github.com/OpenTalker/SadTalker/releases/download/v0.0.2-rc/mapping_00229-model.pth.tar",
        "wget -O /app/SadTalker/checkpoints/SadTalker_V002.safetensors https://github.com/OpenTalker/SadTalker/releases/download/v0.0.2-rc/SadTalker_V002.safetensors",
        "npm install -g @marp-team/marp-cli",
        "pip install --upgrade setuptools wheel"
    ])
)

# Shared storage for models and temp files
shared_volume = modal.Volume.from_name("courseai-a100-storage", create_if_missing=True)

@app.function(
    image=gpu_image,
    gpu=modal.gpu.A100(count=1, size="80GB"),
    volumes={"/app/storage": shared_volume},
    timeout=1800,  # 30 minutes
    memory=32768,  # 32GB RAM
    keep_warm=1
)
def health_check() -> Dict[str, Any]:
    """Comprehensive A100 GPU health check with all services"""
    try:
        import torch
        gpu_available = torch.cuda.is_available()
        gpu_count = torch.cuda.device_count() if gpu_available else 0
        gpu_name = torch.cuda.get_device_name(0) if gpu_available else "No GPU"
        
        # Check GPU memory
        if gpu_available:
            gpu_memory_total = torch.cuda.get_device_properties(0).total_memory / (1024**3)
            torch.cuda.empty_cache()
            gpu_memory_free = (torch.cuda.get_device_properties(0).total_memory - torch.cuda.memory_allocated(0)) / (1024**3)
        else:
            gpu_memory_total = gpu_memory_free = 0
        
        # Test PyTorch GPU operations
        if gpu_available:
            test_tensor = torch.randn(1000, 1000).cuda()
            test_result = torch.mm(test_tensor, test_tensor.t())
            torch.cuda.synchronize()
            del test_tensor, test_result
            torch.cuda.empty_cache()
        
        # Check SadTalker installation
        sadtalker_path = Path("/app/SadTalker")
        sadtalker_available = sadtalker_path.exists()
        
        # Check TTS installation
        try:
            from TTS.api import TTS
            tts_available = True
        except ImportError:
            tts_available = False
        
        # Check Marp installation
        try:
            marp_result = subprocess.run(["marp", "--version"], capture_output=True, text=True)
            marp_available = marp_result.returncode == 0
        except:
            marp_available = False
            
        return {
            "status": "online",
            "gpu_available": gpu_available,
            "gpu_count": gpu_count,
            "gpu_name": gpu_name,
            "gpu_memory_total_gb": round(gpu_memory_total, 2),
            "gpu_memory_free_gb": round(gpu_memory_free, 2),
            "services": {
                "sadtalker": sadtalker_available,
                "tts": tts_available,
                "marp": marp_available
            },
            "timestamp": int(1000)  # Current timestamp
        }
        
    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "gpu_available": False,
            "gpu_count": 0,
            "gpu_name": "Error",
            "gpu_memory_total_gb": 0,
            "gpu_memory_free_gb": 0,
            "services": {
                "sadtalker": False,
                "tts": False,
                "marp": False
            },
            "timestamp": 0
        }

@app.function(
    image=gpu_image,
    gpu=modal.gpu.A100(count=1, size="80GB"),
    volumes={"/app/storage": shared_volume},
    timeout=600,  # 10 minutes
    memory=16384
)
def generate_high_quality_tts(
    text: str,
    voice_preset: str = "tts_models/en/ljspeech/tacotron2-DDC",
    temperature: float = 0.7
) -> Dict[str, Any]:
    """Generate high-quality TTS using Coqui TTS on A100 GPU"""
    try:
        from TTS.api import TTS
        import soundfile as sf
        
        # Initialize TTS model
        tts = TTS(voice_preset).to("cuda")
        
        # Generate audio
        with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
            tts.tts_to_file(text=text, file_path=temp_file.name)
            
            # Read audio file and convert to base64
            with open(temp_file.name, "rb") as audio_file:
                audio_data = audio_file.read()
                audio_base64 = base64.b64encode(audio_data).decode()
            
            os.unlink(temp_file.name)
        
        return {
            "status": "success",
            "text": text,
            "voice_preset": voice_preset,
            "audio_base64": audio_base64,
            "audio_format": "wav",
            "gpu_used": True,
            "timestamp": int(time.time() * 1000)
        }
        
    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "text": text,
            "voice_preset": voice_preset,
            "gpu_used": False,
            "timestamp": int(time.time() * 1000)
        }

@app.function(
    image=gpu_image,
    gpu=modal.gpu.A100(count=1, size="80GB"),
    volumes={"/app/storage": shared_volume},
    timeout=1200,  # 20 minutes
    memory=24576
)
def generate_sadtalker_video(
    image_base64: str,
    audio_base64: str,
    enhancer: str = "gfpgan",
    size: int = 512
) -> Dict[str, Any]:
    """Generate SadTalker avatar video using A100 GPU"""
    try:
        import sys
        import cv2
        from PIL import Image
        
        # Add SadTalker to path
        sys.path.append("/app/SadTalker")
        from src.utils.preprocess import CropAndExtract
        from src.test_audio2coeff import Audio2Coeff
        from src.facerender.animate import AnimateFromCoeff
        from src.generate_batch import get_data
        from src.generate_facerender_batch import get_facerender_data
        
        # Create temp directories
        temp_dir = tempfile.mkdtemp()
        image_path = os.path.join(temp_dir, "input_image.jpg")
        audio_path = os.path.join(temp_dir, "input_audio.wav")
        output_path = os.path.join(temp_dir, "output_video.mp4")
        
        # Decode base64 inputs
        image_data = base64.b64decode(image_base64)
        audio_data = base64.b64decode(audio_base64)
        
        # Save input files
        with open(image_path, "wb") as f:
            f.write(image_data)
        with open(audio_path, "wb") as f:
            f.write(audio_data)
        
        # Initialize SadTalker components
        preprocess_model = CropAndExtract("/app/SadTalker/checkpoints", "cuda")
        audio2coeff = Audio2Coeff("/app/SadTalker/checkpoints", "cuda")
        animate = AnimateFromCoeff("/app/SadTalker/checkpoints", "cuda")
        
        # Process image and audio
        first_frame_dir = os.path.join(temp_dir, "first_frame_dir")
        os.makedirs(first_frame_dir, exist_ok=True)
        
        # Extract first frame and coefficients
        first_coeff_path, crop_pic_path, crop_info = preprocess_model.generate(
            image_path, first_frame_dir, "full", source_image_flag=True, pic_size=size
        )
        
        # Generate audio coefficients
        batch = get_data(first_coeff_path, audio_path, "cuda", ref_eyeblink=None, still=True)
        coeff_path = audio2coeff.generate(batch, "/app/SadTalker/checkpoints", pose_style=0, ref_pose=None)
        
        # Generate final video
        data = get_facerender_data(coeff_path, crop_pic_path, first_coeff_path, audio_path, 
                                   batch_size=1, input_yaw_list=None, input_pitch_list=None, 
                                   input_roll_list=None, expression_scale=1.0, still_mode=True, 
                                   preprocess="crop", size=size)
        
        animate.generate(data, "/app/SadTalker/checkpoints", pic_path=crop_pic_path, 
                        crop_info=crop_info, enhancer=enhancer, background_enhancer=None, 
                        face_enhancer=None, verbose=False, save_path=output_path)
        
        # Read output video and convert to base64
        with open(output_path, "rb") as video_file:
            video_data = video_file.read()
            video_base64 = base64.b64encode(video_data).decode()
        
        # Cleanup
        import shutil
        shutil.rmtree(temp_dir)
        
        return {
            "status": "success",
            "video_base64": video_base64,
            "video_format": "mp4",
            "enhancer": enhancer,
            "size": size,
            "gpu_used": True,
            "timestamp": int(time.time() * 1000)
        }
        
    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "enhancer": enhancer,
            "size": size,
            "gpu_used": False,
            "timestamp": int(time.time() * 1000)
        }

@app.function(
    image=gpu_image,
    timeout=300,  # 5 minutes
    memory=4096
)
def generate_marp_slides(
    markdown_content: str,
    theme: str = "default",
    output_format: str = "pdf"
) -> Dict[str, Any]:
    """Generate presentation slides using Marp CLI"""
    try:
        temp_dir = tempfile.mkdtemp()
        markdown_path = os.path.join(temp_dir, "slides.md")
        output_path = os.path.join(temp_dir, f"slides.{output_format}")
        
        # Write markdown content
        with open(markdown_path, "w", encoding="utf-8") as f:
            f.write(markdown_content)
        
        # Generate slides using Marp
        cmd = [
            "marp",
            markdown_path,
            "--theme", theme,
            "--output", output_path,
            "--allow-local-files"
        ]
        
        if output_format == "pdf":
            cmd.extend(["--pdf"])
        elif output_format == "html":
            cmd.extend(["--html"])
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=180)
        
        if result.returncode != 0:
            raise Exception(f"Marp generation failed: {result.stderr}")
        
        # Read output file and convert to base64
        with open(output_path, "rb") as output_file:
            output_data = output_file.read()
            slides_base64 = base64.b64encode(output_data).decode()
        
        # Cleanup
        import shutil
        shutil.rmtree(temp_dir)
        
        return {
            "status": "success",
            "slides_base64": slides_base64,
            "format": output_format,
            "theme": theme,
            "timestamp": int(time.time() * 1000)
        }
        
    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "format": output_format,
            "theme": theme,
            "timestamp": int(time.time() * 1000)
        }

@app.function(
    image=gpu_image,
    gpu=modal.gpu.A100(count=1, size="80GB"),
    volumes={"/app/storage": shared_volume},
    timeout=3600,  # 1 hour for complete course
    memory=32768
)
def complete_avatar_course_workflow(
    course_title: str,
    lesson_scripts: List[Dict[str, str]],
    avatar_image_base64: str,
    voice_preset: str = "tts_models/en/ljspeech/tacotron2-DDC"
) -> Dict[str, Any]:
    """Complete Avatar Course Creation workflow using A100 GPU"""
    try:
        results = {
            "status": "success",
            "course_title": course_title,
            "total_lessons": len(lesson_scripts),
            "lessons": [],
            "slides": None,
            "gpu_used": True,
            "timestamp": int(time.time() * 1000)
        }
        
        # Process each lesson
        for i, lesson in enumerate(lesson_scripts):
            lesson_result = {
                "lesson_id": i + 1,
                "title": lesson["title"],
                "tts_status": "processing",
                "video_status": "processing"
            }
            
            try:
                # Generate TTS audio
                tts_result = generate_high_quality_tts.local(
                    lesson["script"], voice_preset
                )
                
                if tts_result["status"] == "success":
                    lesson_result["tts_status"] = "completed"
                    lesson_result["audio_base64"] = tts_result["audio_base64"]
                    
                    # Generate avatar video
                    video_result = generate_sadtalker_video.local(
                        avatar_image_base64, tts_result["audio_base64"]
                    )
                    
                    if video_result["status"] == "success":
                        lesson_result["video_status"] = "completed"
                        lesson_result["video_base64"] = video_result["video_base64"]
                    else:
                        lesson_result["video_status"] = "error"
                        lesson_result["video_error"] = video_result.get("error", "Unknown error")
                else:
                    lesson_result["tts_status"] = "error"
                    lesson_result["tts_error"] = tts_result.get("error", "Unknown error")
                    
            except Exception as lesson_error:
                lesson_result["tts_status"] = "error"
                lesson_result["video_status"] = "error"
                lesson_result["error"] = str(lesson_error)
            
            results["lessons"].append(lesson_result)
        
        # Generate course slides
        try:
            slide_content = f"""---
marp: true
theme: default
---

# {course_title}

## Course Overview

"""
            
            for lesson in lesson_scripts:
                slide_content += f"""---

## {lesson['title']}

{lesson['script'][:200]}...

"""
            
            slides_result = generate_marp_slides.local(slide_content, "default", "pdf")
            
            if slides_result["status"] == "success":
                results["slides"] = {
                    "status": "completed",
                    "slides_base64": slides_result["slides_base64"]
                }
            else:
                results["slides"] = {
                    "status": "error",
                    "error": slides_result.get("error", "Unknown error")
                }
                
        except Exception as slides_error:
            results["slides"] = {
                "status": "error",
                "error": str(slides_error)
            }
        
        return results
        
    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "course_title": course_title,
            "total_lessons": len(lesson_scripts),
            "lessons": [],
            "slides": None,
            "gpu_used": False,
            "timestamp": int(time.time() * 1000)
        }

# Web API endpoints
@app.function(
    image=gpu_image,
    timeout=60
)
@modal.web_endpoint(method="GET")
def health():
    """Health check endpoint"""
    return health_check.remote()

@app.function(
    image=gpu_image,
    gpu=modal.gpu.A100(count=1, size="80GB"),
    volumes={"/app/storage": shared_volume},
    timeout=600
)
@modal.web_endpoint(method="POST")
def tts(request_data: Dict[str, Any]):
    """TTS generation endpoint"""
    text = request_data.get("text", "")
    voice_preset = request_data.get("voice_preset", "tts_models/en/ljspeech/tacotron2-DDC")
    
    if not text:
        return {"status": "error", "error": "Text is required"}
    
    return generate_high_quality_tts.remote(text, voice_preset)

@app.function(
    image=gpu_image,
    gpu=modal.gpu.A100(count=1, size="80GB"),
    volumes={"/app/storage": shared_volume},
    timeout=1200
)
@modal.web_endpoint(method="POST")
def sadtalker(request_data: Dict[str, Any]):
    """SadTalker video generation endpoint"""
    image_base64 = request_data.get("image_base64", "")
    audio_base64 = request_data.get("audio_base64", "")
    
    if not image_base64 or not audio_base64:
        return {"status": "error", "error": "Both image_base64 and audio_base64 are required"}
    
    return generate_sadtalker_video.remote(image_base64, audio_base64)

@app.function(
    image=gpu_image,
    timeout=300
)
@modal.web_endpoint(method="POST")
def slides(request_data: Dict[str, Any]):
    """Slide generation endpoint"""
    markdown_content = request_data.get("markdown_content", "")
    theme = request_data.get("theme", "default")
    output_format = request_data.get("output_format", "pdf")
    
    if not markdown_content:
        return {"status": "error", "error": "Markdown content is required"}
    
    return generate_marp_slides.remote(markdown_content, theme, output_format)

@app.function(
    image=gpu_image,
    gpu=modal.gpu.A100(count=1, size="80GB"),
    volumes={"/app/storage": shared_volume},
    timeout=3600
)
@modal.web_endpoint(method="POST")
def avatar_course(request_data: Dict[str, Any]):
    """Complete Avatar Course generation endpoint"""
    course_title = request_data.get("course_title", "")
    lesson_scripts = request_data.get("lesson_scripts", [])
    avatar_image_base64 = request_data.get("avatar_image_base64", "")
    voice_preset = request_data.get("voice_preset", "tts_models/en/ljspeech/tacotron2-DDC")
    
    if not course_title or not lesson_scripts or not avatar_image_base64:
        return {
            "status": "error", 
            "error": "course_title, lesson_scripts, and avatar_image_base64 are required"
        }
    
    return complete_avatar_course_workflow.remote(
        course_title, lesson_scripts, avatar_image_base64, voice_preset
    )

if __name__ == "__main__":
    print("Modal A100 80G GPU Production App - Avatar Course Creation")
    print("Available endpoints:")
    print("  /health - GPU health check")
    print("  /tts - Text-to-speech generation")
    print("  /sadtalker - Avatar video generation")
    print("  /slides - Presentation slide generation")
    print("  /avatar-course - Complete avatar course workflow")