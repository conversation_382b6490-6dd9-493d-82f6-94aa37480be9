import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>Header, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { <PERSON>, CardHeader, Card<PERSON>ontent, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Loader2, Check, Star } from 'lucide-react';
import { useMutation } from '@tanstack/react-query';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';

interface PlansDialogProps {
  isOpen: boolean;
  onClose: () => void;
  currentPlan: string;
  onPlanSelected: () => void;
}

type PlanType = {
  id: string;
  name: string;
  description: string;
  monthlyPrice: number;
  yearlyPrice: number;
  features: string[];
  popular?: boolean;
  color?: string;
};

const plans: PlanType[] = [
  {
    id: 'starter',
    name: 'Starter',
    description: 'Perfect for beginners and hobbyists',
    monthlyPrice: 49,
    yearlyPrice: 470,
    features: [
      'Basic AI course creation',
      '5 courses',
      '50 AI credits/month',
      'Standard quality exports',
      'Email support'
    ],
    color: 'bg-blue-100 text-blue-800 border-blue-200'
  },
  {
    id: 'pro',
    name: 'Pro',
    description: 'Best for content creators and educators',
    monthlyPrice: 129,
    yearlyPrice: 1250,
    features: [
      'Advanced AI features',
      'Unlimited courses',
      '500 AI credits/month',
      'HD quality exports',
      'Priority support',
      'Custom branding',
      'Team collaboration',
      'Analytics dashboard'
    ],
    popular: true,
    color: 'bg-purple-100 text-purple-800 border-purple-200'
  },
  {
    id: 'business',
    name: 'Business',
    description: 'For organizations and educational institutions',
    monthlyPrice: 299,
    yearlyPrice: 2900,
    features: [
      'All Pro features',
      'Team collaboration',
      '1500 AI credits/month',
      '4K quality exports',
      'Dedicated support',
      'Advanced analytics',
      'API access',
      'SSO authentication',
      'Multiple team management'
    ],
    color: 'bg-indigo-100 text-indigo-800 border-indigo-200'
  },
  {
    id: 'enterprise',
    name: 'Enterprise',
    description: 'Custom solutions for large organizations',
    monthlyPrice: 0,
    yearlyPrice: 0, 
    features: [
      'All Business features',
      'Custom features development',
      'Unlimited AI credits',
      'White-labeling',
      'Dedicated account manager',
      'SLA guarantees',
      'On-premise deployment options',
      'Advanced security features',
      'Integration with existing LMS'
    ],
    color: 'bg-slate-100 text-slate-800 border-slate-200'
  }
];

export default function PlansDialog({ isOpen, onClose, currentPlan, onPlanSelected }: PlansDialogProps) {
  const [selectedPlan, setSelectedPlan] = useState<string>(currentPlan || 'starter');
  const [billingPeriod, setBillingPeriod] = useState<'monthly' | 'yearly'>('monthly');
  const { toast } = useToast();

  const changePlanMutation = useMutation({
    mutationFn: async (planId: string) => {
      const response = await apiRequest('POST', '/api/billing/change-plan', { 
        planId,
        billingPeriod
      });
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to change plan');
      }
      return await response.json();
    },
    onSuccess: () => {
      toast({
        title: 'Plan updated',
        description: `Your subscription has been updated to the ${selectedPlan.charAt(0).toUpperCase() + selectedPlan.slice(1)} plan.`,
      });
      onPlanSelected();
    },
    onError: (error) => {
      toast({
        title: 'Error updating plan',
        description: error.message || 'Something went wrong. Please try again.',
        variant: 'destructive'
      });
    }
  });

  const handleSelectPlan = (planId: string) => {
    setSelectedPlan(planId);
  };

  const handleChangePlan = () => {
    if (selectedPlan === 'enterprise') {
      toast({
        title: 'Enterprise plan',
        description: 'Please contact our sales team to discuss enterprise pricing and features.',
      });
      onClose();
      return;
    }
    
    changePlanMutation.mutate(selectedPlan);
  };

  const savings = (plan: PlanType) => {
    const monthlyTotal = plan.monthlyPrice * 12;
    const savings = monthlyTotal - plan.yearlyPrice;
    const savingsPercent = Math.round((savings / monthlyTotal) * 100);
    return savingsPercent;
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[800px] p-0 gap-0 overflow-hidden">
        <DialogHeader className="px-6 pt-6 pb-4 border-b">
          <DialogTitle className="text-2xl">Choose a Plan</DialogTitle>
          <DialogDescription>
            Select the plan that best fits your needs. All plans include core features.
          </DialogDescription>
        </DialogHeader>

        <div className="px-6 py-4">
          <div className="flex justify-center mb-6">
            <Tabs 
              defaultValue="monthly" 
              value={billingPeriod}
              onValueChange={(value) => setBillingPeriod(value as 'monthly' | 'yearly')}
              className="w-[400px]"
            >
              <TabsList className="grid grid-cols-2">
                <TabsTrigger value="monthly">Monthly</TabsTrigger>
                <TabsTrigger value="yearly">
                  Yearly
                  <Badge className="ml-2 bg-green-100 text-green-800 hover:bg-green-100">Save up to 20%</Badge>
                </TabsTrigger>
              </TabsList>
            </Tabs>
          </div>

          <RadioGroup value={selectedPlan} onValueChange={handleSelectPlan} className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {plans.map((plan) => (
              <div key={plan.id} className={`relative ${plan.id === selectedPlan ? 'ring-2 ring-primary ring-offset-2' : ''}`}>
                {plan.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-primary text-primary-foreground text-xs px-3 py-1 rounded-full font-semibold z-10 whitespace-nowrap">
                    Most Popular
                  </div>
                )}
                <Card className={`h-full border ${plan.color || ''}`}>
                  <CardHeader className="pb-4">
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <h3 className="font-semibold text-lg">{plan.name}</h3>
                        <RadioGroupItem value={plan.id} id={plan.id} className="sr-only" />
                        <div className={`h-5 w-5 rounded-full flex items-center justify-center border ${plan.id === selectedPlan ? 'border-primary bg-primary text-primary-foreground' : 'border-muted-foreground'}`}>
                          {plan.id === selectedPlan && <Check className="h-3 w-3" />}
                        </div>
                      </div>
                      <p className="text-sm text-muted-foreground">{plan.description}</p>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="mb-4">
                      {plan.id === 'enterprise' ? (
                        <div>
                          <p className="text-2xl font-bold">Custom</p>
                          <p className="text-sm text-muted-foreground">Contact sales for pricing</p>
                        </div>
                      ) : (
                        <div>
                          <p className="text-3xl font-bold">
                            ${billingPeriod === 'monthly' ? plan.monthlyPrice : Math.round(plan.yearlyPrice / 12)}
                            <span className="text-sm font-normal text-muted-foreground">/mo</span>
                          </p>
                          {billingPeriod === 'yearly' && (
                            <p className="text-sm text-muted-foreground">
                              ${plan.yearlyPrice} billed yearly (save {savings(plan)}%)
                            </p>
                          )}
                        </div>
                      )}
                    </div>
                    <ul className="space-y-2 text-sm">
                      {plan.features.map((feature, index) => (
                        <li key={index} className="flex items-start">
                          <div className="mr-2 mt-0.5 text-primary">
                            <Check className="h-4 w-4" />
                          </div>
                          <span>{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                  <CardFooter>
                    <Label
                      htmlFor={plan.id}
                      className={`w-full cursor-pointer py-2 px-4 rounded-md text-center text-sm font-medium 
                        ${plan.id === selectedPlan 
                          ? 'bg-primary text-primary-foreground' 
                          : 'bg-muted hover:bg-muted/80'}`}
                    >
                      {plan.id === currentPlan ? 'Current Plan' : 'Select Plan'}
                    </Label>
                  </CardFooter>
                </Card>
              </div>
            ))}
          </RadioGroup>
        </div>

        <DialogFooter className="px-6 py-4 border-t">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button 
            onClick={handleChangePlan} 
            disabled={selectedPlan === currentPlan || changePlanMutation.isPending}
            className={selectedPlan === currentPlan ? 'opacity-50 cursor-not-allowed' : ''}
          >
            {changePlanMutation.isPending ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Processing...
              </>
            ) : selectedPlan === 'enterprise' ? (
              'Contact Sales'
            ) : selectedPlan === currentPlan ? (
              'Current Plan'
            ) : (
              'Confirm Change'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}