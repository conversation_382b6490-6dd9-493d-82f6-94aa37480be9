import { useState, useEffect } from "react";
import { useLocation } from "wouter";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Loader2, Play, Video, Zap, Search, Save } from "lucide-react";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { apiRequest } from "@/lib/queryClient";
import { 
  generateVideo, 
  VideoGenerationParams, 
  GeneratedVideo,
  checkVideoStatus,
  generateScript,
  ScriptGenerationParams,
  GeneratedScript
} from "../lib/ai";

// Interfaces for video types
interface PixabayVideo {
  id: number;
  pageURL: string;
  type: string;
  tags: string;
  duration: number;
  picture_id: string;
  videos: {
    large: { url: string; width: number; height: number; size: number; };
    medium: { url: string; width: number; height: number; size: number; };
    small: { url: string; width: number; height: number; size: number; };
    tiny: { url: string; width: number; height: number; size: number; };
  };
  user: string;
  userImageURL: string;
}

interface PexelsVideo {
  id: number;
  width: number;
  height: number;
  url: string;
  image: string;
  duration: number;
  user: {
    id: number;
    name: string;
    url: string;
  };
  video_files: {
    id: number;
    quality: string;
    file_type: string;
    width: number | null;
    height: number | null;
    fps: number | null;
    link: string;
  }[];
  video_pictures: {
    id: number;
    picture: string;
    nr: number;
  }[];
}

interface VideoGenerationFormProps {
  onGenerateVideo: (params: VideoGenerationParams) => void;
  isPending: boolean;
}

const AnimationPreferenceOptions = [
  { value: "minimal", label: "Minimal (3 scenes)" },
  { value: "moderate", label: "Moderate (6 scenes)" },
  { value: "dynamic", label: "Dynamic (10 scenes)" },
];

const VoiceOptions = [
  { value: "alloy", label: "Alloy (Balanced)" },
  { value: "echo", label: "Echo (Baritone)" },
  { value: "fable", label: "Fable (Warm)" },
  { value: "onyx", label: "Onyx (Deep)" },
  { value: "nova", label: "Nova (Feminine)" },
  { value: "shimmer", label: "Shimmer (Bright)" },
];

function VideoGenerationForm({ onGenerateVideo, isPending }: VideoGenerationFormProps) {
  const [courseTitle, setCourseTitle] = useState("");
  const { toast } = useToast();
  const [isGeneratingScript, setIsGeneratingScript] = useState(false);
  const [videoType, setVideoType] = useState<"ai" | "free">("free");
  const [searchQuery, setSearchQuery] = useState("");
  const [isSearching, setIsSearching] = useState(false);
  const [searchResults, setSearchResults] = useState<(PixabayVideo | PexelsVideo)[]>([]);
  const [selectedVideo, setSelectedVideo] = useState<PixabayVideo | PexelsVideo | null>(null);
  const [searchSource, setSearchSource] = useState<"pixabay" | "pexels">("pixabay");
  const [isPixabaySearchOpen, setIsPixabaySearchOpen] = useState(false);
  
  // Function to search for free videos
  const searchFreeVideos = async () => {
    if (!searchQuery.trim()) {
      toast({
        title: "Search query required",
        description: "Please enter a search term to find videos.",
        variant: "destructive",
      });
      return;
    }
    
    setIsSearching(true);
    
    try {
      // Search videos based on selected source
      const endpoint = searchSource === "pixabay" 
        ? `/api/pixabay/videos?query=${encodeURIComponent(searchQuery)}`
        : `/api/pexels/videos?query=${encodeURIComponent(searchQuery)}`;
      
      const response = await apiRequest("GET", endpoint);
      
      if (!response.ok) {
        throw new Error("Failed to search for videos");
      }
      
      const data = await response.json();
      
      // Handle different response formats from Pixabay vs Pexels
      const videos = searchSource === "pixabay" ? data.hits : data.videos;
      
      setSearchResults(videos || []);
      
      if (!videos || videos.length === 0) {
        toast({
          title: "No videos found",
          description: `Try a different search term or video source.`,
        });
      }
    } catch (error) {
      console.error("Search error:", error);
      toast({
        title: "Search failed",
        description: error instanceof Error ? error.message : "Failed to search for videos",
        variant: "destructive",
      });
    } finally {
      setIsSearching(false);
    }
  };
  
  // Function to import a selected free video
  const importFreeVideo = async () => {
    if (!selectedVideo) {
      toast({
        title: "No video selected",
        description: "Please select a video to import.",
        variant: "destructive",
      });
      return;
    }
    
    try {
      // Prepare import payload based on source
      const importPayload = {
        id: selectedVideo.id,
        type: "video",
        title: courseTitle || `Video about ${searchQuery}`,
        description: `Educational video about ${searchQuery}`,
      };
      
      // Call the appropriate import endpoint
      const endpoint = searchSource === "pixabay" ? "/api/pixabay/import" : "/api/pexels/import";
      const response = await apiRequest("POST", endpoint, importPayload);
      
      if (!response.ok) {
        throw new Error("Failed to import video");
      }
      
      const importedMedia = await response.json();
      
      toast({
        title: "Video imported successfully",
        description: "The selected video has been added to your media library.",
      });
      
      // Close the dialog
      setIsPixabaySearchOpen(false);
      
      // Return the imported video URL and other details to the parent component
      // This is a placeholder - we're not actually generating a video with AI
      onGenerateVideo({
        title: courseTitle || `Video about ${searchQuery}`,
        script: "Free video imported from " + searchSource,
        videoUrl: importedMedia.url,
        freeVideoImport: true,
        source: searchSource,
        sourceId: selectedVideo.id.toString(),
      });
      
    } catch (error) {
      console.error("Import error:", error);
      toast({
        title: "Import failed",
        description: error instanceof Error ? error.message : "Failed to import video",
        variant: "destructive",
      });
    }
  };

  // Auto-generation mutation - will both generate script and then video
  const autoGenerationMutation = useMutation({
    mutationFn: async (title: string) => {
      setIsGeneratingScript(true);
      try {
        // Generate script from title
        const scriptResponse = await generateScript({
          title,
          description: "An educational course video about " + title,
        });
        
        return scriptResponse.script;
      } finally {
        setIsGeneratingScript(false);
      }
    },
    onSuccess: (script: string) => {
      // After script generation succeeds, start video generation with default voice
      onGenerateVideo({
        title: courseTitle,
        script,
        style: "Educational professional",
        animationPreference: "moderate",
        narrationVoice: "Rachel", // Default female voice from ElevenLabs
      });
      
      toast({
        title: "Script generated successfully",
        description: "Now creating your video with AI-generated content...",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Script generation failed",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!courseTitle.trim()) {
      toast({
        title: "Course title required",
        description: "Please enter a course title to generate your video lesson.",
        variant: "destructive",
      });
      return;
    }
    
    if (videoType === "ai") {
      // Start the AI video generation process
      autoGenerationMutation.mutate(courseTitle);
    } else {
      // Open free video search dialog
      setIsPixabaySearchOpen(true);
      // Pre-populate the search field with the course title
      setSearchQuery(courseTitle);
    }
  };

  return (
    <>
      <form onSubmit={handleSubmit} className="space-y-6">
        <div>
          <Label htmlFor="title" className="text-xl font-medium">What do you want to teach?</Label>
          <p className="text-sm text-muted-foreground mb-3">
            Just enter a topic and we'll create a complete video lesson for you
          </p>
          <Input
            id="title"
            value={courseTitle}
            onChange={(e) => setCourseTitle(e.target.value)}
            placeholder="e.g., Digital Marketing Basics"
            className="mt-1 text-lg py-6"
            disabled={isPending || isGeneratingScript}
            required
          />
        </div>
        
        <div className="space-y-3">
          <Label className="text-lg font-medium">Choose Video Type</Label>
          <RadioGroup 
            value={videoType} 
            onValueChange={(value) => setVideoType(value as "ai" | "free")}
            className="grid grid-cols-1 md:grid-cols-2 gap-4"
          >
            <div>
              <RadioGroupItem value="free" id="free" className="peer sr-only" />
              <Label
                htmlFor="free"
                className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary"
              >
                <Search className="mb-3 h-6 w-6" />
                <div className="space-y-1 text-center">
                  <p className="text-sm font-medium leading-none">Free Stock Videos</p>
                  <p className="text-xs text-muted-foreground">
                    Choose from Pixabay or Pexels stock video library
                  </p>
                </div>
                <Badge className="mt-2" variant="outline">Free</Badge>
              </Label>
            </div>
            
            <div>
              <RadioGroupItem value="ai" id="ai" className="peer sr-only" />
              <Label
                htmlFor="ai"
                className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary"
              >
                <Zap className="mb-3 h-6 w-6" />
                <div className="space-y-1 text-center">
                  <p className="text-sm font-medium leading-none">AI-Generated Video</p>
                  <p className="text-xs text-muted-foreground">
                    Create custom video with AI narration and animations
                  </p>
                </div>
                <Badge className="mt-2" variant="secondary">200 AI Credits</Badge>
              </Label>
            </div>
          </RadioGroup>
        </div>

        <Button 
          type="submit" 
          className="w-full mt-6 py-6 text-lg" 
          disabled={isPending || isGeneratingScript || !courseTitle.trim()}
          size="lg"
        >
          {isPending ? (
            <>
              <Loader2 className="mr-2 h-5 w-5 animate-spin" />
              {videoType === "ai" ? "Creating Your Video..." : "Searching for Videos..."}
            </>
          ) : isGeneratingScript ? (
            <>
              <Loader2 className="mr-2 h-5 w-5 animate-spin" />
              Writing Your Script...
            </>
          ) : (
            <>
              <Video className="mr-2 h-5 w-5" />
              {videoType === "ai" ? "Create AI Video Lesson" : "Find Free Stock Videos"}
            </>
          )}
        </Button>
        
        <Alert>
          <AlertTitle className="flex items-center text-primary">
            {videoType === "ai" ? "AI Video Generation" : "Free Stock Video"}
          </AlertTitle>
          <AlertDescription className="mt-2">
            {videoType === "ai" ? (
              <>
                With AI video generation, we'll automatically create:
                <ul className="list-disc pl-5 mt-2 space-y-1 text-sm">
                  <li>Professional educational script tailored to your topic</li>
                  <li>Engaging AI-generated narration with natural voice</li>
                  <li>Visual media and animations to illustrate key concepts</li>
                  <li>Synchronized subtitles for better accessibility</li>
                  <li><strong>Cost: 200 AI credits</strong></li>
                </ul>
              </>
            ) : (
              <>
                With free stock videos, you'll be able to:
                <ul className="list-disc pl-5 mt-2 space-y-1 text-sm">
                  <li>Search high-quality professional stock videos</li>
                  <li>Browse videos from Pixabay and Pexels libraries</li>
                  <li>Select and import videos directly to your media library</li>
                  <li>Use the videos in your courses without restrictions</li>
                  <li><strong>Cost: Free (no AI credits required)</strong></li>
                </ul>
              </>
            )}
          </AlertDescription>
        </Alert>
      </form>
      
      {/* Free Video Search Dialog */}
      <Dialog open={isPixabaySearchOpen} onOpenChange={setIsPixabaySearchOpen}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Search Free Stock Videos</DialogTitle>
            <DialogDescription>
              Find and select high-quality stock videos from {searchSource === "pixabay" ? "Pixabay" : "Pexels"} for your course.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4 mt-4">
            <div className="flex items-center space-x-4">
              <div className="flex-1">
                <Input
                  type="text"
                  placeholder="Search for videos..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyDown={(e) => e.key === "Enter" && searchFreeVideos()}
                />
              </div>
              
              <Select 
                value={searchSource} 
                onValueChange={(value) => setSearchSource(value as "pixabay" | "pexels")}
              >
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="Source" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="pixabay">Pixabay</SelectItem>
                  <SelectItem value="pexels">Pexels</SelectItem>
                </SelectContent>
              </Select>
              
              <Button onClick={searchFreeVideos} disabled={isSearching}>
                {isSearching ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  "Search"
                )}
              </Button>
            </div>
            
            {/* Search Results */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
              {searchResults.map((video: any) => {
                // Get correct thumbnail URL based on source
                const thumbnailUrl = searchSource === "pixabay" 
                  ? `https://i.vimeocdn.com/video/${video.picture_id}_640x360.jpg`
                  : video.image;
                
                return (
                  <Card 
                    key={video.id} 
                    className={`cursor-pointer overflow-hidden ${selectedVideo?.id === video.id ? 'ring-2 ring-primary' : ''}`}
                    onClick={() => setSelectedVideo(video)}
                  >
                    <div className="relative aspect-video">
                      <img 
                        src={thumbnailUrl} 
                        alt="Video thumbnail" 
                        className="h-full w-full object-cover"
                      />
                      <div className="absolute bottom-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded">
                        {Math.floor(video.duration / 60)}:{(video.duration % 60).toString().padStart(2, '0')}
                      </div>
                    </div>
                    <CardContent className="p-3">
                      <p className="text-xs truncate">
                        {searchSource === "pixabay" ? `By: ${video.user}` : `By: ${video.user.name}`}
                      </p>
                    </CardContent>
                  </Card>
                );
              })}
              
              {searchResults.length === 0 && !isSearching && (
                <div className="col-span-full text-center py-6 text-muted-foreground">
                  {searchQuery ? 'No videos found. Try a different search term.' : 'Enter a search term to find videos.'}
                </div>
              )}
              
              {isSearching && (
                <div className="col-span-full text-center py-6">
                  <Loader2 className="h-8 w-8 animate-spin mx-auto" />
                  <p className="mt-2 text-muted-foreground">Searching for videos...</p>
                </div>
              )}
            </div>
          </div>
          
          <div className="flex justify-between mt-4">
            <Button variant="outline" onClick={() => setIsPixabaySearchOpen(false)}>
              Cancel
            </Button>
            <Button onClick={importFreeVideo} disabled={!selectedVideo}>
              <Save className="mr-2 h-4 w-4" />
              Import Selected Video
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}

function VideoProcessingStatus({ videoData }: { videoData: GeneratedVideo }) {
  const { processingStatus, estimatedCompletionTime } = videoData;
  const now = Date.now();
  let progress = 0;
  let timeRemaining = "Calculating...";
  // Check if this is a free video
  const isFreeVideo = videoData.id.startsWith('free-');

  if (isFreeVideo) {
    // Free videos are always ready immediately
    progress = 100;
    timeRemaining = "Ready to use";
  } else if (processingStatus === "processing") {
    // Calculate percentage based on time elapsed vs estimated completion time
    const startTime = now - 30000; // Assume job started 30 seconds ago
    const totalDuration = estimatedCompletionTime - startTime;
    const elapsed = now - startTime;
    progress = Math.min(95, Math.max(5, Math.round((elapsed / totalDuration) * 100)));
    
    // Calculate time remaining
    const remainingMs = Math.max(0, estimatedCompletionTime - now);
    const remainingMinutes = Math.ceil(remainingMs / 60000);
    timeRemaining = remainingMinutes <= 1 
      ? "Less than a minute remaining" 
      : `About ${remainingMinutes} minutes remaining`;
  } else if (processingStatus === "completed") {
    progress = 100;
    timeRemaining = "Completed";
  } else if (processingStatus === "failed") {
    progress = 100;
    timeRemaining = "Failed";
  } else if (processingStatus === "queued") {
    progress = 5;
    timeRemaining = "Queued, waiting to start...";
  }

  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <span className="text-sm font-medium capitalize">
            {isFreeVideo ? "Ready" : processingStatus}
            {isFreeVideo && <Badge variant="outline" className="ml-2">Free Stock Video</Badge>}
          </span>
          <span className="text-sm text-muted-foreground">{timeRemaining}</span>
        </div>
        <Progress value={progress} className="h-2" />
      </div>

      {videoData.processingStatus === "failed" && (
        <Alert variant="destructive">
          <AlertTitle>Generation Failed</AlertTitle>
          <AlertDescription>An error occurred during video generation.</AlertDescription>
        </Alert>
      )}

      {videoData.thumbnailUrl && (
        <div className="mt-4">
          <p className="text-sm font-medium mb-2">
            {isFreeVideo ? "Video Thumbnail:" : "Preview Thumbnail:"}
          </p>
          <img 
            src={videoData.thumbnailUrl} 
            alt="Video thumbnail" 
            className="rounded-md w-full h-auto object-cover"
          />
        </div>
      )}

      {videoData.videoUrl && (processingStatus === "completed" || isFreeVideo) && (
        <div className="mt-4">
          <p className="text-sm font-medium mb-2">
            {isFreeVideo ? "Imported Stock Video:" : "Generated Video:"}
          </p>
          <div className="relative pt-[56.25%] rounded-md overflow-hidden bg-muted">
            {videoData.videoUrl.endsWith('.mp4') ? (
              <video 
                src={videoData.videoUrl} 
                controls
                poster={videoData.thumbnailUrl}
                className="absolute inset-0 w-full h-full object-cover"
              >
                Your browser does not support the video tag.
              </video>
            ) : (
              <>
                <img 
                  src={videoData.thumbnailUrl || videoData.videoUrl} 
                  alt="Video preview" 
                  className="absolute inset-0 w-full h-full object-cover"
                />
                <div className="absolute inset-0 flex items-center justify-center">
                  <Play className="h-16 w-16 text-primary bg-background bg-opacity-80 rounded-full p-4" />
                </div>
              </>
            )}
          </div>
          <div className="flex justify-between items-center mt-2">
            <p className="text-sm text-muted-foreground">
              {videoData.duration > 0 
                ? `Duration: ${Math.floor(videoData.duration / 60)}:${(videoData.duration % 60).toString().padStart(2, '0')}`
                : isFreeVideo 
                  ? "Click to play video" 
                  : "Duration information unavailable"}
            </p>
            {isFreeVideo && (
              <Badge variant="secondary" className="ml-auto">
                {videoData.id.split('-')[1]} {/* Display source (pixabay/pexels) */}
              </Badge>
            )}
          </div>
        </div>
      )}

      {!isFreeVideo && videoData.animationKeyframes && videoData.animationKeyframes.length > 0 && (
        <div className="mt-6">
          <p className="text-sm font-medium mb-2">Animation Keyframes:</p>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
            {videoData.animationKeyframes.map((keyframe, index) => (
              <Card key={index} className="overflow-hidden">
                <CardHeader className="p-3">
                  <CardTitle className="text-sm">Scene {index + 1}</CardTitle>
                  <CardDescription className="text-xs">
                    {Math.floor(keyframe.timestamp / 60)}:{(keyframe.timestamp % 60).toString().padStart(2, '0')}
                  </CardDescription>
                </CardHeader>
                <CardContent className="p-3 text-xs">
                  {keyframe.description}
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}
      
      {/* Information about what happens next */}
      <div className="mt-6">
        {isFreeVideo ? (
          <Alert>
            <AlertTitle className="flex items-center">
              <span className="text-primary mr-2">✓</span> Video Ready to Use
            </AlertTitle>
            <AlertDescription className="mt-2">
              This free stock video has been imported and is ready to use in your courses or lessons. 
              You can also create additional videos using either free stock videos or AI-generated videos.
            </AlertDescription>
          </Alert>
        ) : (
          processingStatus === "completed" ? (
            <Alert>
              <AlertTitle className="flex items-center">
                <span className="text-primary mr-2">✓</span> AI Generation Complete
              </AlertTitle>
              <AlertDescription className="mt-2">
                Your AI-generated video is ready to use in your courses or lessons. 
                The video includes AI-narrated content and animations based on your script.
              </AlertDescription>
            </Alert>
          ) : (
            processingStatus === "processing" && (
              <Alert>
                <AlertTitle className="flex items-center">
                  <Loader2 className="mr-2 h-4 w-4 animate-spin text-primary" /> Processing
                </AlertTitle>
                <AlertDescription className="mt-2">
                  AI video generation typically takes 2-3 minutes. We're currently:
                  <ul className="list-disc pl-5 mt-2 space-y-1 text-sm">
                    <li>Converting your script to spoken narration</li>
                    <li>Generating visuals to match your content</li>
                    <li>Synchronizing audio with animations</li>
                  </ul>
                </AlertDescription>
              </Alert>
            )
          )
        )}
      </div>
    </div>
  );
}

export default function VideoGeneratorPage() {
  // Redirect to the new mini-course-creator page
  const [_, navigate] = useLocation();
  useEffect(() => {
    navigate('/mini-course-creator');
  }, [navigate]);
  
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState<"create" | "status">("create");
  const [generatedVideo, setGeneratedVideo] = useState<GeneratedVideo | null>(null);
  const [pollingInterval, setPollingInterval] = useState<number | null>(null);

  const videoGenerationMutation = useMutation({
    mutationFn: async (params: VideoGenerationParams) => {
      const response = await generateVideo(params);
      return response;
    },
    onSuccess: (data: GeneratedVideo) => {
      setGeneratedVideo(data);
      setActiveTab("status");
      
      // Customize message and behavior based on whether this is a free video or AI-generated
      if (data.id.startsWith('free-')) {
        // For free videos, no need to poll as they're instantly available
        toast({
          title: "Video imported successfully",
          description: "The free stock video has been added to your collection.",
        });
      } else {
        // For AI videos, start polling for status updates
        if (data.processingStatus === "queued" || data.processingStatus === "processing") {
          startPolling(data.id);
        }
        
        toast({
          title: "Video generation started",
          description: "Your AI video is being created. You'll be notified when it's ready.",
        });
      }
    },
    onError: (error: Error) => {
      // Check if this is an insufficient credits error with suggestion for free option
      if (error.message.includes("Insufficient AI credits")) {
        toast({
          title: "Insufficient AI Credits",
          description: "You don't have enough AI credits for an AI-generated video. Try using the free stock video option instead.",
          variant: "destructive",
        });
        
        // Automatically switch to free video mode
        const formElement = document.getElementById("free");
        if (formElement) {
          (formElement as HTMLInputElement).checked = true;
          // Trigger the onChange event
          const changeEvent = new Event('change', { bubbles: true });
          formElement.dispatchEvent(changeEvent);
          // Update state directly too
          const radioGroups = document.querySelectorAll('input[name="videoType"]');
          radioGroups.forEach((radio) => {
            if ((radio as HTMLInputElement).value === "free") {
              (radio as HTMLInputElement).checked = true;
            }
          });
        }
      } else {
        toast({
          title: "Video generation failed",
          description: error.message,
          variant: "destructive",
        });
      }
    },
  });

  const checkStatusMutation = useMutation({
    mutationFn: async (videoId: string) => {
      return await checkVideoStatus(videoId);
    },
    onSuccess: (data: GeneratedVideo) => {
      setGeneratedVideo(data);
      
      // Stop polling if we've reached a terminal state
      if (data.processingStatus === "completed" || data.processingStatus === "failed") {
        stopPolling();
        
        if (data.processingStatus === "completed") {
          toast({
            title: "Video generated successfully",
            description: "Your video is ready to view and download.",
          });
        } else if (data.processingStatus === "failed") {
          toast({
            title: "Video generation failed",
            description: data.error || "An unexpected error occurred",
            variant: "destructive",
          });
        }
      }
    },
    onError: (error: Error) => {
      stopPolling();
      toast({
        title: "Status check failed",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const startPolling = (videoId: string) => {
    // Clear any existing interval
    stopPolling();
    
    // Poll every 5 seconds
    const intervalId = window.setInterval(() => {
      if (!checkStatusMutation.isPending) {
        checkStatusMutation.mutate(videoId);
      }
    }, 5000);
    
    setPollingInterval(intervalId);
  };

  const stopPolling = () => {
    if (pollingInterval) {
      clearInterval(pollingInterval);
      setPollingInterval(null);
    }
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopPolling();
    };
  }, []);

  return (
    <div className="container max-w-4xl py-10">
      <div className="flex flex-col items-center text-center mb-10">
        <h1 className="text-3xl font-bold tracking-tight">One-Click Video Lesson Generator</h1>
        <p className="text-muted-foreground mt-2 max-w-2xl">
          Turn any course title into a complete video lesson with just one click.
          Our AI generates the script, narration, visuals, and subtitles automatically.
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as "create" | "status")}>
        <TabsList className="grid grid-cols-2 mb-8">
          <TabsTrigger value="create">Create Video</TabsTrigger>
          <TabsTrigger value="status" disabled={!generatedVideo}>View Progress</TabsTrigger>
        </TabsList>
        
        <TabsContent value="create" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Create in One Click</CardTitle>
              <CardDescription>
                Just tell us what you want to teach, and we'll handle everything else
              </CardDescription>
            </CardHeader>
            <CardContent>
              <VideoGenerationForm 
                onGenerateVideo={(params) => videoGenerationMutation.mutate(params)}
                isPending={videoGenerationMutation.isPending}
              />
            </CardContent>
            <CardFooter className="border-t px-6 py-4">
              <p className="text-xs text-muted-foreground">
                Our advanced AI technology handles everything for you automatically:
                <span className="block mt-1">✓ Script writing with ChatGPT</span>
                <span className="block">✓ Natural voice narration with ElevenLabs</span>
                <span className="block">✓ Professional visuals from Pexels/Pixabay</span>
                <span className="block mt-1">The process takes 2-3 minutes to complete.</span>
              </p>
            </CardFooter>
          </Card>
        </TabsContent>
        
        <TabsContent value="status" className="space-y-6">
          {generatedVideo ? (
            <Card>
              <CardHeader>
                <CardTitle>{generatedVideo.title}</CardTitle>
                <CardDescription>
                  Status: <span className="capitalize">{generatedVideo.processingStatus}</span>
                </CardDescription>
              </CardHeader>
              <CardContent>
                <VideoProcessingStatus videoData={generatedVideo} />
              </CardContent>
              <CardFooter className="flex justify-between border-t px-6 py-4">
                <Button 
                  variant="outline" 
                  onClick={() => setActiveTab("create")}
                >
                  Create Another Video
                </Button>
                
                {generatedVideo.processingStatus === "completed" && (
                  <Button>
                    Download Video
                  </Button>
                )}
              </CardFooter>
            </Card>
          ) : (
            <Card>
              <CardContent className="p-6">
                <p className="text-center text-muted-foreground">
                  No video generation in progress. Generate a video first.
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}