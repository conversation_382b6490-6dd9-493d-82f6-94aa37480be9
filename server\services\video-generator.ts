import fs from 'fs';
import path from 'path';
import { promisify } from 'util';
import { exec } from 'child_process';

const execPromise = promisify(exec);
const writeFile = promisify(fs.writeFile);
const mkdir = promisify(fs.mkdir);

// Define the directories
const uploadsDir = path.join(process.cwd(), 'uploads');
const videoDir = path.join(uploadsDir, 'videos');
const tempDir = path.join(process.cwd(), 'temp');

// Ensure temp and output directories exist
async function ensureDirectoriesExist() {
  for (const dir of [uploadsDir, videoDir, tempDir]) {
    try {
      await mkdir(dir, { recursive: true });
    } catch (err) {
      if ((err as NodeJS.ErrnoException).code !== 'EEXIST') {
        throw err;
      }
    }
  }
}

interface ScriptedVideoParams {
  script: string;
  imagePaths: string[];
  outputFileName: string;
  duration?: number; // Duration in seconds, defaults to 10 seconds per image
  fps?: number; // Frames per second, defaults to 30
}

interface VideoResult {
  path: string;
  url: string;
}

/**
 * Create a video from a script and images using FFmpeg.
 * The script is displayed as subtitles (if subtitles are enabled).
 */
export async function createScriptedVideo({
  script,
  imagePaths,
  outputFileName,
  duration = imagePaths.length * 10, // Default to 10 seconds per image
  fps = 30,
}: ScriptedVideoParams): Promise<VideoResult> {
  await ensureDirectoriesExist();

  // Create a unique filename
  const outputPath = path.join(videoDir, `${outputFileName}.mp4`);
  const imageListPath = path.join(tempDir, `${outputFileName}_images.txt`);
  
  // Calculate duration per image
  const imageCount = imagePaths.length;
  const durationPerImage = duration / imageCount;
  
  // Create images.txt file for ffmpeg
  let imageListContent = '';
  for (const imgPath of imagePaths) {
    imageListContent += `file '${imgPath}'\nduration ${durationPerImage}\n`;
  }
  // Add the last image again (required by ffmpeg)
  if (imagePaths.length > 0) {
    imageListContent += `file '${imagePaths[imagePaths.length - 1]}'\n`;
  }

  await writeFile(imageListPath, imageListContent);

  try {
    // Use ffmpeg to create a video slideshow from images
    const ffmpegCmd = `ffmpeg -y -f concat -safe 0 -i "${imageListPath}" -vsync vfr -pix_fmt yuv420p -r ${fps} "${outputPath}"`;
    
    console.log(`Executing FFmpeg command: ${ffmpegCmd}`);
    const { stdout, stderr } = await execPromise(ffmpegCmd);
    
    if (stderr && !stderr.includes('frame=')) {
      console.error('FFmpeg error output:', stderr);
    }
    
    // Clean up the temporary file
    fs.unlink(imageListPath, (err) => {
      if (err) console.error('Error deleting temporary image list:', err);
    });
    
    return {
      path: outputPath,
      url: `/uploads/videos/${path.basename(outputPath)}`,
    };
  } catch (error) {
    console.error('Error generating video:', error);
    throw new Error('Failed to generate video');
  }
}

interface AudioVideoParams {
  audioPath: string;
  imagePaths: string[];
  outputFileName: string;
  fps?: number;
}

/**
 * Create a video synced to an audio file using FFmpeg.
 * The images are displayed in sequence and timed to match the audio duration.
 */
export async function createAudioVideo({
  audioPath,
  imagePaths,
  outputFileName,
  fps = 30,
}: AudioVideoParams): Promise<VideoResult> {
  await ensureDirectoriesExist();

  // Create a unique filename
  const outputPath = path.join(videoDir, `${outputFileName}.mp4`);
  const imageListPath = path.join(tempDir, `${outputFileName}_images.txt`);
  
  // Get audio duration using ffprobe
  const ffprobeCmd = `ffprobe -v error -show_entries format=duration -of default=noprint_wrappers=1:nokey=1 "${audioPath}"`;
  const { stdout } = await execPromise(ffprobeCmd);
  const audioDuration = parseFloat(stdout.trim());
  
  if (isNaN(audioDuration)) {
    throw new Error('Failed to determine audio duration');
  }
  
  // Calculate duration per image
  const imageCount = imagePaths.length;
  const durationPerImage = audioDuration / imageCount;
  
  // Create images.txt file for ffmpeg
  let imageListContent = '';
  for (const imgPath of imagePaths) {
    imageListContent += `file '${imgPath}'\nduration ${durationPerImage}\n`;
  }
  // Add the last image again (required by ffmpeg)
  if (imagePaths.length > 0) {
    imageListContent += `file '${imagePaths[imagePaths.length - 1]}'\n`;
  }
  
  await writeFile(imageListPath, imageListContent);

  try {
    // Use ffmpeg to create a video slideshow from images and sync with audio
    const ffmpegCmd = `ffmpeg -y -f concat -safe 0 -i "${imageListPath}" -i "${audioPath}" -vsync vfr -pix_fmt yuv420p -r ${fps} -c:a aac -b:a 192k -shortest "${outputPath}"`;
    
    console.log(`Executing FFmpeg command: ${ffmpegCmd}`);
    const { stdout, stderr } = await execPromise(ffmpegCmd);
    
    if (stderr && !stderr.includes('frame=')) {
      console.error('FFmpeg error output:', stderr);
    }
    
    // Clean up the temporary file
    fs.unlink(imageListPath, (err) => {
      if (err) console.error('Error deleting temporary image list:', err);
    });
    
    return {
      path: outputPath,
      url: `/uploads/videos/${path.basename(outputPath)}`,
    };
  } catch (error) {
    console.error('Error generating video with audio:', error);
    throw new Error('Failed to generate video with audio');
  }
}

interface AnimatedVideoParams {
  script: string;
  audioPath?: string;
  imagePaths: string[];
  outputFileName: string;
  transitionDuration?: number; // Duration of transitions in seconds
  fps?: number;
  includeSubtitles?: boolean;
}

/**
 * Create an animated video with transitions between images and synced audio.
 * Can also include subtitles based on the script.
 */
export async function createAnimatedVideo({
  script,
  audioPath,
  imagePaths,
  outputFileName,
  transitionDuration = 1,
  fps = 30,
  includeSubtitles = false,
}: AnimatedVideoParams): Promise<VideoResult> {
  await ensureDirectoriesExist();

  // Create a unique filename
  const outputPath = path.join(videoDir, `${outputFileName}.mp4`);
  const tempVideoPath = path.join(tempDir, `${outputFileName}_temp.mp4`);
  const subtitlesPath = path.join(tempDir, `${outputFileName}_subtitles.srt`);
  
  // Determine duration
  let videoDuration = imagePaths.length * 5; // Default 5 seconds per image
  
  if (audioPath) {
    // Get audio duration using ffprobe if audio is provided
    const ffprobeCmd = `ffprobe -v error -show_entries format=duration -of default=noprint_wrappers=1:nokey=1 "${audioPath}"`;
    const { stdout } = await execPromise(ffprobeCmd);
    const audioDuration = parseFloat(stdout.trim());
    
    if (!isNaN(audioDuration)) {
      videoDuration = audioDuration;
    }
  }
  
  // Calculate duration per image (accounting for transitions)
  const imageCount = imagePaths.length;
  const totalTransitionTime = (imageCount - 1) * transitionDuration;
  const remainingTime = videoDuration - totalTransitionTime;
  const durationPerImage = remainingTime / imageCount;
  
  // Create complex filter for transitions
  let filterComplex = '';
  
  // Add all images as inputs
  for (let i = 0; i < imagePaths.length; i++) {
    filterComplex += `[${i}:v]scale=1920:1080:force_original_aspect_ratio=decrease,pad=1920:1080:(ow-iw)/2:(oh-ih)/2,setsar=1,format=yuv420p[v${i}];`;
  }
  
  // Create the transitions
  for (let i = 0; i < imagePaths.length - 1; i++) {
    filterComplex += `[v${i}][v${i+1}]xfade=transition=fade:duration=${transitionDuration}:offset=${durationPerImage * (i+1)}[v${i+1}fade];`;
  }
  
  // Generate subtitles if requested
  if (includeSubtitles) {
    const subtitleContent = generateSRT(script, videoDuration);
    await writeFile(subtitlesPath, subtitleContent);
  }
  
  try {
    // First command: Create video with transitions
    let ffmpegInputs = '';
    for (const imgPath of imagePaths) {
      ffmpegInputs += `-loop 1 -t ${durationPerImage + transitionDuration} -i "${imgPath}" `;
    }
    
    const finalOutput = imagePaths.length > 1 ? `[v${imagePaths.length-1}fade]` : '[v0]';
    
    let ffmpegCmd = `ffmpeg -y ${ffmpegInputs} -filter_complex "${filterComplex}${finalOutput}format=yuv420p[outv]" -map "[outv]" -r ${fps} "${tempVideoPath}"`;
    
    console.log(`Executing FFmpeg command 1: ${ffmpegCmd}`);
    await execPromise(ffmpegCmd);
    
    // Second command: Add audio and/or subtitles
    let additionalInputs = '';
    let additionalMaps = '';
    let additionalFilters = '';
    
    if (audioPath) {
      additionalInputs += `-i "${audioPath}" `;
      additionalMaps += '-map 1:a ';
      additionalFilters += '-c:a aac -b:a 192k ';
    }
    
    if (includeSubtitles) {
      additionalFilters += `-vf subtitles="${subtitlesPath.replace(/\\/g, '\\\\')}" `;
    }
    
    ffmpegCmd = `ffmpeg -y -i "${tempVideoPath}" ${additionalInputs}-map 0:v ${additionalMaps}${additionalFilters}-c:v libx264 -crf 23 -shortest "${outputPath}"`;
    
    console.log(`Executing FFmpeg command 2: ${ffmpegCmd}`);
    await execPromise(ffmpegCmd);
    
    // Clean up temporary files
    fs.unlink(tempVideoPath, (err) => {
      if (err) console.error('Error deleting temporary video:', err);
    });
    
    if (includeSubtitles) {
      fs.unlink(subtitlesPath, (err) => {
        if (err) console.error('Error deleting subtitles file:', err);
      });
    }
    
    return {
      path: outputPath,
      url: `/uploads/videos/${path.basename(outputPath)}`,
    };
  } catch (error) {
    console.error('Error generating animated video:', error);
    throw new Error('Failed to generate animated video');
  }
}

/**
 * Generate a simple SRT subtitle file from a script
 */
function generateSRT(script: string, duration: number): string {
  // Split script into sentences or chunks
  const words = script.split(/\s+/);
  const chunks: string[] = [];
  let currentChunk = '';
  
  for (const word of words) {
    if (currentChunk.length + word.length > 40) {
      chunks.push(currentChunk.trim());
      currentChunk = word;
    } else {
      currentChunk += ' ' + word;
    }
  }
  
  if (currentChunk.trim().length > 0) {
    chunks.push(currentChunk.trim());
  }
  
  // Calculate timing for each chunk
  const timePerChunk = duration / chunks.length;
  
  // Generate SRT format
  let srt = '';
  chunks.forEach((chunk, index) => {
    const startTime = index * timePerChunk;
    const endTime = (index + 1) * timePerChunk;
    
    srt += `${index + 1}\n`;
    srt += `${formatSRTTime(startTime)} --> ${formatSRTTime(endTime)}\n`;
    srt += `${chunk}\n\n`;
  });
  
  return srt;
}

/**
 * Format seconds into SRT time format (HH:MM:SS,mmm)
 */
function formatSRTTime(seconds: number): string {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  const milliseconds = Math.floor((seconds - Math.floor(seconds)) * 1000);
  
  return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(secs).padStart(2, '0')},${String(milliseconds).padStart(3, '0')}`;
}