import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useUser } from '@/hooks/use-user';
// Layout is provided by app-content.tsx

import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter,
} from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { EmptyState } from '@/components/ui/empty-state';
import { apiRequest } from '@/lib/queryClient';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { ColorfulIcon } from '@/components/ui/colorful-icon';
import { Search, BookOpen, Layers, GraduationCap, BookMarked, LifeBuoy, School, Shield, Briefcase, CheckCircle2, ArrowRight } from 'lucide-react';

// Define the form schema
const formSchema = z.object({
  apiKey: z.string().min(1, 'API Key is required'),
  apiSecret: z.string().optional(),
  instanceUrl: z.string().optional(),
  subdomain: z.string().optional(),
});

// Define Platform type
interface Platform {
  id: number;
  name: string;
  slug: string;
  description: string;
  iconPath?: string;
  primaryColor?: string;
  features: string[];
}

// Mock platforms data
const mockPlatforms: Platform[] = [
  {
    id: 1,
    name: 'Moodle',
    slug: 'moodle',
    description: 'Open-source learning platform designed to provide educators, administrators and learners with a robust and secure system.',
    primaryColor: '#f98012',
    features: ['Open Source', 'Customizable', 'Mobile-friendly', 'SCORM Compliant'],
  },
  {
    id: 2,
    name: 'Canvas',
    slug: 'canvas',
    description: 'A modern, easy-to-use LMS that connects all the digital tools and resources teachers use into one simple place.',
    primaryColor: '#e23b3b',
    features: ['Cloud-Based', 'Mobile Apps', 'Analytics', 'Integrated Tools'],
  },
  {
    id: 3,
    name: 'Blackboard',
    slug: 'blackboard',
    description: 'A virtual learning environment and learning management system developed by Blackboard Inc.',
    primaryColor: '#2d3b45',
    features: ['Assessment Tools', 'Content Management', 'Collaboration Tools'],
  },
  {
    id: 4,
    name: 'Teachable',
    slug: 'teachable',
    description: 'An online learning platform that allows instructors to create and sell courses on their own branded websites.',
    primaryColor: '#1c232a',
    features: ['Course Creator', 'Sales Pages', 'Payment Processing'],
  },
  {
    id: 5,
    name: 'Thinkific',
    slug: 'thinkific',
    description: 'A software platform that enables entrepreneurs to create, market, sell, and deliver their own online courses.',
    primaryColor: '#43c9cb',
    features: ['Course Builder', 'Marketing Tools', 'Student Progress Tracking'],
  },
];

// Define Integration type
interface Integration {
  id: number;
  userId: number;
  platformId: number;
  apiKey: string;
  apiSecret?: string;
  settings: {
    subdomain?: string;
    instanceUrl?: string;
  };
  connectedAt: string;
  updatedAt: string;
}

// Mock integrations data
const mockIntegrations: Integration[] = [
  // Empty initially
];

export default function LMSPlatformsPage() {
  const { user } = useUser();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState('all');
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [connectingPlatform, setConnectingPlatform] = useState<Platform | null>(null);

  // Form setup using react-hook-form and zod validation
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      apiKey: '',
      apiSecret: '',
      instanceUrl: '',
      subdomain: '',
    },
  });

  // Fetch integrations data
  const { data: integrations = [], isLoading: isLoadingIntegrations } = useQuery({
    queryKey: ['/api/platform-integrations/lms'],
    queryFn: async () => {
      try {
        const res = await apiRequest('GET', '/api/platform-integrations/lms');
        const data = await res.json();
        return data;
      } catch (error) {
        console.error('Failed to fetch LMS integrations:', error);
        return [];
      }
    },
  });

  // Mutation for connecting a platform
  const connectMutation = useMutation({
    mutationFn: async (data: any) => {
      const res = await apiRequest('POST', '/api/platform-integrations/lms', {
        ...data,
        platformId: connectingPlatform?.id,
      });
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/platform-integrations/lms'] });
      toast({
        title: 'Success',
        description: `${connectingPlatform?.name} has been connected successfully.`,
      });
      setIsDialogOpen(false);
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: `Failed to connect ${connectingPlatform?.name}: ${error.message}`,
        variant: 'destructive',
      });
    },
  });

  // Mutation for disconnecting a platform
  const disconnectMutation = useMutation({
    mutationFn: async (integrationId: number) => {
      const res = await apiRequest('DELETE', `/api/platform-integrations/lms/${integrationId}`);
      return res.json();
    },
    onSuccess: (_, integrationId) => {
      queryClient.invalidateQueries({ queryKey: ['/api/platform-integrations/lms'] });
      const platformName = mockPlatforms.find(
        p => p.id === mockIntegrations.find(i => i.id === integrationId)?.platformId
      )?.name;
      toast({
        title: 'Disconnected',
        description: `${platformName || 'Platform'} has been disconnected.`,
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: `Failed to disconnect platform: ${error.message}`,
        variant: 'destructive',
      });
    },
  });

  // Filter platforms based on search term and active tab
  const filteredPlatforms = mockPlatforms.filter(platform => {
    const matchesSearch = platform.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         platform.description.toLowerCase().includes(searchTerm.toLowerCase());
    const isPlatformConnected = mockIntegrations.some(i => i.platformId === platform.id);
    
    if (activeTab === 'connected' && !isPlatformConnected) return false;
    
    return matchesSearch;
  });

  // Handle form submission
  const onSubmit = (values: z.infer<typeof formSchema>) => {
    if (!connectingPlatform) return;
    
    connectMutation.mutate({
      apiKey: values.apiKey,
      apiSecret: values.apiSecret,
      settings: {
        subdomain: values.subdomain,
        instanceUrl: values.instanceUrl,
      },
    });
  };

  const openConnectDialog = (platform: any) => {
    setConnectingPlatform(platform);
    setIsDialogOpen(true);
    form.reset();
  };

  const getIconForPlatform = (slug: string) => {
    switch (slug) {
      case 'moodle': return BookOpen;
      case 'canvas': return Layers;
      case 'blackboard': return GraduationCap;
      case 'teachable': return BookMarked;
      case 'thinkific': return School;
      default: return BookOpen;
    }
  };

  return (
    <div className="container mx-auto py-6 max-w-6xl">
      <div className="flex flex-col space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">LMS Platforms</h1>
            <p className="text-gray-500 mt-1">
              Connect your learning management systems to distribute your courses
            </p>
          </div>
        </div>
          
        {/* Search and filter controls */}
        <div className="flex flex-col space-y-4 sm:flex-row sm:space-y-0 sm:space-x-4 sm:items-center justify-between">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              type="search"
              placeholder="Search LMS platforms..."
              className="pl-10 w-full sm:w-[300px]"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
            
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full sm:w-auto">
            <TabsList>
              <TabsTrigger value="all">All Platforms</TabsTrigger>
              <TabsTrigger value="connected">Connected</TabsTrigger>
            </TabsList>
          </Tabs>
        </div>

        {/* Platforms grid */}
        {filteredPlatforms.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredPlatforms.map((platform) => {
              const isPlatformConnected = mockIntegrations.some(i => i.platformId === platform.id);
              const integration = mockIntegrations.find(i => i.platformId === platform.id);
              const Icon = getIconForPlatform(platform.slug);
              
              return (
                <Card key={platform.id} className="overflow-hidden border border-gray-200 hover:border-primary/50 transition-all duration-300">
                  <CardHeader className="pb-2">
                    <div className="flex items-start justify-between mb-2">
                      <div className="h-10 w-10 rounded-lg flex items-center justify-center" 
                           style={{ backgroundColor: `${platform.primaryColor}20` }}>
                        <Icon className="h-5 w-5" style={{ color: platform.primaryColor }} />
                      </div>
                      {isPlatformConnected && (
                        <Badge variant="outline" className="bg-emerald-50 text-emerald-700 border-emerald-200">
                          <CheckCircle2 className="h-3 w-3 mr-1" /> Connected
                        </Badge>
                      )}
                    </div>
                    <CardTitle className="text-xl">{platform.name}</CardTitle>
                    <CardDescription>{platform.description}</CardDescription>
                  </CardHeader>
                  
                  <CardContent className="pb-2">
                    <div className="flex flex-wrap gap-2">
                      {platform.features.map((feature) => (
                        <Badge key={feature} variant="secondary" className="font-normal">
                          {feature}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                  
                  <CardFooter className="flex flex-wrap gap-2 pt-2">
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      className="text-xs"
                      onClick={() => window.open(`https://${platform.slug}.com/api/documentation`, '_blank')}
                    >
                      API Documentation
                    </Button>
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      className="text-xs"
                      onClick={() => window.open(`https://${platform.slug}.com`, '_blank')}
                    >
                      Visit Website
                    </Button>
                    
                    {isPlatformConnected ? (
                      <Button 
                        variant="destructive" 
                        size="sm"
                        className="ml-auto"
                        onClick={() => disconnectMutation.mutate(integration!.id)}
                      >
                        Disconnect
                      </Button>
                    ) : (
                      <Button 
                        variant="default" 
                        size="sm"
                        className="ml-auto"
                        onClick={() => openConnectDialog(platform)}
                      >
                        Connect
                      </Button>
                    )}
                  </CardFooter>
                </Card>
              );
            })}
          </div>
        ) : (
          <EmptyState
            icon={BookOpen}
            title="No platforms found"
            description="No LMS platforms match your search criteria."
            actions={
              <Button variant="default" onClick={() => setSearchTerm('')}>
                Clear Search
              </Button>
            }
          />
        )}
        
        {/* Connected platforms guide section */}
        {mockIntegrations.length > 0 && (
          <div className="mt-8 p-6 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-100">
            <h2 className="text-lg font-semibold mb-2">Publishing Courses to Connected Platforms</h2>
            <p className="text-gray-600 mb-4">Follow these steps to publish your course to your connected LMS platforms:</p>
            
            <div className="space-y-3">
              <div className="flex items-start">
                <div className="bg-white rounded-full p-1 mr-3 text-blue-500">
                  <span className="flex items-center justify-center w-5 h-5 bg-blue-100 rounded-full text-xs font-bold">1</span>
                </div>
                <p className="text-sm">Complete your course creation and ensure all content is finalized</p>
              </div>
              
              <div className="flex items-start">
                <div className="bg-white rounded-full p-1 mr-3 text-blue-500">
                  <span className="flex items-center justify-center w-5 h-5 bg-blue-100 rounded-full text-xs font-bold">2</span>
                </div>
                <p className="text-sm">Go to your course settings and select "Distribution" tab</p>
              </div>
              
              <div className="flex items-start">
                <div className="bg-white rounded-full p-1 mr-3 text-blue-500">
                  <span className="flex items-center justify-center w-5 h-5 bg-blue-100 rounded-full text-xs font-bold">3</span>
                </div>
                <p className="text-sm">Choose the LMS platform you wish to publish to and click "Publish"</p>
              </div>
              
              <div className="mt-4">
                <Button variant="default" className="gap-2">
                  View Publishing Guide <ArrowRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        )}
        
        {/* Connect Platform Dialog */}
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Connect to {connectingPlatform?.name}</DialogTitle>
              <DialogDescription>
                Enter your API credentials to connect with {connectingPlatform?.name}. 
                You can find these in your {connectingPlatform?.name} dashboard.
              </DialogDescription>
            </DialogHeader>
            
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="apiKey"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>API Key</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter your API key" {...field} />
                      </FormControl>
                      <FormDescription>
                        Your API key from the {connectingPlatform?.name} developer dashboard
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="apiSecret"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>API Secret (optional)</FormLabel>
                      <FormControl>
                        <Input 
                          type="password"
                          placeholder="Enter your API secret" 
                          {...field} 
                        />
                      </FormControl>
                      <FormDescription>
                        Your API secret or token from {connectingPlatform?.name}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                {['moodle', 'canvas', 'blackboard'].includes(connectingPlatform?.slug || '') && (
                  <FormField
                    control={form.control}
                    name="instanceUrl"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Instance URL</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="https://your-school.com/lms"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          The URL of your {connectingPlatform?.name} instance
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}
                
                {['teachable', 'thinkific'].includes(connectingPlatform?.slug || '') && (
                  <FormField
                    control={form.control}
                    name="subdomain"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Subdomain</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="your-school"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          Your {connectingPlatform?.name} subdomain (e.g., for your-school.{connectingPlatform?.slug}.com)
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}
                
                <DialogFooter>
                  <Button variant="outline" type="button" onClick={() => setIsDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button type="submit">Connect Platform</Button>
                </DialogFooter>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
}