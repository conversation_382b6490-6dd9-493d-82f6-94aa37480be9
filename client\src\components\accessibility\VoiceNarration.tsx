import React, { useState, useEffect, useRef } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>lider } from "@/components/ui/slider";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Mic, Volume2, VolumeX, Play, Pause, SkipForward, SkipBack } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";

interface VoiceNarrationProps {
  content: string;
  title?: string;
  autoPlay?: boolean;
  onComplete?: () => void;
  onError?: (error: string) => void;
}

export const VoiceNarration: React.FC<VoiceNarrationProps> = ({
  content,
  title = 'Content',
  autoPlay = false,
  onComplete,
  onError
}) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  const [progress, setProgress] = useState(0);
  const [volume, setVolume] = useState(80);
  const [rate, setRate] = useState(1);
  const [isMuted, setIsMuted] = useState(false);
  const [availableVoices, setAvailableVoices] = useState<{id: string, name: string, language: string}[]>([]);
  const [selectedVoice, setSelectedVoice] = useState<string | null>(null);
  const [autoNarrate, setAutoNarrate] = useState(autoPlay);
  
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const { toast } = useToast();

  // Fetch available voices on component mount
  useEffect(() => {
    const fetchVoices = async () => {
      try {
        const response = await apiRequest('GET', '/api/ai/tts-models');
        if (response.ok) {
          const data = await response.json();
          setAvailableVoices(data.voices);
          // Set default voice to first available voice
          if (data.voices.length > 0 && !selectedVoice) {
            setSelectedVoice(data.voices[0].id);
          }
        }
      } catch (error) {
        console.error('Failed to fetch voices:', error);
        toast({
          title: 'Error',
          description: 'Failed to fetch available voices. Please try again later.',
          variant: 'destructive',
        });
      }
    };

    fetchVoices();
  }, [toast]);

  // Auto narrate if enabled
  useEffect(() => {
    if (autoNarrate && content && !audioUrl) {
      generateNarration();
    }
  }, [autoNarrate, content, audioUrl]);

  // Setup audio event listeners
  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const updateProgress = () => {
      const value = (audio.currentTime / audio.duration) * 100;
      setProgress(isNaN(value) ? 0 : value);
    };

    const handleEnded = () => {
      setIsPlaying(false);
      setProgress(0);
      if (onComplete) onComplete();
    };

    audio.addEventListener('timeupdate', updateProgress);
    audio.addEventListener('ended', handleEnded);

    return () => {
      audio.removeEventListener('timeupdate', updateProgress);
      audio.removeEventListener('ended', handleEnded);
    };
  }, [audioUrl, onComplete]);

  // Generate narration audio from content
  const generateNarration = async () => {
    if (!content || content.trim() === '') {
      toast({
        title: 'Error',
        description: 'No content provided for narration.',
        variant: 'destructive',
      });
      return;
    }

    if (!selectedVoice) {
      toast({
        title: 'Warning',
        description: 'No voice selected. Defaulting to system voice.',
      });
    }

    setIsGenerating(true);
    try {
      const response = await apiRequest('POST', '/api/ai/text-to-speech', {
        text: content,
        title: title,
        voiceId: selectedVoice,
        rate: rate
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to generate narration');
      }

      const data = await response.json();
      setAudioUrl(data.audioUrl);
      
      toast({
        title: 'Success',
        description: 'Narration generated successfully!',
      });

      // Auto play if enabled
      if (autoNarrate) {
        setTimeout(() => {
          if (audioRef.current) {
            audioRef.current.play()
              .then(() => setIsPlaying(true))
              .catch(err => console.error('Failed to auto-play:', err));
          }
        }, 500);
      }
    } catch (error) {
      console.error('Error generating narration:', error);
      const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
      toast({
        title: 'Error',
        description: `Failed to generate narration: ${errorMessage}`,
        variant: 'destructive',
      });
      if (onError) onError(errorMessage);
    } finally {
      setIsGenerating(false);
    }
  };

  // Play/pause controls
  const togglePlayPause = () => {
    const audio = audioRef.current;
    if (!audio) return;

    if (isPlaying) {
      audio.pause();
      setIsPlaying(false);
      setIsPaused(true);
    } else {
      audio.play()
        .then(() => {
          setIsPlaying(true);
          setIsPaused(false);
        })
        .catch(err => console.error('Failed to play audio:', err));
    }
  };

  // Skip forward/backward
  const skipForward = () => {
    const audio = audioRef.current;
    if (!audio) return;
    audio.currentTime = Math.min(audio.duration, audio.currentTime + 10);
  };

  const skipBackward = () => {
    const audio = audioRef.current;
    if (!audio) return;
    audio.currentTime = Math.max(0, audio.currentTime - 10);
  };

  // Update volume
  const handleVolumeChange = (value: number[]) => {
    const audio = audioRef.current;
    if (!audio) return;
    
    const newVolume = value[0];
    setVolume(newVolume);
    audio.volume = newVolume / 100;
    
    if (newVolume === 0) {
      setIsMuted(true);
    } else if (isMuted) {
      setIsMuted(false);
    }
  };

  // Toggle mute
  const toggleMute = () => {
    const audio = audioRef.current;
    if (!audio) return;
    
    if (isMuted) {
      audio.volume = volume / 100;
      setIsMuted(false);
    } else {
      audio.volume = 0;
      setIsMuted(true);
    }
  };

  // Change speech rate
  const handleRateChange = (value: number[]) => {
    setRate(value[0]);
    if (audioRef.current) {
      audioRef.current.playbackRate = value[0];
    }
  };

  return (
    <Card className="w-full mb-4">
      <CardContent className="pt-6">
        {/* Voice selection */}
        <div className="mb-4 flex flex-wrap gap-2">
          {availableVoices.map(voice => (
            <Badge 
              key={voice.id}
              variant={selectedVoice === voice.id ? "default" : "outline"}
              className="cursor-pointer"
              onClick={() => setSelectedVoice(voice.id)}
            >
              {voice.name} ({voice.language})
            </Badge>
          ))}
          {availableVoices.length === 0 && (
            <Badge variant="outline">Loading voices...</Badge>
          )}
        </div>

        {/* Main controls */}
        <div className="flex flex-col space-y-4">
          {/* Audio element */}
          <audio ref={audioRef} src={audioUrl || ''} preload="auto" />

          {/* Controls section */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              {!audioUrl ? (
                <Button 
                  onClick={generateNarration} 
                  disabled={isGenerating}
                  size="sm"
                  className="gap-1"
                >
                  {isGenerating ? (
                    <>
                      <div className="animate-spin h-4 w-4 border-2 border-current border-t-transparent rounded-full" />
                      Generating...
                    </>
                  ) : (
                    <>
                      <Mic className="h-4 w-4" />
                      Generate Narration
                    </>
                  )}
                </Button>
              ) : (
                <div className="flex items-center space-x-2">
                  <Button
                    size="icon"
                    variant="outline"
                    onClick={skipBackward}
                    disabled={!audioUrl}
                  >
                    <SkipBack className="h-4 w-4" />
                  </Button>
                  <Button
                    size="icon"
                    onClick={togglePlayPause}
                    disabled={!audioUrl}
                  >
                    {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                  </Button>
                  <Button
                    size="icon"
                    variant="outline"
                    onClick={skipForward}
                    disabled={!audioUrl}
                  >
                    <SkipForward className="h-4 w-4" />
                  </Button>
                </div>
              )}
            </div>

            {/* Volume control */}
            <div className="flex items-center space-x-2">
              <Button
                size="icon"
                variant="ghost"
                onClick={toggleMute}
                disabled={!audioUrl}
              >
                {isMuted ? <VolumeX className="h-4 w-4" /> : <Volume2 className="h-4 w-4" />}
              </Button>
              <div className="w-20">
                <Slider
                  disabled={!audioUrl}
                  value={[volume]}
                  max={100}
                  step={1}
                  onValueChange={handleVolumeChange}
                />
              </div>
            </div>
          </div>

          {/* Progress bar */}
          <div className="space-y-2">
            <Slider
              disabled={!audioUrl || !isPlaying}
              value={[progress]}
              max={100}
              step={0.1}
              onValueChange={(value) => {
                if (audioRef.current) {
                  const newTime = (value[0] / 100) * audioRef.current.duration;
                  audioRef.current.currentTime = newTime;
                  setProgress(value[0]);
                }
              }}
            />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>
                {audioRef.current ? 
                  `${Math.floor(audioRef.current.currentTime / 60)}:${Math.floor(audioRef.current.currentTime % 60).toString().padStart(2, '0')}` : 
                  '0:00'}
              </span>
              <span>
                {audioRef.current && !isNaN(audioRef.current.duration) ? 
                  `${Math.floor(audioRef.current.duration / 60)}:${Math.floor(audioRef.current.duration % 60).toString().padStart(2, '0')}` : 
                  '0:00'}
              </span>
            </div>
          </div>

          {/* Playback rate */}
          <div className="flex items-center space-x-4">
            <div className="text-sm font-medium">Speed:</div>
            <div className="w-32">
              <Slider
                value={[rate]}
                min={0.5}
                max={2}
                step={0.1}
                onValueChange={handleRateChange}
              />
            </div>
            <div className="text-sm">{rate.toFixed(1)}x</div>
          </div>

          {/* Auto-narrate toggle */}
          <div className="flex items-center space-x-2 mt-2">
            <Switch
              checked={autoNarrate}
              onCheckedChange={setAutoNarrate}
              id="auto-narrate"
            />
            <label
              htmlFor="auto-narrate"
              className="text-sm font-medium leading-none cursor-pointer"
            >
              Auto-narrate content
            </label>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default VoiceNarration;