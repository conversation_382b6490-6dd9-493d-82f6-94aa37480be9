import { useState, useEffect, useCallback } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Search, Download, Grid, List, Filter, Image, Video, Star, Clock, Eye } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';

interface StockMedia {
  id: string;
  url: string;
  thumbnail?: string;
  width: number;
  height: number;
  alt?: string;
  photographer?: string;
  photographer_url?: string;
  duration?: number;
  tags?: string[];
  provider: string;
}

interface UnifiedStockMediaSelectorProps {
  onSelect: (media: StockMedia) => void;
  type?: 'photo' | 'video' | 'both';
  className?: string;
}

export function UnifiedStockMediaSelector({ 
  onSelect, 
  type = 'both', 
  className = '' 
}: UnifiedStockMediaSelectorProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState<'photo' | 'video'>(type === 'video' ? 'video' : 'photo');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [page, setPage] = useState(1);
  const [importedMedia, setImportedMedia] = useState<Set<string>>(new Set());
  
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Get suggested categories
  const { data: categories } = useQuery({
    queryKey: ['/api/stock/categories'],
    queryFn: async () => {
      const response = await apiRequest('GET', '/api/stock/categories');
      return response.json();
    },
    staleTime: 60 * 60 * 1000, // Cache for 1 hour
  });

  // Search photos
  const { 
    data: photoResults, 
    isLoading: loadingPhotos, 
    error: photoError 
  } = useQuery({
    queryKey: ['/api/pexels/photos', searchQuery, page],
    queryFn: async () => {
      const response = await apiRequest('GET', `/api/pexels/photos?query=${encodeURIComponent(searchQuery || 'education')}&page=${page}&per_page=20`);
      return response.json();
    },
    enabled: activeTab === 'photo',
    staleTime: 5 * 60 * 1000,
  });

  // Search videos
  const { 
    data: videoResults, 
    isLoading: loadingVideos, 
    error: videoError 
  } = useQuery({
    queryKey: ['/api/pexels/videos', searchQuery, page],
    queryFn: async () => {
      const response = await apiRequest('GET', `/api/pexels/videos?query=${encodeURIComponent(searchQuery || 'learning')}&page=${page}&per_page=20`);
      return response.json();
    },
    enabled: activeTab === 'video',
    staleTime: 5 * 60 * 1000,
  });

  // Get trending media (use default search when no query)
  const { data: trendingMedia } = useQuery({
    queryKey: ['/api/pixabay/photos', 'trending', activeTab],
    queryFn: async () => {
      if (activeTab === 'photo') {
        const response = await apiRequest('GET', `/api/pixabay/photos?query=education&page=1&per_page=12`);
        return response.json();
      } else {
        const response = await apiRequest('GET', `/api/pixabay/videos?query=learning&page=1&per_page=12`);
        return response.json();
      }
    },
    enabled: !searchQuery || searchQuery.length < 3,
    staleTime: 30 * 60 * 1000, // Cache for 30 minutes
  });

  // Import media mutation
  const importMutation = useMutation({
    mutationFn: async ({ mediaData, type }: { mediaData: StockMedia; type: 'photo' | 'video' }) => {
      const response = await apiRequest('POST', '/api/stock/import', {
        mediaData,
        type
      });
      return response.json();
    },
    onSuccess: (data, variables) => {
      setImportedMedia(prev => new Set([...prev, variables.mediaData.id]));
      toast({
        title: "Media Imported",
        description: data.message || "Media has been added to your library",
      });
      // Invalidate imported media cache
      queryClient.invalidateQueries({ queryKey: ['/api/stock/imported'] });
    },
    onError: (error: any) => {
      toast({
        title: "Import Failed",
        description: error.message || "Failed to import media to your library",
        variant: "destructive"
      });
    }
  });

  const handleSearch = useCallback((query: string) => {
    setSearchQuery(query);
    setPage(1);
  }, []);

  const handleCategorySelect = useCallback((category: string) => {
    setSelectedCategory(category);
    setSearchQuery(category);
    setPage(1);
  }, []);

  const handleImport = useCallback((media: StockMedia) => {
    importMutation.mutate({
      mediaData: media,
      type: activeTab
    });
  }, [importMutation, activeTab]);

  const handleSelect = useCallback((media: StockMedia) => {
    onSelect(media);
  }, [onSelect]);

  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const getCurrentResults = () => {
    if (searchQuery && searchQuery.length >= 3) {
      return activeTab === 'photo' ? photoResults : videoResults;
    }
    return trendingMedia;
  };

  const getCurrentLoading = () => {
    return activeTab === 'photo' ? loadingPhotos : loadingVideos;
  };

  const getCurrentItems = () => {
    const results = getCurrentResults();
    // API returns array directly, not wrapped in photos/videos property
    return Array.isArray(results) ? results : [];
  };

  const renderMediaItem = (item: any) => {
    const isImported = importedMedia.has(item.id?.toString());
    
    // Handle different data structures from Pexels/Pixabay APIs
    const imageUrl = item.src?.medium || item.webformatURL || item.url || item.thumbnail;
    const title = item.alt || item.title || `${activeTab === 'photo' ? 'Photo' : 'Video'} by ${item.photographer || item.user || 'Unknown'}`;
    
    if (viewMode === 'list') {
      return (
        <div key={item.id} className="flex items-center space-x-4 p-4 border rounded-lg hover:bg-gray-50">
          <div className="relative w-24 h-16 flex-shrink-0">
            <img
              src={imageUrl}
              alt={title}
              className="w-full h-full object-cover rounded"
              loading="lazy"
              onError={(e) => {
                // Fallback to different image sources if first one fails
                const target = e.target as HTMLImageElement;
                if (item.src?.small && target.src !== item.src.small) {
                  target.src = item.src.small;
                } else if (item.src?.original && target.src !== item.src.original) {
                  target.src = item.src.original;
                }
              }}
            />
            {activeTab === 'video' && item.duration && (
              <Badge className="absolute bottom-1 right-1 text-xs bg-black/70 text-white">
                <Clock className="w-3 h-3 mr-1" />
                {formatDuration(item.duration)}
              </Badge>
            )}
          </div>
          
          <div className="flex-1 min-w-0">
            <p className="font-medium text-sm truncate">
              {item.alt || `${activeTab === 'photo' ? 'Photo' : 'Video'} by ${item.photographer || 'Unknown'}`}
            </p>
            <p className="text-xs text-gray-500">
              {item.width} × {item.height}
              {item.photographer && ` • by ${item.photographer}`}
            </p>
            {item.tags && item.tags.length > 0 && (
              <div className="flex flex-wrap gap-1 mt-1">
                {item.tags.slice(0, 3).map((tag, index) => (
                  <Badge key={index} variant="secondary" className="text-xs">
                    {tag}
                  </Badge>
                ))}
              </div>
            )}
          </div>
          
          <div className="flex space-x-2">
            <Button
              size="sm"
              variant="outline"
              onClick={() => handleImport(item)}
              disabled={importMutation.isPending || isImported}
            >
              {isImported ? (
                <>
                  <Star className="w-4 h-4 mr-1 fill-current" />
                  Saved
                </>
              ) : (
                <>
                  <Download className="w-4 h-4 mr-1" />
                  Import
                </>
              )}
            </Button>
            <Button size="sm" onClick={() => handleSelect(item)}>
              Select
            </Button>
          </div>
        </div>
      );
    }

    return (
      <Card key={item.id} className="group cursor-pointer hover:shadow-lg transition-shadow">
        <CardContent className="p-0">
          <div className="relative aspect-video w-full overflow-hidden rounded-t-lg">
            <img
              src={imageUrl}
              alt={title}
              className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
              loading="lazy"
              onError={(e) => {
                // Fallback to different image sources if first one fails
                const target = e.target as HTMLImageElement;
                if (item.src?.small && target.src !== item.src.small) {
                  target.src = item.src.small;
                } else if (item.src?.original && target.src !== item.src.original) {
                  target.src = item.src.original;
                }
              }}
            />
            
            {activeTab === 'video' && item.duration && (
              <Badge className="absolute bottom-2 right-2 bg-black/70 text-white">
                <Clock className="w-3 h-3 mr-1" />
                {formatDuration(item.duration)}
              </Badge>
            )}

            <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors">
              <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                <div className="flex space-x-2">
                  <Button
                    size="sm"
                    variant="secondary"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleImport(item);
                    }}
                    disabled={importMutation.isPending || isImported}
                  >
                    {isImported ? (
                      <>
                        <Star className="w-4 h-4 mr-1 fill-current" />
                        Saved
                      </>
                    ) : (
                      <>
                        <Download className="w-4 h-4 mr-1" />
                        Import
                      </>
                    )}
                  </Button>
                  <Button
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleSelect(item);
                    }}
                  >
                    Select
                  </Button>
                </div>
              </div>
            </div>
          </div>
          
          <div className="p-3">
            <p className="font-medium text-sm truncate mb-1">
              {item.alt || `${activeTab === 'photo' ? 'Photo' : 'Video'} by ${item.photographer || 'Unknown'}`}
            </p>
            <p className="text-xs text-gray-500 mb-2">
              {item.width} × {item.height}
              {item.photographer && ` • by ${item.photographer}`}
            </p>
            {item.tags && item.tags.length > 0 && (
              <div className="flex flex-wrap gap-1">
                {item.tags.slice(0, 2).map((tag, index) => (
                  <Badge key={index} variant="secondary" className="text-xs">
                    {tag}
                  </Badge>
                ))}
                {item.tags.length > 2 && (
                  <Badge variant="secondary" className="text-xs">
                    +{item.tags.length - 2}
                  </Badge>
                )}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Search and Filters */}
      <div className="space-y-4">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder="Search for photos and videos..."
            value={searchQuery}
            onChange={(e) => handleSearch(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* Category Suggestions */}
        {categories && !searchQuery && (
          <div className="space-y-2">
            <p className="text-sm font-medium text-gray-700">Popular Categories</p>
            <div className="flex flex-wrap gap-2">
              {(categories[activeTab === 'photo' ? 'photos' : 'videos'] || []).slice(0, 8).map((category: string) => (
                <Button
                  key={category}
                  variant="outline"
                  size="sm"
                  onClick={() => handleCategorySelect(category)}
                  className="text-xs"
                >
                  {category}
                </Button>
              ))}
            </div>
          </div>
        )}

        {/* Controls */}
        <div className="flex items-center justify-between">
          <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'photo' | 'video')}>
            <TabsList>
              {(type === 'both' || type === 'photo') && (
                <TabsTrigger value="photo">
                  <Image className="w-4 h-4 mr-2" />
                  Photos
                </TabsTrigger>
              )}
              {(type === 'both' || type === 'video') && (
                <TabsTrigger value="video">
                  <Video className="w-4 h-4 mr-2" />
                  Videos
                </TabsTrigger>
              )}
            </TabsList>
          </Tabs>

          <div className="flex items-center space-x-2">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('grid')}
            >
              <Grid className="w-4 h-4" />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('list')}
            >
              <List className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Results */}
      <div className="space-y-4">
        {getCurrentLoading() && (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        )}

        {!getCurrentLoading() && getCurrentItems().length === 0 && searchQuery && (
          <div className="text-center py-12">
            <p className="text-gray-500">No results found for "{searchQuery}"</p>
            <p className="text-sm text-gray-400 mt-1">Try different keywords or browse categories</p>
          </div>
        )}

        {getCurrentItems().length > 0 && (
          <>
            <div className="flex items-center justify-between">
              <p className="text-sm text-gray-600">
                {searchQuery ? `Results for "${searchQuery}"` : `Trending ${activeTab === 'photo' ? 'Photos' : 'Videos'}`}
              </p>
              <p className="text-sm text-gray-500">
                {getCurrentResults()?.total_results 
                  ? `${getCurrentResults().total_results.toLocaleString()} total`
                  : `${getCurrentItems().length} items`
                }
              </p>
            </div>

            {viewMode === 'grid' ? (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                {getCurrentItems().map(renderMediaItem)}
              </div>
            ) : (
              <div className="space-y-2">
                {getCurrentItems().map(renderMediaItem)}
              </div>
            )}

            {/* Load More */}
            {searchQuery && getCurrentResults()?.total_results > getCurrentItems().length && (
              <div className="text-center">
                <Button
                  variant="outline"
                  onClick={() => setPage(prev => prev + 1)}
                  disabled={getCurrentLoading()}
                >
                  Load More
                </Button>
              </div>
            )}
          </>
        )}

        {(photoError || videoError) && (
          <div className="text-center py-12">
            <p className="text-red-500">Failed to load media</p>
            <p className="text-sm text-gray-400 mt-1">Please try again later</p>
          </div>
        )}
      </div>
    </div>
  );
}