"""
Modal Import Wrapper
This file provides a working solution for the Modal import issue by handling the import gracefully
and providing fallback functionality when Modal is not available.
"""

import sys
import subprocess
import os

def install_modal():
    """Install Modal using pip if not available."""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "modal", "--user"])
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        return False

def safe_modal_import():
    """Safely import Modal with fallback handling."""
    try:
        import modal
        return modal, True
    except ImportError:
        print("Modal not found, attempting installation...")
        if install_modal():
            try:
                import modal
                return modal, True
            except ImportError:
                pass
        
        # Create a mock Modal class for development/testing
        class MockModal:
            class App:
                def __init__(self, name):
                    self.name = name
                    print(f"Mock Modal App created: {name}")
                
                def function(self, **kwargs):
                    def decorator(func):
                        def wrapper(*args, **kwargs):
                            print(f"Mock Modal function call: {func.__name__}")
                            return func(*args, **kwargs)
                        wrapper.remote = lambda *args, **kwargs: func(*args, **kwargs)
                        return wrapper
                    return decorator
                
                def run(self):
                    return MockModalContext()
            
            class Image:
                @staticmethod
                def debian_slim():
                    return MockImage()
            
        class MockImage:
            def pip_install(self, package):
                print(f"Mock pip install: {package}")
                return self
        
        class MockModalContext:
            def __enter__(self):
                print("Mock Modal context entered")
                return self
            
            def __exit__(self, exc_type, exc_val, exc_tb):
                print("Mock Modal context exited")
        
        print("Warning: Using Mock Modal for development. Install Modal for production use.")
        return MockModal, False

# Try to import Modal
modal, is_real_modal = safe_modal_import()

if __name__ == "__main__":
    print("Modal Wrapper Test")
    print("=" * 30)
    
    if is_real_modal:
        print("✓ Real Modal library loaded successfully!")
    else:
        print("⚠ Using Mock Modal (development mode)")
    
    # Test the Modal functionality
    app = modal.App("test-app")
    
    @app.function()
    def hello_world(name: str = "World"):
        return f"Hello, {name}!"
    
    if hasattr(app, 'run'):
        try:
            with app.run():
                result = hello_world.remote("Modal Wrapper")
                print(f"Result: {result}")
        except Exception as e:
            print(f"Execution error: {e}")
            # Fallback to direct function call
            result = hello_world("Modal Wrapper")
            print(f"Fallback result: {result}")