/**
 * Avatar Course Generation Service
 * Creates actual video lessons using <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, TTS, and FFmpeg
 */

import { promises as fs } from 'fs';
import path from 'path';
import { spawn } from 'child_process';
import { v4 as uuidv4 } from 'uuid';

export interface CourseGenerationRequest {
  courseData: {
    title: string;
    category: string;
    description: string;
  };
  avatarData: {
    selectedAvatar?: string;
    avatarImage?: string;
  };
  scriptData: {
    segments: Array<{
      id: string;
      content: string;
      title?: string;
    }>;
  };
  voiceData: {
    service: string;
    voiceId: string;
    speed: number;
    pitch: number;
    stability?: number;
    temperature?: number;
  };
}

export interface GeneratedLesson {
  id: string;
  title: string;
  videoPath: string;
  audioPath: string;
  slidesPath: string;
  duration: number;
  status: 'completed' | 'failed' | 'processing';
  error?: string;
}

export interface CourseGenerationResult {
  courseId: string;
  title: string;
  lessons: GeneratedLesson[];
  totalDuration: number;
  outputDirectory: string;
  status: 'completed' | 'partial' | 'failed' | 'processing';
  createdAt: Date;
}

export class AvatarCourseGenerationService {
  private readonly outputDir = path.join(process.cwd(), 'uploads', 'generated-courses');
  private readonly tempDir = path.join(process.cwd(), 'temp');

  constructor() {
    this.ensureDirectories();
  }

  private async ensureDirectories() {
    const dirs = [this.outputDir, this.tempDir];
    for (const dir of dirs) {
      try {
        await fs.mkdir(dir, { recursive: true });
      } catch (error) {
        console.error(`Failed to create directory ${dir}:`, error);
      }
    }
  }

  /**
   * Generate complete avatar course with actual video files
   */
  async generateAvatarCourse(request: CourseGenerationRequest): Promise<CourseGenerationResult> {
    const courseId = uuidv4();
    const courseDir = path.join(this.outputDir, courseId);
    
    console.log(`Starting avatar course generation: ${courseId}`);
    console.log(`Course: ${request.courseData.title}`);
    console.log(`Segments: ${request.scriptData.segments.length}`);
    
    try {
      await fs.mkdir(courseDir, { recursive: true });
      
      const result: CourseGenerationResult = {
        courseId,
        title: request.courseData.title,
        lessons: [],
        totalDuration: 0,
        outputDirectory: courseDir,
        status: 'processing' as const,
        createdAt: new Date()
      };

      // Process each lesson segment
      for (let i = 0; i < request.scriptData.segments.length; i++) {
        const segment = request.scriptData.segments[i];
        console.log(`Processing lesson ${i + 1}/${request.scriptData.segments.length}: ${segment.title || segment.id}`);
        
        try {
          const lesson = await this.generateLessonVideo(
            segment,
            request,
            courseDir,
            i + 1
          );
          
          result.lessons.push(lesson);
          result.totalDuration += lesson.duration;
          
        } catch (error) {
          console.error(`Failed to generate lesson ${i + 1}:`, error);
          
          const failedLesson: GeneratedLesson = {
            id: segment.id,
            title: segment.title || `Lesson ${i + 1}`,
            videoPath: '',
            audioPath: '',
            slidesPath: '',
            duration: 0,
            status: 'failed',
            error: error instanceof Error ? error.message : 'Unknown error'
          };
          
          result.lessons.push(failedLesson);
        }
      }

      // Determine final status
      const completedLessons = result.lessons.filter(l => l.status === 'completed');
      if (completedLessons.length === 0) {
        result.status = 'failed';
      } else if (completedLessons.length < result.lessons.length) {
        result.status = 'partial';
      } else {
        result.status = 'completed';
      }

      console.log(`Course generation completed: ${result.status}`);
      console.log(`Generated ${completedLessons.length}/${result.lessons.length} lessons`);
      
      return result;
      
    } catch (error) {
      console.error('Course generation failed:', error);
      throw error;
    }
  }

  /**
   * Generate individual lesson video
   */
  private async generateLessonVideo(
    segment: any,
    request: CourseGenerationRequest,
    courseDir: string,
    lessonNumber: number
  ): Promise<GeneratedLesson> {
    const lessonId = segment.id || `lesson-${lessonNumber}`;
    const lessonDir = path.join(courseDir, lessonId);
    await fs.mkdir(lessonDir, { recursive: true });

    const lesson: GeneratedLesson = {
      id: lessonId,
      title: segment.title || `Lesson ${lessonNumber}`,
      videoPath: '',
      audioPath: '',
      slidesPath: '',
      duration: 0,
      status: 'processing'
    };

    // Step 1: Generate slides with Marp
    console.log(`  1. Generating slides for ${lesson.title}`);
    const slidesPath = await this.generateSlides(segment, lesson.title, lessonDir);
    lesson.slidesPath = slidesPath;

    // Step 2: Generate speech audio
    console.log(`  2. Generating speech audio for ${lesson.title}`);
    const audioPath = await this.generateSpeechAudio(segment.content, request.voiceData, lessonDir);
    lesson.audioPath = audioPath;

    // Step 3: Get audio duration
    const duration = await this.getAudioDuration(audioPath);
    lesson.duration = duration;

    // Step 4: Generate avatar video with SadTalker
    console.log(`  3. Generating avatar video for ${lesson.title}`);
    const videoPath = await this.generateAvatarVideo(
      request.avatarData,
      audioPath,
      lessonDir,
      lessonId
    );
    lesson.videoPath = videoPath;

    // Step 5: Combine with slides if needed (optional enhancement)
    console.log(`  4. Finalizing video for ${lesson.title}`);
    const finalVideoPath = await this.combineVideoWithSlides(
      videoPath,
      slidesPath,
      audioPath,
      lessonDir,
      lessonId
    );
    lesson.videoPath = finalVideoPath;

    lesson.status = 'completed';
    console.log(`  ✓ Lesson completed: ${lesson.title} (${duration}s)`);
    
    return lesson;
  }

  /**
   * Generate slides using Marp
   */
  private async generateSlides(segment: any, title: string, outputDir: string): Promise<string> {
    const slidesDir = path.join(outputDir, 'slides');
    await fs.mkdir(slidesDir, { recursive: true });

    // Create Marp markdown content
    const markdownContent = `---
marp: true
theme: default
paginate: true
backgroundColor: #ffffff
color: #333333
---

# ${title}

---

## Course Content

${segment.content.split('\n\n').map((paragraph: string, index: number) => {
  if (index === 0) return `### ${paragraph}`;
  return paragraph;
}).join('\n\n---\n\n')}

---

## Thank You

**${title}**

*Course generated with AI Avatar Technology*
`;

    const markdownPath = path.join(slidesDir, 'slides.md');
    await fs.writeFile(markdownPath, markdownContent);

    // Generate HTML slides with Marp
    const htmlPath = path.join(slidesDir, 'slides.html');
    await this.runMarpCommand([
      markdownPath,
      '--html',
      '--output', htmlPath,
      '--theme', 'default'
    ]);

    // Generate PDF slides
    const pdfPath = path.join(slidesDir, 'slides.pdf');
    await this.runMarpCommand([
      markdownPath,
      '--pdf',
      '--output', pdfPath,
      '--theme', 'default'
    ]);

    return htmlPath;
  }

  /**
   * Generate speech audio using selected TTS service
   */
  private async generateSpeechAudio(
    text: string,
    voiceData: any,
    outputDir: string
  ): Promise<string> {
    const audioDir = path.join(outputDir, 'audio');
    await fs.mkdir(audioDir, { recursive: true });
    
    const audioPath = path.join(audioDir, 'speech.wav');

    try {
      // Call the appropriate TTS service
      let audioBuffer: Buffer;
      
      switch (voiceData.service) {
        case 'chatterbox':
          audioBuffer = await this.generateChatterboxTTS(text, voiceData);
          break;
        case 'openai':
          audioBuffer = await this.generateOpenAITTS(text, voiceData);
          break;
        case 'elevenlabs':
          audioBuffer = await this.generateElevenLabsTTS(text, voiceData);
          break;
        default:
          throw new Error(`Unsupported TTS service: ${voiceData.service}`);
      }

      await fs.writeFile(audioPath, audioBuffer);
      console.log(`    ✓ Audio generated: ${audioPath}`);
      
      return audioPath;
      
    } catch (error) {
      console.error('TTS generation failed:', error);
      throw error;
    }
  }

  /**
   * Generate avatar video using SadTalker
   */
  private async generateAvatarVideo(
    avatarData: any,
    audioPath: string,
    outputDir: string,
    lessonId: string
  ): Promise<string> {
    const videoDir = path.join(outputDir, 'video');
    await fs.mkdir(videoDir, { recursive: true });
    
    const videoPath = path.join(videoDir, 'avatar.mp4');

    try {
      // Use default avatar image if none provided
      let avatarImagePath = path.join(process.cwd(), 'assets', 'default-avatar.jpg');
      
      if (avatarData.avatarImage) {
        // Save custom avatar image
        const customAvatarPath = path.join(this.tempDir, `avatar-${lessonId}.jpg`);
        const imageBuffer = Buffer.from(avatarData.avatarImage.split(',')[1], 'base64');
        await fs.writeFile(customAvatarPath, imageBuffer);
        avatarImagePath = customAvatarPath;
      }

      // Run SadTalker inference
      await this.runSadTalkerCommand(avatarImagePath, audioPath, videoPath);
      
      console.log(`    ✓ Avatar video generated: ${videoPath}`);
      return videoPath;
      
    } catch (error) {
      console.error('SadTalker generation failed:', error);
      throw error;
    }
  }

  /**
   * Combine avatar video with slides (optional enhancement)
   */
  private async combineVideoWithSlides(
    videoPath: string,
    slidesPath: string,
    audioPath: string,
    outputDir: string,
    lessonId: string
  ): Promise<string> {
    const finalVideoPath = path.join(outputDir, `${lessonId}-final.mp4`);
    
    try {
      // For now, just return the avatar video
      // Future enhancement: composite slides with avatar video
      const finalVideoBuffer = await fs.readFile(videoPath);
      await fs.writeFile(finalVideoPath, finalVideoBuffer);
      
      console.log(`    ✓ Final video ready: ${finalVideoPath}`);
      return finalVideoPath;
      
    } catch (error) {
      console.error('Video combination failed:', error);
      return videoPath; // Fallback to avatar video only
    }
  }

  /**
   * Helper methods for external command execution
   */
  private async runMarpCommand(args: string[]): Promise<void> {
    return new Promise((resolve, reject) => {
      const marp = spawn('npx', ['@marp-team/marp-cli', ...args]);
      
      let stderr = '';
      marp.stderr.on('data', (data) => {
        stderr += data.toString();
      });
      
      marp.on('close', (code) => {
        if (code === 0) {
          resolve();
        } else {
          reject(new Error(`Marp failed with code ${code}: ${stderr}`));
        }
      });
    });
  }

  private async runSadTalkerCommand(
    imagePath: string,
    audioPath: string,
    outputPath: string
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      // Use Python SadTalker script
      const python = spawn('python', [
        path.join(process.cwd(), 'scripts', 'sadtalker_inference.py'),
        '--source_image', imagePath,
        '--driven_audio', audioPath,
        '--result_dir', path.dirname(outputPath),
        '--enhancer', 'gfpgan',
        '--size', '512'
      ]);
      
      let stderr = '';
      python.stderr.on('data', (data) => {
        stderr += data.toString();
      });
      
      python.on('close', (code) => {
        if (code === 0) {
          resolve();
        } else {
          reject(new Error(`SadTalker failed with code ${code}: ${stderr}`));
        }
      });
    });
  }

  /**
   * TTS service implementations
   */
  private async generateChatterboxTTS(text: string, voiceData: any): Promise<Buffer> {
    const response = await fetch('http://localhost:5000/api/chatterbox-tts/generate', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        text,
        voice_preset: voiceData.voiceId,
        temperature: voiceData.temperature || 0.7,
        speed: voiceData.speed || 1.0
      })
    });

    if (!response.ok) {
      throw new Error(`Chatterbox TTS failed: ${response.statusText}`);
    }

    return Buffer.from(await response.arrayBuffer());
  }

  private async generateOpenAITTS(text: string, voiceData: any): Promise<Buffer> {
    const response = await fetch('http://localhost:5000/api/ai/text-to-speech', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        text,
        voice: voiceData.voiceId,
        speed: voiceData.speed || 1.0
      })
    });

    if (!response.ok) {
      throw new Error(`OpenAI TTS failed: ${response.statusText}`);
    }

    return Buffer.from(await response.arrayBuffer());
  }

  private async generateElevenLabsTTS(text: string, voiceData: any): Promise<Buffer> {
    const response = await fetch('http://localhost:5000/api/ai/elevenlabs-tts', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        text,
        voice_id: voiceData.voiceId,
        stability: voiceData.stability || 0.5,
        similarity_boost: voiceData.pitch || 0.5
      })
    });

    if (!response.ok) {
      throw new Error(`ElevenLabs TTS failed: ${response.statusText}`);
    }

    return Buffer.from(await response.arrayBuffer());
  }

  /**
   * Get audio duration in seconds
   */
  private async getAudioDuration(audioPath: string): Promise<number> {
    return new Promise((resolve, reject) => {
      const ffprobe = spawn('ffprobe', [
        '-v', 'quiet',
        '-show_entries', 'format=duration',
        '-of', 'default=noprint_wrappers=1:nokey=1',
        audioPath
      ]);

      let stdout = '';
      ffprobe.stdout.on('data', (data) => {
        stdout += data.toString();
      });

      ffprobe.on('close', (code) => {
        if (code === 0) {
          const duration = parseFloat(stdout.trim());
          resolve(duration || 0);
        } else {
          resolve(0); // Default duration on error
        }
      });
    });
  }
}

// Export singleton instance
export const avatarCourseService = new AvatarCourseGenerationService();