#!/usr/bin/env python3
"""
Modal A100 80G GPU Setup Script for Course AI Platform
Comprehensive setup for high-performance GPU computing with Modal

This script configures Modal with A100 80G GPU for:
- Large language model inference and training
- High-quality text-to-speech synthesis (Bark, <PERSON><PERSON> TTS)
- Image/video generation and processing
- Scientific computing and data analysis
- Multi-modal AI applications

Requirements:
- Modal account and API token
- Verified payment method for GPU usage
- Python 3.8+ environment
"""

import os
import sys
import json
import subprocess
import time
from typing import Dict, List, Optional, Any

# Modal GPU configurations available
GPU_CONFIGS = {
    "A100_80G": {
        "gpu": "A100:1",
        "memory": "80GB",
        "description": "NVIDIA A100 80GB - Premium GPU for large model training/inference",
        "hourly_cost": "$2.50-4.00",
        "use_cases": ["Large LLM inference", "Model training", "High-quality TTS", "Video processing"]
    },
    "A100_40G": {
        "gpu": "A100:1", 
        "memory": "40GB",
        "description": "NVIDIA A100 40GB - High-performance GPU for most AI workloads",
        "hourly_cost": "$1.50-2.50",
        "use_cases": ["LLM inference", "Image generation", "Standard TTS", "Data processing"]
    },
    "T4": {
        "gpu": "T4:1",
        "memory": "16GB", 
        "description": "NVIDIA T4 - Cost-effective GPU for lighter workloads",
        "hourly_cost": "$0.20-0.60",
        "use_cases": ["Small model inference", "Basic image processing", "Development testing"]
    }
}

def print_header():
    """Print setup header with information"""
    print("=" * 70)
    print("Modal A100 GPU Setup for Course AI Platform")
    print("=" * 70)
    print()
    print("This script will configure Modal with A100 GPU access for:")
    print("✓ High-performance AI model inference and training")
    print("✓ Advanced text-to-speech synthesis (Bark, Coqui TTS)")
    print("✓ Image and video generation/processing")
    print("✓ Scientific computing and data analysis")
    print("✓ Multi-modal AI applications")
    print()

def check_modal_installation():
    """Check if Modal is installed and working"""
    print("🔍 Checking Modal installation...")
    
    try:
        import modal
        print(f"✓ Modal version {modal.__version__} found")
        return True
    except ImportError:
        print("❌ Modal not found. Installing...")
        return install_modal()

def install_modal():
    """Install Modal with proper dependencies"""
    try:
        print("📦 Installing Modal...")
        subprocess.run([
            sys.executable, "-m", "pip", "install", 
            "modal-client", "--upgrade"
        ], check=True, capture_output=True)
        
        # Verify installation
        import modal
        print(f"✓ Modal {modal.__version__} installed successfully")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install Modal: {e}")
        return False
    except ImportError:
        print("❌ Modal installation verification failed")
        return False

def check_modal_token():
    """Check if Modal token is configured"""
    print("🔐 Checking Modal authentication...")
    
    # Check environment variable
    token = os.getenv('MODAL_TOKEN_ID') and os.getenv('MODAL_TOKEN_SECRET')
    if token:
        print("✓ Modal token found in environment variables")
        return True
    
    # Check if user is authenticated via CLI
    try:
        result = subprocess.run([
            "modal", "token", "verify"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ Modal CLI authentication verified")
            return True
        else:
            print("❌ Modal authentication required")
            return False
            
    except FileNotFoundError:
        print("❌ Modal CLI not found")
        return False

def setup_modal_authentication():
    """Guide user through Modal authentication setup"""
    print()
    print("🔑 Modal Authentication Setup Required")
    print("=" * 50)
    print()
    print("To use Modal with A100 GPU, you need:")
    print("1. Modal account (sign up at https://modal.com)")
    print("2. Verified payment method for GPU billing")
    print("3. API token for authentication")
    print()
    
    print("Setup options:")
    print("1. Browser authentication (recommended)")
    print("2. Environment variables")
    print()
    
    choice = input("Choose setup method (1 or 2): ").strip()
    
    if choice == "1":
        return setup_browser_auth()
    elif choice == "2":
        return setup_env_auth()
    else:
        print("❌ Invalid choice")
        return False

def setup_browser_auth():
    """Setup Modal via browser authentication"""
    print()
    print("🌐 Setting up browser authentication...")
    print("This will open your browser to authenticate with Modal")
    print()
    
    try:
        subprocess.run(["modal", "token", "new"], check=True)
        print("✓ Browser authentication completed")
        return True
    except subprocess.CalledProcessError:
        print("❌ Browser authentication failed")
        return False
    except FileNotFoundError:
        print("❌ Modal CLI not available")
        return setup_env_auth()

def setup_env_auth():
    """Setup Modal via environment variables"""
    print()
    print("🔧 Environment Variable Setup")
    print("=" * 40)
    print()
    print("You need to get your API credentials from:")
    print("https://modal.com/settings/tokens")
    print()
    
    token_id = input("Enter your MODAL_TOKEN_ID: ").strip()
    token_secret = input("Enter your MODAL_TOKEN_SECRET: ").strip()
    
    if not token_id or not token_secret:
        print("❌ Both token ID and secret are required")
        return False
    
    # Set environment variables
    os.environ['MODAL_TOKEN_ID'] = token_id
    os.environ['MODAL_TOKEN_SECRET'] = token_secret
    
    # Verify authentication
    try:
        import modal
        # Test authentication by listing apps
        modal.App.list()
        print("✓ Authentication successful")
        return True
    except Exception as e:
        print(f"❌ Authentication failed: {e}")
        return False

def display_gpu_options():
    """Display available GPU configurations"""
    print()
    print("🚀 Available GPU Configurations")
    print("=" * 50)
    
    for i, (key, config) in enumerate(GPU_CONFIGS.items(), 1):
        print(f"{i}. {config['description']}")
        print(f"   Memory: {config['memory']}")
        print(f"   Cost: {config['hourly_cost']}/hour")
        print(f"   Best for: {', '.join(config['use_cases'])}")
        print()

def create_modal_app_template(gpu_type: str = "A100_80G"):
    """Create Modal app template with A100 GPU configuration"""
    
    gpu_config = GPU_CONFIGS[gpu_type]
    
    template_code = f'''"""
Modal A100 GPU Application Template
Configured for high-performance AI workloads on Course AI Platform
"""

import modal
import os

# Create Modal app
app = modal.App("courseai-a100-gpu")

# Configure A100 GPU image with common ML libraries
gpu_image = (
    modal.Image.debian_slim(python_version="3.11")
    .pip_install([
        # Core ML libraries
        "torch>=2.0.0",
        "transformers>=4.30.0", 
        "accelerate>=0.20.0",
        "datasets>=2.10.0",
        "tokenizers>=0.13.0",
        
        # Audio processing for TTS
        "librosa>=0.10.0",
        "soundfile>=0.12.0",
        "scipy>=1.10.0",
        "numpy>=1.24.0",
        
        # High-quality TTS engines
        "bark-ml>=0.0.1",
        "coqui-tts>=0.13.0",
        "espeak-ng",
        
        # Image/video processing
        "pillow>=9.5.0",
        "opencv-python>=4.7.0",
        "imageio>=2.28.0",
        
        # API and utilities
        "fastapi>=0.95.0",
        "uvicorn>=0.21.0",
        "pydantic>=1.10.0",
        "httpx>=0.24.0",
        "python-multipart>=0.0.6",
    ])
    .apt_install([
        "ffmpeg",
        "espeak-ng", 
        "espeak-ng-data",
        "git-lfs",
        "wget",
        "curl"
    ])
    .run_commands([
        "git lfs install",
    ])
)

# A100 GPU configuration
GPU_CONFIG = "{gpu_config['gpu']}"

@app.function(
    image=gpu_image,
    gpu=GPU_CONFIG,
    timeout=3600,  # 1 hour timeout
    memory=32768,  # 32GB RAM
    cpu=8,         # 8 CPU cores
)
def high_quality_tts(
    text: str,
    voice_preset: str = "v2/en_speaker_6",
    temperature: float = 0.7,
    silence_duration: float = 0.25
):
    """
    Generate high-quality speech using Bark TTS on A100 GPU
    
    Args:
        text: Text to convert to speech
        voice_preset: Voice model to use
        temperature: Generation temperature (0.0-1.0)
        silence_duration: Silence between sentences
    
    Returns:
        Audio data as bytes
    """
    from bark import SAMPLE_RATE, generate_audio, preload_models
    import numpy as np
    import io
    import soundfile as sf
    
    # Preload models on GPU
    preload_models()
    
    # Generate audio
    audio_array = generate_audio(
        text,
        history_prompt=voice_preset,
        text_temp=temperature,
        waveform_temp=0.7,
        silent=True
    )
    
    # Convert to bytes
    buffer = io.BytesIO()
    sf.write(buffer, audio_array, SAMPLE_RATE, format='WAV')
    return buffer.getvalue()

@app.function(
    image=gpu_image,
    gpu=GPU_CONFIG,
    timeout=1800,  # 30 minutes
    memory=16384,  # 16GB RAM
    cpu=4,
)
def batch_tts_generation(
    lesson_texts: list,
    voice_preset: str = "v2/en_speaker_6"
):
    """
    Generate TTS for multiple lessons in batch
    Optimized for course content generation
    """
    from bark import SAMPLE_RATE, generate_audio, preload_models
    import base64
    import io
    import soundfile as sf
    
    # Preload models once for batch processing
    preload_models()
    
    results = []
    
    for lesson in lesson_texts:
        title = lesson.get("title", "Untitled")
        text = lesson.get("text", "")
        
        if not text:
            continue
            
        # Generate audio
        audio_array = generate_audio(
            text,
            history_prompt=voice_preset,
            text_temp=0.7,
            waveform_temp=0.7,
            silent=True
        )
        
        # Convert to base64
        buffer = io.BytesIO()
        sf.write(buffer, audio_array, SAMPLE_RATE, format='WAV')
        audio_b64 = base64.b64encode(buffer.getvalue()).decode()
        
        results.append({{
            "title": title,
            "audioData": audio_b64,
            "format": "wav",
            "sampleRate": SAMPLE_RATE,
            "durationSeconds": len(audio_array) / SAMPLE_RATE,
            "sizeBytes": len(buffer.getvalue())
        }})
    
    return results

@app.function(
    image=gpu_image,
    gpu=GPU_CONFIG,
    timeout=3600,
    memory=24576,  # 24GB RAM
    cpu=6,
)
def large_model_inference(
    model_name: str,
    prompt: str,
    max_tokens: int = 512,
    temperature: float = 0.7
):
    """
    Run large language model inference on A100 GPU
    Supports models up to 70B parameters
    """
    import torch
    from transformers import AutoTokenizer, AutoModelForCausalLM
    
    # Load model and tokenizer
    tokenizer = AutoTokenizer.from_pretrained(model_name)
    model = AutoModelForCausalLM.from_pretrained(
        model_name,
        torch_dtype=torch.float16,
        device_map="auto",
        trust_remote_code=True
    )
    
    # Tokenize input
    inputs = tokenizer(prompt, return_tensors="pt").to(model.device)
    
    # Generate response
    with torch.no_grad():
        outputs = model.generate(
            inputs.input_ids,
            max_length=inputs.input_ids.shape[1] + max_tokens,
            temperature=temperature,
            do_sample=True,
            pad_token_id=tokenizer.eos_token_id
        )
    
    # Decode response
    response = tokenizer.decode(
        outputs[0][inputs.input_ids.shape[1]:], 
        skip_special_tokens=True
    )
    
    return response

@app.function(
    image=gpu_image,
    gpu=GPU_CONFIG,
    timeout=1800,
    memory=16384,
    cpu=4,
)
def image_generation(
    prompt: str,
    width: int = 512,
    height: int = 512,
    num_inference_steps: int = 50
):
    """
    Generate images using Stable Diffusion on A100 GPU
    """
    import torch
    from diffusers import StableDiffusionPipeline
    import base64
    import io
    
    # Load pipeline
    pipe = StableDiffusionPipeline.from_pretrained(
        "runwayml/stable-diffusion-v1-5",
        torch_dtype=torch.float16
    ).to("cuda")
    
    # Generate image
    image = pipe(
        prompt,
        width=width,
        height=height,
        num_inference_steps=num_inference_steps
    ).images[0]
    
    # Convert to base64
    buffer = io.BytesIO()
    image.save(buffer, format="PNG")
    image_b64 = base64.b64encode(buffer.getvalue()).decode()
    
    return image_b64

# Health check function
@app.function(image=gpu_image)
def health_check():
    """Check if GPU is available and working"""
    import torch
    
    return {{
        "gpu_available": torch.cuda.is_available(),
        "gpu_count": torch.cuda.device_count(),
        "gpu_name": torch.cuda.get_device_name(0) if torch.cuda.is_available() else None,
        "cuda_version": torch.version.cuda,
        "pytorch_version": torch.__version__
    }}

# Entry point for local testing
if __name__ == "__main__":
    with app.run():
        # Test GPU availability
        print("Testing A100 GPU setup...")
        result = health_check.remote()
        print(f"GPU Status: {{result}}")
        
        # Test TTS generation
        print("Testing TTS generation...")
        audio_data = high_quality_tts.remote(
            "Welcome to Course AI Platform with A100 GPU acceleration!"
        )
        print(f"Generated {{len(audio_data)}} bytes of audio")
'''
    
    return template_code

def save_modal_app(gpu_type: str = "A100_80G"):
    """Save Modal app template to file"""
    template_code = create_modal_app_template(gpu_type)
    
    filename = f"modal_a100_app.py"
    with open(filename, 'w') as f:
        f.write(template_code)
    
    print(f"✓ Modal app template saved as '{filename}'")
    return filename

def create_deployment_script():
    """Create deployment and testing script"""
    
    script_content = '''#!/usr/bin/env python3
"""
Modal A100 GPU Deployment and Testing Script
"""

import subprocess
import sys
import json

def deploy_modal_app():
    """Deploy the Modal app"""
    print("🚀 Deploying Modal A100 GPU app...")
    
    try:
        result = subprocess.run([
            "modal", "deploy", "modal_a100_app.py"
        ], check=True, capture_output=True, text=True)
        
        print("✓ Deployment successful!")
        print(result.stdout)
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Deployment failed: {e}")
        print(f"Error output: {e.stderr}")
        return False

def test_gpu_functions():
    """Test GPU functions"""
    print("🧪 Testing A100 GPU functions...")
    
    try:
        # Test health check
        print("Testing GPU health check...")
        result = subprocess.run([
            "modal", "run", "modal_a100_app.py::health_check"
        ], capture_output=True, text=True, check=True)
        
        gpu_status = json.loads(result.stdout.strip())
        print(f"GPU Status: {gpu_status}")
        
        if not gpu_status.get('gpu_available'):
            print("❌ GPU not available")
            return False
            
        print("✓ GPU health check passed")
        
        # Test TTS generation
        print("Testing TTS generation...")
        result = subprocess.run([
            "modal", "run", "modal_a100_app.py::high_quality_tts",
            "--text", "Hello from A100 GPU!"
        ], capture_output=True, check=True)
        
        print(f"✓ TTS generation successful ({len(result.stdout)} bytes)")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Testing failed: {e}")
        return False

def main():
    """Main deployment script"""
    print("Modal A100 GPU Deployment Script")
    print("=" * 40)
    
    # Deploy app
    if not deploy_modal_app():
        return False
    
    # Test functions
    if not test_gpu_functions():
        return False
    
    print("✅ A100 GPU setup completed successfully!")
    print()
    print("Next steps:")
    print("1. Monitor usage at https://modal.com/apps")
    print("2. Check billing at https://modal.com/settings/billing")
    print("3. Use the functions in your Course AI Platform")
    
    return True

if __name__ == "__main__":
    main()
'''
    
    with open("deploy_modal_a100.py", 'w') as f:
        f.write(script_content)
    
    # Make executable
    os.chmod("deploy_modal_a100.py", 0o755)
    
    print("✓ Deployment script saved as 'deploy_modal_a100.py'")

def create_integration_guide():
    """Create integration guide for Course AI Platform"""
    
    guide_content = '''# Modal A100 GPU Integration Guide

## Overview
This guide explains how to integrate Modal A100 GPU capabilities with your Course AI Platform.

## Available Functions

### 1. High-Quality TTS Generation
```python
# Generate speech for a single lesson
audio_data = high_quality_tts.remote(
    text="Your lesson content here",
    voice_preset="v2/en_speaker_6",
    temperature=0.7
)
```

### 2. Batch TTS Processing
```python
# Process multiple lessons at once
lessons = [
    {"title": "Lesson 1", "text": "Content 1"},
    {"title": "Lesson 2", "text": "Content 2"}
]
results = batch_tts_generation.remote(lessons)
```

### 3. Large Model Inference
```python
# Run large language models
response = large_model_inference.remote(
    model_name="microsoft/DialoGPT-large",
    prompt="Explain machine learning",
    max_tokens=512
)
```

### 4. Image Generation
```python
# Generate course thumbnails/images
image_b64 = image_generation.remote(
    prompt="Educational illustration about AI",
    width=512,
    height=512
)
```

## Integration with Course AI Platform

### Update chatterboxModalTTS.ts
Replace the execution method to use Modal:

```typescript
private async executeModalFunction(functionName: string, params: any): Promise<any> {
  const { spawn } = require('child_process');
  
  return new Promise((resolve, reject) => {
    const process = spawn('modal', [
      'run', 'modal_a100_app.py::' + functionName,
      '--params', JSON.stringify(params)
    ]);
    
    let output = '';
    process.stdout.on('data', (data) => output += data);
    process.on('close', (code) => {
      if (code === 0) {
        resolve(JSON.parse(output));
      } else {
        reject(new Error('Modal function failed'));
      }
    });
  });
}
```

## Cost Management

### GPU Usage Optimization
- Use batch processing for multiple operations
- Set appropriate timeouts to avoid runaway costs
- Monitor usage in Modal dashboard
- Consider using T4 GPUs for development/testing

### Billing Alerts
- Set up billing alerts in Modal dashboard
- Monitor monthly usage limits
- Use spot instances when available

## Security Best Practices

### API Keys Management
- Store Modal tokens as environment variables
- Never commit tokens to version control
- Use Replit Secrets for token storage
- Rotate tokens regularly

### Access Control
- Limit Modal app permissions
- Use separate tokens for development/production
- Monitor API usage logs

## Troubleshooting

### Common Issues
1. **GPU Out of Memory**: Reduce batch size or model size
2. **Timeout Errors**: Increase function timeout
3. **Authentication Failures**: Check Modal token validity
4. **High Costs**: Review usage patterns and optimize

### Performance Optimization
- Pre-load models once per batch
- Use appropriate GPU memory allocation
- Cache frequently used models
- Optimize batch sizes for your workload

## Next Steps
1. Deploy the Modal app: `python deploy_modal_a100.py`
2. Test GPU functions with sample data
3. Integrate with your Course AI Platform APIs
4. Monitor performance and costs
5. Scale usage based on demand
'''
    
    with open("MODAL_A100_INTEGRATION.md", 'w') as f:
        f.write(guide_content)
    
    print("✓ Integration guide saved as 'MODAL_A100_INTEGRATION.md'")

def main():
    """Main setup function"""
    print_header()
    
    # Check Modal installation
    if not check_modal_installation():
        print("❌ Modal installation failed")
        return False
    
    # Check authentication
    if not check_modal_token():
        if not setup_modal_authentication():
            print("❌ Authentication setup failed")
            return False
    
    # Display GPU options
    display_gpu_options()
    
    # Get user choice
    while True:
        try:
            choice = int(input("Select GPU configuration (1-3): "))
            if 1 <= choice <= 3:
                gpu_keys = list(GPU_CONFIGS.keys())
                selected_gpu = gpu_keys[choice - 1]
                break
            else:
                print("❌ Please enter 1, 2, or 3")
        except ValueError:
            print("❌ Please enter a valid number")
    
    selected_config = GPU_CONFIGS[selected_gpu]
    print(f"\\n✓ Selected: {selected_config['description']}")
    print(f"  Cost: {selected_config['hourly_cost']}/hour")
    
    # Confirm setup
    confirm = input("\\nProceed with setup? (y/N): ").lower()
    if confirm != 'y':
        print("Setup cancelled")
        return False
    
    print("\\n🔧 Creating Modal A100 GPU configuration...")
    
    # Create app template
    app_file = save_modal_app(selected_gpu)
    
    # Create deployment script
    create_deployment_script()
    
    # Create integration guide
    create_integration_guide()
    
    print("\\n✅ Modal A100 GPU setup completed!")
    print("=" * 50)
    print()
    print("Files created:")
    print(f"✓ {app_file} - Modal app with A100 GPU functions")
    print("✓ deploy_modal_a100.py - Deployment script")
    print("✓ MODAL_A100_INTEGRATION.md - Integration guide")
    print()
    print("Next steps:")
    print("1. Review the generated files")
    print("2. Run: python deploy_modal_a100.py")
    print("3. Test GPU functions")
    print("4. Integrate with Course AI Platform")
    print()
    print("⚠️  Important reminders:")
    print("- A100 GPU usage is billed by the minute")
    print("- Set up billing alerts in Modal dashboard")
    print("- Monitor usage to control costs")
    print("- Use spot instances when possible for cost savings")
    
    return True

if __name__ == "__main__":
    if not main():
        sys.exit(1)