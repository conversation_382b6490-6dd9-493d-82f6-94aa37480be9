import { createContext, useState, useContext, useEffect, ReactNode } from 'react';

// Define accessibility settings types
export type ColorContrastLevel = 'default' | 'enhanced' | 'high';
export type FontSize = 'default' | 'large' | 'x-large';
export type MotionReduction = 'default' | 'reduced';

export interface AccessibilitySettings {
  colorContrast: ColorContrastLevel;
  fontSize: FontSize;
  motionReduction: MotionReduction;
}

// Create context with default values
interface AccessibilityContextValue {
  settings: AccessibilitySettings;
  setColorContrast: (level: ColorContrastLevel) => void;
  setFontSize: (size: FontSize) => void;
  setMotionReduction: (motion: MotionReduction) => void;
  resetSettings: () => void;
}

const defaultSettings: AccessibilitySettings = {
  colorContrast: 'default',
  fontSize: 'default',
  motionReduction: 'default',
};

const AccessibilityContext = createContext<AccessibilityContextValue | undefined>(undefined);

export const useAccessibility = () => {
  const context = useContext(AccessibilityContext);
  if (!context) {
    throw new Error('useAccessibility must be used within an AccessibilityProvider');
  }
  return context;
};

interface AccessibilityProviderProps {
  children: ReactNode;
}

export function AccessibilityProvider({ children }: AccessibilityProviderProps) {
  const [settings, setSettings] = useState<AccessibilitySettings>(defaultSettings);
  
  // Load settings from localStorage on mount
  useEffect(() => {
    const savedSettings = localStorage.getItem('accessibility-settings');
    if (savedSettings) {
      try {
        const parsedSettings = JSON.parse(savedSettings);
        setSettings(parsedSettings);
      } catch (error) {
        console.error('Failed to parse saved accessibility settings:', error);
      }
    }
  }, []);
  
  // Save settings to localStorage whenever they change
  useEffect(() => {
    localStorage.setItem('accessibility-settings', JSON.stringify(settings));
    
    // Apply settings to the document/html element
    const htmlElement = document.documentElement;
    
    // Apply color contrast classes
    htmlElement.classList.remove('color-contrast-enhanced', 'color-contrast-high');
    if (settings.colorContrast === 'enhanced') {
      htmlElement.classList.add('color-contrast-enhanced');
    } else if (settings.colorContrast === 'high') {
      htmlElement.classList.add('color-contrast-high');
    }
    
    // Apply font size classes
    htmlElement.classList.remove('font-size-large', 'font-size-x-large');
    if (settings.fontSize === 'large') {
      htmlElement.classList.add('font-size-large');
    } else if (settings.fontSize === 'x-large') {
      htmlElement.classList.add('font-size-x-large');
    }
    
    // Apply motion reduction
    htmlElement.classList.remove('motion-reduced');
    if (settings.motionReduction === 'reduced') {
      htmlElement.classList.add('motion-reduced');
    }
  }, [settings]);
  
  const setColorContrast = (level: ColorContrastLevel) => {
    setSettings(prev => ({ ...prev, colorContrast: level }));
  };
  
  const setFontSize = (size: FontSize) => {
    setSettings(prev => ({ ...prev, fontSize: size }));
  };
  
  const setMotionReduction = (motion: MotionReduction) => {
    setSettings(prev => ({ ...prev, motionReduction: motion }));
  };
  
  const resetSettings = () => {
    setSettings(defaultSettings);
  };
  
  return (
    <AccessibilityContext.Provider
      value={{
        settings,
        setColorContrast,
        setFontSize,
        setMotionReduction,
        resetSettings,
      }}
    >
      {children}
    </AccessibilityContext.Provider>
  );
}