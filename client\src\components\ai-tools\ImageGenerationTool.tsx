import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';
import { Loader2, Download, RefreshCw } from 'lucide-react';
import { Slider } from '@/components/ui/slider';

interface ImageGenerationToolProps {
  onImageGenerated?: (url: string) => void;
  defaultPrompt?: string;
  defaultModel?: 'kandinsky' | 'wan';
  heading?: string;
  description?: string;
}

export function ImageGenerationTool({
  onImageGenerated,
  defaultPrompt = '',
  defaultModel = 'kandinsky',
  heading = 'Image Generation',
  description = 'Generate images using Kandinsky 2.2 or WAN 2.1 open-source models running on RunPod H100.'
}: ImageGenerationToolProps) {
  const { toast } = useToast();
  const [prompt, setPrompt] = useState(defaultPrompt);
  const [negativePrompt, setNegativePrompt] = useState('');
  const [model, setModel] = useState<'kandinsky' | 'wan'>(defaultModel);
  const [steps, setSteps] = useState(30);
  const [width, setWidth] = useState(512);
  const [height, setHeight] = useState(512);
  const [generating, setGenerating] = useState(false);
  const [generatedImage, setGeneratedImage] = useState<{ url: string; width: number; height: number; } | null>(null);
  
  const handleGenerate = async () => {
    if (!prompt.trim()) {
      toast({
        title: 'Prompt is required',
        description: 'Please enter a prompt for image generation',
        variant: 'destructive',
      });
      return;
    }
    
    setGenerating(true);
    setGeneratedImage(null);
    
    try {
      const response = await apiRequest(
        'POST', 
        '/api/ai-tools/image-generation', 
        {
          prompt,
          negativePrompt,
          model,
          steps,
          width,
          height
        }
      );
      
      const data = await response.json();
      
      if (data.url) {
        setGeneratedImage({
          url: data.url,
          width: data.width || 512,
          height: data.height || 512
        });
        
        if (onImageGenerated) {
          onImageGenerated(data.url);
        }
      } else {
        toast({
          title: 'Generation failed',
          description: data.message || 'Failed to generate image',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Image generation error:', error);
      toast({
        title: 'Error',
        description: 'Failed to connect to the image generation service',
        variant: 'destructive',
      });
    } finally {
      setGenerating(false);
    }
  };
  
  const handleDownload = () => {
    if (!generatedImage) return;
    
    // Create a temporary link element
    const link = document.createElement('a');
    link.href = generatedImage.url;
    link.download = `generated-image-${Date.now()}.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };
  
  const dimensionOptions = [
    { value: 256, label: '256px' },
    { value: 512, label: '512px' },
    { value: 768, label: '768px' },
    { value: 1024, label: '1024px' },
  ];
  
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>{heading}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <Label htmlFor="prompt">Prompt</Label>
          <Textarea
            id="prompt"
            placeholder="Describe the image you want to generate..."
            className="min-h-[100px]"
            value={prompt}
            onChange={(e) => setPrompt(e.target.value)}
            disabled={generating}
          />
        </div>
        
        <div>
          <Label htmlFor="negativePrompt">Negative Prompt (Optional)</Label>
          <Textarea
            id="negativePrompt"
            placeholder="Elements to avoid in the generated image..."
            className="min-h-[60px]"
            value={negativePrompt}
            onChange={(e) => setNegativePrompt(e.target.value)}
            disabled={generating}
          />
          <p className="text-xs text-muted-foreground mt-1">
            Specify elements you don't want in the image, e.g., "blurry, low quality, distorted faces"
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="model">Model</Label>
            <Select 
              value={model} 
              onValueChange={(value: 'kandinsky' | 'wan') => setModel(value)}
              disabled={generating}
            >
              <SelectTrigger id="model">
                <SelectValue placeholder="Select model" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="kandinsky">Kandinsky 2.2 (Photorealistic)</SelectItem>
                <SelectItem value="wan">WAN 2.1 (Anime/Stylized)</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="steps">Steps: {steps}</Label>
            <Slider
              id="steps"
              defaultValue={[steps]}
              min={10}
              max={50}
              step={1}
              onValueChange={(value) => setSteps(value[0])}
              disabled={generating}
            />
            <p className="text-xs text-muted-foreground">
              More steps = better quality but slower generation
            </p>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="width">Width</Label>
            <Select 
              value={width.toString()} 
              onValueChange={(value) => setWidth(parseInt(value))}
              disabled={generating}
            >
              <SelectTrigger id="width">
                <SelectValue placeholder="Select width" />
              </SelectTrigger>
              <SelectContent>
                {dimensionOptions.map((option) => (
                  <SelectItem key={`width-${option.value}`} value={option.value.toString()}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="height">Height</Label>
            <Select 
              value={height.toString()} 
              onValueChange={(value) => setHeight(parseInt(value))}
              disabled={generating}
            >
              <SelectTrigger id="height">
                <SelectValue placeholder="Select height" />
              </SelectTrigger>
              <SelectContent>
                {dimensionOptions.map((option) => (
                  <SelectItem key={`height-${option.value}`} value={option.value.toString()}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
        
        <Button
          className="w-full"
          disabled={generating || !prompt.trim()}
          onClick={handleGenerate}
        >
          {generating ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Generating...
            </>
          ) : (
            'Generate Image'
          )}
        </Button>
        
        {generatedImage && (
          <div className="pt-4">
            <div className="text-sm font-medium mb-2">Generated Image</div>
            <div className="bg-secondary rounded-md p-4 flex justify-center">
              <img 
                src={generatedImage.url} 
                alt="Generated image" 
                className="max-w-full rounded shadow-md" 
                style={{ maxHeight: '400px' }}
              />
            </div>
          </div>
        )}
      </CardContent>
      <CardFooter className="flex justify-between">
        {generatedImage && (
          <>
            <div className="text-xs text-muted-foreground">
              Model: {model}, Size: {generatedImage.width}x{generatedImage.height}
            </div>
            <div className="flex space-x-2">
              <Button variant="outline" size="sm" onClick={handleGenerate}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Regenerate
              </Button>
              <Button variant="outline" size="sm" onClick={handleDownload}>
                <Download className="h-4 w-4 mr-2" />
                Download
              </Button>
            </div>
          </>
        )}
      </CardFooter>
    </Card>
  );
}

export default ImageGenerationTool;