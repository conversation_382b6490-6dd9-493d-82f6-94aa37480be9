import React from "react";
import { motion } from "framer-motion";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Brain, Video, Headphones, PenTool, Share2, BarChart3, 
  Zap, Globe, Tv, Layers, Users, Lock, Settings, Palette, 
  Gauge, Rocket, Cloud, Sparkles, Play, CheckCircle2,
  ArrowRight, Star, MessageSquare, FileVideo, Mic
} from "lucide-react";
import { Link } from "wouter";

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { 
    opacity: 1, 
    y: 0,
    transition: { duration: 0.5 }
  }
};

export default function FeaturesPage() {
  const featureCategories = [
    {
      title: "AI-Powered Course Creation",
      description: "Transform ideas into professional courses with cutting-edge AI technology",
      gradient: "from-blue-600 to-purple-600",
      features: [
        {
          icon: <Brain className="h-8 w-8" />,
          title: "Intelligent Course Structure",
          description: "Generate complete course outlines with modules, lessons, and learning objectives from just a topic description.",
          badge: "NEW"
        },
        {
          icon: <Zap className="h-8 w-8" />,
          title: "AI Script Generation",
          description: "Create engaging, educational scripts that match your voice and teaching style automatically.",
          badge: "POPULAR"
        },
        {
          icon: <Palette className="h-8 w-8" />,
          title: "Content Enhancement",
          description: "Improve your content with AI-powered examples, analogies, and professional writing suggestions."
        }
      ]
    },
    {
      title: "Avatar Video Generation",
      description: "Create lifelike talking head videos with SadTalker AI technology",
      gradient: "from-purple-600 to-pink-600",
      features: [
        {
          icon: <FileVideo className="h-8 w-8" />,
          title: "SadTalker Avatar Videos",
          description: "Generate realistic talking head videos from static images using A100 GPU acceleration.",
          badge: "PREMIUM"
        },
        {
          icon: <Mic className="h-8 w-8" />,
          title: "Enterprise Voice Synthesis",
          description: "Chatterbox TTS with 10 premium voices, plus OpenAI and ElevenLabs integration.",
          badge: "ENTERPRISE"
        },
        {
          icon: <Video className="h-8 w-8" />,
          title: "Real Video Generation",
          description: "Create actual video files for each lesson with Marp slides, FFmpeg assembly, and professional output."
        }
      ]
    },
    {
      title: "Professional Media Production",
      description: "Generate high-quality multimedia content with professional tools",
      gradient: "from-pink-600 to-red-600",
      features: [
        {
          icon: <Tv className="h-8 w-8" />,
          title: "Marp Slide Generation",
          description: "Create beautiful presentation slides automatically from your course content using Marp."
        },
        {
          icon: <Headphones className="h-8 w-8" />,
          title: "Multi-Provider TTS",
          description: "Choose from Chatterbox TTS, OpenAI TTS, or ElevenLabs for the perfect voice for your content."
        },
        {
          icon: <Settings className="h-8 w-8" />,
          title: "Voice Customization",
          description: "Fine-tune voice speed, pitch, volume, and stability with advanced voice preview controls."
        }
      ]
    },
    {
      title: "Distribution & Analytics",
      description: "Share your courses and track performance across platforms",
      gradient: "from-green-600 to-blue-600",
      features: [
        {
          icon: <Share2 className="h-8 w-8" />,
          title: "Multi-Platform Publishing",
          description: "Distribute courses to multiple learning platforms with one-click publishing workflows."
        },
        {
          icon: <BarChart3 className="h-8 w-8" />,
          title: "Advanced Analytics",
          description: "Track learner engagement, completion rates, and performance with detailed analytics dashboards."
        },
        {
          icon: <Users className="h-8 w-8" />,
          title: "Team Collaboration",
          description: "Work with team members on course creation with real-time collaboration and review tools."
        }
      ]
    },
    {
      title: "Enterprise & Security",
      description: "Enterprise-grade features for professional course creators",
      gradient: "from-gray-600 to-blue-600",
      features: [
        {
          icon: <Lock className="h-8 w-8" />,
          title: "Advanced Security",
          description: "Enterprise-grade security with SSO, role-based access control, and data encryption."
        },
        {
          icon: <Cloud className="h-8 w-8" />,
          title: "Cloud Infrastructure",
          description: "Scalable cloud hosting with CDN distribution and automatic backups for your content."
        },
        {
          icon: <Rocket className="h-8 w-8" />,
          title: "API Integration",
          description: "Integrate with your existing tools and workflows using our comprehensive REST API."
        }
      ]
    }
  ];

  const stats = [
    { number: "10,000+", label: "Courses Created" },
    { number: "50+", label: "Languages Supported" },
    { number: "99.9%", label: "Uptime Guarantee" },
    { number: "24/7", label: "Support Available" }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Hero Section */}
      <motion.section 
        className="relative overflow-hidden pt-20 pb-16"
        initial="hidden"
        animate="visible"
        variants={containerVariants}
      >
        {/* Background Effects */}
        <div className="absolute inset-0">
          <div className="absolute top-0 left-1/4 w-96 h-96 bg-blue-400 rounded-full opacity-10 blur-3xl"></div>
          <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-purple-400 rounded-full opacity-10 blur-3xl"></div>
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div 
            className="text-center"
            variants={itemVariants}
          >
            <motion.div
              className="inline-flex items-center space-x-2 mb-6"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.2 }}
            >
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600 blur-lg opacity-20"></div>
                <Sparkles className="relative h-12 w-12 text-blue-600" />
              </div>
              <span className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                CourseAI Features
              </span>
            </motion.div>

            <h1 className="text-5xl lg:text-6xl font-bold text-gray-900 mb-6">
              Everything you need to create
              <span className="block bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                professional courses
              </span>
            </h1>
            
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto leading-relaxed">
              From AI-powered content generation to avatar video creation, our comprehensive platform 
              provides all the tools you need to build engaging, professional online courses.
            </p>

            <motion.div 
              className="flex flex-col sm:flex-row gap-4 justify-center"
              variants={itemVariants}
            >
              <Link href="/auth">
                <Button 
                  size="lg" 
                  className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-4 text-lg shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  Start Creating <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
              <Button 
                variant="outline" 
                size="lg"
                className="border-2 border-gray-300 hover:border-blue-500 px-8 py-4 text-lg bg-white/80 backdrop-blur-sm"
              >
                <Play className="mr-2 h-5 w-5" />
                Watch Demo
              </Button>
            </motion.div>
          </motion.div>
        </div>
      </motion.section>

      {/* Stats Section */}
      <motion.section 
        className="py-16 bg-white/50 backdrop-blur-sm border-y border-gray-200/50"
        variants={containerVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <motion.div 
                key={index}
                className="text-center"
                variants={itemVariants}
              >
                <div className="text-4xl lg:text-5xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-2">
                  {stat.number}
                </div>
                <div className="text-gray-600 font-medium">{stat.label}</div>
              </motion.div>
            ))}
          </div>
        </div>
      </motion.section>

      {/* Features Grid */}
      <motion.section 
        className="py-20"
        variants={containerVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="space-y-20">
            {featureCategories.map((category, categoryIndex) => (
              <motion.div 
                key={categoryIndex}
                variants={itemVariants}
                className="space-y-8"
              >
                {/* Category Header */}
                <div className="text-center">
                  <h2 className="text-4xl font-bold text-gray-900 mb-4">
                    {category.title}
                  </h2>
                  <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                    {category.description}
                  </p>
                </div>

                {/* Features Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                  {category.features.map((feature, featureIndex) => (
                    <motion.div
                      key={featureIndex}
                      whileHover={{ scale: 1.02, y: -5 }}
                      transition={{ duration: 0.2 }}
                    >
                      <Card className="h-full bg-white/60 backdrop-blur-sm border border-gray-200/50 shadow-lg hover:shadow-xl transition-all duration-300">
                        <CardHeader className="space-y-4">
                          <div className="flex items-start justify-between">
                            <div className={`p-3 rounded-xl bg-gradient-to-r ${category.gradient} text-white shadow-lg`}>
                              {feature.icon}
                            </div>
                            {feature.badge && (
                              <Badge 
                                variant="secondary" 
                                className="bg-gradient-to-r from-yellow-400 to-orange-500 text-white border-0 font-semibold"
                              >
                                {feature.badge}
                              </Badge>
                            )}
                          </div>
                          <CardTitle className="text-xl font-semibold text-gray-900">
                            {feature.title}
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <CardDescription className="text-gray-600 leading-relaxed text-base">
                            {feature.description}
                          </CardDescription>
                        </CardContent>
                      </Card>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </motion.section>

      {/* CTA Section */}
      <motion.section 
        className="py-20 bg-gradient-to-r from-blue-600 to-purple-600 text-white relative overflow-hidden"
        variants={containerVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
      >
        {/* Background Effects */}
        <div className="absolute inset-0">
          <div className="absolute top-0 left-0 w-96 h-96 bg-white rounded-full opacity-5 blur-3xl"></div>
          <div className="absolute bottom-0 right-0 w-96 h-96 bg-white rounded-full opacity-5 blur-3xl"></div>
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div variants={itemVariants}>
            <h2 className="text-4xl lg:text-5xl font-bold mb-6">
              Ready to create amazing courses?
            </h2>
            <p className="text-xl mb-8 max-w-3xl mx-auto opacity-90">
              Join thousands of educators and creators who are already using CourseAI 
              to build professional, engaging online courses with the power of AI.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/auth">
                <Button 
                  size="lg" 
                  variant="secondary"
                  className="bg-white text-blue-600 hover:bg-gray-100 px-8 py-4 text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  Get Started Free <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
              <Link href="/product/pricing">
                <Button 
                  size="lg" 
                  variant="outline"
                  className="border-2 border-white text-white hover:bg-white hover:text-blue-600 px-8 py-4 text-lg font-semibold transition-all duration-300"
                >
                  View Pricing
                </Button>
              </Link>
            </div>
          </motion.div>
        </div>
      </motion.section>
    </div>
  );
}