/**
 * Enhanced Avatar Course Generation Service
 * Leverages EchoMimic V2 + Mistral AI + Coqui TTS for avatar-based course creation
 */

import { dbManager, safeDbOperation } from '../db-enhanced';
import { courses, modules, lessons, mediaLibrary } from '@shared/schema';
import { eq } from 'drizzle-orm';
import * as mistralService from './mistralPrimaryService';
import * as coquiTTS from './coqui-tts';
import * as kokoroTTS from './kokoro-tts';
import { marpSlideService } from './marpSlideService';
import { awsS3Service } from './awsS3Service';
import { modalA100Service } from './modalA100Service';
import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';

interface AvatarCourseOptions {
  title: string;
  userId: number;
  targetAudience?: string;
  category: string;
  difficulty?: 'beginner' | 'intermediate' | 'advanced';
  duration?: 'short' | 'medium' | 'long';
  avatarConfig: {
    type: 'image' | 'video';
    sourceUrl: string; // User-provided image or video
    style?: 'professional' | 'casual' | 'animated';
    background?: 'office' | 'classroom' | 'studio' | 'custom';
  };
  voiceSettings?: {
    voiceId?: string;
    speed?: number;
    pitch?: number;
    emotion?: 'neutral' | 'enthusiastic' | 'calm';
  };
}

interface AvatarGenerationProgress {
  jobId: string;
  status: 'initializing' | 'generating_structure' | 'creating_content' | 'processing_avatar' | 'generating_media' | 'assembling_videos' | 'finalizing' | 'completed' | 'error';
  progress: number; // 0-100
  currentStep: string;
  estimatedTimeRemaining?: number;
  courseId?: number;
  error?: string;
  startTime: Date;
  lastUpdate: Date;
  avatarProcessingStatus?: {
    stage: 'preparing' | 'uploading' | 'processing' | 'downloading' | 'completed';
    progress: number;
  };
}

interface AvatarScene {
  backgroundType: 'slide' | 'environment' | 'custom';
  backgroundUrl?: string;
  duration: number;
  startTime: number;
  endTime: number;
  content: {
    title?: string;
    bulletPoints?: string[];
    diagram?: string;
  };
}

interface AvatarVideoSegment {
  avatarVideoUrl: string;
  audioUrl: string;
  backgroundScenes: AvatarScene[];
  duration: number;
  captions?: string;
}

export class EnhancedAvatarCourseGenerator {
  private progressMap = new Map<string, AvatarGenerationProgress>();
  private readonly MAX_RETRIES = 3;
  private readonly LESSON_TARGET_DURATION = 300; // 5 minutes
  private readonly SCENE_CHANGE_INTERVAL = 120; // 2 minutes for avatar courses
  private readonly AVATAR_PROCESSING_TIMEOUT = 600000; // 10 minutes

  /**
   * Start avatar course generation
   */
  async generateAvatarCourse(options: AvatarCourseOptions): Promise<{ jobId: string; estimatedTime: number }> {
    const jobId = uuidv4();
    const estimatedTime = this.calculateEstimatedTime(options);

    // Initialize progress tracking
    const progress: AvatarGenerationProgress = {
      jobId,
      status: 'initializing',
      progress: 0,
      currentStep: 'Initializing avatar course generation...',
      estimatedTimeRemaining: estimatedTime,
      startTime: new Date(),
      lastUpdate: new Date(),
      avatarProcessingStatus: {
        stage: 'preparing',
        progress: 0
      }
    };

    this.progressMap.set(jobId, progress);

    // Start generation process asynchronously
    this.executeAvatarGeneration(jobId, options).catch(error => {
      console.error(`Avatar course generation failed for job ${jobId}:`, error);
      this.updateProgress(jobId, {
        status: 'error',
        progress: 0,
        currentStep: 'Avatar course generation failed',
        error: error.message
      });
    });

    return { jobId, estimatedTime };
  }

  /**
   * Get generation progress
   */
  getProgress(jobId: string): AvatarGenerationProgress | null {
    return this.progressMap.get(jobId) || null;
  }

  /**
   * Execute the complete avatar course generation pipeline
   */
  private async executeAvatarGeneration(jobId: string, options: AvatarCourseOptions): Promise<void> {
    try {
      // Step 1: Generate course structure (10%)
      this.updateProgress(jobId, {
        status: 'generating_structure',
        progress: 5,
        currentStep: 'Generating course structure with Mistral AI...'
      });

      const courseStructure = await this.generateCourseStructure(options);
      
      this.updateProgress(jobId, {
        progress: 10,
        currentStep: 'Course structure generated successfully'
      });

      // Step 2: Create database entries (15%)
      this.updateProgress(jobId, {
        status: 'creating_content',
        progress: 15,
        currentStep: 'Creating course in database...'
      });

      const courseId = await this.createCourseInDatabase(courseStructure, options);
      
      this.updateProgress(jobId, {
        progress: 20,
        currentStep: 'Course created in database',
        courseId
      });

      // Step 3: Process avatar (40%)
      this.updateProgress(jobId, {
        status: 'processing_avatar',
        progress: 25,
        currentStep: 'Processing avatar with EchoMimic V2...',
        avatarProcessingStatus: { stage: 'preparing', progress: 0 }
      });

      const avatarAssets = await this.processAvatarAssets(jobId, options.avatarConfig);

      // Step 4: Generate media assets (60%)
      this.updateProgress(jobId, {
        status: 'generating_media',
        progress: 45,
        currentStep: 'Generating voice narration and background content...'
      });

      await this.generateAvatarMediaAssets(jobId, courseId, courseStructure, options);

      // Step 5: Assemble avatar videos (85%)
      this.updateProgress(jobId, {
        status: 'assembling_videos',
        progress: 65,
        currentStep: 'Assembling avatar video lessons...'
      });

      await this.assembleAvatarVideoLessons(jobId, courseId, courseStructure, avatarAssets);

      // Step 6: Finalize course (100%)
      this.updateProgress(jobId, {
        status: 'finalizing',
        progress: 90,
        currentStep: 'Finalizing avatar course...'
      });

      await this.finalizeAvatarCourse(courseId);

      this.updateProgress(jobId, {
        status: 'completed',
        progress: 100,
        currentStep: 'Avatar course generation completed successfully!',
        estimatedTimeRemaining: 0
      });

    } catch (error) {
      console.error(`Avatar course generation error for job ${jobId}:`, error);
      throw error;
    }
  }

  /**
   * Generate course structure using Mistral AI (reuse from traditional)
   */
  private async generateCourseStructure(options: AvatarCourseOptions): Promise<any> {
    const moduleCount = this.getModuleCount(options.duration);
    const lessonCount = this.getLessonCount(options.duration);

    const prompt = `Create a comprehensive avatar-based course structure for: "${options.title}"

Requirements:
- Target Audience: ${options.targetAudience || 'General learners'}
- Category: ${options.category}
- Difficulty: ${options.difficulty || 'intermediate'}
- Duration: ${options.duration || 'medium'} course
- Modules: ${moduleCount} modules
- Lessons per module: ${lessonCount} lessons
- Avatar Style: ${options.avatarConfig.style || 'professional'}

Generate a detailed course structure optimized for avatar presentation with:
1. Course description and learning objectives
2. ${moduleCount} modules with clear titles, descriptions, and learning objectives
3. ${lessonCount} lessons per module with detailed scripts (500-800 words each)
4. Key visual elements for each lesson (charts, diagrams, bullet points)
5. Background scene suggestions for avatar presentation
6. Engagement techniques suitable for avatar delivery

Format as JSON with this structure:
{
  "title": "Course Title",
  "description": "Comprehensive course description",
  "targetAudience": "Target audience description",
  "learningObjectives": ["objective1", "objective2", ...],
  "estimatedDuration": minutes,
  "avatarOptimized": true,
  "modules": [
    {
      "title": "Module Title",
      "description": "Module description",
      "learningObjectives": ["objective1", "objective2"],
      "order": 1,
      "lessons": [
        {
          "title": "Lesson Title",
          "description": "Lesson description",
          "script": "Detailed 500-800 word script optimized for avatar delivery",
          "visualElements": ["chart", "diagram", "bullet-points"],
          "backgroundScenes": ["office", "classroom", "studio"],
          "duration": 300,
          "order": 1,
          "keyPoints": ["point1", "point2", "point3"],
          "engagementTechniques": ["direct-address", "questions", "examples"]
        }
      ]
    }
  ]
}`;

    try {
      const response = await mistralService.generateText({
        prompt,
        maxTokens: 8000,
        temperature: 0.7
      });

      if (response.success && response.text) {
        const courseStructure = JSON.parse(response.text);
        return this.validateAvatarCourseStructure(courseStructure);
      }
      
      throw new Error('Mistral generation failed');
      
    } catch (error) {
      console.error('Mistral avatar course structure generation failed:', error);
      return await this.generateAvatarCourseStructureFallback(options);
    }
  }

  /**
   * Validate avatar course structure
   */
  private validateAvatarCourseStructure(structure: any): any {
    if (!structure.title || !structure.modules || !Array.isArray(structure.modules)) {
      throw new Error('Invalid avatar course structure format');
    }

    structure.modules.forEach((module: any, moduleIndex: number) => {
      if (!module.lessons || !Array.isArray(module.lessons)) {
        throw new Error(`Module ${moduleIndex + 1} missing lessons`);
      }
      
      module.lessons.forEach((lesson: any, lessonIndex: number) => {
        if (!lesson.script || lesson.script.length < 100) {
          throw new Error(`Lesson ${lessonIndex + 1} in module ${moduleIndex + 1} has insufficient script content`);
        }
        
        // Ensure avatar-specific fields
        if (!lesson.visualElements) {
          lesson.visualElements = ['bullet-points', 'title-slide'];
        }
        if (!lesson.backgroundScenes) {
          lesson.backgroundScenes = ['office', 'classroom'];
        }
        if (!lesson.engagementTechniques) {
          lesson.engagementTechniques = ['direct-address', 'examples'];
        }
      });
    });

    return structure;
  }

  /**
   * Fallback avatar course structure generation
   */
  private async generateAvatarCourseStructureFallback(options: AvatarCourseOptions): Promise<any> {
    const moduleCount = this.getModuleCount(options.duration);
    const lessonCount = this.getLessonCount(options.duration);

    return {
      title: options.title,
      description: `An engaging avatar-based course on ${options.title} designed for ${options.targetAudience || 'learners'}.`,
      targetAudience: options.targetAudience || 'General learners',
      learningObjectives: [
        `Master the fundamentals of ${options.title}`,
        `Apply concepts through avatar-guided instruction`,
        `Develop practical skills with visual demonstrations`
      ],
      estimatedDuration: moduleCount * lessonCount * 5,
      avatarOptimized: true,
      modules: Array.from({ length: moduleCount }, (_, i) => ({
        title: `Module ${i + 1}: ${options.title} Fundamentals`,
        description: `Learn essential concepts through interactive avatar instruction.`,
        learningObjectives: [`Understand core principles`, `Apply practical techniques`],
        order: i + 1,
        lessons: Array.from({ length: lessonCount }, (_, j) => ({
          title: `Lesson ${j + 1}: Key Concepts`,
          description: `Explore fundamental principles with avatar guidance.`,
          script: this.generateAvatarFallbackScript(options.title, i + 1, j + 1),
          visualElements: ['title-slide', 'bullet-points', 'summary'],
          backgroundScenes: ['office', 'classroom'],
          duration: 300,
          order: j + 1,
          keyPoints: ['Core concept 1', 'Core concept 2', 'Core concept 3'],
          engagementTechniques: ['direct-address', 'questions', 'examples']
        }))
      }))
    };
  }

  /**
   * Generate fallback script for avatar delivery
   */
  private generateAvatarFallbackScript(title: string, moduleNum: number, lessonNum: number): string {
    return `Hello and welcome to Module ${moduleNum}, Lesson ${lessonNum} of our ${title} course. I'm excited to guide you through this important topic today.

In this lesson, we'll explore the fundamental concepts that will help you master ${title}. I'll be walking you through each point step by step, so you can follow along easily.

Let's start by looking at the key principles. As you can see on the screen, there are three main areas we need to focus on. Each of these builds upon the previous one, creating a solid foundation for your understanding.

First, let's examine the core concept. This is essential because it forms the basis for everything else we'll learn. I want you to pay close attention to how this applies to real-world situations.

Now, let's move on to the practical applications. This is where theory meets practice, and you'll see how these concepts work in actual scenarios. I'll show you some examples that demonstrate the principles in action.

Finally, we'll look at advanced techniques that will help you take your skills to the next level. These strategies are what separate beginners from experts in this field.

As we wrap up this lesson, remember that mastering ${title} is a journey. Each concept we've covered today is a stepping stone to greater understanding. In our next lesson, we'll build upon these foundations and explore more advanced topics.

Thank you for your attention, and I look forward to continuing this learning journey with you.`;
  }

  /**
   * Calculate estimated generation time for avatar courses
   */
  private calculateEstimatedTime(options: AvatarCourseOptions): number {
    const moduleCount = this.getModuleCount(options.duration);
    const lessonCount = this.getLessonCount(options.duration);
    const totalLessons = moduleCount * lessonCount;
    
    // Avatar courses take longer due to video processing
    // Base time: 3 minutes per lesson + avatar processing overhead
    return Math.max(600, totalLessons * 180 + 300); // minimum 10 minutes
  }

  /**
   * Get module count based on duration
   */
  private getModuleCount(duration?: string): number {
    switch (duration) {
      case 'short': return 3;
      case 'medium': return 4;
      case 'long': return 6;
      default: return 4;
    }
  }

  /**
   * Get lesson count per module based on duration
   */
  private getLessonCount(duration?: string): number {
    switch (duration) {
      case 'short': return 4;
      case 'medium': return 5;
      case 'long': return 6;
      default: return 5;
    }
  }

  /**
   * Update progress tracking
   */
  private updateProgress(jobId: string, updates: Partial<AvatarGenerationProgress>): void {
    const current = this.progressMap.get(jobId);
    if (current) {
      const updated = {
        ...current,
        ...updates,
        lastUpdate: new Date()
      };
      
      // Update estimated time remaining
      if (updated.progress > 0) {
        const elapsed = Date.now() - updated.startTime.getTime();
        const totalEstimated = elapsed / (updated.progress / 100);
        updated.estimatedTimeRemaining = Math.max(0, totalEstimated - elapsed);
      }
      
      this.progressMap.set(jobId, updated);
    }
  }

  /**
   * Create course entries in database with avatar-specific fields
   */
  private async createCourseInDatabase(structure: any, options: AvatarCourseOptions): Promise<number> {
    return await safeDbOperation(async () => {
      const db = dbManager.getDatabase();

      // Create course with avatar configuration
      const [course] = await db.insert(courses).values({
        userId: options.userId,
        title: structure.title,
        description: structure.description,
        targetAudience: structure.targetAudience,
        category: options.category,
        status: 'generating',
        structure: {
          ...structure,
          avatarConfig: options.avatarConfig,
          courseType: 'avatar'
        } as any,
        lessonsCount: structure.modules.reduce((total: number, module: any) => total + module.lessons.length, 0)
      }).returning();

      // Create modules and lessons
      for (const moduleData of structure.modules) {
        const [module] = await db.insert(modules).values({
          courseId: course.id,
          title: moduleData.title,
          description: moduleData.description,
          order: moduleData.order,
          lessonsCount: moduleData.lessons.length
        }).returning();

        for (const lessonData of moduleData.lessons) {
          await db.insert(lessons).values({
            courseId: course.id,
            moduleId: module.id,
            title: lessonData.title,
            description: lessonData.description,
            script: lessonData.script,
            duration: lessonData.duration,
            order: lessonData.order,
            status: 'generating',
            // Avatar-specific lesson metadata
            metadata: {
              visualElements: lessonData.visualElements,
              backgroundScenes: lessonData.backgroundScenes,
              engagementTechniques: lessonData.engagementTechniques,
              keyPoints: lessonData.keyPoints
            } as any
          });
        }
      }

      return course.id;
    }) || 0;
  }

  /**
   * Process avatar assets using EchoMimic V2
   */
  private async processAvatarAssets(jobId: string, avatarConfig: any): Promise<any> {
    try {
      this.updateProgress(jobId, {
        avatarProcessingStatus: { stage: 'uploading', progress: 10 }
      });

      // Upload source image/video to Modal A100 service
      const uploadResult = await modalA100Service.uploadAvatarSource({
        sourceUrl: avatarConfig.sourceUrl,
        type: avatarConfig.type,
        style: avatarConfig.style || 'professional'
      });

      this.updateProgress(jobId, {
        avatarProcessingStatus: { stage: 'processing', progress: 30 }
      });

      // Process avatar with EchoMimic V2
      const processingResult = await modalA100Service.processAvatarWithEchoMimic({
        sourceId: uploadResult.sourceId,
        outputFormat: 'mp4',
        resolution: '1920x1080',
        quality: 'high',
        backgroundRemoval: true
      });

      this.updateProgress(jobId, {
        avatarProcessingStatus: { stage: 'downloading', progress: 80 }
      });

      // Download processed avatar assets
      const avatarAssets = await modalA100Service.downloadAvatarAssets({
        processingId: processingResult.processingId,
        includeVariations: true
      });

      this.updateProgress(jobId, {
        progress: 40,
        currentStep: 'Avatar processing completed successfully',
        avatarProcessingStatus: { stage: 'completed', progress: 100 }
      });

      return {
        baseAvatarUrl: avatarAssets.baseAvatarUrl,
        variations: avatarAssets.variations,
        backgroundMasks: avatarAssets.backgroundMasks,
        lipSyncModel: avatarAssets.lipSyncModel,
        processingMetadata: avatarAssets.metadata
      };

    } catch (error) {
      console.error('Avatar processing failed:', error);

      // Fallback to basic avatar processing
      return await this.processAvatarFallback(avatarConfig);
    }
  }

  /**
   * Fallback avatar processing
   */
  private async processAvatarFallback(avatarConfig: any): Promise<any> {
    // Basic avatar processing without EchoMimic V2
    return {
      baseAvatarUrl: avatarConfig.sourceUrl,
      variations: [avatarConfig.sourceUrl],
      backgroundMasks: [],
      lipSyncModel: null,
      processingMetadata: { fallback: true }
    };
  }

  /**
   * Generate avatar-specific media assets
   */
  private async generateAvatarMediaAssets(jobId: string, courseId: number, structure: any, options: AvatarCourseOptions): Promise<void> {
    const totalLessons = structure.modules.reduce((total: number, module: any) => total + module.lessons.length, 0);
    let processedLessons = 0;

    for (const module of structure.modules) {
      for (const lesson of module.lessons) {
        try {
          this.updateProgress(jobId, {
            progress: 45 + (processedLessons / totalLessons) * 20,
            currentStep: `Generating media for: ${lesson.title}`
          });

          // Generate voice narration with avatar-optimized settings
          const audioUrl = await this.generateAvatarVoiceNarration(lesson.script, lesson.title, options.voiceSettings);

          // Generate background slides and scenes
          const backgroundAssets = await this.generateAvatarBackgroundAssets(lesson);

          // Generate visual elements (charts, diagrams)
          const visualElements = await this.generateVisualElements(lesson);

          // Store media references
          await this.storeAvatarMediaReferences(courseId, lesson, audioUrl, backgroundAssets, visualElements);

          processedLessons++;

        } catch (error) {
          console.error(`Failed to generate avatar media for lesson: ${lesson.title}`, error);
        }
      }
    }

    this.updateProgress(jobId, {
      progress: 65,
      currentStep: 'All avatar media assets generated successfully'
    });
  }

  /**
   * Generate voice narration optimized for avatar delivery
   */
  private async generateAvatarVoiceNarration(script: string, title: string, voiceSettings?: any): Promise<string> {
    try {
      // Use Coqui TTS with avatar-optimized settings
      const coquiResult = await coquiTTS.generateSpeech({
        text: script,
        voiceId: voiceSettings?.voiceId || 'tts_models/en/ljspeech/tacotron2-DDC',
        language: 'en',
        speed: voiceSettings?.speed || 1.0,
        pitch: voiceSettings?.pitch || 1.0,
        emotion: voiceSettings?.emotion || 'neutral',
        // Avatar-specific optimizations
        pauseLength: 0.3, // Longer pauses for avatar lip-sync
        emphasisStrength: 1.2, // Enhanced emphasis for visual engagement
        breathingPauses: true // Natural breathing for avatar realism
      });

      if (coquiResult.success && coquiResult.audioUrl) {
        return coquiResult.audioUrl;
      }

      throw new Error('Coqui TTS failed');

    } catch (error) {
      console.error('Coqui TTS failed, trying Kokoro TTS:', error);

      // Fallback to Kokoro TTS
      const kokoroResult = await kokoroTTS.generateSpeech({
        text: script,
        voiceId: voiceSettings?.voiceId || 'kokoro-v0_19',
        language: 'en',
        speed: voiceSettings?.speed || 1.0
      });

      if (kokoroResult.success && kokoroResult.audioUrl) {
        return kokoroResult.audioUrl;
      }

      throw new Error('Both Coqui and Kokoro TTS failed');
    }
  }

  /**
   * Generate background assets for avatar scenes
   */
  private async generateAvatarBackgroundAssets(lesson: any): Promise<any[]> {
    const backgroundAssets = [];

    for (const sceneType of lesson.backgroundScenes) {
      try {
        // Generate background slides using Marp
        const slideContent = this.createAvatarSlideMarkdown(lesson, sceneType);

        const slideResult = await marpSlideService.generateSlides({
          markdown: slideContent,
          theme: this.getAvatarSlideTheme(sceneType),
          format: 'html'
        });

        if (slideResult.success && slideResult.outputPath) {
          backgroundAssets.push({
            type: 'background',
            sceneType,
            url: slideResult.outputPath,
            duration: this.SCENE_CHANGE_INTERVAL
          });
        }
      } catch (error) {
        console.error(`Failed to generate background for scene: ${sceneType}`, error);
      }
    }

    return backgroundAssets;
  }

  /**
   * Create avatar-optimized slide markdown
   */
  private createAvatarSlideMarkdown(lesson: any, sceneType: string): string {
    const backgroundClass = this.getBackgroundClass(sceneType);

    return `---
marp: true
theme: default
class: ${backgroundClass}
paginate: false
---

<!-- _class: title-slide -->
# ${lesson.title}

---

<!-- _class: content-slide -->
## Key Learning Points

${lesson.keyPoints.map((point: string) => `- ${point}`).join('\n')}

---

<!-- _class: visual-slide -->
## ${lesson.visualElements.includes('diagram') ? 'Concept Diagram' : 'Summary'}

${lesson.visualElements.includes('bullet-points') ?
  lesson.keyPoints.map((point: string) => `• ${point}`).join('\n\n') :
  'Visual representation of key concepts'
}

---

<!-- _class: engagement-slide -->
## Think About This

${lesson.engagementTechniques.includes('questions') ?
  'How can you apply these concepts in your own context?' :
  'Consider the practical applications of what we\'ve learned.'
}
`;
  }

  /**
   * Get avatar slide theme based on scene type
   */
  private getAvatarSlideTheme(sceneType: string): string {
    const themes: { [key: string]: string } = {
      'office': 'corporate',
      'classroom': 'academic',
      'studio': 'modern',
      'custom': 'default'
    };

    return themes[sceneType] || 'default';
  }

  /**
   * Get background CSS class for scene type
   */
  private getBackgroundClass(sceneType: string): string {
    const classes: { [key: string]: string } = {
      'office': 'office-background',
      'classroom': 'classroom-background',
      'studio': 'studio-background',
      'custom': 'custom-background'
    };

    return classes[sceneType] || 'default-background';
  }

  /**
   * Generate visual elements (charts, diagrams)
   */
  private async generateVisualElements(lesson: any): Promise<any[]> {
    const visualElements = [];

    for (const elementType of lesson.visualElements) {
      try {
        if (elementType === 'chart' || elementType === 'diagram') {
          // Generate educational diagrams using Marp
          const diagramContent = this.createDiagramMarkdown(lesson, elementType);

          const diagramResult = await marpSlideService.generateSlides({
            markdown: diagramContent,
            theme: 'diagram',
            format: 'html'
          });

          if (diagramResult.success && diagramResult.outputPath) {
            visualElements.push({
              type: 'visual',
              elementType,
              url: diagramResult.outputPath,
              duration: 15 // 15 seconds per visual element
            });
          }
        }
      } catch (error) {
        console.error(`Failed to generate visual element: ${elementType}`, error);
      }
    }

    return visualElements;
  }

  /**
   * Create diagram markdown content
   */
  private createDiagramMarkdown(lesson: any, elementType: string): string {
    return `---
marp: true
theme: diagram
class: diagram-slide
---

# ${lesson.title}
## ${elementType === 'chart' ? 'Data Visualization' : 'Concept Diagram'}

${lesson.keyPoints.map((point: string, index: number) =>
  `${index + 1}. ${point}`
).join('\n\n')}

---

## Visual Representation

\`\`\`mermaid
graph TD
    A[${lesson.keyPoints[0] || 'Concept 1'}] --> B[${lesson.keyPoints[1] || 'Concept 2'}]
    B --> C[${lesson.keyPoints[2] || 'Concept 3'}]
    C --> D[Application]
\`\`\`
`;
  }

  /**
   * Store avatar media references in database
   */
  private async storeAvatarMediaReferences(
    courseId: number,
    lesson: any,
    audioUrl: string,
    backgroundAssets: any[],
    visualElements: any[]
  ): Promise<void> {
    await safeDbOperation(async () => {
      const db = dbManager.getDatabase();

      // Store audio with avatar-specific metadata
      await db.insert(mediaLibrary).values({
        type: 'audio',
        name: `${lesson.title}-avatar-narration.mp3`,
        url: audioUrl,
        userId: 1,
        mimeType: 'audio/mpeg',
        fileSize: 0,
        source: 'coqui_tts_avatar',
        lessonId: lesson.order,
        courseId,
        metadata: {
          avatarOptimized: true,
          pauseLength: 0.3,
          emphasisStrength: 1.2
        } as any
      });

      // Store background assets
      for (const asset of backgroundAssets) {
        await db.insert(mediaLibrary).values({
          type: 'background',
          name: `${lesson.title}-${asset.sceneType}-background.html`,
          url: asset.url,
          userId: 1,
          mimeType: 'text/html',
          fileSize: 0,
          source: 'marp_avatar',
          lessonId: lesson.order,
          courseId,
          metadata: {
            sceneType: asset.sceneType,
            duration: asset.duration
          } as any
        });
      }

      // Store visual elements
      for (const element of visualElements) {
        await db.insert(mediaLibrary).values({
          type: 'visual',
          name: `${lesson.title}-${element.elementType}.html`,
          url: element.url,
          userId: 1,
          mimeType: 'text/html',
          fileSize: 0,
          source: 'marp_diagram',
          lessonId: lesson.order,
          courseId,
          metadata: {
            elementType: element.elementType,
            duration: element.duration
          } as any
        });
      }
    });
  }

  /**
   * Assemble avatar video lessons
   */
  private async assembleAvatarVideoLessons(jobId: string, courseId: number, structure: any, avatarAssets: any): Promise<void> {
    const totalLessons = structure.modules.reduce((total: number, module: any) => total + module.lessons.length, 0);
    let processedLessons = 0;

    for (const module of structure.modules) {
      for (const lesson of module.lessons) {
        try {
          this.updateProgress(jobId, {
            progress: 65 + (processedLessons / totalLessons) * 20,
            currentStep: `Assembling avatar video for: ${lesson.title}`
          });

          const videoUrl = await this.assembleAvatarVideoLesson(courseId, lesson, avatarAssets);

          // Update lesson with video URL
          await this.updateAvatarLessonVideo(courseId, lesson, videoUrl);

          processedLessons++;

        } catch (error) {
          console.error(`Failed to assemble avatar video for lesson: ${lesson.title}`, error);
        }
      }
    }

    this.updateProgress(jobId, {
      progress: 85,
      currentStep: 'All avatar video lessons assembled successfully'
    });
  }

  /**
   * Assemble individual avatar video lesson
   */
  private async assembleAvatarVideoLesson(courseId: number, lesson: any, avatarAssets: any): Promise<string> {
    const tempDir = path.join(process.cwd(), 'temp', `avatar-course-${courseId}`, `lesson-${lesson.order}`);

    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    try {
      // Get lesson media assets
      const mediaAssets = await this.getAvatarLessonMediaAssets(courseId, lesson.order);

      // Prepare media files
      const preparedMedia = await this.prepareAvatarMediaFiles(tempDir, mediaAssets, avatarAssets);

      // Create FFmpeg command for avatar video assembly
      const outputPath = path.join(tempDir, `${lesson.title.replace(/[^a-zA-Z0-9]/g, '_')}_avatar.mp4`);
      const ffmpegCommand = this.buildAvatarFFmpegCommand(preparedMedia, outputPath, lesson.duration, avatarAssets);

      // Execute FFmpeg
      await this.executeFFmpeg(ffmpegCommand);

      // Upload to S3
      const s3Url = await this.uploadAvatarVideoToS3(outputPath, courseId, lesson);

      // Cleanup
      this.cleanupTempFiles(tempDir);

      return s3Url;

    } catch (error) {
      console.error(`Avatar video assembly failed for lesson ${lesson.title}:`, error);
      this.cleanupTempFiles(tempDir);
      throw error;
    }
  }

  /**
   * Get avatar lesson media assets
   */
  private async getAvatarLessonMediaAssets(courseId: number, lessonOrder: number): Promise<any[]> {
    return await safeDbOperation(async () => {
      const db = dbManager.getDatabase();
      return await db.select()
        .from(mediaLibrary)
        .where(eq(mediaLibrary.courseId, courseId) && eq(mediaLibrary.lessonId, lessonOrder));
    }) || [];
  }

  /**
   * Prepare avatar media files
   */
  private async prepareAvatarMediaFiles(tempDir: string, mediaAssets: any[], avatarAssets: any): Promise<any[]> {
    const preparedMedia = [];

    // Add avatar base video
    const avatarPath = path.join(tempDir, 'avatar_base.mp4');
    await this.downloadFile(avatarAssets.baseAvatarUrl, avatarPath);
    preparedMedia.push({
      type: 'avatar',
      localPath: avatarPath,
      duration: null
    });

    // Prepare other media assets
    for (const asset of mediaAssets) {
      try {
        const localPath = path.join(tempDir, `${asset.type}_${asset.id}.${this.getFileExtension(asset.mimeType)}`);
        await this.downloadFile(asset.url, localPath);

        preparedMedia.push({
          ...asset,
          localPath,
          duration: asset.metadata?.duration || this.SCENE_CHANGE_INTERVAL
        });

      } catch (error) {
        console.error(`Failed to prepare avatar media asset ${asset.id}:`, error);
      }
    }

    return preparedMedia;
  }

  /**
   * Build FFmpeg command for avatar video assembly
   */
  private buildAvatarFFmpegCommand(mediaAssets: any[], outputPath: string, duration: number, avatarAssets: any): string {
    const audioFile = mediaAssets.find(asset => asset.type === 'audio');
    const avatarFile = mediaAssets.find(asset => asset.type === 'avatar');
    const backgroundFiles = mediaAssets.filter(asset => asset.type === 'background');

    if (!audioFile || !avatarFile) {
      throw new Error('Missing required avatar or audio files');
    }

    // Complex FFmpeg command for avatar video with backgrounds
    let command = 'ffmpeg -y ';

    // Add avatar video input
    command += `-i "${avatarFile.localPath}" `;

    // Add background inputs
    backgroundFiles.forEach(bg => {
      command += `-i "${bg.localPath}" `;
    });

    // Add audio input
    command += `-i "${audioFile.localPath}" `;

    // Create complex filter for avatar compositing
    const videoFilter = this.createAvatarVideoFilter(backgroundFiles.length, duration);

    command += `-filter_complex "${videoFilter}" `;
    command += `-c:v libx264 -c:a aac -b:a 128k `;
    command += `-r 30 -s 1920x1080 `;
    command += `-t ${duration} `;
    command += `"${outputPath}"`;

    return command;
  }

  /**
   * Create avatar video filter for compositing
   */
  private createAvatarVideoFilter(backgroundCount: number, duration: number): string {
    if (backgroundCount === 0) {
      // Simple avatar with solid background
      return '[0:v]scale=1920:1080[avatar];color=c=white:s=1920x1080[bg];[bg][avatar]overlay=0:0[v]';
    }

    // Complex compositing with background changes
    let filter = '';

    // Scale avatar and backgrounds
    filter += '[0:v]scale=640:1080[avatar];';

    for (let i = 1; i <= backgroundCount; i++) {
      filter += `[${i}:v]scale=1920:1080[bg${i}];`;
    }

    // Create background sequence with transitions
    if (backgroundCount === 1) {
      filter += '[bg1][avatar]overlay=640:0[v]';
    } else {
      // Multiple backgrounds with timed transitions
      const sceneDuration = duration / backgroundCount;
      filter += '[bg1]';

      for (let i = 2; i <= backgroundCount; i++) {
        const startTime = (i - 1) * sceneDuration;
        filter += `[bg${i}]blend=all_mode=fade:all_opacity=0.5:enable='between(t,${startTime},${startTime + 1})'`;
      }

      filter += '[bgseq];[bgseq][avatar]overlay=640:0[v]';
    }

    return filter;
  }

  /**
   * Execute FFmpeg command
   */
  private async executeFFmpeg(command: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const { exec } = require('child_process');

      exec(command, { timeout: 600000 }, (error: any, stdout: any, stderr: any) => {
        if (error) {
          console.error('Avatar FFmpeg error:', error);
          console.error('Avatar FFmpeg stderr:', stderr);
          reject(error);
        } else {
          console.log('Avatar FFmpeg completed successfully');
          resolve();
        }
      });
    });
  }

  /**
   * Upload avatar video to S3
   */
  private async uploadAvatarVideoToS3(localPath: string, courseId: number, lesson: any): Promise<string> {
    try {
      const fileName = `avatar-courses/${courseId}/lessons/${lesson.order}/${lesson.title.replace(/[^a-zA-Z0-9]/g, '_')}_avatar.mp4`;
      const s3Url = await awsS3Service.uploadFile(localPath, fileName);
      return s3Url;
    } catch (error) {
      console.error('Avatar S3 upload failed:', error);
      throw error;
    }
  }

  /**
   * Update lesson with avatar video URL
   */
  private async updateAvatarLessonVideo(courseId: number, lesson: any, videoUrl: string): Promise<void> {
    await safeDbOperation(async () => {
      const db = dbManager.getDatabase();
      await db.update(lessons)
        .set({
          videoUrl,
          status: 'completed',
          updatedAt: new Date(),
          metadata: {
            ...lesson.metadata,
            avatarVideoUrl: videoUrl,
            courseType: 'avatar'
          } as any
        })
        .where(eq(lessons.courseId, courseId) && eq(lessons.order, lesson.order));
    });
  }

  /**
   * Download file from URL
   */
  private async downloadFile(url: string, localPath: string): Promise<void> {
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`Failed to download file: ${response.statusText}`);
    }

    const buffer = await response.arrayBuffer();
    fs.writeFileSync(localPath, Buffer.from(buffer));
  }

  /**
   * Get file extension from MIME type
   */
  private getFileExtension(mimeType: string): string {
    const extensions: { [key: string]: string } = {
      'audio/mpeg': 'mp3',
      'audio/wav': 'wav',
      'video/mp4': 'mp4',
      'video/webm': 'webm',
      'text/html': 'html',
      'image/jpeg': 'jpg',
      'image/png': 'png'
    };

    return extensions[mimeType] || 'bin';
  }

  /**
   * Cleanup temporary files
   */
  private cleanupTempFiles(tempDir: string): void {
    try {
      if (fs.existsSync(tempDir)) {
        fs.rmSync(tempDir, { recursive: true, force: true });
      }
    } catch (error) {
      console.error('Failed to cleanup avatar temp files:', error);
    }
  }

  /**
   * Finalize avatar course
   */
  private async finalizeAvatarCourse(courseId: number): Promise<void> {
    await safeDbOperation(async () => {
      const db = dbManager.getDatabase();
      await db.update(courses)
        .set({
          status: 'published',
          completion: 100,
          updatedAt: new Date(),
          metadata: {
            courseType: 'avatar',
            generationCompleted: new Date(),
            avatarOptimized: true
          } as any
        })
        .where(eq(courses.id, courseId));
    });
  }
}
}

export const enhancedAvatarCourseGenerator = new EnhancedAvatarCourseGenerator();
