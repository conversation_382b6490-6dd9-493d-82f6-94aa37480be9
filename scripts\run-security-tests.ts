#!/usr/bin/env tsx
/**
 * Comprehensive Security Test Runner
 * Executes all security validation tests and generates final security report
 */

import { spawn } from 'child_process';
import fs from 'fs';
import path from 'path';

interface TestResult {
  name: string;
  passed: boolean;
  score: number;
  duration: number;
  output: string;
  error?: string;
}

class SecurityTestRunner {
  private results: TestResult[] = [];
  private startTime: Date = new Date();

  /**
   * Run all security tests
   */
  async runAllSecurityTests(): Promise<void> {
    console.log('🔒 Running Comprehensive Security Test Suite\n');
    console.log('=' .repeat(70));

    try {
      // Test 1: Security Configuration Validation
      await this.runTest('Security Configuration Validation', 'tsx scripts/validate-security-config.ts');

      // Test 2: Environment Security Check
      await this.runTest('Environment Security Check', 'tsx scripts/check-environment-security.ts');

      // Test 3: Security Audit Tests
      await this.runTest('Security Audit Tests', 'tsx scripts/security-audit-tests.ts');

      // Test 4: Database Security Test
      await this.runTest('Database Security Test', 'npm run db:test-functionality');

      // Test 5: Dependency Security Audit
      await this.runTest('Dependency Security Audit', 'npm audit --audit-level=moderate');

      // Test 6: Enhanced Course Workflows Test
      await this.runTest('Enhanced Course Workflows Test', 'npm run test:course-workflows');

      // Generate final security report
      this.generateFinalSecurityReport();

    } catch (error) {
      console.error('❌ Security test suite failed:', error);
      throw error;
    }
  }

  /**
   * Run individual security test
   */
  private async runTest(name: string, command: string): Promise<void> {
    const startTime = Date.now();
    
    try {
      console.log(`🔍 Running: ${name}...`);
      
      const result = await this.executeCommand(command);
      const duration = Date.now() - startTime;
      
      // Determine if test passed based on exit code and output
      const passed = result.exitCode === 0;
      const score = this.calculateTestScore(result.output, passed);
      
      this.results.push({
        name,
        passed,
        score,
        duration,
        output: result.output,
        error: result.error
      });
      
      const status = passed ? '✅' : '❌';
      console.log(`${status} ${name} - ${duration}ms - Score: ${score}%`);
      
    } catch (error) {
      const duration = Date.now() - startTime;
      
      this.results.push({
        name,
        passed: false,
        score: 0,
        duration,
        output: '',
        error: error instanceof Error ? error.message : String(error)
      });
      
      console.log(`❌ ${name} - ${duration}ms - FAILED`);
    }
  }

  /**
   * Execute shell command
   */
  private executeCommand(command: string): Promise<{ exitCode: number; output: string; error: string }> {
    return new Promise((resolve) => {
      const [cmd, ...args] = command.split(' ');
      const process = spawn(cmd, args, { 
        stdio: ['pipe', 'pipe', 'pipe'],
        shell: true,
        cwd: process.cwd()
      });

      let output = '';
      let error = '';

      process.stdout?.on('data', (data) => {
        output += data.toString();
      });

      process.stderr?.on('data', (data) => {
        error += data.toString();
      });

      process.on('close', (code) => {
        resolve({
          exitCode: code || 0,
          output,
          error
        });
      });

      process.on('error', (err) => {
        resolve({
          exitCode: 1,
          output,
          error: err.message
        });
      });

      // Set timeout for long-running tests
      setTimeout(() => {
        process.kill();
        resolve({
          exitCode: 1,
          output,
          error: 'Test timeout'
        });
      }, 300000); // 5 minutes timeout
    });
  }

  /**
   * Calculate test score based on output
   */
  private calculateTestScore(output: string, passed: boolean): number {
    if (!passed) return 0;

    // Look for score indicators in output
    const scoreMatch = output.match(/(?:score|security score):\s*(\d+)/i);
    if (scoreMatch) {
      return parseInt(scoreMatch[1]);
    }

    // Look for percentage indicators
    const percentMatch = output.match(/(\d+)%/);
    if (percentMatch) {
      return parseInt(percentMatch[1]);
    }

    // Look for pass/fail indicators
    const passCount = (output.match(/✅|passed|success/gi) || []).length;
    const failCount = (output.match(/❌|failed|error/gi) || []).length;
    
    if (passCount + failCount > 0) {
      return Math.round((passCount / (passCount + failCount)) * 100);
    }

    // Default score for passed tests
    return passed ? 85 : 0;
  }

  /**
   * Generate final security report
   */
  private generateFinalSecurityReport(): void {
    console.log('\n' + '='.repeat(70));
    console.log('🔒 FINAL SECURITY TEST REPORT');
    console.log('='.repeat(70));

    const totalTests = this.results.length;
    const passedTests = this.results.filter(r => r.passed).length;
    const failedTests = totalTests - passedTests;
    const totalDuration = this.results.reduce((sum, r) => sum + r.duration, 0);
    const averageScore = this.results.reduce((sum, r) => sum + r.score, 0) / totalTests;

    console.log(`\n📊 OVERALL SUMMARY:`);
    console.log(`   Total Tests: ${totalTests}`);
    console.log(`   Passed: ${passedTests} ✅`);
    console.log(`   Failed: ${failedTests} ❌`);
    console.log(`   Average Score: ${averageScore.toFixed(1)}% ${averageScore >= 85 ? '✅' : averageScore >= 70 ? '⚠️' : '❌'}`);
    console.log(`   Total Duration: ${(totalDuration / 1000).toFixed(1)}s`);

    // Individual test results
    console.log(`\n📋 DETAILED RESULTS:`);
    this.results.forEach(result => {
      const status = result.passed ? '✅' : '❌';
      console.log(`   ${status} ${result.name}`);
      console.log(`      Score: ${result.score}% | Duration: ${result.duration}ms`);
      if (result.error) {
        console.log(`      Error: ${result.error.substring(0, 100)}...`);
      }
    });

    // Security readiness assessment
    console.log(`\n🚀 DEPLOYMENT READINESS ASSESSMENT:`);
    
    const criticalTests = ['Security Configuration Validation', 'Security Audit Tests'];
    const criticalPassed = criticalTests.every(test => 
      this.results.find(r => r.name === test)?.passed
    );

    const highScoreTests = this.results.filter(r => r.score >= 85).length;
    const lowScoreTests = this.results.filter(r => r.score < 70).length;

    if (!criticalPassed) {
      console.log('   🛑 NOT READY - Critical security tests failed');
      console.log('   ❌ Address critical security issues before deployment');
    } else if (lowScoreTests > 0) {
      console.log('   ⚠️ CAUTION - Some tests have low security scores');
      console.log('   🔧 Review and improve security configurations');
    } else if (averageScore >= 85) {
      console.log('   ✅ READY - Security tests passed with good scores');
      console.log('   🎉 Application meets security standards for deployment');
    } else {
      console.log('   ⚠️ REVIEW - Security tests passed but scores could be improved');
      console.log('   📈 Consider additional security enhancements');
    }

    // Security checklist status
    console.log(`\n📋 SECURITY CHECKLIST STATUS:`);
    const checklistItems = [
      { name: 'Secrets Management', test: 'Security Configuration Validation' },
      { name: 'Database Security', test: 'Database Security Test' },
      { name: 'Input Validation', test: 'Security Audit Tests' },
      { name: 'Environment Security', test: 'Environment Security Check' },
      { name: 'Dependency Security', test: 'Dependency Security Audit' },
      { name: 'Application Security', test: 'Enhanced Course Workflows Test' }
    ];

    checklistItems.forEach(item => {
      const testResult = this.results.find(r => r.name === item.test);
      const status = testResult?.passed ? '✅' : '❌';
      const score = testResult?.score || 0;
      console.log(`   ${status} ${item.name}: ${score}%`);
    });

    // Final recommendations
    console.log(`\n💡 RECOMMENDATIONS:`);
    
    if (averageScore >= 85 && criticalPassed) {
      console.log('   🎯 Security posture is excellent');
      console.log('   🔄 Continue regular security monitoring');
      console.log('   📅 Schedule quarterly security reviews');
    } else {
      console.log('   🔧 Address failed security tests');
      console.log('   📈 Improve security configurations to achieve 85+ scores');
      console.log('   🧪 Re-run security tests after fixes');
      console.log('   👥 Consider security team review');
    }

    // Security score calculation
    const finalSecurityScore = this.calculateFinalSecurityScore();
    console.log(`\n🎯 FINAL SECURITY SCORE: ${finalSecurityScore}/100`);
    
    if (finalSecurityScore >= 85) {
      console.log('🎉 EXCELLENT - Ready for production deployment');
    } else if (finalSecurityScore >= 70) {
      console.log('⚠️ GOOD - Minor improvements recommended');
    } else {
      console.log('❌ NEEDS IMPROVEMENT - Address security issues before deployment');
    }

    console.log('\n' + '='.repeat(70));
    
    // Exit with appropriate code
    if (finalSecurityScore >= 85 && criticalPassed) {
      console.log('✅ ALL SECURITY TESTS PASSED! Application ready for deployment.');
      process.exit(0);
    } else if (finalSecurityScore >= 70) {
      console.log('⚠️ Security tests mostly passed. Review recommendations.');
      process.exit(1);
    } else {
      console.log('❌ Security tests failed. Address issues before deployment.');
      process.exit(2);
    }
  }

  /**
   * Calculate final security score
   */
  private calculateFinalSecurityScore(): number {
    const weights = {
      'Security Configuration Validation': 25,
      'Security Audit Tests': 25,
      'Environment Security Check': 15,
      'Database Security Test': 15,
      'Dependency Security Audit': 10,
      'Enhanced Course Workflows Test': 10
    };

    let weightedScore = 0;
    let totalWeight = 0;

    this.results.forEach(result => {
      const weight = weights[result.name as keyof typeof weights] || 5;
      weightedScore += result.score * weight;
      totalWeight += weight;
    });

    return Math.round(weightedScore / totalWeight);
  }

  /**
   * Save security report to file
   */
  private saveSecurityReport(): void {
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        totalTests: this.results.length,
        passedTests: this.results.filter(r => r.passed).length,
        averageScore: this.results.reduce((sum, r) => sum + r.score, 0) / this.results.length,
        finalScore: this.calculateFinalSecurityScore()
      },
      results: this.results
    };

    const reportPath = path.join(process.cwd(), 'security-test-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    console.log(`📄 Security report saved to: ${reportPath}`);
  }
}

// CLI interface
async function main() {
  const runner = new SecurityTestRunner();
  await runner.runAllSecurityTests();
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(error => {
    console.error('Security test runner failed:', error);
    process.exit(1);
  });
}

export { SecurityTestRunner };
