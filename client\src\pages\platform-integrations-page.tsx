import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { AlertCircle, Check, ExternalLink, Share2 } from 'lucide-react';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import * as SocialIcons from 'react-icons/si';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { PageHeader } from '@/components/ui/page-header';
import DashboardLayout from '@/components/layouts/dashboard-layout';
import { Skeleton } from '@/components/ui/skeleton';

type PlatformIntegration = {
  id: number;
  name: string;
  slug: string;
  icon: string;
  description: string;
  category: string;
  authType: 'apiKey' | 'oauth';
  fields?: {
    name: string;
    label: string;
    type: string;
    required: boolean;
    placeholder?: string;
  }[];
  oauthUrl?: string;
  connected?: boolean;
  connectionId?: number;
};

// This function dynamically imports icons from react-icons/si
const getDynamicIcon = (iconName: string) => {
  // @ts-ignore - SocialIcons contains dynamic key access
  const Icon = SocialIcons[iconName] || SocialIcons.SiInternetarchive;
  return <Icon className="w-6 h-6" />;
};

const PlatformIntegrationsPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState('all');
  const [selectedPlatform, setSelectedPlatform] = useState<PlatformIntegration | null>(null);
  const [formData, setFormData] = useState<Record<string, string>>({});
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch platform integrations
  const { data: platforms, isLoading } = useQuery({
    queryKey: ['/api/platforms'],
    queryFn: async () => {
      const res = await apiRequest('GET', '/api/platforms');
      return await res.json();
    },
  });

  // Fetch active user integrations
  const { data: userIntegrations, isLoading: isLoadingIntegrations } = useQuery({
    queryKey: ['/api/integrations'],
    queryFn: async () => {
      const res = await apiRequest('GET', '/api/integrations');
      return await res.json();
    },
  });

  // Connect platform mutation
  const connectMutation = useMutation({
    mutationFn: async ({ slug, data }: { slug: string; data: any }) => {
      const res = await apiRequest('POST', `/api/platform-integrations/${slug}/connect`, data);
      return await res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/platforms'] });
      queryClient.invalidateQueries({ queryKey: ['/api/integrations'] });
      setSelectedPlatform(null);
      setFormData({});
      toast({
        title: 'Platform connected',
        description: 'You can now publish your courses to this platform.',
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'Connection failed',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  // Disconnect platform mutation
  const disconnectMutation = useMutation({
    mutationFn: async (id: number) => {
      await apiRequest('DELETE', `/api/platform-integrations/${id}/disconnect`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/platforms'] });
      queryClient.invalidateQueries({ queryKey: ['/api/integrations'] });
      toast({
        title: 'Platform disconnected',
        description: 'The platform has been successfully disconnected.',
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'Disconnection failed',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  // Handle connection form submit
  const handleConnect = (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedPlatform) return;

    connectMutation.mutate({
      slug: selectedPlatform.slug,
      data: {
        ...formData,
        config: formData,
      },
    });
  };

  // Handle OAuth flow
  const handleOAuthConnect = (url: string) => {
    // In a production app, you'd open a popup or redirect to the OAuth URL
    window.open(url, '_blank', 'width=600,height=700');
    // Handle the OAuth callback in a real application
  };

  // Handle field changes
  const handleFieldChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  // Filter platforms by category
  const filteredPlatforms = platforms?.filter((platform: PlatformIntegration) => {
    if (activeTab === 'all') return true;
    if (activeTab === 'connected') return platform.connected;
    return platform.category === activeTab;
  });

  // Map platforms data with connection status
  const enhancedPlatforms = React.useMemo(() => {
    if (!platforms || !userIntegrations) return [];
    
    return platforms.map((platform: PlatformIntegration) => {
      const integration = userIntegrations.find(
        (i: any) => i.platform === platform.slug && i.status === 'active'
      );
      
      return {
        ...platform,
        connected: !!integration,
        connectionId: integration?.id,
      };
    });
  }, [platforms, userIntegrations]);

  const platformCategories = React.useMemo(() => {
    if (!platforms) return [];
    
    const categories = new Set(platforms.map((p: PlatformIntegration) => p.category));
    return Array.from(categories);
  }, [platforms]);

  return (
    <DashboardLayout>
      <div className="container mx-auto py-6">
        <PageHeader
          title="Platform Integrations"
          description="Connect your courses to external platforms and marketplaces"
          icon={<Share2 className="h-6 w-6" />}
        />

        {isLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-6">
            {[1, 2, 3, 4, 5, 6].map((i) => (
              <Card key={i} className="overflow-hidden">
                <CardHeader className="pb-2">
                  <Skeleton className="h-6 w-24 mb-2" />
                  <Skeleton className="h-4 w-full" />
                </CardHeader>
                <CardContent>
                  <Skeleton className="h-4 w-full mb-2" />
                  <Skeleton className="h-4 w-3/4" />
                </CardContent>
                <CardFooter>
                  <Skeleton className="h-10 w-full" />
                </CardFooter>
              </Card>
            ))}
          </div>
        ) : (
          <>
            <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab} className="mt-6">
              <TabsList className="mb-4">
                <TabsTrigger value="all">All Platforms</TabsTrigger>
                <TabsTrigger value="connected">Connected</TabsTrigger>
                {platformCategories.map((category) => (
                  <TabsTrigger key={category} value={category}>
                    {category.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}
                  </TabsTrigger>
                ))}
              </TabsList>

              <TabsContent value={activeTab} className="mt-0">
                {!filteredPlatforms || filteredPlatforms.length === 0 ? (
                  <Alert>
                    <AlertCircle className="h-4 w-4" />
                    <AlertTitle>No platforms found</AlertTitle>
                    <AlertDescription>
                      {activeTab === 'connected'
                        ? 'You have not connected any platforms yet. Connect a platform to get started.'
                        : 'No platforms available in this category.'}
                    </AlertDescription>
                  </Alert>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {enhancedPlatforms
                      .filter((platform: PlatformIntegration) => {
                        if (activeTab === 'all') return true;
                        if (activeTab === 'connected') return platform.connected;
                        return platform.category === activeTab;
                      })
                      .map((platform: PlatformIntegration) => (
                        <Card key={platform.slug} className="overflow-hidden">
                          <CardHeader className="pb-2">
                            <div className="flex justify-between items-start">
                              <div className="flex items-center gap-2">
                                {getDynamicIcon(platform.icon)}
                                <CardTitle className="text-xl">{platform.name}</CardTitle>
                              </div>
                              {platform.connected && (
                                <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                                  <Check className="h-3 w-3 mr-1" /> Connected
                                </Badge>
                              )}
                            </div>
                            <CardDescription>{platform.description}</CardDescription>
                          </CardHeader>
                          <CardContent>
                            <div className="text-sm text-muted-foreground">
                              <div className="flex gap-2 items-center">
                                <Badge variant="secondary" className="text-xs">
                                  {platform.category.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}
                                </Badge>
                                <Badge variant="outline" className="text-xs">
                                  {platform.authType === 'oauth' ? 'OAuth' : 'API Key'}
                                </Badge>
                              </div>
                            </div>
                          </CardContent>
                          <CardFooter className="flex justify-between border-t bg-muted/50 p-4">
                            {platform.connected ? (
                              <Button
                                variant="destructive"
                                onClick={() => disconnectMutation.mutate(platform.connectionId!)}
                                disabled={disconnectMutation.isPending}
                              >
                                Disconnect
                              </Button>
                            ) : (
                              <Button 
                                variant="default" 
                                onClick={() => setSelectedPlatform(platform)}
                              >
                                Connect
                              </Button>
                            )}
                            <Button variant="outline" size="icon">
                              <ExternalLink className="h-4 w-4" />
                            </Button>
                          </CardFooter>
                        </Card>
                      ))}
                  </div>
                )}
              </TabsContent>
            </Tabs>

            {/* Connection Dialog */}
            <Dialog open={!!selectedPlatform} onOpenChange={(open) => !open && setSelectedPlatform(null)}>
              <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                  <DialogTitle className="flex items-center gap-2">
                    {selectedPlatform && getDynamicIcon(selectedPlatform.icon)}
                    Connect to {selectedPlatform?.name}
                  </DialogTitle>
                  <DialogDescription>
                    {selectedPlatform?.description}
                  </DialogDescription>
                </DialogHeader>
                
                {selectedPlatform?.authType === 'apiKey' && selectedPlatform.fields && (
                  <form onSubmit={handleConnect}>
                    <div className="grid gap-4 py-4">
                      {selectedPlatform.fields.map((field) => (
                        <div key={field.name} className="grid grid-cols-4 items-center gap-4">
                          <Label htmlFor={field.name} className="text-right">
                            {field.label} {field.required && <span className="text-red-500">*</span>}
                          </Label>
                          <Input
                            id={field.name}
                            name={field.name}
                            type={field.type}
                            placeholder={field.placeholder}
                            required={field.required}
                            onChange={handleFieldChange}
                            className="col-span-3"
                          />
                        </div>
                      ))}
                    </div>
                    <DialogFooter>
                      <Button type="submit" disabled={connectMutation.isPending}>
                        {connectMutation.isPending ? 'Connecting...' : 'Connect Platform'}
                      </Button>
                    </DialogFooter>
                  </form>
                )}
                
                {selectedPlatform?.authType === 'oauth' && (
                  <div className="py-4">
                    <p className="mb-4 text-sm text-muted-foreground">
                      Click the button below to connect with your {selectedPlatform.name} account. 
                      You'll be redirected to {selectedPlatform.name} to authorize access.
                    </p>
                    <Button 
                      className="w-full" 
                      onClick={() => selectedPlatform.oauthUrl && handleOAuthConnect(selectedPlatform.oauthUrl)}
                    >
                      Connect with {selectedPlatform.name}
                    </Button>
                  </div>
                )}
              </DialogContent>
            </Dialog>
          </>
        )}
      </div>
    </DashboardLayout>
  );
};

export default PlatformIntegrationsPage;