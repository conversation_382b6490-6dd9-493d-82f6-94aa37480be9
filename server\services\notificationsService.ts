import { db } from '../db';
import { notifications, notificationTypes, userNotificationPreferences, type InsertNotification, type InsertNotificationType, type InsertUserNotificationPreference } from '@shared/schema';
import { eq, and, desc, isNull, lt, gte, sql, or, not } from 'drizzle-orm';

export class NotificationsService {
  // Create a new notification type
  async createNotificationType(data: InsertNotificationType) {
    const [result] = await db.insert(notificationTypes).values(data).returning();
    return result;
  }

  // Get all notification types
  async getNotificationTypes() {
    return await db.select().from(notificationTypes).orderBy(notificationTypes.category, notificationTypes.displayName);
  }

  // Get notification type by ID
  async getNotificationTypeById(id: number) {
    const [result] = await db
      .select()
      .from(notificationTypes)
      .where(eq(notificationTypes.id, id));
    return result;
  }

  // Get notification type by type string
  async getNotificationTypeByType(type: string) {
    const [result] = await db
      .select()
      .from(notificationTypes)
      .where(eq(notificationTypes.type, type));
    return result;
  }

  // Create a new notification
  async createNotification(data: InsertNotification) {
    const [result] = await db.insert(notifications).values(data).returning();
    return result;
  }

  // Create a notification with resolved type
  async sendNotification({
    userId,
    type,
    title,
    message,
    linkUrl,
    iconOverride,
    sourceName,
    sourceId,
    sourceType,
    metadata,
    expiresAt
  }: {
    userId: number;
    type: string;
    title: string;
    message: string;
    linkUrl?: string;
    iconOverride?: string;
    sourceName?: string;
    sourceId?: number;
    sourceType?: string;
    metadata?: Record<string, any>;
    expiresAt?: Date;
  }) {
    // Get notification type
    const notificationType = await this.getNotificationTypeByType(type);
    if (!notificationType) {
      throw new Error(`Notification type ${type} not found`);
    }

    // Check if user has preferences for this notification type
    const userPrefs = await this.getUserNotificationPreference(userId, notificationType.id);
    
    // If user has explicitly disabled this notification type, don't send it
    if (userPrefs && !userPrefs.enabled) {
      return null;
    }

    // Create notification
    const notificationData: InsertNotification = {
      userId,
      typeId: notificationType.id,
      title,
      message,
      linkUrl,
      iconOverride,
      sourceName,
      sourceId,
      sourceType,
      metadata: metadata || {},
      expiresAt,
    };

    const notification = await this.createNotification(notificationData);
    return notification;
  }

  // Get user's unread notifications
  async getUserUnreadNotifications(userId: number) {
    const results = await db.query.notifications.findMany({
      where: and(
        eq(notifications.userId, userId),
        eq(notifications.isRead, false),
        // Only return notifications that haven't expired
        or(isNull(notifications.expiresAt), gte(notifications.expiresAt, new Date()))
      ),
      with: {
        type: true // Join with notification type
      },
      orderBy: [desc(notifications.createdAt)],
    });

    return results;
  }

  // Get user's notifications with pagination
  async getUserNotifications(userId: number, limit = 20, offset = 0) {
    // Get total count
    const [{ count }] = await db
      .select({ count: sql`COUNT(*)` })
      .from(notifications)
      .where(eq(notifications.userId, userId));

    // Get notifications with join to notification types
    const results = await db.query.notifications.findMany({
      where: eq(notifications.userId, userId),
      with: {
        type: true
      },
      orderBy: [desc(notifications.createdAt)],
      limit,
      offset,
    });

    return {
      data: results,
      count: Number(count),
      limit,
      offset,
    };
  }

  // Mark a notification as read
  async markNotificationAsRead(id: number, userId: number) {
    const [result] = await db
      .update(notifications)
      .set({ isRead: true })
      .where(and(
        eq(notifications.id, id),
        eq(notifications.userId, userId)
      ))
      .returning();
    return result;
  }

  // Mark all notifications as read for a user
  async markAllNotificationsAsRead(userId: number) {
    await db
      .update(notifications)
      .set({ isRead: true })
      .where(and(
        eq(notifications.userId, userId),
        eq(notifications.isRead, false)
      ));
    return true;
  }

  // Delete a notification
  async deleteNotification(id: number, userId: number) {
    await db
      .delete(notifications)
      .where(and(
        eq(notifications.id, id),
        eq(notifications.userId, userId)
      ));
    return true;
  }

  // Get notification by ID
  async getNotificationById(id: number) {
    const [result] = await db
      .select()
      .from(notifications)
      .where(eq(notifications.id, id));
    return result;
  }

  // Get user notification preferences
  async getUserNotificationPreferences(userId: number) {
    return await db.query.userNotificationPreferences.findMany({
      where: eq(userNotificationPreferences.userId, userId),
      with: {
        type: true
      }
    });
  }

  // Get user notification preference for a specific type
  async getUserNotificationPreference(userId: number, typeId: number) {
    const [result] = await db
      .select()
      .from(userNotificationPreferences)
      .where(and(
        eq(userNotificationPreferences.userId, userId),
        eq(userNotificationPreferences.typeId, typeId)
      ));
    return result;
  }

  // Update or create user notification preference
  async updateUserNotificationPreference(data: InsertUserNotificationPreference) {
    const { userId, typeId } = data;
    
    // Check if preference exists
    const existing = await this.getUserNotificationPreference(userId, typeId);
    
    if (existing) {
      // Update
      const [result] = await db
        .update(userNotificationPreferences)
        .set(data)
        .where(and(
          eq(userNotificationPreferences.userId, userId),
          eq(userNotificationPreferences.typeId, typeId)
        ))
        .returning();
      return result;
    } else {
      // Create
      const [result] = await db
        .insert(userNotificationPreferences)
        .values(data)
        .returning();
      return result;
    }
  }

  // Initialize default notification preferences for a new user
  async initializeUserNotificationPreferences(userId: number) {
    // Get all notification types
    const types = await this.getNotificationTypes();
    
    // Create preferences for each type
    const preferences = types.map(type => ({
      userId,
      typeId: type.id,
      enabled: type.isDefault,
      emailEnabled: type.isDefault,
      pushEnabled: type.isDefault,
    }));
    
    // Insert all preferences
    if (preferences.length > 0) {
      await db.insert(userNotificationPreferences).values(preferences);
    }
    
    return true;
  }

  // Count unread notifications
  async countUnreadNotifications(userId: number) {
    const [{ count }] = await db
      .select({ count: sql`COUNT(*)` })
      .from(notifications)
      .where(and(
        eq(notifications.userId, userId),
        eq(notifications.isRead, false),
        // Only count notifications that haven't expired
        or(
          isNull(notifications.expiresAt),
          gte(notifications.expiresAt, new Date())
        )
      ));
    
    return Number(count);
  }

  // Clean up expired notifications
  async cleanupExpiredNotifications() {
    const now = new Date();
    await db
      .delete(notifications)
      .where(and(
        lt(notifications.expiresAt, now),
        not(isNull(notifications.expiresAt))
      ));
    return true;
  }
}

export const notificationsService = new NotificationsService();
