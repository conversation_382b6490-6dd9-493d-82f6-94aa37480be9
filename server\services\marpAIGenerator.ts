
import { openAIService } from './openai';
import { db } from '../db';
import { courses, modules, lessons } from '@shared/schema';
import { eq, and } from 'drizzle-orm';

interface MarpSlideConfig {
  theme: 'default' | 'gaia' | 'uncover';
  includeImages: boolean;
  includeCode: boolean;
  maxSlidesPerLesson: number;
  slidesPerPoint: number;
}

interface SlideContent {
  title: string;
  content: string[];
  images?: string[];
  code?: string;
  notes?: string;
}

export class MarpAIGenerator {
  
  /**
   * Generate Marp slides from course content using AI
   */
  async generateSlidesFromCourse(
    courseId: number, 
    userId: number, 
    config: MarpSlideConfig = {
      theme: 'default',
      includeImages: false,
      includeCode: false,
      maxSlidesPerLesson: 5,
      slidesPerPoint: 1
    }
  ): Promise<string> {
    
    // Fetch course data
    const course = await db.query.courses.findFirst({
      where: and(eq(courses.id, courseId), eq(courses.userId, userId)),
      with: {
        modules: {
          orderBy: (modules, { asc }) => [asc(modules.order)],
          with: {
            lessons: {
              orderBy: (lessons, { asc }) => [asc(lessons.order)]
            }
          }
        }
      }
    });

    if (!course) {
      throw new Error('Course not found');
    }

    // Generate slides structure using AI
    const slidesContent = await this.generateSlidesStructure(course, config);
    
    // Convert to Marp markdown
    return this.convertToMarpMarkdown(slidesContent, config, course.title);
  }

  /**
   * Generate slides from lesson content specifically
   */
  async generateSlidesFromLesson(
    courseId: number,
    lessonId: number,
    userId: number,
    config: MarpSlideConfig
  ): Promise<{ markdown: string; previewHtml?: string }> {
    
    const lesson = await db.query.lessons.findFirst({
      where: eq(lessons.id, lessonId),
      with: {
        module: {
          with: {
            course: true
          }
        }
      }
    });

    if (!lesson || lesson.module.course.userId !== userId) {
      throw new Error('Lesson not found or access denied');
    }

    // Use AI to convert lesson script to slide structure
    const slideStructure = await this.convertLessonToSlides(lesson, config);
    
    // Generate Marp markdown
    const markdown = this.convertSingleLessonToMarp(slideStructure, config, lesson.title);
    
    return { 
      markdown,
      previewHtml: await this.generatePreviewHtml(markdown)
    };
  }

  /**
   * Generate AI-driven slide structure from course
   */
  private async generateSlidesStructure(course: any, config: MarpSlideConfig): Promise<SlideContent[]> {
    const slides: SlideContent[] = [];

    // Title slide
    slides.push({
      title: course.title,
      content: [
        course.description || 'Course Overview',
        `Generated on ${new Date().toLocaleDateString()}`
      ]
    });

    // Table of contents
    if (course.modules && course.modules.length > 1) {
      slides.push({
        title: 'Table of Contents',
        content: course.modules.map((module: any, index: number) => 
          `${index + 1}. ${module.title || `Module ${index + 1}`}`
        )
      });
    }

    // Process each module
    for (const module of course.modules || []) {
      // Module title slide
      slides.push({
        title: module.title || 'Module',
        content: [module.description || 'Module Overview']
      });

      // Process lessons in module
      for (const lesson of module.lessons || []) {
        if (lesson.script) {
          const lessonSlides = await this.convertLessonToSlides(lesson, config);
          slides.push(...lessonSlides);
        }
      }
    }

    return slides;
  }

  /**
   * Convert a single lesson to slide structure using AI
   */
  private async convertLessonToSlides(lesson: any, config: MarpSlideConfig): Promise<SlideContent[]> {
    const prompt = `
Convert the following lesson content into ${config.maxSlidesPerLesson} presentation slides.
Each slide should have a clear title and 2-4 bullet points.
Keep content concise and presentation-friendly.

Lesson Title: ${lesson.title}
Content: ${lesson.script}

Format your response as JSON with this structure:
{
  "slides": [
    {
      "title": "Slide Title",
      "content": ["Point 1", "Point 2", "Point 3"],
      "notes": "Speaker notes (optional)"
    }
  ]
}
`;

    try {
      const response = await openAIService.generateText(prompt, {
        maxTokens: 1500,
        temperature: 0.7
      });

      const aiResponse = JSON.parse(response);
      return aiResponse.slides || [];
    } catch (error) {
      console.error('Error generating AI slides:', error);
      // Fallback: simple content splitting
      return this.fallbackSlideGeneration(lesson, config);
    }
  }

  /**
   * Fallback slide generation without AI
   */
  private fallbackSlideGeneration(lesson: any, config: MarpSlideConfig): SlideContent[] {
    const slides: SlideContent[] = [];
    
    // Add lesson title slide
    slides.push({
      title: lesson.title || 'Lesson',
      content: ['Lesson Overview']
    });

    if (lesson.script) {
      // Split script into paragraphs and create slides
      const paragraphs = lesson.script.split(/\n\n+/).filter(Boolean);
      const chunksPerSlide = Math.ceil(paragraphs.length / config.maxSlidesPerLesson);
      
      for (let i = 0; i < paragraphs.length; i += chunksPerSlide) {
        const slideContent = paragraphs.slice(i, i + chunksPerSlide);
        slides.push({
          title: `${lesson.title} - Part ${Math.floor(i / chunksPerSlide) + 1}`,
          content: slideContent
        });
      }
    }

    return slides;
  }

  /**
   * Convert slide structure to Marp markdown
   */
  private convertToMarpMarkdown(slides: SlideContent[], config: MarpSlideConfig, title: string): string {
    let markdown = `---
marp: true
theme: ${config.theme}
paginate: true
backgroundColor: #fff
---

`;

    for (const slide of slides) {
      markdown += `# ${slide.title}\n\n`;
      
      if (slide.content && slide.content.length > 0) {
        for (const point of slide.content) {
          markdown += `- ${point}\n`;
        }
      }
      
      if (slide.code && config.includeCode) {
        markdown += `\n\`\`\`\n${slide.code}\n\`\`\`\n`;
      }
      
      if (slide.notes) {
        markdown += `\n<!-- ${slide.notes} -->\n`;
      }
      
      markdown += '\n---\n\n';
    }

    return markdown;
  }

  /**
   * Convert single lesson to Marp markdown
   */
  private convertSingleLessonToMarp(slides: SlideContent[], config: MarpSlideConfig, lessonTitle: string): string {
    return this.convertToMarpMarkdown(slides, config, lessonTitle);
  }

  /**
   * Generate HTML preview (simplified)
   */
  private async generatePreviewHtml(markdown: string): Promise<string> {
    // This is a simplified preview - in production you'd use Marp CLI
    const htmlContent = markdown
      .replace(/^# (.*$)/gm, '<h1>$1</h1>')
      .replace(/^- (.*$)/gm, '<li>$1</li>')
      .replace(/\n/g, '<br>');
    
    return `
      <div style="font-family: Arial, sans-serif; padding: 20px;">
        ${htmlContent}
      </div>
    `;
  }

  /**
   * Process slides with Marp CLI (if available)
   */
  async processMarpSlides(
    markdown: string, 
    format: 'html' | 'pdf' | 'pptx' = 'html'
  ): Promise<{ url: string; filename: string }> {
    
    const filename = `slides-${Date.now()}.${format}`;
    const outputPath = `/uploads/${filename}`;
    
    // For now, just save the markdown - in production you'd use Marp CLI
    const fs = await import('fs');
    const path = await import('path');
    
    const fullPath = path.join(process.cwd(), 'uploads', filename);
    
    if (format === 'html') {
      const htmlContent = `
<!DOCTYPE html>
<html>
<head>
    <title>Generated Slides</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        h1 { color: #333; border-bottom: 2px solid #007acc; }
        .slide { border: 1px solid #ddd; margin: 20px 0; padding: 20px; }
    </style>
</head>
<body>
    <pre>${markdown}</pre>
</body>
</html>`;
      
      fs.writeFileSync(fullPath, htmlContent);
    } else {
      // For other formats, save markdown for now
      fs.writeFileSync(fullPath, markdown);
    }
    
    return {
      url: outputPath,
      filename
    };
  }
}

export const marpAIGenerator = new MarpAIGenerator();
