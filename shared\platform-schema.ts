import { pgTable, text, serial, integer, jsonb, timestamp } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

// Define the platforms table
export const platformsTable = pgTable("platforms", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  slug: text("slug").notNull().unique(),
  description: text("description").notNull(),
  category: text("category").notNull(), // 'video', 'lms', 'marketplace', etc.
  iconUrl: text("icon_url"),
  website: text("website").notNull(),
  apiDocsUrl: text("api_docs_url").notNull(),
  features: text("features").array().notNull(),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// Define the integrations table for storing platform connection data
export const platformIntegrationsTable = pgTable("platform_integrations_new", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").notNull(),
  platformId: integer("platform_id").notNull(),
  apiKey: text("api_key").notNull(),
  apiSecret: text("api_secret"),
  accessToken: text("access_token"),
  refreshToken: text("refresh_token"),
  settings: jsonb("settings").default({}),
  connectedAt: timestamp("connected_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// Create Zod schemas for data validation
export const insertPlatformTableSchema = createInsertSchema(platformsTable).omit({ 
  id: true,
  createdAt: true,
  updatedAt: true
});

export const insertPlatformIntegrationTableSchema = createInsertSchema(platformIntegrationsTable).omit({
  id: true,
  connectedAt: true,
  updatedAt: true
});

// Export types
export type Platform = typeof platformsTable.$inferSelect;
export type InsertPlatform = typeof platformsTable.$inferInsert;

export type PlatformIntegration = typeof platformIntegrationsTable.$inferSelect;
export type InsertPlatformIntegration = typeof platformIntegrationsTable.$inferInsert;

// Re-export for compatibility with existing code
export { platformsTable as platforms };
export { platformIntegrationsTable as integrations };
export { insertPlatformTableSchema as insertPlatformSchema };
export { insertPlatformIntegrationTableSchema as insertIntegrationSchema };