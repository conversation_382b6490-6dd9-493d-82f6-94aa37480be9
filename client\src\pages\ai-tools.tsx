import React from 'react';
import AIToolsPage from '@/components/ai-tools/AIToolsPage';
import { useLocation } from 'wouter';

// Get courseId and lessonId from query parameters if present
const getQueryParams = () => {
  if (typeof window === 'undefined') return {};
  
  const params = new URLSearchParams(window.location.search);
  const courseId = params.get('courseId');
  const lessonId = params.get('lessonId');
  
  return {
    courseId: courseId ? parseInt(courseId) : undefined,
    lessonId: lessonId ? parseInt(lessonId) : undefined
  };
};

export default function AITools() {
  const { courseId, lessonId } = getQueryParams();
  
  return (
    <div className="min-h-screen py-4">
      <AIToolsPage courseId={courseId} lessonId={lessonId} />
    </div>
  );
}