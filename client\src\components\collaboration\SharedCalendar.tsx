import { useState } from 'react';
import { Calendar } from '@/components/ui/calendar';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { CalendarIcon, Plus, Users, Clock } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { format } from 'date-fns';
import { toast } from '@/hooks/use-toast';

// Event type definition
type CalendarEvent = {
  id: string;
  title: string;
  description: string;
  date: Date;
  startTime: string;
  endTime: string;
  type: 'meeting' | 'deadline' | 'review' | 'other';
  participants: {
    id: number;
    name: string;
    avatarUrl?: string;
  }[];
};

// Props for the shared calendar component
interface SharedCalendarProps {
  courseId?: number;
  teamId?: number;
  events?: CalendarEvent[];
  onCreateEvent?: (event: Omit<CalendarEvent, 'id'>) => void;
}

// Sample events data
const sampleEvents: CalendarEvent[] = [
  {
    id: '1',
    title: 'Content Review Meeting',
    description: 'Review the script and visuals for Module 2',
    date: new Date(2025, 3, 29),
    startTime: '10:00',
    endTime: '11:00',
    type: 'meeting',
    participants: [
      { id: 1, name: 'Alex Kim', avatarUrl: '' },
      { id: 2, name: 'Jamie Smith', avatarUrl: '' },
    ]
  },
  {
    id: '2',
    title: 'Module 3 Deadline',
    description: 'Complete all content for Module 3',
    date: new Date(2025, 4, 5),
    startTime: '23:59',
    endTime: '23:59',
    type: 'deadline',
    participants: [
      { id: 1, name: 'Alex Kim', avatarUrl: '' },
      { id: 3, name: 'Taylor Johnson', avatarUrl: '' },
    ]
  }
];

// Function to get event type badge color
function getEventTypeBadge(type: string) {
  switch (type) {
    case 'meeting':
      return <Badge variant="outline" className="bg-blue-100 text-blue-800 hover:bg-blue-100">Meeting</Badge>;
    case 'deadline':
      return <Badge variant="outline" className="bg-red-100 text-red-800 hover:bg-red-100">Deadline</Badge>;
    case 'review':
      return <Badge variant="outline" className="bg-amber-100 text-amber-800 hover:bg-amber-100">Review</Badge>;
    default:
      return <Badge variant="outline" className="bg-slate-100 text-slate-800 hover:bg-slate-100">Other</Badge>;
  }
}

export default function SharedCalendar({ courseId, teamId, events = sampleEvents, onCreateEvent }: SharedCalendarProps) {
  const [date, setDate] = useState<Date | undefined>(new Date());
  const [showAddEventDialog, setShowAddEventDialog] = useState(false);
  const [newEvent, setNewEvent] = useState<Omit<CalendarEvent, 'id'>>({
    title: '',
    description: '',
    date: new Date(),
    startTime: '09:00',
    endTime: '10:00',
    type: 'meeting',
    participants: []
  });
  
  // Get events for the selected date
  const eventsForDate = date 
    ? events.filter(event => 
        event.date.getDate() === date.getDate() && 
        event.date.getMonth() === date.getMonth() && 
        event.date.getFullYear() === date.getFullYear()
      )
    : [];
  
  // Custom event data for calendar display
  const eventDates = events.map(event => new Date(event.date));
  
  // Handle creating a new event
  const handleCreateEvent = () => {
    if (!newEvent.title) {
      toast({
        title: "Error",
        description: "Event title is required",
        variant: "destructive",
      });
      return;
    }
    
    if (onCreateEvent) {
      onCreateEvent(newEvent);
    }
    
    // For demo purposes
    toast({
      title: "Event Created",
      description: `${newEvent.title} has been added to the calendar`,
    });
    
    setShowAddEventDialog(false);
  };
  
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-bold">Shared Calendar</h2>
        <Dialog open={showAddEventDialog} onOpenChange={setShowAddEventDialog}>
          <DialogTrigger asChild>
            <Button size="sm">
              <Plus className="h-4 w-4 mr-2" /> Add Event
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Create New Event</DialogTitle>
              <DialogDescription>
                Add an event to the collaboration calendar.
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="title" className="text-right">
                  Title
                </Label>
                <Input
                  id="title"
                  value={newEvent.title}
                  onChange={(e) => setNewEvent({...newEvent, title: e.target.value})}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="date" className="text-right">
                  Date
                </Label>
                <div className="col-span-3 relative">
                  <Input
                    id="date"
                    value={newEvent.date ? format(newEvent.date, 'PPP') : ''}
                    className="pl-10"
                    readOnly
                  />
                  <CalendarIcon className="absolute left-3 top-2.5 h-4 w-4 text-slate-500" />
                </div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="time" className="text-right">
                  Time
                </Label>
                <div className="col-span-3 flex gap-2 items-center">
                  <Input
                    id="startTime"
                    type="time"
                    value={newEvent.startTime}
                    onChange={(e) => setNewEvent({...newEvent, startTime: e.target.value})}
                  />
                  <span className="text-slate-500">to</span>
                  <Input
                    id="endTime"
                    type="time"
                    value={newEvent.endTime}
                    onChange={(e) => setNewEvent({...newEvent, endTime: e.target.value})}
                  />
                </div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="type" className="text-right">
                  Type
                </Label>
                <Select 
                  value={newEvent.type} 
                  onValueChange={(value: any) => setNewEvent({...newEvent, type: value})}
                >
                  <SelectTrigger className="col-span-3">
                    <SelectValue placeholder="Select event type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="meeting">Meeting</SelectItem>
                    <SelectItem value="deadline">Deadline</SelectItem>
                    <SelectItem value="review">Review</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-4 items-start gap-4">
                <Label htmlFor="description" className="text-right pt-2">
                  Description
                </Label>
                <Textarea
                  id="description"
                  value={newEvent.description}
                  onChange={(e) => setNewEvent({...newEvent, description: e.target.value})}
                  className="col-span-3"
                  rows={3}
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setShowAddEventDialog(false)}>
                Cancel
              </Button>
              <Button onClick={handleCreateEvent}>
                Create Event
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card className="md:col-span-2 flex flex-col">
          <CardHeader className="pb-4">
            <CardTitle className="text-base font-medium">Calendar</CardTitle>
            <CardDescription>Select a date to view events</CardDescription>
          </CardHeader>
          <CardContent className="py-0 flex-grow">
            <Calendar
              mode="single"
              selected={date}
              onSelect={setDate}
              className="rounded-md border"
              disabled={(date) => date < new Date(new Date().setHours(0, 0, 0, 0))}
              components={{
                DayContent: (props) => {
                  const date = props.date;
                  
                  // Check if there are events on this day
                  const hasEvents = eventDates.some(eventDate => 
                    eventDate.getDate() === date.getDate() &&
                    eventDate.getMonth() === date.getMonth() &&
                    eventDate.getFullYear() === date.getFullYear()
                  );
                  
                  return (
                    <div className="relative w-full h-full flex items-center justify-center">
                      {date.getDate()}
                      {hasEvents && (
                        <div className="absolute bottom-1 w-1 h-1 bg-primary rounded-full"></div>
                      )}
                    </div>
                  );
                },
              }}
            />
          </CardContent>
        </Card>
        
        <Card className="md:col-span-3">
          <CardHeader className="pb-4">
            <CardTitle className="text-base font-medium">
              Events for {date ? format(date, 'MMMM d, yyyy') : 'selected date'}
            </CardTitle>
            <CardDescription>
              {eventsForDate.length === 0 
                ? 'No events scheduled for this date' 
                : `${eventsForDate.length} event${eventsForDate.length === 1 ? '' : 's'} scheduled`
              }
            </CardDescription>
          </CardHeader>
          <CardContent className="py-0">
            <div className="space-y-4">
              {eventsForDate.length === 0 ? (
                <div className="text-center py-8 text-slate-500">
                  <CalendarIcon className="h-10 w-10 mx-auto mb-2 text-slate-300" />
                  <p>No events for this date</p>
                  <p className="text-sm">Click 'Add Event' to schedule something</p>
                </div>
              ) : (
                eventsForDate.map((event) => (
                  <div key={event.id} className="p-4 rounded-lg border bg-slate-50">
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="font-medium">{event.title}</h3>
                        <div className="flex items-center gap-2 mt-1 text-slate-500 text-sm">
                          <Clock className="h-3.5 w-3.5" />
                          <span>{event.startTime} - {event.endTime}</span>
                        </div>
                      </div>
                      {getEventTypeBadge(event.type)}
                    </div>
                    
                    {event.description && (
                      <p className="mt-2 text-sm text-slate-600">{event.description}</p>
                    )}
                    
                    {event.participants.length > 0 && (
                      <div className="mt-3">
                        <div className="flex items-center gap-1 mb-1.5 text-sm text-slate-500">
                          <Users className="h-3.5 w-3.5" />
                          <span>{event.participants.length} participants</span>
                        </div>
                        <div className="flex -space-x-2">
                          {event.participants.map((participant) => (
                            <Avatar key={participant.id} className="h-7 w-7 border-2 border-white">
                              <AvatarFallback>{participant.name.charAt(0)}</AvatarFallback>
                              {participant.avatarUrl && (
                                <AvatarImage src={participant.avatarUrl} alt={participant.name} />
                              )}
                            </Avatar>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                ))
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}