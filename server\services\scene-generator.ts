import { OpenAI } from 'openai';
import { generateWithGemini } from './gemini-service';

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export interface Scene {
  id: string;
  title: string;
  content: string;
  duration: number;
  backgroundType: 'image' | 'video';
  backgroundQuery: string;
  voiceText: string;
  timestamp: number;
}

export interface SceneGenerationParams {
  script: string;
  title: string;
  targetScenes: number;
  style: string;
}

export async function generateScenes(params: SceneGenerationParams): Promise<Scene[]> {
  const { script, title, targetScenes, style } = params;

  const prompt = `
Create ${targetScenes} video scenes from this educational script for a lesson titled "${title}".

Script:
${script}

Style: ${style}

Requirements:
- Each scene should be 5-15 seconds long
- Create engaging, educational content that flows naturally
- Include appropriate background media search queries
- Alternate between image and video backgrounds
- Each scene should have clear, concise narration text
- Ensure scenes build upon each other logically

Return a JSON array of scenes with this structure:
{
  "scenes": [
    {
      "id": "scene-1",
      "title": "Scene Title",
      "content": "Detailed scene description",
      "duration": 10,
      "backgroundType": "image",
      "backgroundQuery": "relevant search keywords",
      "voiceText": "Text to be spoken",
      "timestamp": 0
    }
  ]
}

Focus on creating scenes that are visually engaging and educationally effective.
`;

  try {
    // Try OpenAI first
    if (process.env.OPENAI_API_KEY) {
      const response = await openai.chat.completions.create({
        model: 'gpt-4o',
        messages: [
          {
            role: 'system',
            content: 'You are an expert video production assistant specializing in educational content. Create engaging, well-structured video scenes.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.7,
        max_tokens: 2000
      });

      const content = response.choices[0]?.message?.content;
      if (content) {
        const parsed = JSON.parse(content);
        return parsed.scenes || [];
      }
    }

    // Fallback to Gemini
    const geminiResponse = await generateWithGemini(prompt);
    if (geminiResponse) {
      const parsed = JSON.parse(geminiResponse);
      return parsed.scenes || [];
    }

    // Fallback to rule-based generation
    return generateFallbackScenes(script, targetScenes);

  } catch (error) {
    console.error('Scene generation error:', error);
    return generateFallbackScenes(script, targetScenes);
  }
}

function generateFallbackScenes(script: string, targetScenes: number): Scene[] {
  const sentences = script.split(/[.!?]+/).filter(s => s.trim().length > 0);
  const scenesPerGroup = Math.ceil(sentences.length / targetScenes);
  const scenes: Scene[] = [];
  
  for (let i = 0; i < targetScenes; i++) {
    const startIdx = i * scenesPerGroup;
    const endIdx = Math.min(startIdx + scenesPerGroup, sentences.length);
    const sceneText = sentences.slice(startIdx, endIdx).join('. ').trim();
    
    if (sceneText) {
      scenes.push({
        id: `scene-${i + 1}`,
        title: `Scene ${i + 1}`,
        content: sceneText,
        duration: Math.max(5, Math.min(15, sceneText.length / 10)),
        backgroundType: i % 2 === 0 ? 'image' : 'video',
        backgroundQuery: extractKeywords(sceneText),
        voiceText: sceneText,
        timestamp: i * 10
      });
    }
  }
  
  return scenes;
}

function extractKeywords(text: string): string {
  const commonWords = ['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should'];
  const words = text.toLowerCase().split(/\W+/).filter(word => 
    word.length > 3 && !commonWords.includes(word)
  );
  return words.slice(0, 3).join(' ');
}