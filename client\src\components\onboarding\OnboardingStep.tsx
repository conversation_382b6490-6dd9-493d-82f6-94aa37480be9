import React from 'react';
import { motion } from 'framer-motion';

interface OnboardingStepProps {
  title: string;
  description: string;
  icon: string;
  animationKey: string;
  primaryColor: string;
  image: string;
  direction: number;
}

// Animation variants for slide transitions
const variants = {
  enter: (direction: number) => ({
    x: direction > 0 ? 1000 : -1000,
    opacity: 0,
  }),
  center: {
    x: 0,
    opacity: 1,
  },
  exit: (direction: number) => ({
    x: direction < 0 ? 1000 : -1000,
    opacity: 0,
  }),
};

// Animation variants for staggered content
const contentVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: (custom: number) => ({
    opacity: 1,
    y: 0,
    transition: {
      delay: custom * 0.2,
      duration: 0.5,
      ease: "easeOut"
    }
  })
};

export function OnboardingStep({
  title,
  description,
  icon,
  animationKey,
  primaryColor,
  image,
  direction,
}: OnboardingStepProps) {
  // Placeholder images if actual ones aren't available
  const placeholderImage = `https://via.placeholder.com/500x400?text=${encodeURIComponent(title)}`;
  const displayImage = image || placeholderImage;

  return (
    <motion.div
      className="absolute inset-0 grid grid-cols-1 md:grid-cols-2"
      custom={direction}
      variants={variants}
      initial="enter"
      animate="center"
      exit="exit"
      transition={{
        x: { type: "spring", stiffness: 300, damping: 30 },
        opacity: { duration: 0.2 }
      }}
    >
      {/* Left Content Panel */}
      <div className="flex flex-col justify-center p-10 space-y-6">
        <motion.div
          className={`w-16 h-16 rounded-full flex items-center justify-center text-3xl ${primaryColor} text-white`}
          custom={0}
          variants={contentVariants}
          initial="hidden"
          animate="visible"
        >
          {icon}
        </motion.div>

        <motion.h2
          className="text-3xl font-bold text-gray-900"
          custom={1}
          variants={contentVariants}
          initial="hidden"
          animate="visible"
        >
          {title}
        </motion.h2>

        <motion.p
          className="text-lg text-gray-600 leading-relaxed"
          custom={2}
          variants={contentVariants}
          initial="hidden"
          animate="visible"
        >
          {description}
        </motion.p>

        <motion.div
          custom={3}
          variants={contentVariants}
          initial="hidden"
          animate="visible"
          className="pt-4"
        >
          {/* Feature-specific content can be added here */}
          {animationKey === "ai" && (
            <div className="flex items-center space-x-3 text-sm text-gray-700">
              <div className="w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center">
                <span className="text-lg">🔮</span>
              </div>
              <span>AI generates course outlines, scripts, and assessments</span>
            </div>
          )}
          
          {animationKey === "avatars" && (
            <div className="flex items-center space-x-3 text-sm text-gray-700">
              <div className="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center">
                <span className="text-lg">🗣️</span>
              </div>
              <span>Text-to-speech in 25+ languages with multiple voices</span>
            </div>
          )}
          
          {animationKey === "collaborate" && (
            <div className="flex items-center space-x-3 text-sm text-gray-700">
              <div className="w-8 h-8 rounded-full bg-amber-100 flex items-center justify-center">
                <span className="text-lg">🤝</span>
              </div>
              <span>Real-time collaboration with team members</span>
            </div>
          )}
          
          {animationKey === "analytics" && (
            <div className="flex items-center space-x-3 text-sm text-gray-700">
              <div className="w-8 h-8 rounded-full bg-pink-100 flex items-center justify-center">
                <span className="text-lg">📈</span>
              </div>
              <span>Detailed metrics on student engagement and performance</span>
            </div>
          )}
        </motion.div>
      </div>

      {/* Right Visual Panel */}
      <div className={`relative ${primaryColor} flex items-center justify-center p-10`}>
        <div className="absolute inset-0 opacity-20">
          {/* Background pattern/decoration */}
          <svg width="100%" height="100%" viewBox="0 0 800 800" xmlns="http://www.w3.org/2000/svg">
            <defs>
              <pattern id="grid" width="80" height="80" patternUnits="userSpaceOnUse">
                <path d="M 80 0 L 0 0 0 80" fill="none" stroke="white" strokeWidth="1" />
              </pattern>
            </defs>
            <rect width="800" height="800" fill="url(#grid)" />
          </svg>
        </div>

        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ 
            opacity: 1, 
            scale: 1,
            transition: { 
              delay: 0.3,
              duration: 0.6, 
              ease: "easeOut" 
            }
          }}
          className="relative z-10 w-full max-w-sm"
        >
          {/* Step-specific illustration */}
          <div className="bg-white rounded-lg shadow-xl overflow-hidden">
            <img 
              src={displayImage} 
              alt={title} 
              className="w-full h-auto max-h-[300px] object-contain"
            />
          </div>
        </motion.div>
      </div>
    </motion.div>
  );
}