import React from 'react';
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>rigger 
} from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { 
  Sparkles, 
  Clock, 
  BrainCircuit, 
  MessageSquare, 
  Zap, 
  PlayCircle,
  BarChart3,
  FileQuestion,
  PanelLeftClose,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>
} from "lucide-react";
import { cn } from "@/lib/utils";
import { useToast } from "@/hooks/use-toast";
import { AdvancedOptionsPanel } from "./AdvancedOptionsPanel";

export interface CourseAdvancedOptionsProps {
  isExpanded: boolean;
  onToggleExpand: () => void;
  currentStep: string;
  courseId?: number | undefined;
  onApplyMicroLearning?: (settings: MicroLearningSettings) => void;
  onGenerateQuiz?: (settings: QuizGenerationSettings) => void;
  onAIContentAssist?: (type: string) => void;
}

export interface MicroLearningSettings {
  enabled: boolean;
  segmentCount: number;
  breakIntervalType: 'time' | 'content';
  knowledgeCheckFrequency: 'low' | 'medium' | 'high';
  autoSummarize: boolean;
}

export interface QuizGenerationSettings {
  questionCount: number;
  questionTypes: string[];
  difficulty: string;
  includeFlashcards: boolean;
  includeStudySummary: boolean;
}

export function CourseAdvancedOptions({
  isExpanded,
  onToggleExpand,
  currentStep,
  courseId,
  onApplyMicroLearning,
  onGenerateQuiz,
  onAIContentAssist
}: CourseAdvancedOptionsProps) {
  const { toast } = useToast();
  
  // State for micro-learning options
  const [microLearning, setMicroLearning] = React.useState<MicroLearningSettings>({
    enabled: false,
    segmentCount: 5,
    breakIntervalType: 'content',
    knowledgeCheckFrequency: 'medium',
    autoSummarize: true
  });
  
  // State for quiz generation options
  const [quizOptions, setQuizOptions] = React.useState<QuizGenerationSettings>({
    questionCount: 5,
    questionTypes: ['multiple-choice'],
    difficulty: 'medium',
    includeFlashcards: true,
    includeStudySummary: true
  });

  const handleApplyMicroLearning = () => {
    if (onApplyMicroLearning) {
      onApplyMicroLearning(microLearning);
      toast({
        title: "Micro-Learning Settings Applied",
        description: "Your content will be optimized for bite-sized learning."
      });
    }
  };

  const handleGenerateQuiz = () => {
    if (onGenerateQuiz) {
      onGenerateQuiz(quizOptions);
      toast({
        title: "Generating Quiz",
        description: "Creating assessment questions from your content..."
      });
    }
  };

  const handleAIAssist = (type: string) => {
    if (onAIContentAssist) {
      onAIContentAssist(type);
      toast({
        title: "AI Assistant Activated",
        description: `Working on ${type.toLowerCase()} for your course...`
      });
    }
  };

  // If not expanded, show the toggle button only
  if (!isExpanded) {
    return (
      <div className="fixed right-0 top-1/2 transform -translate-y-1/2 z-10">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button 
                variant="secondary" 
                size="icon" 
                onClick={onToggleExpand}
                className="h-12 w-12 rounded-l-lg rounded-r-none shadow-md border-r-0"
              >
                <PanelLeftOpen className="h-5 w-5" />
              </Button>
            </TooltipTrigger>
            <TooltipContent side="left">
              <p className="text-xs">Open Advanced Options</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
    );
  }

  // Determine which tab to show based on current step
  let activeTab = "ai-assist";
  if (currentStep === "micro-learning") {
    activeTab = "micro-learning";
  } else if (currentStep === "quiz") {
    activeTab = "quiz";
  }

  return (
    <div className="fixed right-0 top-0 bottom-0 z-10 w-80 bg-white/90 backdrop-blur-sm shadow-lg border-l overflow-auto">
      <div className="sticky top-0 bg-white/90 backdrop-blur-sm border-b z-20 flex justify-between items-center p-4">
        <div className="flex items-center gap-2">
          <Sparkles className="h-4 w-4 text-primary" />
          <h3 className="font-medium text-sm">Advanced Tools</h3>
        </div>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button 
                variant="ghost" 
                size="icon" 
                onClick={onToggleExpand}
                className="h-8 w-8"
              >
                <PanelLeftClose className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent side="left">
              <p className="text-xs">Close Panel</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>

      <div className="p-4">
        <Tabs defaultValue={activeTab} className="w-full">
          <TabsList className="grid grid-cols-3 mb-4">
            <TabsTrigger value="ai-assist" className="text-xs">
              <BrainCircuit className="h-3 w-3 mr-1" />
              AI Assist
            </TabsTrigger>
            <TabsTrigger value="micro-learning" className="text-xs">
              <Clock className="h-3 w-3 mr-1" />
              Micro-Learning
            </TabsTrigger>
            <TabsTrigger value="quiz" className="text-xs">
              <FileQuestion className="h-3 w-3 mr-1" />
              Quiz
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="ai-assist" className="space-y-4 mt-2">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center">
                  <BrainCircuit className="h-4 w-4 mr-2 text-primary" />
                  AI Content Assistant
                </CardTitle>
                <CardDescription className="text-xs">
                  Let AI help you create and improve your course content
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-2">
                <Button 
                  variant="secondary" 
                  size="sm" 
                  className="w-full justify-start text-xs"
                  onClick={() => handleAIAssist('Generate Full Script')}
                >
                  <Zap className="h-3 w-3 mr-2 text-amber-500" /> Generate Full Script
                </Button>
                <Button 
                  variant="secondary" 
                  size="sm" 
                  className="w-full justify-start text-xs"
                  onClick={() => handleAIAssist('Improve Existing Script')}
                >
                  <Zap className="h-3 w-3 mr-2 text-indigo-500" /> Improve Existing Script
                </Button>
                <Button 
                  variant="secondary" 
                  size="sm" 
                  className="w-full justify-start text-xs"
                  onClick={() => handleAIAssist('Create Section Summaries')}
                >
                  <Zap className="h-3 w-3 mr-2 text-green-500" /> Create Section Summaries
                </Button>
                <Button 
                  variant="secondary" 
                  size="sm" 
                  className="w-full justify-start text-xs"
                  onClick={() => handleAIAssist('Add Learning Objectives')}
                >
                  <Zap className="h-3 w-3 mr-2 text-blue-500" /> Add Learning Objectives
                </Button>
                <Button 
                  variant="secondary" 
                  size="sm" 
                  className="w-full justify-start text-xs"
                  onClick={() => handleAIAssist('Create Presentation Slides')}
                >
                  <Zap className="h-3 w-3 mr-2 text-rose-500" /> Create Presentation Slides
                </Button>
              </CardContent>
              <CardFooter className="pt-3 flex justify-between items-center border-t text-xs text-muted-foreground">
                <div className="flex items-center">
                  <RefreshCw className="h-3 w-3 mr-1" />
                  AI credits: 125
                </div>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button variant="ghost" size="icon" className="h-6 w-6">
                        <HelpCircle className="h-3 w-3" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent side="left" className="max-w-xs">
                      <p className="text-xs">
                        The AI assistant uses your AI credits to generate content. Each generation costs between 5-25 credits depending on complexity.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </CardFooter>
            </Card>
            
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center">
                  <MessageSquare className="h-4 w-4 mr-2 text-primary" />
                  Voice & Script Tools
                </CardTitle>
                <CardDescription className="text-xs">
                  Enhance your script and voice narration
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <Label className="text-xs mb-1 block">Voice Style</Label>
                    <Select defaultValue="professional">
                      <SelectTrigger className="text-xs h-8">
                        <SelectValue placeholder="Select style" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="professional">Professional</SelectItem>
                        <SelectItem value="friendly">Friendly</SelectItem>
                        <SelectItem value="energetic">Energetic</SelectItem>
                        <SelectItem value="calm">Calm</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label className="text-xs mb-1 block">Accent</Label>
                    <Select defaultValue="american">
                      <SelectTrigger className="text-xs h-8">
                        <SelectValue placeholder="Select accent" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="american">American</SelectItem>
                        <SelectItem value="british">British</SelectItem>
                        <SelectItem value="australian">Australian</SelectItem>
                        <SelectItem value="indian">Indian</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                
                <div className="space-y-1">
                  <div className="flex justify-between">
                    <Label className="text-xs">Voice Speed</Label>
                    <span className="text-xs font-medium">100%</span>
                  </div>
                  <Slider
                    defaultValue={[100]}
                    max={150}
                    min={70}
                    step={5}
                    className="flex-1"
                  />
                </div>
                
                <Button className="w-full text-xs">
                  <PlayCircle className="h-3 w-3 mr-2" />
                  Preview Voice
                </Button>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="micro-learning" className="space-y-4 mt-2">
            <Card>
              <CardHeader className="pb-3">
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="text-sm flex items-center">
                      <Clock className="h-4 w-4 mr-2 text-primary" />
                      Micro-Learning Settings
                    </CardTitle>
                    <CardDescription className="text-xs">
                      Break content into bite-sized segments
                    </CardDescription>
                  </div>
                  <div className="flex items-center gap-2">
                    <Label htmlFor="enable-micro" className="text-xs cursor-pointer">Enable</Label>
                    <Switch 
                      id="enable-micro" 
                      checked={microLearning.enabled}
                      onCheckedChange={(checked) => setMicroLearning(prev => ({...prev, enabled: checked}))}
                    />
                  </div>
                </div>
              </CardHeader>
              
              <CardContent className={cn("space-y-4", !microLearning.enabled && "opacity-50 pointer-events-none")}>
                <div className="space-y-1">
                  <div className="flex justify-between">
                    <Label className="text-xs">Number of Segments</Label>
                    <span className="text-xs font-medium">{microLearning.segmentCount}</span>
                  </div>
                  <Slider
                    value={[microLearning.segmentCount]}
                    onValueChange={(value) => setMicroLearning(prev => ({...prev, segmentCount: value[0]}))}
                    max={10}
                    min={2}
                    step={1}
                    className="flex-1"
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Divide your content into {microLearning.segmentCount} segments for optimal learning
                  </p>
                </div>
                
                <div className="space-y-2">
                  <Label className="text-xs">Break Interval Type</Label>
                  <div className="flex gap-2">
                    <Badge 
                      variant="outline" 
                      className={cn("cursor-pointer", microLearning.breakIntervalType === 'time' && "bg-primary/10 hover:bg-primary/20")}
                      onClick={() => setMicroLearning(prev => ({...prev, breakIntervalType: 'time'}))}
                    >
                      Time-based
                    </Badge>
                    <Badge 
                      variant="outline" 
                      className={cn("cursor-pointer", microLearning.breakIntervalType === 'content' && "bg-primary/10 hover:bg-primary/20")}
                      onClick={() => setMicroLearning(prev => ({...prev, breakIntervalType: 'content'}))}
                    >
                      Content-based
                    </Badge>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label className="text-xs">Knowledge Check Frequency</Label>
                  <div className="flex gap-2">
                    <Badge 
                      variant="outline" 
                      className={cn("cursor-pointer", microLearning.knowledgeCheckFrequency === 'low' && "bg-primary/10 hover:bg-primary/20")}
                      onClick={() => setMicroLearning(prev => ({...prev, knowledgeCheckFrequency: 'low'}))}
                    >
                      Low
                    </Badge>
                    <Badge 
                      variant="outline" 
                      className={cn("cursor-pointer", microLearning.knowledgeCheckFrequency === 'medium' && "bg-primary/10 hover:bg-primary/20")}
                      onClick={() => setMicroLearning(prev => ({...prev, knowledgeCheckFrequency: 'medium'}))}
                    >
                      Medium
                    </Badge>
                    <Badge 
                      variant="outline" 
                      className={cn("cursor-pointer", microLearning.knowledgeCheckFrequency === 'high' && "bg-primary/10 hover:bg-primary/20")}
                      onClick={() => setMicroLearning(prev => ({...prev, knowledgeCheckFrequency: 'high'}))}
                    >
                      High
                    </Badge>
                  </div>
                </div>
                
                <div className="flex items-center justify-between">
                  <Label htmlFor="auto-summarize" className="text-xs cursor-pointer">
                    Auto-summarize key points
                  </Label>
                  <Switch 
                    id="auto-summarize" 
                    checked={microLearning.autoSummarize}
                    onCheckedChange={(checked) => setMicroLearning(prev => ({...prev, autoSummarize: checked}))}
                  />
                </div>
              </CardContent>
              
              <CardFooter className="pt-3 flex justify-between items-center border-t">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button variant="ghost" size="icon" className="h-6 w-6">
                        <HelpCircle className="h-3 w-3" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent side="left" className="max-w-xs">
                      <p className="text-xs">
                        Micro-learning breaks your content into smaller segments with strategic breaks, which can improve retention by up to 60%.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
                
                <Button 
                  onClick={handleApplyMicroLearning}
                  disabled={!microLearning.enabled}
                  className="text-xs"
                >
                  <Sparkles className="h-3 w-3 mr-2" />
                  Apply Settings
                </Button>
              </CardFooter>
            </Card>
            
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center">
                  <BarChart3 className="h-4 w-4 mr-2 text-primary" />
                  Learning Effectiveness
                </CardTitle>
                <CardDescription className="text-xs">
                  Research-backed micro-learning benefits
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <div className="w-1 h-8 bg-green-500 rounded-full"></div>
                    <div>
                      <p className="text-xs font-medium">Improved Retention</p>
                      <p className="text-xs text-muted-foreground">+60% knowledge retention</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-1 h-8 bg-blue-500 rounded-full"></div>
                    <div>
                      <p className="text-xs font-medium">Reduced Cognitive Load</p>
                      <p className="text-xs text-muted-foreground">Better focus and understanding</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-1 h-8 bg-amber-500 rounded-full"></div>
                    <div>
                      <p className="text-xs font-medium">Flexible Learning</p>
                      <p className="text-xs text-muted-foreground">Learn in shorter time blocks</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="quiz" className="space-y-4 mt-2">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center">
                  <FileQuestion className="h-4 w-4 mr-2 text-primary" />
                  Quiz Generator
                </CardTitle>
                <CardDescription className="text-xs">
                  Create assessment questions from your content
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-1">
                  <div className="flex justify-between">
                    <Label className="text-xs">Number of Questions</Label>
                    <span className="text-xs font-medium">{quizOptions.questionCount}</span>
                  </div>
                  <Slider
                    value={[quizOptions.questionCount]}
                    onValueChange={(value) => setQuizOptions(prev => ({...prev, questionCount: value[0]}))}
                    max={20}
                    min={1}
                    step={1}
                    className="flex-1"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label className="text-xs">Question Types</Label>
                  <div className="flex flex-wrap gap-2">
                    <Badge 
                      variant="outline" 
                      className={cn("cursor-pointer", quizOptions.questionTypes.includes('multiple-choice') && "bg-primary/10 hover:bg-primary/20")}
                      onClick={() => {
                        const types = quizOptions.questionTypes.includes('multiple-choice') 
                          ? quizOptions.questionTypes.filter(t => t !== 'multiple-choice')
                          : [...quizOptions.questionTypes, 'multiple-choice'];
                        setQuizOptions(prev => ({...prev, questionTypes: types}));
                      }}
                    >
                      Multiple Choice
                    </Badge>
                    <Badge 
                      variant="outline" 
                      className={cn("cursor-pointer", quizOptions.questionTypes.includes('true-false') && "bg-primary/10 hover:bg-primary/20")}
                      onClick={() => {
                        const types = quizOptions.questionTypes.includes('true-false') 
                          ? quizOptions.questionTypes.filter(t => t !== 'true-false')
                          : [...quizOptions.questionTypes, 'true-false'];
                        setQuizOptions(prev => ({...prev, questionTypes: types}));
                      }}
                    >
                      True/False
                    </Badge>
                    <Badge 
                      variant="outline" 
                      className={cn("cursor-pointer", quizOptions.questionTypes.includes('short-answer') && "bg-primary/10 hover:bg-primary/20")}
                      onClick={() => {
                        const types = quizOptions.questionTypes.includes('short-answer') 
                          ? quizOptions.questionTypes.filter(t => t !== 'short-answer')
                          : [...quizOptions.questionTypes, 'short-answer'];
                        setQuizOptions(prev => ({...prev, questionTypes: types}));
                      }}
                    >
                      Short Answer
                    </Badge>
                    <Badge 
                      variant="outline" 
                      className={cn("cursor-pointer", quizOptions.questionTypes.includes('fill-blank') && "bg-primary/10 hover:bg-primary/20")}
                      onClick={() => {
                        const types = quizOptions.questionTypes.includes('fill-blank') 
                          ? quizOptions.questionTypes.filter(t => t !== 'fill-blank')
                          : [...quizOptions.questionTypes, 'fill-blank'];
                        setQuizOptions(prev => ({...prev, questionTypes: types}));
                      }}
                    >
                      Fill in the Blank
                    </Badge>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label className="text-xs">Difficulty Level</Label>
                  <div className="flex gap-2">
                    <Badge 
                      variant="outline" 
                      className={cn("cursor-pointer", quizOptions.difficulty === 'easy' && "bg-primary/10 hover:bg-primary/20")}
                      onClick={() => setQuizOptions(prev => ({...prev, difficulty: 'easy'}))}
                    >
                      Easy
                    </Badge>
                    <Badge 
                      variant="outline" 
                      className={cn("cursor-pointer", quizOptions.difficulty === 'medium' && "bg-primary/10 hover:bg-primary/20")}
                      onClick={() => setQuizOptions(prev => ({...prev, difficulty: 'medium'}))}
                    >
                      Medium
                    </Badge>
                    <Badge 
                      variant="outline" 
                      className={cn("cursor-pointer", quizOptions.difficulty === 'hard' && "bg-primary/10 hover:bg-primary/20")}
                      onClick={() => setQuizOptions(prev => ({...prev, difficulty: 'hard'}))}
                    >
                      Hard
                    </Badge>
                    <Badge 
                      variant="outline" 
                      className={cn("cursor-pointer", quizOptions.difficulty === 'mixed' && "bg-primary/10 hover:bg-primary/20")}
                      onClick={() => setQuizOptions(prev => ({...prev, difficulty: 'mixed'}))}
                    >
                      Mixed
                    </Badge>
                  </div>
                </div>
                
                <div className="flex flex-col gap-3 pt-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="flashcards" className="text-xs cursor-pointer">
                      Include Flashcards
                    </Label>
                    <Switch 
                      id="flashcards" 
                      checked={quizOptions.includeFlashcards}
                      onCheckedChange={(checked) => setQuizOptions(prev => ({...prev, includeFlashcards: checked}))}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label htmlFor="summary" className="text-xs cursor-pointer">
                      Include Study Summary
                    </Label>
                    <Switch 
                      id="summary" 
                      checked={quizOptions.includeStudySummary}
                      onCheckedChange={(checked) => setQuizOptions(prev => ({...prev, includeStudySummary: checked}))}
                    />
                  </div>
                </div>
                
              </CardContent>
              <CardFooter className="pt-3 border-t">
                <Button 
                  className="w-full"
                  onClick={handleGenerateQuiz}
                >
                  <Sparkles className="h-3 w-3 mr-2" />
                  Generate Quiz
                </Button>
              </CardFooter>
            </Card>
            
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center">
                  <MessageSquare className="h-4 w-4 mr-2 text-primary" />
                  Quiz Settings
                </CardTitle>
                <CardDescription className="text-xs">
                  Configure your assessment options
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center justify-between">
                  <Label htmlFor="randomize" className="text-xs cursor-pointer">Randomize Questions</Label>
                  <Switch id="randomize" defaultChecked />
                </div>
                
                <div className="flex items-center justify-between">
                  <Label htmlFor="show-answers" className="text-xs cursor-pointer">Show Correct Answers</Label>
                  <Switch id="show-answers" defaultChecked />
                </div>
                
                <div className="flex items-center justify-between">
                  <Label htmlFor="retry" className="text-xs cursor-pointer">Allow Retries</Label>
                  <Switch id="retry" defaultChecked />
                </div>
                
                <div className="space-y-1">
                  <div className="flex justify-between">
                    <Label className="text-xs">Passing Score (%)</Label>
                    <span className="text-xs font-medium">70%</span>
                  </div>
                  <Slider
                    defaultValue={[70]}
                    max={100}
                    min={50}
                    step={5}
                    className="flex-1"
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}