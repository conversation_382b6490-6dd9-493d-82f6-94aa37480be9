/**
 * Chatterbox TTS service using Modal A100 GPU
 * High-performance text-to-speech for course narration
 */

import { spawn } from 'child_process';
import { writeFileSync, readFileSync, unlinkSync } from 'fs';
import { join } from 'path';
import { v4 as uuidv4 } from 'uuid';

export interface ChatterboxVoice {
  id: string;
  name: string;
  gender: 'male' | 'female';
  language: string;
}

export interface ChatterboxGenerationOptions {
  voice: string;
  temperature: number;
  silenceDuration: number;
  format: 'wav' | 'mp3';
}

export interface BatchLessonContent {
  title: string;
  text: string;
  moduleId?: string;
  lessonId?: string;
}

export interface GeneratedNarration {
  title: string;
  audioData: string; // base64 encoded
  format: string;
  sampleRate: number;
  durationSeconds: number;
  sizeBytes: number;
  moduleId?: string;
  lessonId?: string;
}

class ChatterboxModalTTS {
  private scriptPath: string;
  private useA100GPU: boolean;

  constructor() {
    this.scriptPath = join(process.cwd(), 'chatterbox_tts_simplified.py');
    this.useA100GPU = !!(process.env.MODAL_TOKEN_ID && process.env.MODAL_TOKEN_SECRET);
  }

  /**
   * Get available voice presets
   */
  async getAvailableVoices(): Promise<ChatterboxVoice[]> {
    try {
      const result = await this.executeFunction('list_available_voices', {});
      return result as ChatterboxVoice[];
    } catch (error) {
      console.error('Error fetching voices:', error);
      throw new Error('Failed to fetch available voices');
    }
  }

  /**
   * Generate single speech audio
   */
  async generateSpeech(
    text: string,
    options: Partial<ChatterboxGenerationOptions> = {}
  ): Promise<Buffer> {
    const params = {
      text,
      voice_preset: options.voice || 'v2/en_speaker_6',
      temperature: options.temperature || 0.7,
      silence_duration: options.silenceDuration || 0.25
    };

    try {
      const audioData = await this.executeFunction('generate_high_quality_speech', params);
      return Buffer.from(audioData as string, 'base64');
    } catch (error) {
      console.error('Error generating speech:', error);
      throw new Error('Failed to generate speech audio');
    }
  }

  /**
   * Generate narration for multiple lessons in batch
   */
  async generateCourseNarrationBatch(
    lessons: BatchLessonContent[],
    voicePreset: string = 'v2/en_speaker_6'
  ): Promise<GeneratedNarration[]> {
    const params = {
      lesson_texts: lessons,
      voice_preset: voicePreset,
      output_format: 'wav'
    };

    try {
      const results = await this.executeFunction('generate_course_narration_batch', params);
      return results as GeneratedNarration[];
    } catch (error) {
      console.error('Error generating batch narration:', error);
      throw new Error('Failed to generate course narration batch');
    }
  }

  /**
   * Clone voice from sample audio
   */
  async cloneVoiceFromSample(
    sampleAudioPath: string,
    targetText: string
  ): Promise<Buffer> {
    const params = {
      sample_audio_path: sampleAudioPath,
      target_text: targetText
    };

    try {
      const audioData = await this.executeFunction('clone_voice_from_sample', params);
      return Buffer.from(audioData as string, 'base64');
    } catch (error) {
      console.error('Error cloning voice:', error);
      throw new Error('Failed to clone voice from sample');
    }
  }

  /**
   * Generate narration for a complete course
   */
  async generateFullCourseNarration(
    courseData: {
      title: string;
      modules: Array<{
        id: string;
        title: string;
        lessons: Array<{
          id: string;
          title: string;
          content: string;
        }>;
      }>;
    },
    voicePreset: string = 'v2/en_speaker_6'
  ): Promise<{
    courseTitle: string;
    totalDuration: number;
    modules: Array<{
      moduleId: string;
      moduleTitle: string;
      lessons: GeneratedNarration[];
    }>;
  }> {
    const allLessons: BatchLessonContent[] = [];
    
    // Flatten all lessons for batch processing
    courseData.modules.forEach(module => {
      module.lessons.forEach(lesson => {
        allLessons.push({
          title: lesson.title,
          text: lesson.content,
          moduleId: module.id,
          lessonId: lesson.id
        });
      });
    });

    // Generate all narrations in one batch for efficiency
    const narrations = await this.generateCourseNarrationBatch(allLessons, voicePreset);

    // Organize results by module
    const moduleResults = courseData.modules.map(module => {
      const moduleLessons = narrations.filter(n => n.moduleId === module.id);
      return {
        moduleId: module.id,
        moduleTitle: module.title,
        lessons: moduleLessons
      };
    });

    const totalDuration = narrations.reduce((sum, n) => sum + n.durationSeconds, 0);

    return {
      courseTitle: courseData.title,
      totalDuration,
      modules: moduleResults
    };
  }

  /**
   * Execute TTS function via Python subprocess or A100 GPU
   */
  private async executeFunction(functionName: string, params: any): Promise<any> {
    if (this.useA100GPU) {
      return this.executeA100Function(functionName, params);
    }
    
    return new Promise((resolve, reject) => {
      const paramsJson = JSON.stringify(params);
      
      // Execute simplified TTS function
      const pythonProcess = spawn('python3', [
        this.scriptPath,
        functionName,
        paramsJson
      ]);

      let stdout = '';
      let stderr = '';

      pythonProcess.stdout.on('data', (data) => {
        stdout += data.toString();
      });

      pythonProcess.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      pythonProcess.on('close', (code) => {
        if (code === 0) {
          try {
            const result = JSON.parse(stdout.trim());
            if (result.error) {
              reject(new Error(result.error));
            } else {
              resolve(result);
            }
          } catch (parseError) {
            reject(new Error(`Failed to parse result: ${parseError}`));
          }
        } else {
          reject(new Error(`TTS function failed: ${stderr || stdout}`));
        }
      });

      pythonProcess.on('error', (error) => {
        reject(new Error(`Process error: ${error.message}`));
      });
    });
  }

  /**
   * Execute functions on A100 GPU via Modal
   */
  private async executeA100Function(functionName: string, params: any): Promise<any> {
    return new Promise((resolve, reject) => {
      const modalFunctionMap: Record<string, string> = {
        'list_available_voices': 'list_production_voices',
        'generate_high_quality_speech': 'high_quality_tts_production',
        'generate_course_narration_batch': 'batch_course_narration_a100',
        'clone_voice_from_sample': 'voice_cloning_a100'
      };

      const a100Function = modalFunctionMap[functionName];
      if (!a100Function) {
        reject(new Error(`A100 function mapping not found for: ${functionName}`));
        return;
      }

      const modalProcess = spawn('python3', [
        '-c',
        `
import sys
import json
import os
sys.path.insert(0, '${process.cwd()}')

# Set Modal credentials
os.environ['MODAL_TOKEN_ID'] = '${process.env.MODAL_TOKEN_ID}'
os.environ['MODAL_TOKEN_SECRET'] = '${process.env.MODAL_TOKEN_SECRET}'

try:
    from modal_a100_production import app, ${a100Function}
    
    params = ${JSON.stringify(params)}
    
    with app.run():
        result = ${a100Function}.remote(**params)
        print(json.dumps(result))
        
except Exception as e:
    print(json.dumps({"error": str(e)}))
        `
      ]);

      let stdout = '';
      let stderr = '';

      modalProcess.stdout.on('data', (data) => {
        stdout += data.toString();
      });

      modalProcess.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      modalProcess.on('close', (code) => {
        if (code === 0) {
          try {
            const result = JSON.parse(stdout.trim());
            if (result.error) {
              console.log(`A100 GPU failed, falling back to local: ${result.error}`);
              // Fallback to local execution
              this.executeLocalFunction(functionName, params).then(resolve).catch(reject);
            } else {
              resolve(result);
            }
          } catch (parseError) {
            console.log(`A100 parse error, falling back: ${parseError}`);
            this.executeLocalFunction(functionName, params).then(resolve).catch(reject);
          }
        } else {
          console.log(`A100 process failed, falling back: ${stderr}`);
          this.executeLocalFunction(functionName, params).then(resolve).catch(reject);
        }
      });

      modalProcess.on('error', (error) => {
        console.log(`A100 execution error, falling back: ${error.message}`);
        this.executeLocalFunction(functionName, params).then(resolve).catch(reject);
      });
    });
  }

  /**
   * Execute local TTS functions as fallback
   */
  private async executeLocalFunction(functionName: string, params: any): Promise<any> {
    return new Promise((resolve, reject) => {
      const paramsJson = JSON.stringify(params);
      
      const pythonProcess = spawn('python3', [
        this.scriptPath,
        functionName,
        paramsJson
      ]);

      let stdout = '';
      let stderr = '';

      pythonProcess.stdout.on('data', (data) => {
        stdout += data.toString();
      });

      pythonProcess.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      pythonProcess.on('close', (code) => {
        if (code === 0) {
          try {
            const result = JSON.parse(stdout.trim());
            if (result.error) {
              reject(new Error(result.error));
            } else {
              resolve(result);
            }
          } catch (parseError) {
            reject(new Error(`Failed to parse result: ${parseError}`));
          }
        } else {
          reject(new Error(`Local TTS function failed: ${stderr || stdout}`));
        }
      });

      pythonProcess.on('error', (error) => {
        reject(new Error(`Local process error: ${error.message}`));
      });
    });
  }
}

export const chatterboxModalTTS = new ChatterboxModalTTS();