import { useState } from "react";
import { useUser } from "@/hooks/use-user";
import { useQuery } from "@tanstack/react-query";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Switch } from "@/components/ui/switch";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Separator } from "@/components/ui/separator";
import { Progress } from "@/components/ui/progress";
import { useToast } from "@/hooks/use-toast";
import { UserStats, BillingHistory } from "@/types";
import { getQueryFn, queryClient } from "@/lib/queryClient";

export default function ProfilePage() {
  const { user } = useUser();
  const { toast } = useToast();
  const [selectedTab, setSelectedTab] = useState("account");
  const [isEditing, setIsEditing] = useState(false);
  const [profileData, setProfileData] = useState({
    name: user?.name || "",
    email: user?.email || "",
    password: "",
    confirmPassword: "",
  });
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [show2FASetup, setShow2FASetup] = useState(false);
  const [twoFactorEnabled, setTwoFactorEnabled] = useState(false);
  const [preferences, setPreferences] = useState<{
    theme: 'light' | 'dark' | 'system';
    language: string;
    notifications: {
      email: boolean;
      push: boolean;
      marketing: boolean;
    }
  }>({
    theme: "light",
    language: "english",
    notifications: {
      email: true,
      push: true,
      marketing: false,
    },
  });

  // Get user stats
  const { data: stats } = useQuery<UserStats>({
    queryKey: ["/api/user-stats"],
    queryFn: getQueryFn({ on401: "throw" }),
    enabled: !!user,
  });

  // Mock billing history for UI demonstration
  const billingHistory: BillingHistory[] = [
    {
      id: 1,
      userId: user?.id || 0,
      amount: 29.99,
      currency: "USD",
      description: "Monthly Pro Subscription",
      status: "Paid",
      transactionDate: new Date("2025-03-01"),
    },
    {
      id: 2,
      userId: user?.id || 0,
      amount: 29.99,
      currency: "USD",
      description: "Monthly Pro Subscription",
      status: "Paid",
      transactionDate: new Date("2025-02-01"),
    },
  ];

  // Handle form changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setProfileData((prev) => ({ ...prev, [name]: value }));
  };

  // Handle notification preference changes
  const handleNotificationChange = (key: keyof typeof preferences.notifications) => {
    setPreferences((prev) => ({
      ...prev,
      notifications: {
        ...prev.notifications,
        [key]: !prev.notifications[key],
      },
    }));
  };

  // Handle theme change
  const handleThemeChange = (theme: "light" | "dark" | "system") => {
    setPreferences((prev) => ({ ...prev, theme }));
  };

  // Handle language change
  const handleLanguageChange = (language: string) => {
    setPreferences((prev) => ({ ...prev, language }));
  };

  // Handle profile save
  const handleSaveProfile = async () => {
    try {
      const response = await fetch('/api/users/profile', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: profileData.name,
          email: profileData.email,
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to update profile');
      }
      
      const updatedUser = await response.json();
      
      // Update query cache with the new user data
      queryClient.setQueryData(['/api/auth/me'], updatedUser);
      
      toast({
        title: "Profile updated",
        description: "Your profile information has been updated successfully.",
      });
      setIsEditing(false);
    } catch (error) {
      toast({
        title: "Update failed",
        description: error instanceof Error ? error.message : 'An error occurred',
        variant: "destructive",
      });
    }
  };

  // Handle avatar upload
  const handleAvatarUpload = () => {
    // This would typically involve file upload functionality
    // For now, we'll just show a toast message
    toast({
      title: "Feature in development",
      description: "Avatar upload functionality will be available soon.",
    });
  };

  // Handle password change
  const handlePasswordChange = async () => {
    if (profileData.password !== profileData.confirmPassword) {
      toast({
        title: "Error",
        description: "Passwords do not match.",
        variant: "destructive",
      });
      return;
    }
    
    if (profileData.password.length < 6) {
      toast({
        title: "Error",
        description: "Password must be at least 6 characters long.",
        variant: "destructive",
      });
      return;
    }
    
    try {
      const response = await fetch('/api/users/change-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          currentPassword: 'password', // In a real app, you'd have a field for this
          newPassword: profileData.password,
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to change password');
      }
      
      toast({
        title: "Password updated",
        description: "Your password has been changed successfully.",
      });
      setProfileData((prev) => ({ ...prev, password: "", confirmPassword: "" }));
    } catch (error) {
      toast({
        title: "Password change failed",
        description: error instanceof Error ? error.message : 'An error occurred',
        variant: "destructive",
      });
    }
  };

  // Handle subscribe/upgrade
  const handleSubscribe = () => {
    // In a real app, redirect to payment page
    toast({
      title: "Redirecting to payment",
      description: "You will be redirected to complete your subscription upgrade.",
    });
  };

  // For demonstration purposes, create a mock user if there's no authenticated user
  // In a real app, you'd redirect to login instead
  const mockUser = user || {
    id: 1,
    username: "demo_user",
    email: "<EMAIL>",
    name: "Demo User",
    plan: "Basic",
    role: "user",
    avatarUrl: "",
    emailVerified: true,
    createdAt: new Date("2025-01-01")
  };
  
  // Update profileData with mockUser 
  useEffect(() => {
    setProfileData({
      ...profileData,
      name: mockUser.name,
      email: mockUser.email
    });
  }, [mockUser]);

  return (
    <div className="container py-6 md:py-10">
      <div className="mb-6">
        <h1 className="text-3xl font-bold">My Profile</h1>
        <p className="text-muted-foreground">Manage your account settings and preferences</p>
      </div>

      <Tabs value={selectedTab} onValueChange={setSelectedTab}>
        <TabsList className="mb-6">
          <TabsTrigger value="account">Account</TabsTrigger>
          <TabsTrigger value="subscription">Subscription</TabsTrigger>
          <TabsTrigger value="preferences">Preferences</TabsTrigger>
          <TabsTrigger value="security">Security & Privacy</TabsTrigger>
        </TabsList>

        <TabsContent value="account">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Profile Information */}
            <Card className="md:col-span-2">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Profile Information</CardTitle>
                    <CardDescription>Update your personal information</CardDescription>
                  </div>
                  <Button 
                    variant={isEditing ? "default" : "outline"} 
                    onClick={() => setIsEditing(!isEditing)}
                  >
                    {isEditing ? "Cancel" : "Edit"}
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                {isEditing ? (
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="name">Full Name</Label>
                      <Input 
                        id="name" 
                        name="name" 
                        value={profileData.name} 
                        onChange={handleChange} 
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="email">Email Address</Label>
                      <Input 
                        id="email" 
                        name="email" 
                        type="email" 
                        value={profileData.email} 
                        onChange={handleChange} 
                      />
                    </div>
                    <Button onClick={handleSaveProfile}>Save Changes</Button>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 gap-x-6 gap-y-4 sm:grid-cols-2">
                      <div>
                        <Label className="text-sm text-muted-foreground">Full Name</Label>
                        <div className="font-medium mt-1">{user.name}</div>
                      </div>
                      <div>
                        <Label className="text-sm text-muted-foreground">Email Address</Label>
                        <div className="font-medium mt-1">{user.email}</div>
                      </div>
                      <div>
                        <Label className="text-sm text-muted-foreground">Username</Label>
                        <div className="font-medium mt-1">{user.username}</div>
                      </div>
                      <div>
                        <Label className="text-sm text-muted-foreground">Member Since</Label>
                        <div className="font-medium mt-1">
                          {user.createdAt 
                            ? new Date(user.createdAt).toLocaleDateString() 
                            : 'Not available'}
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Profile Photo */}
            <Card>
              <CardHeader>
                <CardTitle>Profile Photo</CardTitle>
                <CardDescription>Update your profile picture</CardDescription>
              </CardHeader>
              <CardContent className="flex flex-col items-center space-y-4">
                <Avatar className="h-24 w-24">
                  <AvatarImage src={user.avatarUrl || ""} />
                  <AvatarFallback className="text-lg">
                    {user.name.split(" ").map(n => n[0]).join("")}
                  </AvatarFallback>
                </Avatar>
                <Button onClick={handleAvatarUpload} variant="outline">Upload New Photo</Button>
              </CardContent>
            </Card>

            {/* Password Change */}
            <Card className="md:col-span-3">
              <CardHeader>
                <CardTitle>Password</CardTitle>
                <CardDescription>Change your password</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4 max-w-md">
                  <div className="space-y-2">
                    <Label htmlFor="new-password">New Password</Label>
                    <Input 
                      id="new-password" 
                      name="password" 
                      type="password" 
                      value={profileData.password} 
                      onChange={handleChange} 
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="confirm-password">Confirm New Password</Label>
                    <Input 
                      id="confirm-password" 
                      name="confirmPassword" 
                      type="password" 
                      value={profileData.confirmPassword} 
                      onChange={handleChange} 
                    />
                  </div>
                  <Button onClick={handlePasswordChange}>Change Password</Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="subscription">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Current Plan */}
            <Card className="md:col-span-2">
              <CardHeader>
                <CardTitle>Current Plan</CardTitle>
                <CardDescription>Your current subscription plan and usage</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="p-4 bg-primary/5 rounded-lg mb-4">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="text-lg font-semibold">{user.plan} Plan</h3>
                    <div className="bg-primary text-primary-foreground text-xs font-medium px-2 py-1 rounded">
                      Active
                    </div>
                  </div>
                  <p className="text-sm text-muted-foreground mb-2">
                    {user.plan === "Pro" 
                      ? "Full access to all features with unlimited projects"
                      : "Basic access with limited features"}
                  </p>
                  
                  {user.plan !== "Pro" && (
                    <Button onClick={handleSubscribe} className="mt-2">Upgrade to Pro</Button>
                  )}
                </div>

                {stats && (
                  <div className="space-y-4">
                    <div>
                      <div className="flex items-center justify-between mb-2">
                        <Label className="text-sm font-medium">AI Credits</Label>
                        <span className="text-sm text-muted-foreground">{stats.aiCredits} credits left</span>
                      </div>
                      <Progress value={(stats.aiCredits / 500) * 100} />
                    </div>
                    
                    <div>
                      <div className="flex items-center justify-between mb-2">
                        <Label className="text-sm font-medium">Storage Used</Label>
                        <span className="text-sm text-muted-foreground">
                          {stats.storageUsed} MB of {stats.storageLimit} MB
                        </span>
                      </div>
                      <Progress value={(stats.storageUsed / stats.storageLimit) * 100} />
                    </div>
                    
                    <div className="flex justify-between text-sm">
                      <span>Courses: {stats.activeCourses} active / {stats.publishedCourses} published</span>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Plan Options & Payment Methods */}
            <Card>
              <CardHeader>
                <CardTitle>Payment Methods</CardTitle>
                <CardDescription>Manage your payment information</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <h3 className="text-sm font-medium mb-3">Current Payment Method</h3>
                  <div className="flex items-center space-x-3 p-3 bg-slate-50 rounded-md border border-slate-200">
                    <div className="bg-slate-100 p-2 rounded-md">
                      <i className="ri-visa-line text-xl text-blue-600"></i>
                    </div>
                    <div className="flex-1">
                      <p className="text-sm font-medium">Visa ending in 4242</p>
                      <p className="text-xs text-muted-foreground">Expires 12/25</p>
                    </div>
                    <div>
                      <Button variant="ghost" size="sm">
                        <i className="ri-pencil-line"></i>
                      </Button>
                    </div>
                  </div>
                </div>
                
                <div className="pt-3 border-t border-slate-200">
                  <h3 className="text-sm font-medium mb-3">Subscription Actions</h3>
                  <div className="space-y-3">
                    <Button onClick={handleSubscribe} className="w-full">
                      {user.plan === "Pro" ? "Change Plan" : "Upgrade Plan"}
                    </Button>
                    {user.plan === "Pro" && (
                      <Button variant="outline" className="w-full">Cancel Subscription</Button>
                    )}
                  </div>
                </div>
                
                <div className="pt-3 border-t border-slate-200">
                  <Button variant="outline" className="w-full" onClick={() => {
                    toast({
                      title: "Add payment method",
                      description: "Payment method functionality will be available soon.",
                    });
                  }}>
                    <i className="ri-add-line mr-2"></i>
                    Add Payment Method
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Billing History */}
            <Card className="md:col-span-3">
              <CardHeader>
                <CardTitle>Billing History</CardTitle>
                <CardDescription>View your recent transactions</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="border rounded-md">
                  <div className="grid grid-cols-4 gap-4 p-4 font-medium text-sm border-b">
                    <div>Date</div>
                    <div>Description</div>
                    <div>Amount</div>
                    <div>Status</div>
                  </div>
                  
                  {billingHistory.length > 0 ? (
                    billingHistory.map((item) => (
                      <div key={item.id} className="grid grid-cols-4 gap-4 p-4 text-sm border-b last:border-0">
                        <div>{item.transactionDate.toLocaleDateString()}</div>
                        <div>{item.description}</div>
                        <div>${item.amount.toFixed(2)} {item.currency}</div>
                        <div>
                          <span className="inline-block px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">
                            {item.status}
                          </span>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="p-4 text-center text-sm text-muted-foreground">
                      No billing history available
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="preferences">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Theme Preferences */}
            <Card>
              <CardHeader>
                <CardTitle>Appearance</CardTitle>
                <CardDescription>Customize your theme preferences</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <h3 className="text-sm font-medium mb-4">Theme</h3>
                  <div className="grid grid-cols-3 gap-4">
                    <Button 
                      variant={preferences.theme === 'light' ? "default" : "outline"}
                      className="justify-start"
                      onClick={() => handleThemeChange('light')}
                    >
                      <i className="ri-sun-line mr-2" />
                      Light
                    </Button>
                    <Button 
                      variant={preferences.theme === 'dark' ? "default" : "outline"}
                      className="justify-start"
                      onClick={() => handleThemeChange('dark')}
                    >
                      <i className="ri-moon-line mr-2" />
                      Dark
                    </Button>
                    <Button 
                      variant={preferences.theme === 'system' ? "default" : "outline"}
                      className="justify-start"
                      onClick={() => handleThemeChange('system')}
                    >
                      <i className="ri-computer-line mr-2" />
                      System
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Language Preferences */}
            <Card>
              <CardHeader>
                <CardTitle>Language</CardTitle>
                <CardDescription>Choose your preferred language</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <Label htmlFor="language">Select Language</Label>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline" className="w-full justify-between">
                        <span>{preferences.language === "english" ? "English" : preferences.language}</span>
                        <i className="ri-arrow-down-s-line" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent className="w-44">
                      <DropdownMenuItem onClick={() => handleLanguageChange("english")}>
                        English
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleLanguageChange("spanish")}>
                        Spanish
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleLanguageChange("french")}>
                        French
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleLanguageChange("german")}>
                        German
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleLanguageChange("japanese")}>
                        Japanese
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardContent>
            </Card>

            {/* Notification Preferences */}
            <Card className="md:col-span-2">
              <CardHeader>
                <CardTitle>Notifications</CardTitle>
                <CardDescription>Manage your notification preferences</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <h4 className="font-medium">Email Notifications</h4>
                      <p className="text-sm text-muted-foreground">
                        Receive notifications about your courses and account updates via email
                      </p>
                    </div>
                    <Switch 
                      checked={preferences.notifications.email}
                      onCheckedChange={() => handleNotificationChange("email")}
                    />
                  </div>
                  <Separator />
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <h4 className="font-medium">Push Notifications</h4>
                      <p className="text-sm text-muted-foreground">
                        Receive real-time notifications in your browser
                      </p>
                    </div>
                    <Switch 
                      checked={preferences.notifications.push}
                      onCheckedChange={() => handleNotificationChange("push")}
                    />
                  </div>
                  <Separator />
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <h4 className="font-medium">Marketing Communications</h4>
                      <p className="text-sm text-muted-foreground">
                        Receive updates about new features, promotions, and educational content
                      </p>
                    </div>
                    <Switch 
                      checked={preferences.notifications.marketing}
                      onCheckedChange={() => handleNotificationChange("marketing")}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="security">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Two-Factor Authentication */}
            <Card className="md:col-span-2">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Two-Factor Authentication</CardTitle>
                    <CardDescription>Add an extra layer of security to your account</CardDescription>
                  </div>
                  <Switch 
                    checked={twoFactorEnabled}
                    onCheckedChange={(checked) => {
                      setTwoFactorEnabled(checked);
                      setShow2FASetup(checked && !show2FASetup);
                      if (!checked) {
                        toast({
                          title: "2FA Disabled",
                          description: "Two-factor authentication has been disabled for your account.",
                        });
                      }
                    }}
                  />
                </div>
              </CardHeader>
              <CardContent>
                {show2FASetup ? (
                  <div className="space-y-4">
                    <div className="p-4 bg-amber-50 border border-amber-200 rounded-md text-amber-800">
                      <h3 className="text-sm font-medium mb-2">Set up two-factor authentication</h3>
                      <p className="text-sm">Scan the QR code with your authenticator app, then enter the verification code below.</p>
                    </div>
                    
                    <div className="flex justify-center p-4 bg-slate-50 border border-slate-200 rounded-md">
                      <div className="bg-white p-2 rounded">
                        {/* Placeholder for QR code */}
                        <div className="w-48 h-48 bg-slate-100 flex items-center justify-center">
                          <p className="text-sm text-center text-slate-500">QR Code Placeholder</p>
                        </div>
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="verification-code">Verification Code</Label>
                      <Input id="verification-code" placeholder="Enter 6-digit code" />
                    </div>
                    
                    <Button onClick={() => {
                      toast({
                        title: "2FA Enabled",
                        description: "Two-factor authentication has been enabled for your account.",
                      });
                      setShow2FASetup(false);
                    }}>
                      Verify and Enable
                    </Button>
                  </div>
                ) : (
                  <div>
                    <p className="text-sm text-muted-foreground mb-4">
                      {twoFactorEnabled 
                        ? "Two-factor authentication is enabled. You'll need to enter a verification code from your authenticator app when signing in."
                        : "Enable two-factor authentication to add an extra layer of security to your account. You'll need an authenticator app like Google Authenticator or Authy."}
                    </p>
                    
                    {twoFactorEnabled && (
                      <div className="mt-4">
                        <Button 
                          variant="outline" 
                          onClick={() => setShow2FASetup(true)}
                        >
                          Reconfigure 2FA
                        </Button>
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
            
            {/* Login Activity */}
            <Card className="md:col-span-3">
              <CardHeader>
                <CardTitle>Recent Login Activity</CardTitle>
                <CardDescription>Monitor where your account is being accessed from</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="border rounded-md">
                  <div className="grid grid-cols-4 gap-4 p-4 font-medium text-sm border-b">
                    <div>Date & Time</div>
                    <div>IP Address</div>
                    <div>Location</div>
                    <div>Device</div>
                  </div>
                  
                  {/* Mock login activity data */}
                  <div className="grid grid-cols-4 gap-4 p-4 text-sm border-b">
                    <div>{new Date().toLocaleString()}</div>
                    <div>***********</div>
                    <div>New York, USA</div>
                    <div>Chrome on Windows</div>
                  </div>
                  <div className="grid grid-cols-4 gap-4 p-4 text-sm border-b">
                    <div>{new Date(Date.now() - ********).toLocaleString()}</div>
                    <div>***********</div>
                    <div>New York, USA</div>
                    <div>Chrome on Windows</div>
                  </div>
                </div>
                
                <div className="mt-6 space-y-4">
                  <Button variant="outline">
                    <i className="ri-logout-box-line mr-2"></i>
                    Logout from All Devices
                  </Button>
                </div>
              </CardContent>
            </Card>
            
            {/* Account Actions */}
            <Card className="md:col-span-3 border-red-200">
              <CardHeader className="border-b border-red-100">
                <CardTitle className="text-red-600">Danger Zone</CardTitle>
                <CardDescription>Irreversible actions for your account</CardDescription>
              </CardHeader>
              <CardContent className="pt-6">
                <div className="space-y-4">
                  {showDeleteConfirm ? (
                    <div className="p-4 bg-red-50 border border-red-200 rounded-md">
                      <h3 className="text-lg font-semibold text-red-700 mb-2">Confirm Account Deletion</h3>
                      <p className="text-sm text-red-600 mb-4">
                        This action cannot be undone. All your data will be permanently removed.
                      </p>
                      <div className="flex space-x-3">
                        <Button 
                          variant="destructive"
                          onClick={() => {
                            toast({
                              title: "Account deleted",
                              description: "Your account has been successfully deleted.",
                            });
                            // In a real app, would redirect to logout
                          }}
                        >
                          Permanently Delete Account
                        </Button>
                        <Button 
                          variant="outline" 
                          onClick={() => setShowDeleteConfirm(false)}
                        >
                          Cancel
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="font-medium">Delete Account</h3>
                        <p className="text-sm text-muted-foreground">
                          Permanently delete your account and all associated data
                        </p>
                      </div>
                      <Button 
                        variant="destructive"
                        onClick={() => setShowDeleteConfirm(true)}
                      >
                        Delete Account
                      </Button>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}