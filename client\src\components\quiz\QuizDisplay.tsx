import { useState } from 'react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { 
  BookOpen, 
  Target, 
  CheckCircle2, 
  XCircle, 
  Eye, 
  Edit3,
  Clock,
  Star,
  Lightbulb,
  BarChart3,
  Trophy
} from 'lucide-react';

interface Quiz {
  id: string;
  moduleId: string;
  moduleTitle: string;
  questions: Array<{
    question: string;
    options?: string[];
    correctAnswer: string | number;
    explanation?: string;
    type?: string;
  }>;
  generatedAt: Date;
}

interface QuizDisplayProps {
  quizzes: Quiz[];
  onEditQuiz?: (quiz: Quiz) => void;
  onPreviewQuiz?: (quiz: Quiz) => void;
}

export default function QuizDisplay({ quizzes, onEditQuiz, onPreviewQuiz }: QuizDisplayProps) {
  const [activeQuiz, setActiveQuiz] = useState<string>('');
  const [previewMode, setPreviewMode] = useState(false);
  const [selectedAnswers, setSelectedAnswers] = useState<{[key: string]: any}>({});

  const totalQuestions = quizzes.reduce((sum, quiz) => sum + quiz.questions.length, 0);
  const averageQuestionsPerQuiz = quizzes.length > 0 ? Math.round(totalQuestions / quizzes.length) : 0;

  const handleAnswerSelect = (quizId: string, questionIndex: number, answer: any) => {
    setSelectedAnswers(prev => ({
      ...prev,
      [`${quizId}-${questionIndex}`]: answer
    }));
  };

  const getQuestionTypeIcon = (type?: string) => {
    switch (type?.toLowerCase()) {
      case 'multiple_choice': return <Target className="h-4 w-4" />;
      case 'true_false': return <CheckCircle2 className="h-4 w-4" />;
      case 'fill_blank': return <Edit3 className="h-4 w-4" />;
      default: return <BookOpen className="h-4 w-4" />;
    }
  };

  const formatQuestionType = (type?: string) => {
    if (!type) return 'Multiple Choice';
    return type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  if (quizzes.length === 0) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <Target className="h-16 w-16 mx-auto text-muted-foreground mb-4" />
          <h3 className="text-lg font-medium mb-2">No Quizzes Generated Yet</h3>
          <p className="text-muted-foreground">
            Use the "Generate All Quizzes" button to create quizzes for your modules
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Quiz Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <BookOpen className="h-5 w-5 text-blue-500" />
              <span className="text-sm font-medium">Total Quizzes</span>
            </div>
            <p className="text-2xl font-bold mt-1">{quizzes.length}</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Target className="h-5 w-5 text-green-500" />
              <span className="text-sm font-medium">Total Questions</span>
            </div>
            <p className="text-2xl font-bold mt-1">{totalQuestions}</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5 text-purple-500" />
              <span className="text-sm font-medium">Avg per Quiz</span>
            </div>
            <p className="text-2xl font-bold mt-1">{averageQuestionsPerQuiz}</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Trophy className="h-5 w-5 text-yellow-500" />
              <span className="text-sm font-medium">Status</span>
            </div>
            <p className="text-lg font-bold mt-1 text-green-600">Ready</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="overview">Quiz Overview</TabsTrigger>
          <TabsTrigger value="preview">Interactive Preview</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4">
            {quizzes.map((quiz) => (
              <Card key={quiz.id} className="border-l-4 border-l-blue-500">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="flex items-center gap-2">
                        <BookOpen className="h-5 w-5" />
                        {quiz.moduleTitle}
                      </CardTitle>
                      <CardDescription>
                        Generated {quiz.questions.length} questions • {new Date(quiz.generatedAt).toLocaleDateString()}
                      </CardDescription>
                    </div>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setPreviewMode(true)}
                      >
                        <Eye className="h-4 w-4 mr-2" />
                        Preview
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onEditQuiz?.(quiz)}
                      >
                        <Edit3 className="h-4 w-4 mr-2" />
                        Edit
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <Accordion type="single" collapsible>
                    <AccordionItem value="questions">
                      <AccordionTrigger>
                        <span className="flex items-center gap-2">
                          <Target className="h-4 w-4" />
                          View All Questions ({quiz.questions.length})
                        </span>
                      </AccordionTrigger>
                      <AccordionContent>
                        <div className="space-y-4">
                          {quiz.questions.map((question, index) => (
                            <div key={index} className="border rounded-lg p-4 bg-gray-50">
                              <div className="flex items-start justify-between mb-2">
                                <h4 className="font-medium text-gray-800">
                                  {index + 1}. {question.question}
                                </h4>
                                <div className="flex gap-1">
                                  {getQuestionTypeIcon(question.type)}
                                  <Badge variant="outline" className="text-xs">
                                    {formatQuestionType(question.type)}
                                  </Badge>
                                </div>
                              </div>
                              
                              {question.options && question.options.length > 0 && (
                                <div className="space-y-2 mb-3">
                                  {question.options.map((option, optIndex) => (
                                    <div key={optIndex} className="flex items-center gap-2">
                                      {question.correctAnswer === optIndex ? (
                                        <CheckCircle2 className="h-4 w-4 text-green-500" />
                                      ) : (
                                        <XCircle className="h-4 w-4 text-gray-300" />
                                      )}
                                      <span className={`text-sm ${
                                        question.correctAnswer === optIndex 
                                          ? 'text-green-700 font-medium' 
                                          : 'text-gray-600'
                                      }`}>
                                        {option}
                                      </span>
                                    </div>
                                  ))}
                                </div>
                              )}
                              
                              {question.explanation && (
                                <div className="bg-blue-50 p-3 rounded-md mt-3">
                                  <div className="flex items-start gap-2">
                                    <Lightbulb className="h-4 w-4 text-blue-600 mt-0.5" />
                                    <div>
                                      <p className="text-sm font-medium text-blue-800">Explanation</p>
                                      <p className="text-sm text-blue-700">{question.explanation}</p>
                                    </div>
                                  </div>
                                </div>
                              )}
                            </div>
                          ))}
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  </Accordion>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Preview Tab */}
        <TabsContent value="preview" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Eye className="h-5 w-5" />
                Interactive Quiz Preview
              </CardTitle>
              <CardDescription>
                Experience how students will see and interact with your quizzes
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Tabs value={activeQuiz || quizzes[0]?.id} onValueChange={setActiveQuiz}>
                <TabsList className="w-full">
                  {quizzes.map((quiz) => (
                    <TabsTrigger key={quiz.id} value={quiz.id} className="flex-1">
                      {quiz.moduleTitle}
                    </TabsTrigger>
                  ))}
                </TabsList>

                {quizzes.map((quiz) => (
                  <TabsContent key={quiz.id} value={quiz.id} className="space-y-6">
                    <div className="border-b pb-4">
                      <h2 className="text-xl font-bold">{quiz.moduleTitle} Quiz</h2>
                      <div className="flex gap-4 mt-2 text-sm text-muted-foreground">
                        <span className="flex items-center gap-1">
                          <Target className="h-4 w-4" />
                          {quiz.questions.length} questions
                        </span>
                        <span className="flex items-center gap-1">
                          <Star className="h-4 w-4" />
                          AI Generated
                        </span>
                      </div>
                    </div>
                    
                    <div className="space-y-6">
                      {quiz.questions.map((question, index) => (
                        <Card key={index} className="border-2 border-gray-200">
                          <CardContent className="p-6">
                            <div className="space-y-4">
                              <div className="flex items-start justify-between">
                                <h3 className="font-medium text-lg">
                                  {index + 1}. {question.question}
                                </h3>
                                <Badge variant="outline">
                                  {formatQuestionType(question.type)}
                                </Badge>
                              </div>
                              
                              {question.options && question.options.length > 0 && (
                                <div className="space-y-2">
                                  {question.options.map((option, optIndex) => (
                                    <div key={optIndex} className="flex items-center gap-2">
                                      <input
                                        type="radio"
                                        id={`${quiz.id}-${index}-${optIndex}`}
                                        name={`${quiz.id}-question-${index}`}
                                        value={optIndex}
                                        checked={selectedAnswers[`${quiz.id}-${index}`] === optIndex}
                                        onChange={() => handleAnswerSelect(quiz.id, index, optIndex)}
                                        className="text-blue-600"
                                      />
                                      <label
                                        htmlFor={`${quiz.id}-${index}-${optIndex}`}
                                        className="text-sm cursor-pointer"
                                      >
                                        {option}
                                      </label>
                                    </div>
                                  ))}
                                </div>
                              )}
                              
                              {question.type === 'fill_blank' && (
                                <input
                                  type="text"
                                  placeholder="Enter your answer..."
                                  className="w-full p-2 border rounded-md"
                                  value={selectedAnswers[`${quiz.id}-${index}`] || ''}
                                  onChange={(e) => handleAnswerSelect(quiz.id, index, e.target.value)}
                                />
                              )}
                              
                              {question.type === 'short_answer' && (
                                <textarea
                                  placeholder="Enter your answer..."
                                  rows={3}
                                  className="w-full p-2 border rounded-md"
                                  value={selectedAnswers[`${quiz.id}-${index}`] || ''}
                                  onChange={(e) => handleAnswerSelect(quiz.id, index, e.target.value)}
                                />
                              )}
                              
                              {selectedAnswers[`${quiz.id}-${index}`] !== undefined && question.explanation && (
                                <div className="bg-blue-50 p-4 rounded-md border border-blue-200">
                                  <div className="flex items-start gap-2">
                                    <Lightbulb className="h-4 w-4 text-blue-600 mt-0.5" />
                                    <div>
                                      <p className="text-sm font-medium text-blue-800">Explanation</p>
                                      <p className="text-sm text-blue-700">{question.explanation}</p>
                                    </div>
                                  </div>
                                </div>
                              )}
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </TabsContent>
                ))}
              </Tabs>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}