import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Label } from '@/components/ui/label';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useToast } from '@/hooks/use-toast';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { apiRequest, queryClient } from '@/lib/queryClient';
import { 
  Award, 
  Check, 
  Shield, 
  Youtube, 
  Video,
  AlertCircle,
  Plus,
  PlusCircle,
  LucideIcon,
  Loader2,
  ExternalLink,
  X,
  ArrowRight
} from 'lucide-react';
import { ColorfulIcon } from '@/components/ui/colorful-icon';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

// Types for platform/integration data
interface Platform {
  id: number;
  name: string;
  slug: string;
  iconUrl: string;
  category: string;
  description: string;
  authType: 'api_key' | 'oauth';
  apiBaseUrl: string;
  features: string[];
  enabled?: boolean;
  priority?: number;
  createdAt?: string;
}

interface Integration {
  id: number;
  userId: number;
  platformId: number;
  platform: string;
  platformUserId: string | null;
  accessToken: string | null;
  refreshToken: string | null;
  tokenExpiry: string | null;
  config: Record<string, any> | null;
  status: 'active' | 'expired' | 'revoked';
  createdAt: string;
  updatedAt: string;
  platformData?: Platform;
}

// Form schema for platform integration
const integrationFormSchema = z.object({
  platform: z.string(),
  apiKey: z.string().min(1, 'API Key is required'),
  channelId: z.string().optional(),
  accountName: z.string().optional(),
});

type IntegrationFormValues = z.infer<typeof integrationFormSchema>;

// Component to display a video platform card
const VideoPlatformCard: React.FC<{
  platform: Platform;
  integration?: Integration;
  onConnect: (platform: Platform) => void;
  onDisconnect: (integration: Integration) => void;
}> = ({ platform, integration, onConnect, onDisconnect }) => {
  const isConnected = !!integration;
  
  const getIcon = (): LucideIcon => {
    const iconPath = platform.iconUrl || '';
    if (iconPath.includes('youtube')) return Youtube;
    if (iconPath.includes('vimeo')) return Video;
    if (iconPath.includes('video')) return Video;
    if (platform.slug === 'youtube') return Youtube;
    if (platform.slug === 'vimeo') return Video;
    if (platform.slug === 'wistia') return Video;
    return Video;
  };
  
  const Icon = getIcon();
  
  return (
    <Card className="overflow-hidden transition-all hover:shadow-md">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="h-10 w-10 rounded-lg bg-slate-100 flex items-center justify-center">
              <ColorfulIcon 
                icon={Icon} 
                category="media" 
                size={20} 
              />
            </div>
            <div>
              <CardTitle className="text-lg">{platform.name}</CardTitle>
              <CardDescription>{platform.description.substring(0, 60)}...</CardDescription>
            </div>
          </div>
          
          {isConnected && (
            <Badge variant="outline" className="gap-1 border-green-500 text-green-600">
              <Check size={14} />
              Connected
            </Badge>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {platform.features && (
            <div className="flex flex-wrap gap-1.5">
              {platform.features.map((feature, i) => (
                <Badge key={i} variant="secondary" className="font-normal">
                  {feature}
                </Badge>
              ))}
            </div>
          )}
          
          <div className="flex justify-between pt-3 mt-3 border-t border-slate-100">
            <div className="flex-1">
              {isConnected ? (
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button variant="destructive" size="sm">
                      Disconnect
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Disconnect {platform.name}?</AlertDialogTitle>
                      <AlertDialogDescription>
                        This will remove the integration and all associated data. Your videos will remain on {platform.name}, but you won't be able to manage them from CourseAI.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                      <AlertDialogAction 
                        onClick={() => onDisconnect(integration)}
                        className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                      >
                        Disconnect
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              ) : (
                <Button onClick={() => onConnect(platform)} size="sm">
                  Connect
                </Button>
              )}
            </div>
            
            <a 
              href={platform.apiBaseUrl} 
              target="_blank" 
              rel="noopener noreferrer" 
              className="text-sm text-slate-500 hover:text-primary flex items-center gap-1"
            >
              Learn more <ExternalLink size={14} />
            </a>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

// Main Video Platforms page
export default function VideoPlatformsPage() {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [connectingPlatform, setConnectingPlatform] = useState<Platform | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  
  // Form setup
  const form = useForm<IntegrationFormValues>({
    resolver: zodResolver(integrationFormSchema),
    defaultValues: {
      platform: '',
      apiKey: '',
      channelId: '',
      accountName: '',
    }
  });
  
  // Fetch platforms and integrations data
  const { data: platforms, isLoading: platformsLoading } = useQuery({
    queryKey: ['/api/platforms'],
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
  
  const { data: integrations, isLoading: integrationsLoading } = useQuery({
    queryKey: ['/api/integrations'],
    staleTime: 60 * 1000, // 1 minute
  });
  
  // Create integration mutation
  const createIntegration = useMutation({
    mutationFn: async (data: IntegrationFormValues) => {
      const payload = {
        platform: data.platform,
        accessToken: data.apiKey, // Use accessToken since that's what the integration table expects
        ...data.channelId ? { channelId: data.channelId } : {},
        ...data.accountName ? { accountName: data.accountName } : {},
      };
      
      const response = await apiRequest('POST', '/api/integrations', payload);
      return response.json();
    },
    onSuccess: () => {
      toast({
        title: "Platform connected successfully",
        description: "You can now use this platform to publish your courses",
      });
      queryClient.invalidateQueries({ queryKey: ['/api/integrations'] });
      setIsDialogOpen(false);
      form.reset();
    },
    onError: (error: any) => {
      toast({
        title: "Failed to connect platform",
        description: error.message || "An error occurred",
        variant: "destructive",
      });
    }
  });
  
  // Delete integration mutation
  const deleteIntegration = useMutation({
    mutationFn: async (integrationId: number) => {
      await apiRequest('DELETE', `/api/integrations/${integrationId}`);
    },
    onSuccess: () => {
      toast({
        title: "Platform disconnected",
        description: "The integration has been removed",
      });
      queryClient.invalidateQueries({ queryKey: ['/api/integrations'] });
    },
    onError: (error: any) => {
      toast({
        title: "Failed to disconnect platform",
        description: error.message || "An error occurred",
        variant: "destructive",
      });
    }
  });
  
  const handleConnectPlatform = (platform: Platform) => {
    setConnectingPlatform(platform);
    form.setValue('platform', platform.slug);
    setIsDialogOpen(true);
  };
  
  const handleDisconnectPlatform = (integration: Integration) => {
    deleteIntegration.mutate(integration.id);
  };
  
  const onSubmit = (data: IntegrationFormValues) => {
    createIntegration.mutate(data);
  };
  
  // Filter platforms to show only video platforms
  const videoPlatforms = Array.isArray(platforms) 
    ? platforms.filter(p => p.category === 'video_platform')
    : [];
  
  // Map integrations to platforms
  const platformIntegrations = videoPlatforms.map(platform => {
    const integration = Array.isArray(integrations) 
      ? integrations.find(i => i.platform === platform.slug)
      : undefined;
    
    return {
      platform,
      integration
    };
  });
  
  const isLoading = platformsLoading || integrationsLoading;
  
  return (
    <div className="container py-6 max-w-5xl">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold mb-1">Video Platforms</h1>
          <p className="text-slate-500">Connect your video hosting platforms to publish and manage your course videos</p>
        </div>
      </div>
      
      {isLoading ? (
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {platformIntegrations.length > 0 ? (
            platformIntegrations.map(({ platform, integration }) => (
              <VideoPlatformCard
                key={platform.id}
                platform={platform}
                integration={integration}
                onConnect={handleConnectPlatform}
                onDisconnect={handleDisconnectPlatform}
              />
            ))
          ) : (
            <div className="col-span-full flex flex-col items-center justify-center p-10 text-center border rounded-lg border-dashed border-slate-300 bg-slate-50">
              <Video className="h-12 w-12 text-slate-400 mb-4" />
              <h3 className="text-lg font-medium">No Video Platforms Found</h3>
              <p className="text-slate-500 mt-1 mb-4">
                There are currently no video platforms available for integration.
              </p>
            </div>
          )}
        </div>
      )}
      
      {/* Platform Connection Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              Connect to {connectingPlatform?.name}
              {connectingPlatform && (
                <ColorfulIcon
                  icon={
                    connectingPlatform.slug === 'youtube' 
                      ? Youtube 
                      : connectingPlatform.slug === 'vimeo' 
                        ? Video
                        : Video
                  }
                  category="media"
                  size={18}
                />
              )}
            </DialogTitle>
            <DialogDescription>
              Enter your {connectingPlatform?.name} API credentials to connect your account.
            </DialogDescription>
          </DialogHeader>
          
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="apiKey"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>API Key</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter your API key" {...field} />
                    </FormControl>
                    <FormDescription>
                      You can find this in your {connectingPlatform?.name} developer dashboard.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              {connectingPlatform?.slug === 'youtube' && (
                <FormField
                  control={form.control}
                  name="channelId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Channel ID (optional)</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter your YouTube channel ID" {...field} />
                      </FormControl>
                      <FormDescription>
                        The channel ID where your videos will be published.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}
              
              {connectingPlatform?.slug === 'vimeo' && (
                <FormField
                  control={form.control}
                  name="accountName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Account Name (optional)</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter your Vimeo account name" {...field} />
                      </FormControl>
                      <FormDescription>
                        Your Vimeo account name for identification.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}
              
              <DialogFooter className="pt-2">
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={() => setIsDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button 
                  type="submit" 
                  disabled={createIntegration.isPending}
                >
                  {createIntegration.isPending && (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  )}
                  Connect Platform
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
      
      {/* Bottom section with tips */}
      <div className="mt-8 p-4 bg-blue-50 rounded-lg border border-blue-100">
        <h3 className="text-lg font-medium flex items-center gap-2 text-blue-700">
          <AlertCircle size={18} />
          Platform Integration Tips
        </h3>
        <div className="mt-2 text-sm text-blue-700">
          <ul className="pl-6 list-disc space-y-1">
            <li>Ensure your API keys have the appropriate permissions for video upload and management.</li>
            <li>For YouTube, use an OAuth 2.0 application with YouTube Data API v3 access.</li>
            <li>For Vimeo, create an API app in your developer dashboard and request upload access.</li>
            <li>Integration allows automatic publishing of course videos directly to these platforms.</li>
          </ul>
        </div>
      </div>
    </div>
  );
}