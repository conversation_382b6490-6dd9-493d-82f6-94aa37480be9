import { useState } from "react";
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Progress } from "@/components/ui/progress";
import { 
  Users, 
  MessageSquare, 
  CheckSquare, 
  Calendar, 
  FileText, 
  Clock, 
  Plus,
  Filter,
  Search,
  MoreVertical,
  Star,
  Eye,
  Edit,
  Share2
} from "lucide-react";
import { Input } from "@/components/ui/input";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

// Mock data for demonstration - in production this would come from your API
const mockProjects = [
  {
    id: 1,
    name: "Advanced React Course",
    description: "Comprehensive React course covering hooks, context, and performance",
    status: "in-progress",
    progress: 75,
    collaborators: [
      { id: 1, name: "<PERSON>", avatar: null, role: "Lead Creator" },
      { id: 2, name: "Mike Johnson", avatar: null, role: "Content Reviewer" },
      { id: 3, name: "Emma Davis", avatar: null, role: "Video Editor" }
    ],
    dueDate: "2024-02-15",
    priority: "high",
    tasksCompleted: 12,
    totalTasks: 16
  },
  {
    id: 2,
    name: "Python Data Science",
    description: "Learn data analysis and machine learning with Python",
    status: "review",
    progress: 90,
    collaborators: [
      { id: 4, name: "Dr. Alex Kumar", avatar: null, role: "Subject Expert" },
      { id: 5, name: "Lisa Park", avatar: null, role: "Course Designer" }
    ],
    dueDate: "2024-01-30",
    priority: "medium",
    tasksCompleted: 18,
    totalTasks: 20
  },
  {
    id: 3,
    name: "UI/UX Design Fundamentals",
    description: "Master the principles of user interface and experience design",
    status: "planning",
    progress: 25,
    collaborators: [
      { id: 6, name: "Jordan Smith", avatar: null, role: "Design Lead" },
      { id: 7, name: "Taylor Brown", avatar: null, role: "Content Writer" }
    ],
    dueDate: "2024-03-10",
    priority: "low",
    tasksCompleted: 3,
    totalTasks: 12
  }
];

const mockTasks = [
  {
    id: 1,
    title: "Create module 5 video script",
    project: "Advanced React Course",
    assignee: "Sarah Chen",
    priority: "high",
    status: "in-progress",
    dueDate: "2024-01-25",
    description: "Write detailed script for React hooks module"
  },
  {
    id: 2,
    title: "Review lesson assessments",
    project: "Python Data Science",
    assignee: "Dr. Alex Kumar",
    priority: "medium",
    status: "pending",
    dueDate: "2024-01-28",
    description: "Review and approve quiz questions for chapters 8-10"
  },
  {
    id: 3,
    title: "Design course thumbnails",
    project: "UI/UX Design Fundamentals",
    assignee: "Jordan Smith",
    priority: "low",
    status: "completed",
    dueDate: "2024-01-20",
    description: "Create engaging thumbnails for all course modules"
  }
];

const mockDiscussions = [
  {
    id: 1,
    title: "Course structure feedback",
    project: "Advanced React Course",
    author: "Mike Johnson",
    replies: 8,
    lastActivity: "2 hours ago",
    priority: "high"
  },
  {
    id: 2,
    title: "Video quality standards",
    project: "Python Data Science",
    author: "Lisa Park",
    replies: 3,
    lastActivity: "1 day ago",
    priority: "medium"
  },
  {
    id: 3,
    title: "Assignment difficulty levels",
    project: "UI/UX Design Fundamentals",
    author: "Taylor Brown",
    replies: 12,
    lastActivity: "3 days ago",
    priority: "low"
  }
];

export default function CollaborationHub() {
  const [selectedTab, setSelectedTab] = useState("projects");
  const [searchQuery, setSearchQuery] = useState("");

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      "in-progress": { variant: "default" as const, color: "bg-blue-500" },
      "review": { variant: "secondary" as const, color: "bg-yellow-500" },
      "planning": { variant: "outline" as const, color: "bg-gray-500" },
      "completed": { variant: "default" as const, color: "bg-green-500" },
      "pending": { variant: "destructive" as const, color: "bg-red-500" }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig["pending"];
    
    return (
      <Badge variant={config.variant}>
        <div className={`w-2 h-2 rounded-full ${config.color} mr-2`}></div>
        {status.replace("-", " ").toUpperCase()}
      </Badge>
    );
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high": return "text-red-600";
      case "medium": return "text-yellow-600";
      case "low": return "text-green-600";
      default: return "text-gray-600";
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="space-y-2">
          <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
            <Users className="h-8 w-8" />
            Collaboration Hub
          </h1>
          <p className="text-muted-foreground">
            Manage team projects, tasks, and discussions in one place
          </p>
        </div>
        <div className="flex items-center gap-3">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search projects, tasks..."
              className="pl-10 w-64"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            New Project
          </Button>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Active Projects</p>
                <p className="text-2xl font-bold">3</p>
              </div>
              <FileText className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Pending Tasks</p>
                <p className="text-2xl font-bold">7</p>
              </div>
              <CheckSquare className="h-8 w-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Team Members</p>
                <p className="text-2xl font-bold">7</p>
              </div>
              <Users className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Open Discussions</p>
                <p className="text-2xl font-bold">3</p>
              </div>
              <MessageSquare className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs value={selectedTab} onValueChange={setSelectedTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="projects">Projects</TabsTrigger>
          <TabsTrigger value="tasks">Tasks</TabsTrigger>
          <TabsTrigger value="discussions">Discussions</TabsTrigger>
        </TabsList>

        {/* Projects Tab */}
        <TabsContent value="projects" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {mockProjects.map((project) => (
              <Card key={project.id} className="hover:shadow-lg transition-shadow">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="space-y-1">
                      <CardTitle className="text-lg">{project.name}</CardTitle>
                      <div className="flex items-center gap-2">
                        {getStatusBadge(project.status)}
                        <Badge variant="outline" className={getPriorityColor(project.priority)}>
                          {project.priority.toUpperCase()}
                        </Badge>
                      </div>
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreVertical className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem>
                          <Eye className="h-4 w-4 mr-2" />
                          View Details
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Edit className="h-4 w-4 mr-2" />
                          Edit Project
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Share2 className="h-4 w-4 mr-2" />
                          Share
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-sm text-muted-foreground">{project.description}</p>
                  
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Progress</span>
                      <span>{project.progress}%</span>
                    </div>
                    <Progress value={project.progress} className="h-2" />
                  </div>

                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center gap-1">
                      <CheckSquare className="h-4 w-4" />
                      <span>{project.tasksCompleted}/{project.totalTasks} tasks</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Calendar className="h-4 w-4" />
                      <span>{project.dueDate}</span>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex -space-x-2">
                      {project.collaborators.slice(0, 3).map((collaborator) => (
                        <Avatar key={collaborator.id} className="h-6 w-6 border-2 border-white">
                          <AvatarImage src={collaborator.avatar || undefined} />
                          <AvatarFallback className="text-xs">
                            {collaborator.name.split(' ').map(n => n[0]).join('')}
                          </AvatarFallback>
                        </Avatar>
                      ))}
                      {project.collaborators.length > 3 && (
                        <div className="h-6 w-6 rounded-full bg-gray-200 border-2 border-white flex items-center justify-center">
                          <span className="text-xs text-gray-600">+{project.collaborators.length - 3}</span>
                        </div>
                      )}
                    </div>
                    <Button variant="outline" size="sm">
                      View Project
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Tasks Tab */}
        <TabsContent value="tasks" className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Recent Tasks</CardTitle>
                <div className="flex items-center gap-2">
                  <Button variant="outline" size="sm">
                    <Filter className="h-4 w-4 mr-2" />
                    Filter
                  </Button>
                  <Button size="sm">
                    <Plus className="h-4 w-4 mr-2" />
                    New Task
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockTasks.map((task) => (
                  <div key={task.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors">
                    <div className="flex-1 space-y-2">
                      <div className="flex items-center gap-3">
                        <h3 className="font-medium">{task.title}</h3>
                        {getStatusBadge(task.status)}
                        <Badge variant="outline" className={getPriorityColor(task.priority)}>
                          {task.priority.toUpperCase()}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground">{task.description}</p>
                      <div className="flex items-center gap-4 text-sm text-muted-foreground">
                        <span>Project: {task.project}</span>
                        <span>Assignee: {task.assignee}</span>
                        <div className="flex items-center gap-1">
                          <Clock className="h-4 w-4" />
                          <span>Due: {task.dueDate}</span>
                        </div>
                      </div>
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreVertical className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem>
                          <Edit className="h-4 w-4 mr-2" />
                          Edit Task
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Star className="h-4 w-4 mr-2" />
                          Mark Important
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Discussions Tab */}
        <TabsContent value="discussions" className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Team Discussions</CardTitle>
                <Button size="sm">
                  <Plus className="h-4 w-4 mr-2" />
                  Start Discussion
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockDiscussions.map((discussion) => (
                  <div key={discussion.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
                    <div className="flex-1 space-y-2">
                      <div className="flex items-center gap-3">
                        <h3 className="font-medium">{discussion.title}</h3>
                        <Badge variant="outline" className={getPriorityColor(discussion.priority)}>
                          {discussion.priority.toUpperCase()}
                        </Badge>
                      </div>
                      <div className="flex items-center gap-4 text-sm text-muted-foreground">
                        <span>Project: {discussion.project}</span>
                        <span>Started by: {discussion.author}</span>
                        <div className="flex items-center gap-1">
                          <MessageSquare className="h-4 w-4" />
                          <span>{discussion.replies} replies</span>
                        </div>
                        <span>Last activity: {discussion.lastActivity}</span>
                      </div>
                    </div>
                    <Button variant="ghost" size="sm">
                      <MessageSquare className="h-4 w-4 mr-2" />
                      Join Discussion
                    </Button>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}