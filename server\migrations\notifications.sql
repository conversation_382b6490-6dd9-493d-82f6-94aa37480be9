-- Create notification_types table
CREATE TABLE IF NOT EXISTS notification_types (
  id SERIAL PRIMARY KEY,
  type VA<PERSON><PERSON><PERSON>(255) NOT NULL UNIQUE,
  display_name VARCHAR(255) NOT NULL,
  description TEXT,
  icon_name VARCHAR(100),
  icon_color VARCHAR(100),
  importance VARCHAR(50) DEFAULT 'medium',
  category VARCHAR(50) DEFAULT 'system',
  is_default BOOLEAN DEFAULT false
);

-- Create notifications table
CREATE TABLE IF NOT EXISTS notifications (
  id SERIAL PRIMARY KEY,
  user_id INTEGER NOT NULL,
  type_id INTEGER,
  title VARCHAR(255) NOT NULL,
  message TEXT NOT NULL,
  link_url TEXT,
  is_read BOOLEAN DEFAULT false,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  expires_at TIMESTAMP,
  FOREIGN KEY (type_id) REFERENCES notification_types(id) ON DELETE SET NULL
);

-- Create user_notification_preferences table
CREATE TABLE IF NOT EXISTS user_notification_preferences (
  id SERIAL PRIMARY KEY,
  user_id INTEGER NOT NULL,
  type_id INTEGER NOT NULL,
  enabled BOOLEAN DEFAULT true,
  UNIQUE(user_id, type_id),
  FOREIGN KEY (type_id) REFERENCES notification_types(id) ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS notifications_user_id_idx ON notifications(user_id);
CREATE INDEX IF NOT EXISTS notifications_user_id_is_read_idx ON notifications(user_id, is_read);
CREATE INDEX IF NOT EXISTS notifications_expires_at_idx ON notifications(expires_at);