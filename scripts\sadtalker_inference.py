#!/usr/bin/env python3
"""
SadTalker Inference Script for Avatar Course Generation
Generates talking head videos from static images and audio
"""

import os
import sys
import argparse
import subprocess
from pathlib import Path

def run_sadtalker_inference(source_image, driven_audio, result_dir, enhancer='gfpgan', size=512):
    """
    Run SadTalker inference to generate talking head video
    
    Args:
        source_image: Path to source image
        driven_audio: Path to driving audio
        result_dir: Directory to save results
        enhancer: Face enhancer to use (gfpgan, RestoreFormer, codeformer)
        size: Output video size (256 or 512)
    """
    
    # Ensure result directory exists
    os.makedirs(result_dir, exist_ok=True)
    
    # SadTalker directory
    sadtalker_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'SadTalker')
    
    if not os.path.exists(sadtalker_dir):
        print(f"Error: SadTalker directory not found at {sadtalker_dir}")
        sys.exit(1)
    
    # Change to SadTalker directory
    original_cwd = os.getcwd()
    os.chdir(sadtalker_dir)
    
    try:
        # Build command
        cmd = [
            sys.executable, 'inference.py',
            '--driven_audio', driven_audio,
            '--source_image', source_image,
            '--result_dir', result_dir,
            '--still',
            '--preprocess', 'crop',
            '--size', str(size),
            '--enhancer', enhancer,
            '--bg_upsampler', 'realesrgan',
            '--face_upsample'
        ]
        
        print(f"Running SadTalker inference...")
        print(f"Command: {' '.join(cmd)}")
        
        # Run inference
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("SadTalker inference completed successfully")
            
            # Find generated video file
            for file in os.listdir(result_dir):
                if file.endswith('.mp4'):
                    video_path = os.path.join(result_dir, file)
                    print(f"Generated video: {video_path}")
                    return video_path
            
            print("Warning: No MP4 file found in result directory")
            return None
            
        else:
            print(f"SadTalker inference failed with return code {result.returncode}")
            print(f"STDOUT: {result.stdout}")
            print(f"STDERR: {result.stderr}")
            return None
            
    finally:
        # Restore original working directory
        os.chdir(original_cwd)

def main():
    parser = argparse.ArgumentParser(description='SadTalker Inference Script')
    parser.add_argument('--source_image', required=True, help='Path to source image')
    parser.add_argument('--driven_audio', required=True, help='Path to driving audio')
    parser.add_argument('--result_dir', required=True, help='Directory to save results')
    parser.add_argument('--enhancer', default='gfpgan', choices=['gfpgan', 'RestoreFormer', 'codeformer'], 
                       help='Face enhancer to use')
    parser.add_argument('--size', type=int, default=512, choices=[256, 512], 
                       help='Output video size')
    
    args = parser.parse_args()
    
    # Validate inputs
    if not os.path.exists(args.source_image):
        print(f"Error: Source image not found: {args.source_image}")
        sys.exit(1)
    
    if not os.path.exists(args.driven_audio):
        print(f"Error: Driving audio not found: {args.driven_audio}")
        sys.exit(1)
    
    # Run inference
    video_path = run_sadtalker_inference(
        args.source_image,
        args.driven_audio,
        args.result_dir,
        args.enhancer,
        args.size
    )
    
    if video_path:
        print(f"Success: Generated video at {video_path}")
        sys.exit(0)
    else:
        print("Error: Failed to generate video")
        sys.exit(1)

if __name__ == '__main__':
    main()