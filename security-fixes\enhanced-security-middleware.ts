/**
 * Enhanced Security Middleware for AILearnMaster
 * Implements comprehensive security controls for production deployment
 */

import express, { Request, Response, NextFunction } from 'express';
import rateLimit from 'express-rate-limit';
import helmet from 'helmet';
import csrf from 'csurf';
import cors from 'cors';
import { body, validationResult, param, query } from 'express-validator';
import crypto from 'crypto';
import { z } from 'zod';

// Security configuration interface
interface SecurityConfig {
  environment: 'development' | 'staging' | 'production';
  allowedOrigins: string[];
  rateLimits: {
    general: { windowMs: number; max: number };
    auth: { windowMs: number; max: number };
    ai: { windowMs: number; max: number };
    upload: { windowMs: number; max: number };
  };
  session: {
    maxAge: number;
    secure: boolean;
    sameSite: 'strict' | 'lax' | 'none';
  };
}

// Get security configuration based on environment
const getSecurityConfig = (): SecurityConfig => {
  const environment = (process.env.NODE_ENV as any) || 'development';
  
  const configs: Record<string, SecurityConfig> = {
    development: {
      environment: 'development',
      allowedOrigins: ['http://localhost:3000', 'http://localhost:5000'],
      rateLimits: {
        general: { windowMs: 15 * 60 * 1000, max: 1000 }, // 1000 requests per 15 minutes
        auth: { windowMs: 15 * 60 * 1000, max: 10 }, // 10 auth attempts per 15 minutes
        ai: { windowMs: 60 * 1000, max: 10 }, // 10 AI requests per minute
        upload: { windowMs: 60 * 1000, max: 5 } // 5 uploads per minute
      },
      session: {
        maxAge: 24 * 60 * 60 * 1000, // 24 hours
        secure: false,
        sameSite: 'lax'
      }
    },
    staging: {
      environment: 'staging',
      allowedOrigins: ['https://staging.ailearn.com'],
      rateLimits: {
        general: { windowMs: 15 * 60 * 1000, max: 500 },
        auth: { windowMs: 15 * 60 * 1000, max: 5 },
        ai: { windowMs: 60 * 1000, max: 5 },
        upload: { windowMs: 60 * 1000, max: 3 }
      },
      session: {
        maxAge: 12 * 60 * 60 * 1000, // 12 hours
        secure: true,
        sameSite: 'strict'
      }
    },
    production: {
      environment: 'production',
      allowedOrigins: process.env.ALLOWED_ORIGINS?.split(',') || ['https://ailearn.com'],
      rateLimits: {
        general: { windowMs: 15 * 60 * 1000, max: 300 },
        auth: { windowMs: 15 * 60 * 1000, max: 5 },
        ai: { windowMs: 60 * 1000, max: 3 },
        upload: { windowMs: 60 * 1000, max: 2 }
      },
      session: {
        maxAge: 8 * 60 * 60 * 1000, // 8 hours
        secure: true,
        sameSite: 'strict'
      }
    }
  };

  return configs[environment] || configs.development;
};

/**
 * Security Headers Middleware
 */
export const securityHeaders = () => {
  const config = getSecurityConfig();
  
  return helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
        fontSrc: ["'self'", "https://fonts.gstatic.com"],
        imgSrc: ["'self'", "data:", "https:", "blob:"],
        scriptSrc: ["'self'"],
        connectSrc: ["'self'", "https://api.openai.com", "https://api.mistral.ai"],
        mediaSrc: ["'self'", "blob:", "https:"],
        frameSrc: ["'none'"],
        objectSrc: ["'none'"],
        baseUri: ["'self'"],
        formAction: ["'self'"]
      }
    },
    hsts: {
      maxAge: 31536000, // 1 year
      includeSubDomains: true,
      preload: true
    },
    noSniff: true,
    frameguard: { action: 'deny' },
    xssFilter: true,
    referrerPolicy: { policy: 'strict-origin-when-cross-origin' }
  });
};

/**
 * CORS Configuration
 */
export const corsConfig = () => {
  const config = getSecurityConfig();
  
  return cors({
    origin: (origin, callback) => {
      // Allow requests with no origin (mobile apps, etc.)
      if (!origin) return callback(null, true);
      
      if (config.allowedOrigins.includes(origin)) {
        callback(null, true);
      } else {
        callback(new Error('Not allowed by CORS'));
      }
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: [
      'Origin',
      'X-Requested-With',
      'Content-Type',
      'Accept',
      'Authorization',
      'X-CSRF-Token'
    ],
    exposedHeaders: ['X-CSRF-Token'],
    maxAge: 86400 // 24 hours
  });
};

/**
 * Rate Limiting Middleware
 */
export const rateLimiters = {
  general: () => {
    const config = getSecurityConfig();
    return rateLimit({
      windowMs: config.rateLimits.general.windowMs,
      max: config.rateLimits.general.max,
      message: {
        error: 'Too many requests',
        retryAfter: Math.ceil(config.rateLimits.general.windowMs / 1000)
      },
      standardHeaders: true,
      legacyHeaders: false,
      skip: (req) => {
        // Skip rate limiting for health checks
        return req.path === '/api/health';
      }
    });
  },

  auth: () => {
    const config = getSecurityConfig();
    return rateLimit({
      windowMs: config.rateLimits.auth.windowMs,
      max: config.rateLimits.auth.max,
      message: {
        error: 'Too many authentication attempts',
        retryAfter: Math.ceil(config.rateLimits.auth.windowMs / 1000)
      },
      standardHeaders: true,
      legacyHeaders: false,
      keyGenerator: (req) => {
        // Rate limit by IP and user agent for auth endpoints
        return `${req.ip}-${req.get('User-Agent')}`;
      }
    });
  },

  ai: () => {
    const config = getSecurityConfig();
    return rateLimit({
      windowMs: config.rateLimits.ai.windowMs,
      max: config.rateLimits.ai.max,
      message: {
        error: 'AI service rate limit exceeded',
        retryAfter: Math.ceil(config.rateLimits.ai.windowMs / 1000)
      },
      standardHeaders: true,
      legacyHeaders: false,
      keyGenerator: (req) => {
        // Rate limit by user ID for AI endpoints
        return `ai-${req.user?.id || req.ip}`;
      }
    });
  },

  upload: () => {
    const config = getSecurityConfig();
    return rateLimit({
      windowMs: config.rateLimits.upload.windowMs,
      max: config.rateLimits.upload.max,
      message: {
        error: 'Upload rate limit exceeded',
        retryAfter: Math.ceil(config.rateLimits.upload.windowMs / 1000)
      },
      standardHeaders: true,
      legacyHeaders: false
    });
  }
};

/**
 * CSRF Protection
 */
export const csrfProtection = () => {
  return csrf({
    cookie: {
      httpOnly: true,
      secure: getSecurityConfig().environment === 'production',
      sameSite: 'strict'
    },
    ignoreMethods: ['GET', 'HEAD', 'OPTIONS']
  });
};

/**
 * Input Validation Schemas
 */
export const validationSchemas = {
  courseGeneration: [
    body('title')
      .isLength({ min: 3, max: 200 })
      .trim()
      .escape()
      .withMessage('Title must be 3-200 characters'),
    body('category')
      .isIn(['technology', 'business', 'education', 'health', 'science', 'arts'])
      .withMessage('Invalid category'),
    body('type')
      .isIn(['traditional', 'avatar'])
      .withMessage('Invalid course type'),
    body('avatarOptions.avatarConfig.sourceUrl')
      .optional()
      .isURL({ protocols: ['https'] })
      .withMessage('Avatar source must be HTTPS URL')
  ],

  userRegistration: [
    body('email')
      .isEmail()
      .normalizeEmail()
      .withMessage('Valid email required'),
    body('password')
      .isLength({ min: 8 })
      .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
      .withMessage('Password must be 8+ chars with uppercase, lowercase, number, and special character'),
    body('username')
      .isLength({ min: 3, max: 30 })
      .matches(/^[a-zA-Z0-9_-]+$/)
      .withMessage('Username must be 3-30 alphanumeric characters')
  ],

  fileUpload: [
    body('fileType')
      .isIn(['image', 'video', 'audio'])
      .withMessage('Invalid file type'),
    body('fileName')
      .matches(/^[a-zA-Z0-9._-]+$/)
      .withMessage('Invalid file name characters')
  ]
};

/**
 * Validation Error Handler
 */
export const handleValidationErrors = (req: Request, res: Response, next: NextFunction) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      details: errors.array().map(err => ({
        field: err.param,
        message: err.msg
      }))
    });
  }
  next();
};

/**
 * Secure File Upload Validation
 */
export const validateFileUpload = (req: Request, res: Response, next: NextFunction) => {
  if (!req.file) {
    return next();
  }

  const file = req.file;
  const allowedMimeTypes = {
    image: ['image/jpeg', 'image/png', 'image/webp'],
    video: ['video/mp4', 'video/webm', 'video/quicktime'],
    audio: ['audio/mpeg', 'audio/wav', 'audio/ogg']
  };

  // Validate MIME type
  const isValidMimeType = Object.values(allowedMimeTypes).flat().includes(file.mimetype);
  if (!isValidMimeType) {
    return res.status(400).json({ error: 'Invalid file type' });
  }

  // Validate file size (max 10MB)
  const maxSize = 10 * 1024 * 1024;
  if (file.size > maxSize) {
    return res.status(400).json({ error: 'File too large (max 10MB)' });
  }

  // Sanitize filename
  const sanitizedFilename = file.originalname.replace(/[^a-zA-Z0-9._-]/g, '');
  file.originalname = sanitizedFilename;

  next();
};

/**
 * Prompt Injection Protection
 */
export const sanitizeAIPrompt = (prompt: string): string => {
  // Remove potential injection patterns
  const dangerousPatterns = [
    /ignore\s+previous\s+instructions/gi,
    /system\s*:/gi,
    /assistant\s*:/gi,
    /human\s*:/gi,
    /<\s*script/gi,
    /javascript\s*:/gi,
    /data\s*:/gi,
    /vbscript\s*:/gi
  ];

  let sanitized = prompt;
  dangerousPatterns.forEach(pattern => {
    sanitized = sanitized.replace(pattern, '[FILTERED]');
  });

  // Limit length
  sanitized = sanitized.substring(0, 2000);

  // Remove excessive whitespace
  sanitized = sanitized.replace(/\s+/g, ' ').trim();

  return sanitized;
};

/**
 * AI Input Validation Middleware
 */
export const validateAIInput = (req: Request, res: Response, next: NextFunction) => {
  if (req.body.prompt) {
    req.body.prompt = sanitizeAIPrompt(req.body.prompt);
  }
  
  if (req.body.title) {
    req.body.title = sanitizeAIPrompt(req.body.title);
  }

  // Validate prompt length
  if (req.body.prompt && req.body.prompt.length < 10) {
    return res.status(400).json({ error: 'Prompt too short (minimum 10 characters)' });
  }

  next();
};

/**
 * Request ID Middleware for Security Tracking
 */
export const requestId = (req: Request, res: Response, next: NextFunction) => {
  const id = crypto.randomUUID();
  req.headers['x-request-id'] = id;
  res.setHeader('X-Request-ID', id);
  next();
};

/**
 * Security Logging Middleware
 */
export const securityLogger = (req: Request, res: Response, next: NextFunction) => {
  const startTime = Date.now();
  
  res.on('finish', () => {
    const duration = Date.now() - startTime;
    const logData = {
      requestId: req.headers['x-request-id'],
      method: req.method,
      url: req.url,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      userId: req.user?.id,
      statusCode: res.statusCode,
      duration,
      timestamp: new Date().toISOString()
    };

    // Log security-relevant events
    if (res.statusCode >= 400 || req.url.includes('/auth/') || req.url.includes('/api/ai/')) {
      console.log('SECURITY_LOG:', JSON.stringify(logData));
    }
  });

  next();
};

/**
 * Environment Validation
 */
export const validateEnvironment = () => {
  const requiredEnvVars = [
    'DATABASE_URL',
    'SESSION_SECRET',
    'NODE_ENV'
  ];

  const missing = requiredEnvVars.filter(envVar => !process.env[envVar]);
  
  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
  }

  // Validate session secret strength
  const sessionSecret = process.env.SESSION_SECRET;
  if (!sessionSecret || sessionSecret.length < 32) {
    throw new Error('SESSION_SECRET must be at least 32 characters long');
  }

  // Validate production settings
  if (process.env.NODE_ENV === 'production') {
    if (!process.env.ALLOWED_ORIGINS) {
      throw new Error('ALLOWED_ORIGINS must be set in production');
    }
    
    if (process.env.DATABASE_URL && !process.env.DATABASE_URL.includes('sslmode=require')) {
      console.warn('WARNING: Database SSL not enforced in production');
    }
  }
};

/**
 * Complete Security Middleware Setup
 */
export const setupSecurity = (app: express.Application) => {
  // Validate environment first
  validateEnvironment();

  // Request tracking
  app.use(requestId);
  app.use(securityLogger);

  // Security headers
  app.use(securityHeaders());

  // CORS
  app.use(corsConfig());

  // General rate limiting
  app.use(rateLimiters.general());

  // CSRF protection (exclude API endpoints that use other auth)
  app.use('/api/auth', csrfProtection());
  app.use('/api/course-generation', csrfProtection());

  console.log(`🔒 Security middleware configured for ${getSecurityConfig().environment} environment`);
};
