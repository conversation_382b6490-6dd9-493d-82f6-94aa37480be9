import express, { Request, Response, Router } from 'express';
import { z } from 'zod';
import { storage } from '../storage';
import { validateRequestBody } from '../middleware/validation';
import * as openai from '../services/openai';
import OpenAI from 'openai';

const router = Router();

// Authentication middleware
const authMiddleware = (req: Request, res: Response, next: Function) => {
  if (!req.session?.userId) {
    return res.status(401).json({ message: 'Unauthorized' });
  }
  next();
};

// Public routes (no authentication required)

// Get a landing page by slug (public access)
router.get('/public/landing-pages/:slug', async (req: Request, res: Response) => {
  try {
    const { slug } = req.params;
    
    // Get the landing page by slug
    const landingPage = await storage.getLandingPageBySlug(slug);
    
    if (!landingPage) {
      return res.status(404).json({ message: 'Landing page not found' });
    }
    
    // Only published landing pages are accessible publicly
    if (landingPage.status !== 'published') {
      return res.status(404).json({ message: 'Landing page not found' });
    }
    
    // Record the visit
    const visitorIp = req.ip || req.socket.remoteAddress || 'unknown';
    const userAgent = req.headers['user-agent'] || '';
    const referrer = req.headers.referer || '';
    
    // Extract UTM parameters from query string
    const { 
      utm_source, 
      utm_medium, 
      utm_campaign, 
      utm_content, 
      utm_term 
    } = req.query as Record<string, string>;
    
    // Record the visit
    await storage.recordLandingPageVisit({
      landingPageId: landingPage.id,
      visitorIp,
      userAgent,
      referrer,
      utmSource: utm_source,
      utmMedium: utm_medium,
      utmCampaign: utm_campaign,
      utmContent: utm_content,
      utmTerm: utm_term
    });
    
    return res.status(200).json(landingPage);
  } catch (error) {
    console.error('Get public landing page error:', error);
    return res.status(500).json({ message: 'Server error' });
  }
});

// Record a conversion for a landing page
router.post('/public/landing-pages/:slug/convert', async (req: Request, res: Response) => {
  try {
    const { slug } = req.params;
    const { visitId } = req.body;
    
    // Get the landing page by slug
    const landingPage = await storage.getLandingPageBySlug(slug);
    
    if (!landingPage) {
      return res.status(404).json({ message: 'Landing page not found' });
    }
    
    // Only published landing pages can have conversions
    if (landingPage.status !== 'published') {
      return res.status(404).json({ message: 'Landing page not found' });
    }
    
    // If visitId is provided, record the conversion for that specific visit
    if (visitId) {
      const result = await storage.recordLandingPageConversion(visitId);
      if (!result) {
        return res.status(404).json({ message: 'Visit not found' });
      }
    } else {
      // If no visitId, create a new visit and mark it as converted
      const visitorIp = req.ip || req.socket.remoteAddress || 'unknown';
      const userAgent = req.headers['user-agent'] || '';
      const referrer = req.headers.referer || '';
      
      // Record a new visit and immediately convert it
      const visit = await storage.recordLandingPageVisit({
        landingPageId: landingPage.id,
        visitorIp,
        userAgent,
        referrer,
      });
      
      await storage.recordLandingPageConversion(visit.id);
    }
    
    return res.status(200).json({ success: true });
  } catch (error) {
    console.error('Record landing page conversion error:', error);
    return res.status(500).json({ message: 'Server error' });
  }
});

// Apply authentication middleware to all other routes
router.use(authMiddleware);

// Validation schemas
const emailContentSchema = z.object({
  courseId: z.number().optional(),
  emailType: z.enum(['welcome', 'drip', 'promotion', 'announcement']),
  tone: z.enum(['professional', 'friendly', 'casual', 'urgent']).optional().default('professional'),
  customPrompt: z.string().optional(),
});

const generateTextSchema = z.object({
  fieldType: z.enum([
    'headline', 'subheadline', 'description', 'emailSubject',
    'emailContent', 'benefitPoint', 'testimonial', 'callToAction',
    'seoDescription', 'paragraph'
  ]),
  courseId: z.number().optional(),
  context: z.string().optional(),
  tone: z.enum(['professional', 'friendly', 'casual', 'urgent', 'persuasive']).optional(),
  length: z.enum(['short', 'medium', 'long']).optional(),
});

const landingPageSchema = z.object({
  courseId: z.number().optional(),
  style: z.enum(['modern', 'minimal', 'bold', 'creative']).optional().default('modern'),
  colorScheme: z.enum(['blue', 'green', 'purple', 'orange', 'neutral']).optional().default('blue'),
  customPrompt: z.string().optional(),
});

// Generate email content
router.post('/email/generate', validateRequestBody(emailContentSchema), async (req: Request, res: Response) => {
  try {
    const { courseId, emailType, tone, customPrompt } = req.body;
    const userId = req.session!.userId;
    
    // Get the course if courseId is provided
    let course = null;
    if (courseId) {
      course = await storage.getCourse(courseId);
      if (!course) {
        return res.status(404).json({ message: 'Course not found' });
      }
      if (course.userId !== userId) {
        return res.status(403).json({ message: 'Forbidden - you do not own this course' });
      }
    }
    
    // Generate email content
    const emailContent = await openai.generateEmailContent({
      courseInfo: course,
      emailType,
      tone,
      customPrompt,
    });
    
    return res.status(200).json({ emailContent });
  } catch (error) {
    console.error('Generate email content error:', error);
    return res.status(500).json({ message: 'Server error' });
  }
});

// Generate landing page content
router.post('/landing-page/generate', validateRequestBody(landingPageSchema), async (req: Request, res: Response) => {
  try {
    const { courseId, style, colorScheme, customPrompt } = req.body;
    const userId = req.session!.userId;
    
    // Get the course if courseId is provided
    let course = null;
    if (courseId) {
      course = await storage.getCourse(courseId);
      if (!course) {
        return res.status(404).json({ message: 'Course not found' });
      }
      if (course.userId !== userId) {
        return res.status(403).json({ message: 'Forbidden - you do not own this course' });
      }
    }
    
    // Generate landing page content
    const landingPageContent = await openai.generateLandingPageContent({
      courseInfo: course,
      style,
      colorScheme,
      customPrompt,
    });
    
    return res.status(200).json({ landingPageContent });
  } catch (error) {
    console.error('Generate landing page content error:', error);
    return res.status(500).json({ message: 'Server error' });
  }
});

// Get email templates
router.get('/email-templates', async (req: Request, res: Response) => {
  try {
    // For now, return predefined templates
    const templates = [
      {
        id: 1,
        name: 'Welcome Email',
        type: 'welcome',
        description: 'Send a warm welcome to new students',
        template: 'Welcome to {{course_name}}! We\'re excited to have you join us on this learning journey...',
      },
      {
        id: 2,
        name: 'Course Promotion',
        type: 'promotion',
        description: 'Promote your course to potential students',
        template: 'Discover the amazing {{course_name}} and take your skills to the next level...',
      },
      {
        id: 3,
        name: 'New Lesson Announcement',
        type: 'announcement',
        description: 'Announce new lessons or course updates',
        template: 'We\'ve just added new content to {{course_name}}! Check out the latest lessons...',
      },
      {
        id: 4,
        name: 'Follow-up Email',
        type: 'drip',
        description: 'Engage with students who haven\'t completed the course',
        template: 'We noticed you haven\'t completed {{course_name}} yet. Here\'s what you\'re missing...',
      },
    ];
    
    return res.status(200).json(templates);
  } catch (error) {
    console.error('Get email templates error:', error);
    return res.status(500).json({ message: 'Server error' });
  }
});

// Get landing page templates
router.get('/landing-page-templates', async (req: Request, res: Response) => {
  try {
    // For now, return predefined templates
    const templates = [
      {
        id: 1,
        name: 'Modern Professional',
        style: 'modern',
        colorScheme: 'blue',
        description: 'Clean, professional design with a focus on content',
        previewUrl: '/assets/landing-templates/modern-blue.jpg',
      },
      {
        id: 2,
        name: 'Bold Impact',
        style: 'bold',
        colorScheme: 'orange',
        description: 'High-contrast design that makes a statement',
        previewUrl: '/assets/landing-templates/bold-orange.jpg',
      },
      {
        id: 3,
        name: 'Minimal Elegance',
        style: 'minimal',
        colorScheme: 'neutral',
        description: 'Subtle, elegant design with plenty of whitespace',
        previewUrl: '/assets/landing-templates/minimal-neutral.jpg',
      },
      {
        id: 4,
        name: 'Creative Expression',
        style: 'creative',
        colorScheme: 'purple',
        description: 'Unique, artistic design to showcase creative courses',
        previewUrl: '/assets/landing-templates/creative-purple.jpg',
      },
    ];
    
    return res.status(200).json(templates);
  } catch (error) {
    console.error('Get landing page templates error:', error);
    return res.status(500).json({ message: 'Server error' });
  }
});

// Create a landing page
const createLandingPageSchema = z.object({
  title: z.string().min(3, 'Title must be at least 3 characters'),
  slug: z.string().min(3, 'Slug must be at least 3 characters').regex(/^[a-z0-9-]+$/, 'Slug can only contain lowercase letters, numbers, and hyphens'),
  headline: z.string().min(5, 'Headline must be at least 5 characters'),
  subheadline: z.string().min(5, 'Subheadline must be at least 5 characters'),
  description: z.string().min(20, 'Description must be at least 20 characters'),
  callToAction: z.string().min(2, 'Call to action text is required'),
  courseId: z.number().optional(),
  benefits: z.array(z.string()).default([]),
  sections: z.array(z.object({
    title: z.string(),
    content: z.string()
  })).default([]),
  testimonials: z.array(z.object({
    name: z.string(),
    role: z.string().optional(),
    content: z.string(),
    avatar: z.string().optional()
  })).default([]),
  pricing: z.object({
    regular: z.number().optional(),
    discount: z.number().optional(),
    currency: z.string().default('USD')
  }).optional(),
  theme: z.string().default('professional'),
  colorScheme: z.string().default('blue'),
  bannerImageUrl: z.string().optional(),
  customDomain: z.string().optional(),
  seoTitle: z.string().optional(),
  seoDescription: z.string().optional(),
  seoKeywords: z.string().optional(),
  googleAnalyticsId: z.string().optional(),
  facebookPixelId: z.string().optional()
});

router.post('/landing-pages', validateRequestBody(createLandingPageSchema), async (req: Request, res: Response) => {
  try {
    const userId = req.session!.userId;
    const landingPageData = req.body;
    
    // Check if the slug is already in use
    const existingPage = await storage.getLandingPageBySlug(landingPageData.slug);
    if (existingPage) {
      return res.status(400).json({ message: 'A landing page with this slug already exists' });
    }
    
    // If courseId is provided, check if the user owns the course
    if (landingPageData.courseId) {
      const course = await storage.getCourse(landingPageData.courseId);
      if (!course) {
        return res.status(404).json({ message: 'Course not found' });
      }
      if (course.userId !== userId) {
        return res.status(403).json({ message: 'Forbidden - you do not own this course' });
      }
    }
    
    // Create the landing page
    const newLandingPage = await storage.createLandingPage({
      ...landingPageData,
      userId
    });
    
    return res.status(201).json(newLandingPage);
  } catch (error) {
    console.error('Create landing page error:', error);
    return res.status(500).json({ message: 'Server error' });
  }
});

// Get all landing pages for the current user
router.get('/landing-pages', async (req: Request, res: Response) => {
  try {
    const userId = req.session!.userId;
    const landingPages = await storage.getLandingPagesByUserId(userId);
    return res.status(200).json(landingPages);
  } catch (error) {
    console.error('Get landing pages error:', error);
    return res.status(500).json({ message: 'Server error' });
  }
});

// Get a specific landing page
router.get('/landing-pages/:id', async (req: Request, res: Response) => {
  try {
    const userId = req.session!.userId;
    const landingPageId = parseInt(req.params.id);
    
    if (isNaN(landingPageId)) {
      return res.status(400).json({ message: 'Invalid landing page ID' });
    }
    
    const landingPage = await storage.getLandingPage(landingPageId);
    
    if (!landingPage) {
      return res.status(404).json({ message: 'Landing page not found' });
    }
    
    // Check if the user owns the landing page
    if (landingPage.userId !== userId) {
      return res.status(403).json({ message: 'Forbidden - you do not own this landing page' });
    }
    
    return res.status(200).json(landingPage);
  } catch (error) {
    console.error('Get landing page error:', error);
    return res.status(500).json({ message: 'Server error' });
  }
});

// Update a landing page
router.put('/landing-pages/:id', async (req: Request, res: Response) => {
  try {
    const userId = req.session!.userId;
    const landingPageId = parseInt(req.params.id);
    
    if (isNaN(landingPageId)) {
      return res.status(400).json({ message: 'Invalid landing page ID' });
    }
    
    const landingPage = await storage.getLandingPage(landingPageId);
    
    if (!landingPage) {
      return res.status(404).json({ message: 'Landing page not found' });
    }
    
    // Check if the user owns the landing page
    if (landingPage.userId !== userId) {
      return res.status(403).json({ message: 'Forbidden - you do not own this landing page' });
    }
    
    // If slug is changed, check if the new slug is already in use by another page
    if (req.body.slug && req.body.slug !== landingPage.slug) {
      const existingPage = await storage.getLandingPageBySlug(req.body.slug);
      if (existingPage && existingPage.id !== landingPageId) {
        return res.status(400).json({ message: 'A landing page with this slug already exists' });
      }
    }
    
    // Update the landing page
    const updatedLandingPage = await storage.updateLandingPage(landingPageId, req.body);
    
    return res.status(200).json(updatedLandingPage);
  } catch (error) {
    console.error('Update landing page error:', error);
    return res.status(500).json({ message: 'Server error' });
  }
});

// Publish a landing page
router.post('/landing-pages/:id/publish', async (req: Request, res: Response) => {
  try {
    const userId = req.session!.userId;
    const landingPageId = parseInt(req.params.id);
    
    if (isNaN(landingPageId)) {
      return res.status(400).json({ message: 'Invalid landing page ID' });
    }
    
    const landingPage = await storage.getLandingPage(landingPageId);
    
    if (!landingPage) {
      return res.status(404).json({ message: 'Landing page not found' });
    }
    
    // Check if the user owns the landing page
    if (landingPage.userId !== userId) {
      return res.status(403).json({ message: 'Forbidden - you do not own this landing page' });
    }
    
    // Publish the landing page
    const publishedLandingPage = await storage.publishLandingPage(landingPageId);
    
    return res.status(200).json(publishedLandingPage);
  } catch (error) {
    console.error('Publish landing page error:', error);
    return res.status(500).json({ message: 'Server error' });
  }
});

// Delete a landing page
router.delete('/landing-pages/:id', async (req: Request, res: Response) => {
  try {
    const userId = req.session!.userId;
    const landingPageId = parseInt(req.params.id);
    
    if (isNaN(landingPageId)) {
      return res.status(400).json({ message: 'Invalid landing page ID' });
    }
    
    const landingPage = await storage.getLandingPage(landingPageId);
    
    if (!landingPage) {
      return res.status(404).json({ message: 'Landing page not found' });
    }
    
    // Check if the user owns the landing page
    if (landingPage.userId !== userId) {
      return res.status(403).json({ message: 'Forbidden - you do not own this landing page' });
    }
    
    // Delete the landing page
    await storage.deleteLandingPage(landingPageId);
    
    return res.status(204).send();
  } catch (error) {
    console.error('Delete landing page error:', error);
    return res.status(500).json({ message: 'Server error' });
  }
});

// Get landing page analytics
router.get('/landing-pages/:id/analytics', async (req: Request, res: Response) => {
  try {
    const userId = req.session!.userId;
    const landingPageId = parseInt(req.params.id);
    
    if (isNaN(landingPageId)) {
      return res.status(400).json({ message: 'Invalid landing page ID' });
    }
    
    const landingPage = await storage.getLandingPage(landingPageId);
    
    if (!landingPage) {
      return res.status(404).json({ message: 'Landing page not found' });
    }
    
    // Check if the user owns the landing page
    if (landingPage.userId !== userId) {
      return res.status(403).json({ message: 'Forbidden - you do not own this landing page' });
    }
    
    // Get landing page visits
    const visits = await storage.getLandingPageVisits(landingPageId);
    
    // Calculate analytics
    const totalVisits = visits.length;
    const uniqueVisitors = new Set(visits.map(visit => visit.visitorIp).filter(Boolean)).size;
    const conversions = visits.filter(visit => visit.converted).length;
    const conversionRate = totalVisits > 0 ? (conversions / totalVisits) * 100 : 0;
    
    // Group visits by date for chart data
    const visitsPerDay = visits.reduce((acc, visit) => {
      const date = new Date(visit.timestamp).toISOString().split('T')[0];
      if (!acc[date]) {
        acc[date] = 0;
      }
      acc[date]++;
      return acc;
    }, {} as Record<string, number>);
    
    // Group conversions by date for chart data
    const conversionsPerDay = visits.filter(visit => visit.converted).reduce((acc, visit) => {
      // Use the regular timestamp, as we're updating it when converting
      const date = new Date(visit.timestamp).toISOString().split('T')[0];
      if (!acc[date]) {
        acc[date] = 0;
      }
      acc[date]++;
      return acc;
    }, {} as Record<string, number>);
    
    // Get the sources of traffic
    const sources = visits.reduce((acc, visit) => {
      const source = visit.referrer || 'Direct';
      if (!acc[source]) {
        acc[source] = 0;
      }
      acc[source]++;
      return acc;
    }, {} as Record<string, number>);
    
    return res.status(200).json({
      totalVisits,
      uniqueVisitors,
      conversions,
      conversionRate: Math.round(conversionRate * 100) / 100,
      visitsPerDay,
      conversionsPerDay,
      sources
    });
  } catch (error) {
    console.error('Get landing page analytics error:', error);
    return res.status(500).json({ message: 'Server error' });
  }
});

// Generate AI text for specific fields
router.post('/generate-text', validateRequestBody(generateTextSchema), async (req: Request, res: Response) => {
  try {
    const { fieldType, courseId, context, tone, length } = req.body;
    const userId = req.session!.userId;
    
    // Get the course if courseId is provided
    let course = null;
    if (courseId) {
      course = await storage.getCourse(courseId);
      if (!course) {
        return res.status(404).json({ message: 'Course not found' });
      }
      if (course.userId !== userId) {
        return res.status(403).json({ message: 'Forbidden - you do not own this course' });
      }
    }
    
    // Configure the prompt based on field type
    let prompt = 'Generate ';
    let maxLength = '';
    
    // Set length parameters based on the field type and requested length
    switch (length) {
      case 'short':
        maxLength = fieldType === 'emailContent' || fieldType === 'paragraph' ? '50-100 words' : '5-10 words';
        break;
      case 'medium':
        maxLength = fieldType === 'emailContent' || fieldType === 'paragraph' ? '150-200 words' : '10-15 words';
        break;
      case 'long':
        maxLength = fieldType === 'emailContent' || fieldType === 'paragraph' ? '300-500 words' : '15-25 words';
        break;
      default:
        maxLength = fieldType === 'emailContent' || fieldType === 'paragraph' ? '150-200 words' : '10-15 words';
    }
    
    // Build the prompt based on field type
    switch (fieldType) {
      case 'headline':
        prompt += `a compelling headline ${course ? `for a course titled "${course.title}"` : ''} that is ${maxLength} long`;
        break;
      case 'subheadline':
        prompt += `an engaging subheadline ${course ? `for a course titled "${course.title}"` : ''} that is ${maxLength} long`;
        break;
      case 'description':
        prompt += `a clear and informative description ${course ? `for a course titled "${course.title}"` : ''} that is ${maxLength} long`;
        break;
      case 'emailSubject':
        prompt += `a catchy email subject line ${course ? `for a course titled "${course.title}"` : ''} that is ${maxLength} long`;
        break;
      case 'emailContent':
        prompt += `engaging email content ${course ? `for a course titled "${course.title}"` : ''} that is ${maxLength} long`;
        break;
      case 'benefitPoint':
        prompt += `a compelling benefit point ${course ? `for a course titled "${course.title}"` : ''} that is ${maxLength} long`;
        break;
      case 'testimonial':
        prompt += `a realistic testimonial ${course ? `for a course titled "${course.title}"` : ''} that is ${maxLength} long`;
        break;
      case 'callToAction':
        prompt += `a persuasive call to action ${course ? `for a course titled "${course.title}"` : ''} that is ${maxLength} long`;
        break;
      case 'seoDescription':
        prompt += `an SEO-friendly description ${course ? `for a course titled "${course.title}"` : ''} that is ${maxLength} long`;
        break;
      case 'paragraph':
        prompt += `a well-structured paragraph ${course ? `for a course titled "${course.title}"` : ''} that is ${maxLength} long`;
        break;
      default:
        prompt += `text content ${course ? `for a course titled "${course.title}"` : ''} that is ${maxLength} long`;
    }
    
    // Add tone if provided
    if (tone) {
      prompt += ` with a ${tone} tone`;
    }
    
    // Add additional context if provided
    if (context) {
      prompt += `. Additional context: ${context}`;
    }
    
    // Use the OpenAI API to generate text
    const openAiService = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });
    
    const response = await openAiService.chat.completions.create({
      model: "gpt-4o",
      messages: [
        {
          role: "system",
          content: "You are an expert copywriter specializing in online education and marketing. Generate high-quality, engaging text based on the user's request."
        },
        { role: "user", content: prompt }
      ],
      temperature: 0.7,
      max_tokens: 500
    });
    
    // Extract the generated text
    const generatedText = response.choices[0].message.content?.trim() || '';
    
    return res.status(200).json({ text: generatedText });
  } catch (error) {
    console.error('Generate text error:', error);
    return res.status(500).json({ message: 'Server error' });
  }
});

export default router;