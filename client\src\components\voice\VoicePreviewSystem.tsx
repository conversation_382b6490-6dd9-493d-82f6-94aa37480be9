import React, { useState, useEffect, useRef } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Slider } from '@/components/ui/slider';
import { Label } from '@/components/ui/label';
import { 
  Play, 
  Pause, 
  Volume2, 
  Mic, 
  Sparkles, 
  Zap,
  Crown,
  Settings,
  RefreshCw
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';
import ChatterboxVoiceCard from './ChatterboxVoiceCard';

interface Voice {
  id: string;
  name: string;
  gender?: string;
  accent?: string;
  language?: string;
  description?: string;
  preview_url?: string;
  category?: string;
  provider: 'chatterbox' | 'openai' | 'elevenlabs';
  tier: 'free' | 'premium' | 'enterprise';
  tags?: string[];
}

interface VoicePreviewSystemProps {
  selectedVoice: string;
  onVoiceSelect: (voiceId: string, provider: string) => void;
  previewText?: string;
  onVoiceSettingsChange?: (settings: any) => void;
}

export default function VoicePreviewSystem({
  selectedVoice,
  onVoiceSelect,
  previewText = "Hello! This is a preview of how your voice will sound in your course content.",
  onVoiceSettingsChange
}: VoicePreviewSystemProps) {
  const { toast } = useToast();
  const audioRef = useRef<HTMLAudioElement | null>(null);
  
  const [activeProvider, setActiveProvider] = useState<'chatterbox' | 'openai' | 'elevenlabs'>('chatterbox');
  const [playingVoice, setPlayingVoice] = useState<string | null>(null);
  const [voiceSettings, setVoiceSettings] = useState({
    speed: 1.0,
    pitch: 1.0,
    volume: 1.0,
    stability: 0.5,
    similarity: 0.75
  });
  const [isGeneratingPreview, setIsGeneratingPreview] = useState(false);

  // Fetch voices for each provider
  const { data: chatterboxVoices, isLoading: chatterboxLoading } = useQuery({
    queryKey: ['/api/chatterbox-tts/voices'],
    select: (data: any) => data?.voices?.map((voice: any) => ({
      id: voice.id,
      name: voice.name,
      gender: voice.gender,
      accent: voice.accent,
      language: voice.language,
      description: voice.description,
      tier: voice.tier || 'enterprise',
      provider: 'chatterbox',
      tags: voice.tags || []
    })) || []
  });

  const { data: openaiVoices, isLoading: openaiLoading } = useQuery({
    queryKey: ['/api/ai/openai-voices'],
    select: (data: any) => data?.voices?.map((voice: any) => ({
      ...voice,
      provider: 'openai',
      tier: 'premium'
    })) || []
  });

  const { data: elevenlabsVoices, isLoading: elevenlabsLoading } = useQuery({
    queryKey: ['/api/ai/elevenlabs-voices'],
    select: (data: any) => data?.voices?.map((voice: any) => ({
      ...voice,
      provider: 'elevenlabs',
      tier: 'premium'
    })) || []
  });

  // Combine all voices
  const allVoices: Voice[] = [
    ...(chatterboxVoices || []),
    ...(openaiVoices || []),
    ...(elevenlabsVoices || [])
  ];

  const currentProviderVoices = allVoices.filter(voice => voice.provider === activeProvider);

  // Generate voice preview
  const generatePreview = async (voice: Voice) => {
    setIsGeneratingPreview(true);
    try {
      let endpoint = '';
      let payload: any = {
        text: previewText,
        voice: voice.id,
        ...voiceSettings
      };

      switch (voice.provider) {
        case 'chatterbox':
          endpoint = '/api/chatterbox-tts/generate';
          break;
        case 'openai':
          endpoint = '/api/ai/openai-tts';
          break;
        case 'elevenlabs':
          endpoint = '/api/ai/elevenlabs-tts';
          break;
      }

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      });

      const data = await response.json();

      if (data.audioUrl) {
        // Play the generated audio
        if (audioRef.current) {
          audioRef.current.src = data.audioUrl;
          audioRef.current.play();
          setPlayingVoice(voice.id);
        }
      }
    } catch (error) {
      console.error('Error generating voice preview:', error);
      toast({
        title: "Preview Error",
        description: "Failed to generate voice preview. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsGeneratingPreview(false);
    }
  };

  // Handle audio playback
  const togglePlayback = async (voice: Voice) => {
    if (playingVoice === voice.id) {
      audioRef.current?.pause();
      setPlayingVoice(null);
    } else {
      await generatePreview(voice);
    }
  };

  // Handle voice selection
  const handleVoiceSelect = (voice: Voice) => {
    onVoiceSelect(voice.id, voice.provider);
  };

  // Handle settings change
  useEffect(() => {
    if (onVoiceSettingsChange) {
      onVoiceSettingsChange(voiceSettings);
    }
  }, [voiceSettings, onVoiceSettingsChange]);

  const getTierIcon = (tier: string) => {
    switch (tier) {
      case 'enterprise':
        return <Crown className="h-3 w-3 text-purple-500" />;
      case 'premium':
        return <Sparkles className="h-3 w-3 text-blue-500" />;
      default:
        return <Zap className="h-3 w-3 text-green-500" />;
    }
  };

  const getTierColor = (tier: string) => {
    switch (tier) {
      case 'enterprise':
        return 'bg-purple-50 text-purple-700 border-purple-200';
      case 'premium':
        return 'bg-blue-50 text-blue-700 border-blue-200';
      default:
        return 'bg-green-50 text-green-700 border-green-200';
    }
  };

  return (
    <div className="space-y-6">
      <audio ref={audioRef} onEnded={() => setPlayingVoice(null)} />
      
      <Tabs value={activeProvider} onValueChange={(value) => setActiveProvider(value as any)}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="chatterbox" className="flex items-center gap-2">
            <Crown className="h-4 w-4" />
            Chatterbox TTS
            <Badge variant="outline" className="text-xs">A100</Badge>
          </TabsTrigger>
          <TabsTrigger value="openai" className="flex items-center gap-2">
            <Sparkles className="h-4 w-4" />
            OpenAI TTS
          </TabsTrigger>
          <TabsTrigger value="elevenlabs" className="flex items-center gap-2">
            <Mic className="h-4 w-4" />
            ElevenLabs
          </TabsTrigger>
        </TabsList>

        <TabsContent value="chatterbox" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Crown className="h-5 w-5 text-purple-500" />
                Chatterbox TTS Voices
                <Badge className="bg-purple-100 text-purple-800">Enterprise</Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {chatterboxLoading ? (
                  <div className="col-span-2 flex items-center justify-center py-8">
                    <RefreshCw className="h-6 w-6 animate-spin" />
                    <span className="ml-2">Loading voices...</span>
                  </div>
                ) : activeProvider === 'chatterbox' ? (
                  currentProviderVoices.map((voice) => (
                    <ChatterboxVoiceCard
                      key={voice.id}
                      voice={voice}
                      isSelected={selectedVoice === voice.id}
                      isPlaying={playingVoice === voice.id}
                      isLoading={isGeneratingPreview}
                      onSelect={() => handleVoiceSelect(voice)}
                      onTogglePlayback={() => togglePlayback(voice)}
                    />
                  ))
                ) : (
                  currentProviderVoices.map((voice) => (
                    <Card 
                      key={voice.id} 
                      className={`cursor-pointer transition-all hover:shadow-md ${
                        selectedVoice === voice.id ? 'ring-2 ring-purple-500' : ''
                      }`}
                      onClick={() => handleVoiceSelect(voice)}
                    >
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-2">
                            <h4 className="font-medium">{voice.name}</h4>
                            {getTierIcon(voice.tier)}
                          </div>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={(e) => {
                              e.stopPropagation();
                              togglePlayback(voice);
                            }}
                            disabled={isGeneratingPreview}
                          >
                            {playingVoice === voice.id ? (
                              <Pause className="h-4 w-4" />
                            ) : (
                              <Play className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                        <div className="flex flex-wrap gap-1 mb-2">
                          {voice.gender && (
                            <Badge variant="outline" className="text-xs">
                              {voice.gender}
                            </Badge>
                          )}
                          {voice.accent && (
                            <Badge variant="secondary" className="text-xs">
                              {voice.accent}
                            </Badge>
                          )}
                          {voice.language && (
                            <Badge variant="outline" className="text-xs">
                              {voice.language}
                            </Badge>
                          )}
                        </div>
                        {voice.description && (
                          <p className="text-sm text-gray-600">{voice.description}</p>
                        )}
                      </CardContent>
                    </Card>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="openai" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Sparkles className="h-5 w-5 text-blue-500" />
                OpenAI TTS Voices
                <Badge className="bg-blue-100 text-blue-800">Premium</Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {openaiLoading ? (
                  <div className="col-span-2 flex items-center justify-center py-8">
                    <RefreshCw className="h-6 w-6 animate-spin" />
                    <span className="ml-2">Loading voices...</span>
                  </div>
                ) : (
                  currentProviderVoices.map((voice) => (
                    <Card 
                      key={voice.id} 
                      className={`cursor-pointer transition-all hover:shadow-md ${
                        selectedVoice === voice.id ? 'ring-2 ring-blue-500' : ''
                      }`}
                      onClick={() => handleVoiceSelect(voice)}
                    >
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-2">
                            <h4 className="font-medium">{voice.name}</h4>
                            {getTierIcon(voice.tier)}
                          </div>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={(e) => {
                              e.stopPropagation();
                              togglePlayback(voice);
                            }}
                            disabled={isGeneratingPreview}
                          >
                            {playingVoice === voice.id ? (
                              <Pause className="h-4 w-4" />
                            ) : (
                              <Play className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                        <div className="flex flex-wrap gap-1 mb-2">
                          {voice.gender && (
                            <Badge variant="outline" className="text-xs">
                              {voice.gender}
                            </Badge>
                          )}
                          {voice.accent && (
                            <Badge variant="secondary" className="text-xs">
                              {voice.accent}
                            </Badge>
                          )}
                          {voice.language && (
                            <Badge variant="outline" className="text-xs">
                              {voice.language}
                            </Badge>
                          )}
                        </div>
                        {voice.description && (
                          <p className="text-sm text-gray-600">{voice.description}</p>
                        )}
                      </CardContent>
                    </Card>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="elevenlabs" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Mic className="h-5 w-5 text-green-500" />
                ElevenLabs Voices
                <Badge className="bg-green-100 text-green-800">Premium</Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {elevenlabsLoading ? (
                  <div className="col-span-2 flex items-center justify-center py-8">
                    <RefreshCw className="h-6 w-6 animate-spin" />
                    <span className="ml-2">Loading voices...</span>
                  </div>
                ) : (
                  currentProviderVoices.map((voice) => (
                    <Card 
                      key={voice.id} 
                      className={`cursor-pointer transition-all hover:shadow-md ${
                        selectedVoice === voice.id ? 'ring-2 ring-green-500' : ''
                      }`}
                      onClick={() => handleVoiceSelect(voice)}
                    >
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-2">
                            <h4 className="font-medium">{voice.name}</h4>
                            {getTierIcon(voice.tier)}
                          </div>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={(e) => {
                              e.stopPropagation();
                              togglePlayback(voice);
                            }}
                            disabled={isGeneratingPreview}
                          >
                            {playingVoice === voice.id ? (
                              <Pause className="h-4 w-4" />
                            ) : (
                              <Play className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                        <div className="flex flex-wrap gap-1 mb-2">
                          {voice.gender && (
                            <Badge variant="outline" className="text-xs">
                              {voice.gender}
                            </Badge>
                          )}
                          {voice.accent && (
                            <Badge variant="secondary" className="text-xs">
                              {voice.accent}
                            </Badge>
                          )}
                          {voice.language && (
                            <Badge variant="outline" className="text-xs">
                              {voice.language}
                            </Badge>
                          )}
                        </div>
                        {voice.description && (
                          <p className="text-sm text-gray-600">{voice.description}</p>
                        )}
                      </CardContent>
                    </Card>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Voice Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Voice Settings
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Speed: {voiceSettings.speed}x</Label>
              <Slider
                value={[voiceSettings.speed]}
                onValueChange={([value]) => setVoiceSettings(prev => ({ ...prev, speed: value }))}
                min={0.5}
                max={2.0}
                step={0.1}
                className="w-full"
              />
            </div>

            <div className="space-y-2">
              <Label>Pitch: {voiceSettings.pitch}x</Label>
              <Slider
                value={[voiceSettings.pitch]}
                onValueChange={([value]) => setVoiceSettings(prev => ({ ...prev, pitch: value }))}
                min={0.5}
                max={2.0}
                step={0.1}
                className="w-full"
              />
            </div>

            <div className="space-y-2">
              <Label>Volume: {Math.round(voiceSettings.volume * 100)}%</Label>
              <Slider
                value={[voiceSettings.volume]}
                onValueChange={([value]) => setVoiceSettings(prev => ({ ...prev, volume: value }))}
                min={0.1}
                max={1.0}
                step={0.1}
                className="w-full"
              />
            </div>

            <div className="space-y-2">
              <Label>Stability: {Math.round(voiceSettings.stability * 100)}%</Label>
              <Slider
                value={[voiceSettings.stability]}
                onValueChange={([value]) => setVoiceSettings(prev => ({ ...prev, stability: value }))}
                min={0.0}
                max={1.0}
                step={0.05}
                className="w-full"
              />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}