import React, { useState, useEffect } from "react";
import { CourseCreator } from "./CourseCreator";
import { CourseFormatSelector } from "./CourseFormatSelector";
import { TalkingAvatarGenerator } from "./TalkingAvatarGenerator";
import { AvatarCourseCreator } from "./AvatarCourseCreator";

// Utility functions to store and retrieve user's format preference
const formatStorageKey = "course_creator_format_preference";

const saveFormatPreference = (format: string) => {
  try {
    localStorage.setItem(formatStorageKey, format);
  } catch (e) {
    console.error("Could not save format preference to localStorage:", e);
  }
};

const getFormatPreference = (): string | null => {
  try {
    return localStorage.getItem(formatStorageKey);
  } catch (e) {
    console.error("Could not get format preference from localStorage:", e);
    return null;
  }
};

interface CourseCreationFlowProps {
  onClose: () => void;
  mode?: "create" | "edit";
  courseId?: number | null;
}

export function CourseCreationFlow({ onClose, mode = "create", courseId = null }: CourseCreationFlowProps) {
  // Get the saved format preference, if it exists
  const savedFormat = mode === "create" ? getFormatPreference() : null;
  
  // When in edit mode or if the user has a saved preference, we don't show the format selector initially
  const [showFormatSelector, setShowFormatSelector] = useState(mode === "create" && !savedFormat);
  
  // In edit mode, assume there's already a format selected
  // In create mode with a saved preference, use the saved format
  const [selectedFormat, setSelectedFormat] = useState<string | null>(
    mode === "edit" ? "traditional" : savedFormat
  );
  
  // State for simple avatar generator (legacy path)
  const [showSimpleAvatarGenerator, setShowSimpleAvatarGenerator] = useState(false);
  const [avatarGenerationCompleted, setAvatarGenerationCompleted] = useState(false);
  const [generatedAvatarData, setGeneratedAvatarData] = useState<any>(null);
  
  // State for comprehensive avatar course creation
  const [showAvatarCourseCreator, setShowAvatarCourseCreator] = useState(false);
  
  // Initialize avatar course creator if the user already selected it
  useEffect(() => {
    if (selectedFormat === "avatar" && !showAvatarCourseCreator && !showSimpleAvatarGenerator) {
      // For avatar format with saved preference, show the comprehensive avatar course creator immediately
      setShowAvatarCourseCreator(true);
    }
  }, [selectedFormat, showAvatarCourseCreator, showSimpleAvatarGenerator]);
  
  // Effect to handle automatic closing when format selector is closed without selection
  useEffect(() => {
    if (!showFormatSelector && !selectedFormat && !showSimpleAvatarGenerator && !showAvatarCourseCreator) {
      onClose();
    }
  }, [showFormatSelector, selectedFormat, showSimpleAvatarGenerator, showAvatarCourseCreator, onClose]);
  
  // Handle format selection
  const handleSelectFormat = (format: string) => {
    setSelectedFormat(format);
    setShowFormatSelector(false);
    
    // Save the user's format preference for future course creations
    saveFormatPreference(format);
    
    if (format === "avatar") {
      // For avatar format, show the comprehensive avatar course creator
      setShowAvatarCourseCreator(true);
    }
  };
  
  // Legacy handler for simple avatar generation completion
  const handleSimpleAvatarGenerationNext = (generatedData: { videoUrl: string, videoTitle: string }) => {
    setGeneratedAvatarData(generatedData);
    setAvatarGenerationCompleted(true);
    setShowSimpleAvatarGenerator(false);
  };
  
  // Legacy handler for simple avatar generation cancel/back
  const handleSimpleAvatarGenerationPrevious = () => {
    setShowSimpleAvatarGenerator(false);
    setShowFormatSelector(true);
  };
  
  // Allow users to change the format
  const handleChangeFormat = () => {
    setSelectedFormat(null);
    setShowFormatSelector(true);
    setShowAvatarCourseCreator(false);
  };

  return (
    <>
      {/* Format selector dialog */}
      <CourseFormatSelector
        open={showFormatSelector}
        onOpenChange={setShowFormatSelector}
        onSelectFormat={handleSelectFormat}
      />
      
      {/* Comprehensive Avatar Course Creator (new improved flow) */}
      {showAvatarCourseCreator && (
        <div>
          {/* Format change option */}
          {mode === "create" && (
            <div className="absolute top-4 right-12 z-10">
              <button 
                onClick={handleChangeFormat}
                className="text-xs text-muted-foreground hover:text-primary flex items-center gap-1"
              >
                <span>Change Format</span>
              </button>
            </div>
          )}
          
          <AvatarCourseCreator
            onClose={() => {
              setShowAvatarCourseCreator(false);
              onClose();
            }}
            mode={mode}
            courseId={courseId || undefined}
          />
        </div>
      )}
      
      {/* Legacy Simple Avatar generator (kept for backward compatibility) */}
      {showSimpleAvatarGenerator && (
        <div className="fixed inset-0 bg-background z-50 overflow-auto">
          <div className="container mx-auto py-8">
            <TalkingAvatarGenerator
              onVideoGenerated={(videoUrl, videoTitle) => {
                handleSimpleAvatarGenerationNext({ videoUrl, videoTitle });
              }}
              onCancel={handleSimpleAvatarGenerationPrevious}
            />
          </div>
        </div>
      )}
      
      {/* Traditional Course creator */}
      {(selectedFormat && selectedFormat !== "avatar") || (selectedFormat === "avatar" && avatarGenerationCompleted) ? (
        <div>
          {/* Format change option */}
          {mode === "create" && (
            <div className="absolute top-4 right-12 z-10">
              <button 
                onClick={handleChangeFormat}
                className="text-xs text-muted-foreground hover:text-primary flex items-center gap-1"
              >
                <span>Change Format</span>
              </button>
            </div>
          )}
          
          <CourseCreator 
            onClose={onClose} 
            mode={mode}
            initialFormat={selectedFormat || undefined}
            avatarData={selectedFormat === "avatar" ? generatedAvatarData : undefined}
            courseId={courseId}
          />
        </div>
      ) : null}
    </>
  );
}