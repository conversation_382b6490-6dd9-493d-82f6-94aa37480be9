import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import crypto from 'crypto';
import { User } from '@shared/schema';
import { getSecret } from '../security-fixes/secure-secrets-manager';

// Use secure secrets manager for JWT secret
const JWT_SECRET = getSecret('JWT_SECRET') || getSecret('SESSION_SECRET') || process.env.SESSION_SECRET;
if (!JWT_SECRET || JWT_SECRET.length < 32) {
  throw new Error('JWT_SECRET must be at least 32 characters long');
}
const SALT_ROUNDS = 12; // Increased from 10 for better security

/**
 * Authentication Service
 * Handles password hashing, JWT token generation, and verification
 */
export class AuthService {
  /**
   * Hash a password using bcrypt
   */
  static async hashPassword(password: string): Promise<string> {
    try {
      const salt = await bcrypt.genSalt(SALT_ROUNDS);
      return bcrypt.hash(password, salt);
    } catch (error) {
      console.error('Error hashing password:', error);
      throw new Error('Authentication service failure');
    }
  }

  /**
   * Compare a plain text password with a hashed password
   */
  static async comparePassword(password: string, hashedPassword: string): Promise<boolean> {
    try {
      return await bcrypt.compare(password, hashedPassword);
    } catch (error) {
      console.error('Error comparing passwords:', error);
      // Handle password comparison error more gracefully
      // If the error is due to invalid password format, return false
      // Otherwise, throw the error to trigger proper error handling
      if (error instanceof Error && error.message.includes('Invalid password format')) {
        return false;
      }
      throw new Error('Authentication service error');
    }
  }

  /**
   * Generate a JWT token for a user
   */
  static generateToken(user: User): string {
    try {
      const payload = {
        id: user.id,
        username: user.username,
        email: user.email,
        role: user.role
      };

      return jwt.sign(payload, JWT_SECRET, { expiresIn: '7d' });
    } catch (error) {
      console.error('Error generating token:', error);
      throw new Error('Failed to generate authentication token');
    }
  }

  /**
   * Verify a JWT token
   */
  static verifyToken(token: string): any {
    try {
      return jwt.verify(token, JWT_SECRET);
    } catch (error) {
      console.error('Token verification failed:', error);
      return null;
    }
  }

  /**
   * Generate a random token for email verification or password reset
   */
  static generateRandomToken(): string {
    try {
      return crypto.randomBytes(32).toString('hex');
    } catch (error) {
      console.error('Error generating random token:', error);
      // Fallback to a simpler method if crypto fails
      return Math.random().toString(36).substring(2) + 
             Date.now().toString(36) +
             Math.random().toString(36).substring(2);
    }
  }

  /**
   * Create a safe user object (without password) for client response
   */
  static createSafeUser(user: User): Omit<User, 'password' | 'verificationToken' | 'resetPasswordToken' | 'resetPasswordExpiry'> {
    if (!user) {
      console.error('Attempted to create safe user from undefined user');
      throw new Error('Invalid user data');
    }
    
    const { 
      password, 
      verificationToken, 
      resetPasswordToken, 
      resetPasswordExpiry, 
      ...safeUser 
    } = user;
    return safeUser;
  }
}