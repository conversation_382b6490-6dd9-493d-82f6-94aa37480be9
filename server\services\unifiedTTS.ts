import OpenAI from 'openai';
import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// Ensure uploads directory exists
const uploadsDir = path.join(process.cwd(), 'uploads', 'audio');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

export interface Voice {
  id: string;
  name: string;
  gender: 'male' | 'female';
  description: string;
  language: string;
  accent?: string;
  service: 'neural' | 'elevenlabs';
}

export interface TTSRequest {
  text: string;
  voice: string;
  speed: number;
  service: 'neural' | 'elevenlabs';
  quality?: 'standard' | 'hd';
}

export interface TTSResponse {
  success: boolean;
  audioUrl?: string;
  fileName?: string;
  duration?: number;
  service: string;
  error?: string;
}

// OpenAI TTS voices with comprehensive language support
export const openAIVoices: Voice[] = [
  {
    id: 'alloy',
    name: 'Alloy',
    gender: 'female',
    description: 'Balanced and warm tone',
    language: 'Multilingual',
    accent: 'Neutral',
    service: 'neural'
  },
  {
    id: 'echo',
    name: 'Echo',
    gender: 'male',
    description: 'Clear and professional',
    language: 'Multilingual',
    accent: 'Neutral',
    service: 'neural'
  },
  {
    id: 'fable',
    name: 'Fable',
    gender: 'male',
    description: 'Storytelling and engaging',
    language: 'Multilingual',
    accent: 'Neutral',
    service: 'neural'
  },
  {
    id: 'onyx',
    name: 'Onyx',
    gender: 'male',
    description: 'Deep and authoritative',
    language: 'Multilingual',
    accent: 'Neutral',
    service: 'neural'
  },
  {
    id: 'nova',
    name: 'Nova',
    gender: 'female',
    description: 'Friendly and conversational',
    language: 'Multilingual',
    accent: 'Neutral',
    service: 'neural'
  },
  {
    id: 'shimmer',
    name: 'Shimmer',
    gender: 'female',
    description: 'Energetic and bright',
    language: 'Multilingual',
    accent: 'Neutral',
    service: 'neural'
  }
];

export class UnifiedTTSService {
  async generateSpeech(request: TTSRequest): Promise<TTSResponse> {
    if (request.service === 'neural') {
      return this.generateOpenAISpeech(request);
    } else if (request.service === 'elevenlabs') {
      return this.generateElevenLabsSpeech(request);
    }
    
    throw new Error('Unsupported TTS service');
  }

  private async generateOpenAISpeech(request: TTSRequest): Promise<TTSResponse> {
    try {
      if (!process.env.OPENAI_API_KEY) {
        return {
          success: false,
          service: 'neural',
          error: 'Neural Voice service requires API configuration'
        };
      }

      const fileName = `neural-voice-${uuidv4()}.mp3`;
      const filePath = path.join(uploadsDir, fileName);

      const model = request.quality === 'hd' ? 'tts-1-hd' : 'tts-1';

      const mp3 = await openai.audio.speech.create({
        model: model,
        voice: request.voice as any,
        input: request.text,
        speed: request.speed,
      });

      const buffer = Buffer.from(await mp3.arrayBuffer());
      fs.writeFileSync(filePath, buffer);

      // Calculate duration estimate
      const wordCount = request.text.split(/\s+/).length;
      const estimatedDuration = Math.ceil((wordCount / 150) * 60);

      const audioUrl = `/uploads/audio/${fileName}`;

      return {
        success: true,
        audioUrl: audioUrl,
        fileName: fileName,
        duration: estimatedDuration,
        service: 'neural'
      };

    } catch (error: any) {
      console.error('Neural Voice generation error:', error);
      return {
        success: false,
        service: 'neural',
        error: error.message || 'Failed to generate speech'
      };
    }
  }

  private async generateElevenLabsSpeech(request: TTSRequest): Promise<TTSResponse> {
    try {
      // Check if ElevenLabs is configured
      if (!process.env.ELEVENLABS_API_KEY) {
        return {
          success: false,
          service: 'elevenlabs',
          error: 'ElevenLabs requires API key configuration'
        };
      }

      const response = await fetch(`https://api.elevenlabs.io/v1/text-to-speech/${request.voice}`, {
        method: 'POST',
        headers: {
          'Accept': 'audio/mpeg',
          'Content-Type': 'application/json',
          'xi-api-key': process.env.ELEVENLABS_API_KEY
        },
        body: JSON.stringify({
          text: request.text,
          model_id: 'eleven_monolingual_v1',
          voice_settings: {
            stability: 0.5,
            similarity_boost: 0.5,
            style: 0.5,
            use_speaker_boost: true
          }
        })
      });

      if (!response.ok) {
        throw new Error(`ElevenLabs API error: ${response.status}`);
      }

      const fileName = `elevenlabs-${uuidv4()}.mp3`;
      const filePath = path.join(uploadsDir, fileName);
      
      const arrayBuffer = await response.arrayBuffer();
      const buffer = Buffer.from(arrayBuffer);
      fs.writeFileSync(filePath, buffer);

      const wordCount = request.text.split(/\s+/).length;
      const estimatedDuration = Math.ceil((wordCount / 150) * 60);
      const audioUrl = `/uploads/audio/${fileName}`;

      return {
        success: true,
        audioUrl: audioUrl,
        fileName: fileName,
        duration: estimatedDuration,
        service: 'elevenlabs'
      };

    } catch (error: any) {
      console.error('ElevenLabs generation error:', error);
      return {
        success: false,
        service: 'elevenlabs',
        error: error.message || 'Failed to generate speech'
      };
    }
  }

  async getAvailableVoices(): Promise<Voice[]> {
    const voices: Voice[] = [...openAIVoices];

    // Add ElevenLabs voices if API key is configured
    if (process.env.ELEVENLABS_API_KEY) {
      try {
        const response = await fetch('https://api.elevenlabs.io/v1/voices', {
          headers: {
            'xi-api-key': process.env.ELEVENLABS_API_KEY
          }
        });

        if (response.ok) {
          const data = await response.json();
          const elevenLabsVoices = data.voices.map((voice: any) => ({
            id: voice.voice_id,
            name: voice.name,
            gender: voice.labels?.gender || 'unknown',
            description: voice.labels?.description || 'Premium voice',
            language: voice.labels?.language || 'English',
            accent: voice.labels?.accent || 'Various',
            service: 'elevenlabs'
          }));
          voices.push(...elevenLabsVoices);
        }
      } catch (error) {
        console.log('ElevenLabs voices not available');
      }
    }

    return voices;
  }

  async generatePreview(voiceId: string, service: 'neural' | 'elevenlabs'): Promise<TTSResponse> {
    const previewText = "Hello! This is a preview of how this voice sounds. You can use this voice to generate professional audio content for your courses.";
    
    return this.generateSpeech({
      text: previewText,
      voice: voiceId,
      speed: 1.0,
      service: service,
      quality: 'standard'
    });
  }
}

export const unifiedTTS = new UnifiedTTSService();