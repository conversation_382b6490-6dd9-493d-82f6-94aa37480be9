import { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/use-auth';
import { JitsiMeeting } from '@jitsi/react-sdk';
import { useToast } from '@/hooks/use-toast';

// Add global window interface for Jitsi API
declare global {
  interface Window {
    jitsiApi: any;
  }
}
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { CopyToClipboard } from 'react-copy-to-clipboard';
import { Copy, Video, Users, Monitor, MessageCircle } from 'lucide-react';

export default function MeetingsPage() {
  const { user } = useAuth();
  const { toast } = useToast();
  const [roomName, setRoomName] = useState('');
  const [meetingUrl, setMeetingUrl] = useState('');
  const [isInMeeting, setIsInMeeting] = useState(false);
  const [recentMeetings, setRecentMeetings] = useState<string[]>([]);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [jitsiApi, setJitsiApi] = useState<any>(null);

  // Load recent meetings from localStorage
  useEffect(() => {
    const savedMeetings = localStorage.getItem('recentMeetings');
    if (savedMeetings) {
      setRecentMeetings(JSON.parse(savedMeetings));
    }
  }, []);

  // Save recent meetings to localStorage
  useEffect(() => {
    if (recentMeetings.length > 0) {
      localStorage.setItem('recentMeetings', JSON.stringify(recentMeetings));
    }
  }, [recentMeetings]);

  // Generate a secure room name
  const generateSecureRoomName = () => {
    return `courseai-${user?.username || 'guest'}-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`;
  };

  // Handle creating a new meeting
  const createMeeting = () => {
    const newRoomName = roomName || generateSecureRoomName();
    setRoomName(newRoomName);
    setMeetingUrl(`https://${window.location.hostname}/meetings?room=${encodeURIComponent(newRoomName)}`);
    
    // Add to recent meetings
    if (!recentMeetings.includes(newRoomName)) {
      setRecentMeetings([newRoomName, ...recentMeetings.slice(0, 4)]);
    }
    
    setIsInMeeting(true);
    setCreateDialogOpen(false);
  };

  // Handle joining an existing meeting
  const joinMeeting = (room: string) => {
    setRoomName(room);
    setMeetingUrl(`https://${window.location.hostname}/meetings?room=${encodeURIComponent(room)}`);
    setIsInMeeting(true);
  };

  // Handle exiting the meeting
  const exitMeeting = () => {
    // Clean up Jitsi resources if available
    if (window.jitsiApi) {
      try {
        window.jitsiApi.executeCommand('hangup');
        window.jitsiApi.dispose();
      } catch (error) {
        console.error('Error cleaning up Jitsi resources:', error);
      }
    }
    
    // Reset state
    setIsInMeeting(false);
    setRoomName('');
    setMeetingUrl('');
    setJitsiApi(null);
  };

  // Check for room parameter in URL
  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    const roomParam = params.get('room');
    
    if (roomParam) {
      setRoomName(roomParam);
      setMeetingUrl(`https://${window.location.hostname}/meetings?room=${encodeURIComponent(roomParam)}`);
      setIsInMeeting(true);
    }
  }, []);

  // Configuration for Jitsi
  const jitsiConfig = {
    roomName: roomName,
    configOverwrite: {
      // Audio/Video Settings
      startWithAudioMuted: false,
      startWithVideoMuted: false,
      
      // Navigation/UI Control
      enableClosePage: false,
      prejoinPageEnabled: false,
      disableDeepLinking: true,
      enableWelcomePage: false,
      
      // Performance & UX Optimizations
      enableNoisyMicDetection: true,
      disableInviteFunctions: false,
      resolution: 720,
      constraints: {
        video: {
          aspectRatio: 16/9,
          height: {
            ideal: 720,
            max: 720,
            min: 240
          }
        }
      },
      
      // Layout Customization
      defaultLayout: 'tile',
      hideConferenceSubject: false,
      disableShortcuts: false,
      
      // Feature Control
      transcribingEnabled: true,
      enableNoAudioDetection: true,
      disableReactionsModeration: true,
      disableFocus: false,
      
      // Toolbar Configuration
      toolbarButtons: [
        'microphone', 'camera', 'desktop', 'fullscreen',
        'hangup', 'profile', 'chat', 'settings', 'raisehand',
        'videoquality', 'filmstrip', 'tileview', 'select-background'
      ],
      
      // Filmstrip Configuration 
      filmstrip: {
        enabled: true,
        disableResizable: false,
        disableFilmstripAutohiding: false,
        minParticipantCountForFilmstrip: 2
      },
      
      // Video Layout
      maxFullResolutionParticipants: 4,
      disableTileView: false,
      
      // Security
      requireDisplayName: false,
    },
    interfaceConfigOverwrite: {
      TOOLBAR_BUTTONS: [
        'microphone', 'camera', 'desktop', 'fullscreen',
        'hangup', 'profile', 'chat', 'settings', 'raisehand',
        'videoquality', 'filmstrip', 'tileview', 'select-background'
      ],
      SETTINGS_SECTIONS: ['devices', 'language', 'moderator', 'profile'],
      
      // Branding & Appearance
      SHOW_JITSI_WATERMARK: false,
      SHOW_WATERMARK_FOR_GUESTS: false,
      DEFAULT_BACKGROUND: '#1a1b1c',
      DEFAULT_REMOTE_DISPLAY_NAME: 'CourseAI Collaborator',
      DEFAULT_LOCAL_DISPLAY_NAME: user?.name || user?.username || 'You',
      
      // UX Improvements
      DISABLE_JOIN_LEAVE_NOTIFICATIONS: true,
      HIDE_INVITE_MORE_HEADER: false,
      DISABLE_FOCUS_INDICATOR: true,
      
      // Responsiveness
      MOBILE_APP_PROMO: false,
      MAXIMUM_ZOOMING_COEFFICIENT: 1.3,
      VERTICAL_FILMSTRIP: true,
      FILM_STRIP_MAX_HEIGHT: 120,
      
      // Toolbar Behavior
      TOOLBAR_ALWAYS_VISIBLE: true,
      TOOLBAR_TIMEOUT: 4000,
      
      // Video Layout
      TILE_VIEW_MAX_COLUMNS: 5,
      
      // Initial Display Mode
      INITIAL_TOOLBAR_TIMEOUT: 20000,
      
      // Other Interface Settings
      RECENT_LIST_ENABLED: false,
      GENERATE_ROOMNAMES_ON_WELCOME_PAGE: false,
    },
    userInfo: {
      displayName: user?.name || user?.username || 'Guest',
      email: user?.email || '',
    },
    onApiReady: (externalApi: any) => {
      // Store API reference
      window.jitsiApi = externalApi;
      
      // Enhanced Feedback
      externalApi.addListener('videoConferenceJoined', () => {
        toast({
          title: 'Joined meeting',
          description: `You've joined the meeting successfully`,
          duration: 3000,
        });
        
        // Automatically go fullscreen on desktop
        if (window.innerWidth > 768) {
          try {
            externalApi.executeCommand('toggleFullScreen');
          } catch (err) {
            console.log("Could not enter fullscreen mode automatically");
          }
        }
      });
      
      // Automatic cleanup
      externalApi.addListener('videoConferenceLeft', () => {
        console.log('Meeting left via Jitsi event');
        exitMeeting();
      });
      
      // Handle errors
      externalApi.addListener('errorOccurred', (error: any) => {
        console.error('Jitsi error:', error);
        toast({
          title: 'Meeting connection error',
          description: 'There was a problem with the video conference',
          variant: 'destructive'
        });
      });
    },
  };

  return (
    <div className={`${isInMeeting ? 'w-full p-0' : 'container mx-auto p-4 max-w-7xl'}`}>
      {!isInMeeting ? (
        <div>
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-3xl font-bold gradient-heading">Video Meetings</h1>
            <Button onClick={() => setCreateDialogOpen(true)} className="bg-gradient-to-r from-primary to-[#4dabf7]">
              <Video className="mr-2 h-4 w-4" /> Create Meeting
            </Button>
          </div>

          <Tabs defaultValue="join">
            <TabsList className="mb-4">
              <TabsTrigger value="join">Join a Meeting</TabsTrigger>
              <TabsTrigger value="recent">Recent Meetings</TabsTrigger>
              <TabsTrigger value="scheduled">Scheduled Meetings</TabsTrigger>
            </TabsList>
            
            <TabsContent value="join" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Join a Meeting</CardTitle>
                  <CardDescription>
                    Enter a meeting ID to join an existing video conference
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex gap-2">
                    <Input
                      placeholder="Enter meeting ID"
                      value={roomName}
                      onChange={(e) => setRoomName(e.target.value)}
                    />
                    <Button 
                      onClick={() => joinMeeting(roomName)}
                      disabled={!roomName}
                    >
                      Join
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="recent">
              <Card>
                <CardHeader>
                  <CardTitle>Recent Meetings</CardTitle>
                  <CardDescription>
                    Quick access to your recent meetings
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {recentMeetings.length > 0 ? (
                    <div className="space-y-2">
                      {recentMeetings.map((meeting, index) => (
                        <div 
                          key={index} 
                          className="flex items-center justify-between border rounded-md p-3 hover:bg-slate-50 cursor-pointer"
                          onClick={() => joinMeeting(meeting)}
                        >
                          <div className="flex items-center">
                            <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center mr-3">
                              <Video className="h-4 w-4 text-primary" />
                            </div>
                            <div>
                              <p className="font-medium text-sm">{meeting}</p>
                              <p className="text-xs text-slate-500">Click to join</p>
                            </div>
                          </div>
                          <div className="flex space-x-2">
                            <CopyToClipboard 
                              text={`https://${window.location.hostname}/meetings?room=${encodeURIComponent(meeting)}`} 
                              onCopy={() => toast({ title: "Meeting link copied" })}
                            >
                              <Button size="sm" variant="outline">
                                <Copy className="h-4 w-4" />
                              </Button>
                            </CopyToClipboard>
                            <Button size="sm" variant="default" onClick={(e) => {
                              e.stopPropagation();
                              joinMeeting(meeting);
                            }}>
                              Join
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8 text-slate-500">
                      <Users className="h-12 w-12 mx-auto mb-3 text-slate-300" />
                      <p>No recent meetings</p>
                      <p className="text-sm">Your recent meetings will appear here</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="scheduled">
              <Card>
                <CardHeader>
                  <CardTitle>Scheduled Meetings</CardTitle>
                  <CardDescription>
                    View your upcoming scheduled meetings
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-8 text-slate-500">
                    <div className="h-12 w-12 mx-auto mb-3 text-slate-300">
                      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                    </div>
                    <p>No scheduled meetings</p>
                    <p className="text-sm">Coming soon: Schedule meetings in advance</p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          {/* Create Meeting Dialog */}
          <Dialog open={createDialogOpen} onOpenChange={setCreateDialogOpen}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create New Meeting</DialogTitle>
                <DialogDescription>
                  Start a new video conference with your team
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4 my-4">
                <div>
                  <label className="text-sm font-medium mb-1 block">Meeting ID (optional)</label>
                  <Input 
                    placeholder="Enter custom meeting ID or leave blank for auto-generated" 
                    value={roomName}
                    onChange={(e) => setRoomName(e.target.value)}
                  />
                  <p className="text-xs text-slate-500 mt-1">
                    If left blank, a secure random meeting ID will be generated
                  </p>
                </div>
              </div>
              <DialogFooter>
                <Button 
                  variant="outline" 
                  onClick={() => setCreateDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button onClick={createMeeting}>
                  Start Meeting
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      ) : (
        <div className="flex flex-col h-[calc(100vh-16px)] w-full rounded-xl overflow-hidden border shadow-md">
          <div className="bg-slate-800 text-white p-3 flex justify-between items-center z-10">
            <div className="flex items-center space-x-2">
              <Video className="h-5 w-5" />
              <h2 className="font-medium">{roomName}</h2>
            </div>
            <div className="flex items-center space-x-2">
              <CopyToClipboard 
                text={meetingUrl} 
                onCopy={() => toast({ title: "Meeting link copied to clipboard" })}
              >
                <Button size="sm" variant="ghost" className="h-8 text-white hover:bg-slate-700">
                  <Copy className="h-4 w-4 mr-1" /> Copy Link
                </Button>
              </CopyToClipboard>
              <Button 
                size="sm" 
                variant="destructive" 
                className="h-8" 
                onClick={exitMeeting}
              >
                Exit Meeting
              </Button>
            </div>
          </div>
          <div className="flex-1 relative">
            <div className="absolute inset-0 w-full h-full">
              <JitsiMeeting {...jitsiConfig} />
            </div>
          </div>
        </div>
      )}

      {/* Meeting Features Cards (shown only when not in a meeting) */}
      {!isInMeeting && (
        <div className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardHeader>
              <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center mb-4">
                <Video className="h-6 w-6 text-primary" />
              </div>
              <CardTitle>Video Conferencing</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-slate-600">
                High-quality video meetings with up to 100 participants. Secure and optimized for performance.
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center mb-4">
                <Monitor className="h-6 w-6 text-primary" />
              </div>
              <CardTitle>Screen Sharing</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-slate-600">
                Share your screen, applications, or specific browser tabs with meeting participants.
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center mb-4">
                <MessageCircle className="h-6 w-6 text-primary" />
              </div>
              <CardTitle>Chat & Collaboration</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-slate-600">
                Chat with participants during meetings and collaborate in real-time on course content.
              </p>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}