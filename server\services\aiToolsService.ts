import runpodService from './runpodService';
import s3Service from './awsS3Service';
import { db } from '../db';
import { mediaLibrary, aiJobs } from '@shared/schema';
import { v4 as uuidv4 } from 'uuid';
import { eq, and } from 'drizzle-orm';

// Text generation models and parameters
interface TextGenerationParams {
  prompt: string;
  userId: number;
  model?: 'mistral' | 'mixtral';
  courseId?: number;
  lessonId?: number;
  maxTokens?: number;
  temperature?: number;
  topP?: number;
}

// Text-to-speech models and parameters
interface TextToSpeechParams {
  text: string;
  userId: number;
  model?: 'kokoro' | 'coqui';
  voice?: string;
  courseId?: number;
  lessonId?: number;
  speed?: number;
  language?: string;
}

// Image generation models and parameters
interface ImageGenerationParams {
  prompt: string;
  userId: number;
  model?: 'kandinsky' | 'wan';
  courseId?: number;
  lessonId?: number;
  negativePrompt?: string;
  width?: number;
  height?: number;
  numImages?: number;
  guidanceScale?: number;
  steps?: number;
  seed?: number;
}

// Animation generation parameters
interface AnimationGenerationParams {
  prompt: string;
  userId: number;
  courseId?: number;
  lessonId?: number;
  negativePrompt?: string;
  inputImage?: string; // base64 image
  motionStrength?: number;
  frames?: number;
  fps?: number;
  seed?: number;
}

// Job status type
interface JobStatus {
  id: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number;
  result?: any;
  error?: string;
}

// Utility function to ensure folder name is safe
function sanitizeFolderName(name: string): string {
  return name.toLowerCase().replace(/[^a-z0-9]/g, '-');
}

// Generate a cache key for the request to avoid duplicating work
function generateCacheKey(params: any): string {
  return JSON.stringify(params);
}

// Text generation service 
async function generateText(params: TextGenerationParams): Promise<{ jobId: string }> {
  try {
    // Check if RunPod is configured
    if (!runpodService.isConfigured()) {
      throw new Error('RunPod service is not configured');
    }
    
    // Check if the requested model is available
    if (params.model && !runpodService.isEndpointConfigured(params.model)) {
      throw new Error(`RunPod endpoint for ${params.model} is not configured`);
    }
    
    // Use the specified model or fall back to mixtral
    const model = params.model || 'mixtral';
    
    // Create a job record
    const jobId = uuidv4();
    const now = new Date();
    
    // Save job to database
    await db.insert(aiJobs).values({
      id: jobId,
      userId: params.userId,
      prompt: params.prompt,
      model,
      status: 'pending',
      type: 'text',
      courseId: params.courseId || null,
      lessonId: params.lessonId || null,
      createdAt: now,
      updatedAt: now,
      estimatedCompletionTime: 30, // Estimate 30 seconds
    });
    
    // Run the text generation in the background
    setTimeout(async () => {
      try {
        // Call the RunPod service
        const result = await runpodService.generateText(model, {
          prompt: params.prompt,
          maxTokens: params.maxTokens,
          temperature: params.temperature,
          topP: params.topP
        });
        
        // Update the job with the result
        await db.update(aiJobs)
          .set({ 
            status: 'completed', 
            result: result,
            updatedAt: new Date()
          })
          .where(eq(aiJobs.id, jobId));
      } catch (error) {
        console.error('Error generating text:', error);
        
        // Update the job with the error
        await db.update(aiJobs)
          .set({ 
            status: 'failed', 
            error: (error as Error).message,
            updatedAt: new Date()
          })
          .where(eq(aiJobs.id, jobId));
      }
    }, 0);
    
    return { jobId };
  } catch (error) {
    console.error('Error initiating text generation:', error);
    throw error;
  }
}

// Text-to-speech service
async function generateSpeech(params: TextToSpeechParams): Promise<{ jobId: string }> {
  try {
    // Check if RunPod is configured
    if (!runpodService.isConfigured()) {
      throw new Error('RunPod service is not configured');
    }
    
    // Check if the requested model is available
    if (params.model && !runpodService.isEndpointConfigured(params.model)) {
      throw new Error(`RunPod endpoint for ${params.model} is not configured`);
    }
    
    // Check if S3 is configured
    if (!s3Service.isConfigured()) {
      throw new Error('S3 service is not configured for saving audio');
    }
    
    // Use the specified model or fall back to kokoro
    const model = params.model || 'kokoro';
    
    // Create a job record
    const jobId = uuidv4();
    const now = new Date();
    
    // Save job to database
    await db.insert(aiJobs).values({
      id: jobId,
      userId: params.userId,
      prompt: params.text.substring(0, 255), // Store preview of text
      model,
      status: 'pending',
      type: 'speech',
      courseId: params.courseId || null,
      lessonId: params.lessonId || null,
      createdAt: now,
      updatedAt: now,
      // Estimate time based on text length - approx 3 words per second
      estimatedCompletionTime: Math.ceil(params.text.split(' ').length / 3) + 10,
    });
    
    // Run the speech generation in the background
    setTimeout(async () => {
      try {
        // Call the RunPod service
        const result = await runpodService.generateSpeech(model, {
          text: params.text,
          voice: params.voice,
          speed: params.speed,
          language: params.language
        });
        
        // Get the audio buffer
        const audioBase64 = result.audio;
        const audioBuffer = Buffer.from(audioBase64, 'base64');
        
        // Upload the audio to S3
        const fileName = `speech-${jobId}.mp3`;
        const folder = params.courseId 
          ? `courses/${params.courseId}${params.lessonId ? `/lessons/${params.lessonId}` : ''}/audio` 
          : `users/${params.userId}/audio`;
        
        const uploaded = await s3Service.uploadBuffer(audioBuffer, fileName, {
          contentType: 'audio/mpeg',
          folder,
          acl: 'public-read'
        });
        
        // Create a media record
        const [mediaRecord] = await db.insert(mediaLibrary).values({
          name: fileName,
          url: uploaded.url,
          type: 'audio',
          userId: params.userId,
          mimeType: 'audio/mpeg',
          fileSize: uploaded.size,
          courseId: params.courseId || null,
          lessonId: params.lessonId || null,
          source: model,
          duration: result.duration || null,
          createdAt: new Date()
        }).returning();
        
        // Update the job with the result
        await db.update(aiJobs)
          .set({ 
            status: 'completed', 
            result: { 
              ...result,
              mediaId: mediaRecord.id,
              url: uploaded.url
            },
            mediaId: mediaRecord.id,
            updatedAt: new Date()
          })
          .where(eq(aiJobs.id, jobId));
      } catch (error) {
        console.error('Error generating speech:', error);
        
        // Update the job with the error
        await db.update(aiJobs)
          .set({ 
            status: 'failed', 
            error: (error as Error).message,
            updatedAt: new Date()
          })
          .where(eq(aiJobs.id, jobId));
      }
    }, 0);
    
    return { jobId };
  } catch (error) {
    console.error('Error initiating speech generation:', error);
    throw error;
  }
}

// Image generation service
async function generateImage(params: ImageGenerationParams): Promise<{ jobId: string }> {
  try {
    // Check if RunPod is configured
    if (!runpodService.isConfigured()) {
      throw new Error('RunPod service is not configured');
    }
    
    // Check if the requested model is available
    if (params.model && !runpodService.isEndpointConfigured(params.model)) {
      throw new Error(`RunPod endpoint for ${params.model} is not configured`);
    }
    
    // Check if S3 is configured
    if (!s3Service.isConfigured()) {
      throw new Error('S3 service is not configured for saving images');
    }
    
    // Use the specified model or fall back to kandinsky
    const model = params.model || 'kandinsky';
    
    // Create a job record
    const jobId = uuidv4();
    const now = new Date();
    
    // Save job to database
    await db.insert(aiJobs).values({
      id: jobId,
      userId: params.userId,
      prompt: params.prompt,
      model,
      status: 'pending',
      type: 'image',
      courseId: params.courseId || null,
      lessonId: params.lessonId || null,
      createdAt: now,
      updatedAt: now,
      estimatedCompletionTime: 60, // Estimate 60 seconds for image generation
    });
    
    // Run the image generation in the background
    setTimeout(async () => {
      try {
        // Call the RunPod service
        const result = await runpodService.generateImage(model, {
          prompt: params.prompt,
          negative_prompt: params.negativePrompt,
          width: params.width,
          height: params.height,
          num_images: params.numImages,
          guidance_scale: params.guidanceScale,
          steps: params.steps,
          seed: params.seed
        });
        
        // Process and upload each image
        const mediaIds = [];
        const imageUrls = [];
        
        // Ensure we have an array of images, even if only one was generated
        const images = Array.isArray(result.images) ? result.images : [result.images];
        
        for (let i = 0; i < images.length; i++) {
          // Get the image buffer
          const imageBase64 = images[i];
          const imageBuffer = Buffer.from(imageBase64, 'base64');
          
          // Upload the image to S3
          const fileName = `image-${jobId}-${i}.png`;
          const folder = params.courseId 
            ? `courses/${params.courseId}${params.lessonId ? `/lessons/${params.lessonId}` : ''}/images` 
            : `users/${params.userId}/images`;
          
          const uploaded = await s3Service.uploadBuffer(imageBuffer, fileName, {
            contentType: 'image/png',
            folder,
            acl: 'public-read'
          });
          
          // Create a media record
          const [mediaRecord] = await db.insert(mediaLibrary).values({
            name: fileName,
            url: uploaded.url,
            type: 'image',
            userId: params.userId,
            mimeType: 'image/png',
            fileSize: uploaded.size,
            courseId: params.courseId || null,
            lessonId: params.lessonId || null,
            source: model,
            createdAt: new Date()
          }).returning();
          
          mediaIds.push(mediaRecord.id);
          imageUrls.push(uploaded.url);
        }
        
        // Update the job with the result
        await db.update(aiJobs)
          .set({ 
            status: 'completed', 
            result: { 
              mediaIds,
              urls: imageUrls,
              ...result
            },
            mediaId: mediaIds[0], // Link to the first image
            updatedAt: new Date()
          })
          .where(eq(aiJobs.id, jobId));
      } catch (error) {
        console.error('Error generating image:', error);
        
        // Update the job with the error
        await db.update(aiJobs)
          .set({ 
            status: 'failed', 
            error: (error as Error).message,
            updatedAt: new Date()
          })
          .where(eq(aiJobs.id, jobId));
      }
    }, 0);
    
    return { jobId };
  } catch (error) {
    console.error('Error initiating image generation:', error);
    throw error;
  }
}

// Animation generation service
async function generateAnimation(params: AnimationGenerationParams): Promise<{ jobId: string }> {
  try {
    // Check if RunPod is configured
    if (!runpodService.isConfigured()) {
      throw new Error('RunPod service is not configured');
    }
    
    // Check if sadtalker endpoint is available
    if (!runpodService.isEndpointConfigured('sadtalker')) {
      throw new Error('RunPod endpoint for sadtalker (talking avatar) is not configured');
    }
    
    // Check if S3 is configured
    if (!s3Service.isConfigured()) {
      throw new Error('S3 service is not configured for saving animations');
    }
    
    // Create a job record
    const jobId = uuidv4();
    const now = new Date();
    
    // Save job to database
    await db.insert(aiJobs).values({
      id: jobId,
      userId: params.userId,
      prompt: params.prompt,
      model: 'sadtalker',
      status: 'pending',
      type: 'animation',
      courseId: params.courseId || null,
      lessonId: params.lessonId || null,
      createdAt: now,
      updatedAt: now,
      estimatedCompletionTime: 120, // Estimate 120 seconds for animation
    });
    
    // Run the animation generation in the background
    setTimeout(async () => {
      try {
        // Call the RunPod service
        const result = await runpodService.generateAnimation({
          prompt: params.prompt,
          negative_prompt: params.negativePrompt,
          input_image: params.inputImage,
          motion_strength: params.motionStrength,
          frames: params.frames,
          fps: params.fps,
          seed: params.seed
        });
        
        // Get the animation buffer (assuming it's a base64 encoded mp4)
        const animationBase64 = result.animation;
        const animationBuffer = Buffer.from(animationBase64, 'base64');
        
        // Upload the animation to S3
        const fileName = `animation-${jobId}.mp4`;
        const folder = params.courseId 
          ? `courses/${params.courseId}${params.lessonId ? `/lessons/${params.lessonId}` : ''}/animations` 
          : `users/${params.userId}/animations`;
        
        const uploaded = await s3Service.uploadBuffer(animationBuffer, fileName, {
          contentType: 'video/mp4',
          folder,
          acl: 'public-read'
        });
        
        // Create a media record
        const [mediaRecord] = await db.insert(mediaLibrary).values({
          name: fileName,
          url: uploaded.url,
          type: 'video',
          userId: params.userId,
          mimeType: 'video/mp4',
          fileSize: uploaded.size,
          courseId: params.courseId || null,
          lessonId: params.lessonId || null,
          source: 'sadtalker',
          duration: result.duration || null,
          createdAt: new Date()
        }).returning();
        
        // Update the job with the result
        await db.update(aiJobs)
          .set({ 
            status: 'completed', 
            result: { 
              mediaId: mediaRecord.id,
              url: uploaded.url,
              ...result
            },
            mediaId: mediaRecord.id,
            updatedAt: new Date()
          })
          .where(eq(aiJobs.id, jobId));
      } catch (error) {
        console.error('Error generating animation:', error);
        
        // Update the job with the error
        await db.update(aiJobs)
          .set({ 
            status: 'failed', 
            error: (error as Error).message,
            updatedAt: new Date()
          })
          .where(eq(aiJobs.id, jobId));
      }
    }, 0);
    
    return { jobId };
  } catch (error) {
    console.error('Error initiating animation generation:', error);
    throw error;
  }
}

// Get job status
async function getJobStatus(jobId: string): Promise<JobStatus> {
  try {
    const job = await db.select().from(aiJobs).where(eq(aiJobs.id, jobId));
    
    if (!job || job.length === 0) {
      throw new Error(`Job with ID ${jobId} not found`);
    }
    
    const jobData = job[0];
    
    // Calculate progress based on estimated completion time and elapsed time
    let progress = 0;
    if (jobData.status === 'pending' || jobData.status === 'processing') {
      const startTime = new Date(jobData.createdAt).getTime();
      const now = Date.now();
      const elapsed = now - startTime;
      const estimatedMs = (jobData.estimatedCompletionTime || 30) * 1000;
      
      progress = Math.min(Math.round((elapsed / estimatedMs) * 100), 99);
    } else if (jobData.status === 'completed') {
      progress = 100;
    }
    
    return {
      id: jobData.id,
      status: jobData.status,
      progress,
      result: jobData.result,
      error: jobData.error || undefined
    };
  } catch (error) {
    console.error('Error getting job status:', error);
    throw error;
  }
}

// Get user jobs
async function getUserJobs(userId: number, options: { 
  type?: string;
  courseId?: number;
  lessonId?: number;
  limit?: number;
  offset?: number;
} = {}): Promise<any[]> {
  try {
    let query = db.select().from(aiJobs).where(eq(aiJobs.userId, userId));
    
    if (options.type) {
      query = query.where(eq(aiJobs.type, options.type));
    }
    
    if (options.courseId) {
      query = query.where(eq(aiJobs.courseId, options.courseId));
    }
    
    if (options.lessonId) {
      query = query.where(eq(aiJobs.lessonId, options.lessonId));
    }
    
    // Add limit and offset
    if (options.limit) {
      query = query.limit(options.limit);
    }
    
    if (options.offset) {
      query = query.offset(options.offset);
    }
    
    // Order by creation date (newest first)
    query = query.orderBy(aiJobs.createdAt);
    
    return await query;
  } catch (error) {
    console.error('Error getting user jobs:', error);
    throw error;
  }
}

export default {
  generateText,
  generateSpeech,
  generateImage,
  generateAnimation,
  getJobStatus,
  getUserJobs
};