import { storage } from "../storage";
import { AnalyticsEvent, InsertAnalyticsEvent } from "@shared/schema";

/**
 * Analytics Service for handling course and user analytics
 */
export class AnalyticsService {
  /**
   * Records an analytics event in the database
   */
  async recordEvent(event: InsertAnalyticsEvent): Promise<AnalyticsEvent> {
    return await storage.recordAnalyticsEvent(event);
  }

  /**
   * Gets course performance overview with key metrics
   */
  async getCoursePerformance(courseId: number): Promise<any> {
    return await storage.getCoursePerformanceOverview(courseId);
  }

  /**
   * Gets user engagement statistics for a specific user
   */
  async getUserEngagement(userId: number): Promise<any> {
    return await storage.getUserEngagementStats(userId);
  }

  /**
   * Gets a list of popular courses across the platform
   */
  async getPopularCourses(limit: number = 5): Promise<any[]> {
    return await storage.getPopularCourses(limit);
  }

  /**
   * Records a course view event
   */
  async recordCourseView(userId: number, courseId: number, metadata: Record<string, any> = {}): Promise<AnalyticsEvent> {
    const event: InsertAnalyticsEvent = {
      userId,
      courseId,
      eventType: 'course_view',
      eventData: metadata,
    };
    
    return await this.recordEvent(event);
  }

  /**
   * Records a lesson view event
   */
  async recordLessonView(userId: number, courseId: number, lessonId: number, metadata: Record<string, any> = {}): Promise<AnalyticsEvent> {
    const event: InsertAnalyticsEvent = {
      userId,
      courseId,
      lessonId,
      eventType: 'lesson_view',
      eventData: metadata,
    };
    
    return await this.recordEvent(event);
  }

  /**
   * Records a course completion event
   */
  async recordCourseCompletion(userId: number, courseId: number, metadata: Record<string, any> = {}): Promise<AnalyticsEvent> {
    const event: InsertAnalyticsEvent = {
      userId,
      courseId,
      eventType: 'course_complete',
      eventData: metadata,
    };
    
    return await this.recordEvent(event);
  }

  /**
   * Records a lesson completion event
   */
  async recordLessonCompletion(userId: number, courseId: number, lessonId: number, metadata: Record<string, any> = {}): Promise<AnalyticsEvent> {
    const event: InsertAnalyticsEvent = {
      userId,
      courseId,
      lessonId,
      eventType: 'lesson_complete',
      eventData: metadata,
    };
    
    return await this.recordEvent(event);
  }

  /**
   * Records a course enrollment event
   */
  async recordCourseEnrollment(userId: number, courseId: number, metadata: Record<string, any> = {}): Promise<AnalyticsEvent> {
    const event: InsertAnalyticsEvent = {
      userId,
      courseId,
      eventType: 'course_enroll',
      eventData: metadata,
    };
    
    return await this.recordEvent(event);
  }

  /**
   * Records a course rating event
   */
  async recordCourseRating(userId: number, courseId: number, rating: number, feedback: string = ""): Promise<AnalyticsEvent> {
    const event: InsertAnalyticsEvent = {
      userId,
      courseId,
      eventType: 'course_rate',
      eventData: { rating: rating, feedback: feedback } as Record<string, any>,
    };
    
    return await this.recordEvent(event);
  }

  /**
   * Get recent analytics events for a course
   */
  async getCourseEvents(courseId: number, limit: number = 50): Promise<AnalyticsEvent[]> {
    return await storage.getAnalyticsEventsByCourseId(courseId, limit);
  }

  /**
   * Get recent analytics events for a user
   */
  async getUserEvents(userId: number, limit: number = 50): Promise<AnalyticsEvent[]> {
    return await storage.getAnalyticsEventsByUserId(userId, limit);
  }

  /**
   * Get recent analytics events for a lesson
   */
  async getLessonEvents(lessonId: number, limit: number = 50): Promise<AnalyticsEvent[]> {
    return await storage.getAnalyticsEventsByLessonId(lessonId, limit);
  }
}

// Export a singleton instance
export const analyticsService = new AnalyticsService();