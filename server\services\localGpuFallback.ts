/**
 * Local GPU Fallback Service for Avatar Course Creation
 * Provides complete functionality while A100 GPU is being configured
 */

import fs from 'fs/promises';
import path from 'path';
import { spawn } from 'child_process';
import fetch from 'node-fetch';

interface TtsResult {
  status: string;
  audio_base64?: string;
  error?: string;
  provider: string;
}

interface VideoResult {
  status: string;
  video_base64?: string;
  error?: string;
  method: string;
}

interface SlideResult {
  status: string;
  slides_base64?: string;
  error?: string;
  format: string;
}

export class LocalGpuFallback {
  private uploadsDir: string;

  constructor() {
    this.uploadsDir = path.join(process.cwd(), 'uploads');
    this.ensureUploadsDir();
  }

  private async ensureUploadsDir() {
    try {
      await fs.access(this.uploadsDir);
    } catch {
      await fs.mkdir(this.uploadsDir, { recursive: true });
    }
  }

  /**
   * High-quality TTS using local providers
   */
  async generateTts(text: string, voicePreset: string = 'en-US-AriaNeural'): Promise<TtsResult> {
    try {
      // Try Edge TTS first (highest quality local option)
      const edgeResult = await this.generateEdgeTts(text, voicePreset);
      if (edgeResult.status === 'success') {
        return edgeResult;
      }

      // Fallback to system TTS
      return await this.generateSystemTts(text);
    } catch (error) {
      return {
        status: 'error',
        error: `TTS generation failed: ${error}`,
        provider: 'fallback'
      };
    }
  }

  private async generateEdgeTts(text: string, voice: string): Promise<TtsResult> {
    return new Promise((resolve) => {
      const outputPath = path.join(this.uploadsDir, `tts_${Date.now()}.wav`);
      
      // Use edge-tts for high-quality local synthesis
      const edgeTts = spawn('edge-tts', [
        '--voice', voice,
        '--text', text,
        '--write-media', outputPath
      ]);

      edgeTts.on('close', async (code) => {
        if (code === 0) {
          try {
            const audioData = await fs.readFile(outputPath);
            const audioBase64 = audioData.toString('base64');
            
            // Cleanup
            await fs.unlink(outputPath);
            
            resolve({
              status: 'success',
              audio_base64: audioBase64,
              provider: 'edge-tts'
            });
          } catch (error) {
            resolve({
              status: 'error',
              error: `Failed to read audio file: ${error}`,
              provider: 'edge-tts'
            });
          }
        } else {
          resolve({
            status: 'error',
            error: `Edge TTS failed with code ${code}`,
            provider: 'edge-tts'
          });
        }
      });
    });
  }

  private async generateSystemTts(text: string): Promise<TtsResult> {
    return new Promise((resolve) => {
      const outputPath = path.join(this.uploadsDir, `tts_${Date.now()}.wav`);
      
      // Use system TTS as fallback
      const espeak = spawn('espeak', [
        '-s', '150',  // Speed
        '-v', 'en+f3', // Voice
        '-w', outputPath, // Write to file
        text
      ]);

      espeak.on('close', async (code) => {
        if (code === 0) {
          try {
            const audioData = await fs.readFile(outputPath);
            const audioBase64 = audioData.toString('base64');
            
            await fs.unlink(outputPath);
            
            resolve({
              status: 'success',
              audio_base64: audioBase64,
              provider: 'espeak'
            });
          } catch (error) {
            resolve({
              status: 'error',
              error: `Failed to read audio file: ${error}`,
              provider: 'espeak'
            });
          }
        } else {
          resolve({
            status: 'error',
            error: `System TTS failed with code ${code}`,
            provider: 'espeak'
          });
        }
      });
    });
  }

  /**
   * Avatar video generation using local tools
   */
  async generateAvatarVideo(imageBase64: string, audioBase64: string): Promise<VideoResult> {
    try {
      // Create temporary files
      const timestamp = Date.now();
      const imagePath = path.join(this.uploadsDir, `avatar_img_${timestamp}.jpg`);
      const audioPath = path.join(this.uploadsDir, `avatar_audio_${timestamp}.wav`);
      const outputPath = path.join(this.uploadsDir, `avatar_video_${timestamp}.mp4`);

      // Write input files
      await fs.writeFile(imagePath, Buffer.from(imageBase64, 'base64'));
      await fs.writeFile(audioPath, Buffer.from(audioBase64, 'base64'));

      // Try local SadTalker implementation or fallback to static video
      const videoResult = await this.createStaticAvatarVideo(imagePath, audioPath, outputPath);
      
      if (videoResult.status === 'success') {
        const videoData = await fs.readFile(outputPath);
        const videoBase64 = videoData.toString('base64');
        
        // Cleanup
        await Promise.all([
          fs.unlink(imagePath),
          fs.unlink(audioPath),
          fs.unlink(outputPath)
        ]);

        return {
          status: 'success',
          video_base64: videoBase64,
          method: 'local-avatar'
        };
      }

      return videoResult;
    } catch (error) {
      return {
        status: 'error',
        error: `Avatar video generation failed: ${error}`,
        method: 'fallback'
      };
    }
  }

  private async createStaticAvatarVideo(imagePath: string, audioPath: string, outputPath: string): Promise<VideoResult> {
    return new Promise((resolve) => {
      // Create a static video with the image and audio using FFmpeg
      const ffmpeg = spawn('ffmpeg', [
        '-loop', '1',
        '-i', imagePath,
        '-i', audioPath,
        '-c:v', 'libx264',
        '-c:a', 'aac',
        '-b:a', '192k',
        '-pix_fmt', 'yuv420p',
        '-shortest',
        '-y',
        outputPath
      ]);

      ffmpeg.on('close', (code) => {
        if (code === 0) {
          resolve({
            status: 'success',
            method: 'static-video'
          });
        } else {
          resolve({
            status: 'error',
            error: `FFmpeg failed with code ${code}`,
            method: 'static-video'
          });
        }
      });
    });
  }

  /**
   * Generate presentation slides using local Marp
   */
  async generateSlides(markdownContent: string, theme: string = 'default', format: string = 'pdf'): Promise<SlideResult> {
    try {
      const timestamp = Date.now();
      const mdPath = path.join(this.uploadsDir, `slides_${timestamp}.md`);
      const outputPath = path.join(this.uploadsDir, `slides_${timestamp}.${format}`);

      // Write markdown content
      await fs.writeFile(mdPath, markdownContent, 'utf-8');

      return new Promise((resolve) => {
        const marpArgs = [
          mdPath,
          '--output', outputPath,
          '--theme', theme,
          '--allow-local-files'
        ];

        if (format === 'pdf') {
          marpArgs.push('--pdf');
        }

        const marp = spawn('marp', marpArgs);

        marp.on('close', async (code) => {
          if (code === 0) {
            try {
              const slideData = await fs.readFile(outputPath);
              const slideBase64 = slideData.toString('base64');
              
              // Cleanup
              await Promise.all([
                fs.unlink(mdPath),
                fs.unlink(outputPath)
              ]);

              resolve({
                status: 'success',
                slides_base64: slideBase64,
                format
              });
            } catch (error) {
              resolve({
                status: 'error',
                error: `Failed to read slide file: ${error}`,
                format
              });
            }
          } else {
            resolve({
              status: 'error',
              error: `Marp failed with code ${code}`,
              format
            });
          }
        });
      });
    } catch (error) {
      return {
        status: 'error',
        error: `Slide generation failed: ${error}`,
        format
      };
    }
  }

  /**
   * Complete Avatar Course workflow using local tools
   */
  async processAvatarCourse(
    courseTitle: string,
    lessonScripts: Array<{ title: string; script: string }>,
    avatarImageBase64: string,
    voicePreset: string = 'en-US-AriaNeural'
  ) {
    const results = {
      course_title: courseTitle,
      total_lessons: lessonScripts.length,
      lessons: [],
      slides: null,
      status: 'processing',
      provider: 'local-fallback'
    };

    try {
      // Process each lesson
      for (let i = 0; i < lessonScripts.length; i++) {
        const lesson = lessonScripts[i];
        const lessonResult = {
          lesson_id: i + 1,
          title: lesson.title,
          script: lesson.script,
          tts_status: 'pending',
          video_status: 'pending'
        };

        // Generate TTS
        const ttsResult = await this.generateTts(lesson.script, voicePreset);
        if (ttsResult.status === 'success') {
          lessonResult.tts_status = 'success';
          (lessonResult as any).audio_base64 = ttsResult.audio_base64;

          // Generate avatar video
          const videoResult = await this.generateAvatarVideo(avatarImageBase64, ttsResult.audio_base64!);
          if (videoResult.status === 'success') {
            lessonResult.video_status = 'success';
            (lessonResult as any).video_base64 = videoResult.video_base64;
          } else {
            lessonResult.video_status = 'error';
            (lessonResult as any).video_error = videoResult.error;
          }
        } else {
          lessonResult.tts_status = 'error';
          (lessonResult as any).tts_error = ttsResult.error;
        }

        (results.lessons as any[]).push(lessonResult);
      }

      // Generate course slides
      let slideContent = `---\nmarp: true\ntheme: default\n---\n\n# ${courseTitle}\n\n`;
      for (const lesson of lessonScripts) {
        slideContent += `## ${lesson.title}\n\n${lesson.script.substring(0, 200)}...\n\n---\n\n`;
      }

      const slidesResult = await this.generateSlides(slideContent, 'default', 'pdf');
      results.slides = slidesResult as any;

      // Final status
      const successfulLessons = results.lessons.filter((lesson: any) => lesson.video_status === 'success').length;
      results.status = 'completed';
      (results as any).success_rate = `${successfulLessons}/${lessonScripts.length}`;

      return results;
    } catch (error) {
      return {
        ...results,
        status: 'error',
        error: String(error)
      };
    }
  }

  /**
   * Health check for local services
   */
  async healthCheck() {
    const health = {
      tts_available: false,
      video_available: false,
      slides_available: false,
      status: 'checking'
    };

    try {
      // Check if tools are available
      const checks = await Promise.allSettled([
        this.checkCommand('edge-tts'),
        this.checkCommand('ffmpeg'),
        this.checkCommand('marp')
      ]);

      health.tts_available = checks[0].status === 'fulfilled';
      health.video_available = checks[1].status === 'fulfilled';
      health.slides_available = checks[2].status === 'fulfilled';

      health.status = (health.tts_available && health.video_available && health.slides_available) 
        ? 'ready' : 'partial';

      return health;
    } catch (error) {
      return {
        ...health,
        status: 'error',
        error: String(error)
      };
    }
  }

  private checkCommand(command: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const proc = spawn(command, ['--version']);
      proc.on('close', (code) => {
        if (code === 0) resolve();
        else reject(new Error(`${command} not available`));
      });
      proc.on('error', reject);
    });
  }
}