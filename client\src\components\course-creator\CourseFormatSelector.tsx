import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from "@/components/ui/dialog";
import { Card, CardContent } from "@/components/ui/card";
import { UserCircle, VideoIcon, PlusIcon, Layers3, Presentation, Film } from "lucide-react";

interface CourseFormatSelectorProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSelectFormat: (format: string) => void;
}

export function CourseFormatSelector({
  open,
  onOpenChange,
  onSelectFormat,
}: CourseFormatSelectorProps) {
  const handleFormatSelect = (format: string) => {
    onSelectFormat(format);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-3xl p-0 overflow-hidden">
        <DialogHeader className="p-6 pb-0">
          <DialogTitle className="text-2xl font-bold text-center">
            Choose Your Course Format
          </DialogTitle>
          <p className="text-center text-muted-foreground mt-2">
            Select the format that best suits your content and teaching style
          </p>
        </DialogHeader>
        
        <div className="p-6 pt-4 grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Avatar Format */}
          <Card 
            className="relative overflow-hidden border-2 hover:border-primary cursor-pointer transition-all duration-200 hover:shadow-md group"
            onClick={() => handleFormatSelect("avatar")}
          >
            <div className="absolute top-0 right-0 bg-primary/10 text-primary px-3 py-1 text-xs font-medium rounded-bl-md">
              AI-Powered
            </div>
            <div className="absolute inset-0 bg-gradient-to-b from-transparent to-primary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
            <CardContent className="p-6 pt-10">
              <div className="w-16 h-16 rounded-full bg-primary/20 flex items-center justify-center mb-4 mx-auto">
                <UserCircle className="h-10 w-10 text-primary" />
              </div>
              <h3 className="font-bold text-xl text-center mb-2">AI Avatar Presenter</h3>
              <p className="text-muted-foreground text-sm text-center mb-4">
                Create engaging video lessons with an AI-generated talking avatar
              </p>
              
              <ul className="space-y-3 mt-4">
                <li className="flex items-start gap-2">
                  <div className="mt-0.5 text-primary">
                    <Presentation className="h-5 w-5" />
                  </div>
                  <div>
                    <span className="font-medium text-sm">Professional Video Presenter</span>
                    <p className="text-xs text-muted-foreground">
                      Automatically generates a professional presenter avatar
                    </p>
                  </div>
                </li>
                <li className="flex items-start gap-2">
                  <div className="mt-0.5 text-primary">
                    <Layers3 className="h-5 w-5" />
                  </div>
                  <div>
                    <span className="font-medium text-sm">AI Voice Narration</span>
                    <p className="text-xs text-muted-foreground">
                      Choose from multiple natural-sounding voices
                    </p>
                  </div>
                </li>
                <li className="flex items-start gap-2">
                  <div className="mt-0.5 text-primary">
                    <Film className="h-5 w-5" />
                  </div>
                  <div>
                    <span className="font-medium text-sm">Perfect for Explanations</span>
                    <p className="text-xs text-muted-foreground">
                      Ideal for conceptual topics and theoretical content
                    </p>
                  </div>
                </li>
              </ul>
              
              <div className="mt-5 flex justify-center">
                <button 
                  onClick={() => handleFormatSelect("avatar")}
                  className="bg-primary hover:bg-primary-600 text-white px-4 py-2 rounded-md flex items-center gap-2 font-medium"
                >
                  <PlusIcon className="h-4 w-4" />
                  Create Avatar Course
                </button>
              </div>
            </CardContent>
          </Card>
          
          {/* Traditional Format */}
          <Card 
            className="relative overflow-hidden border-2 hover:border-primary cursor-pointer transition-all duration-200 hover:shadow-md group"
            onClick={() => handleFormatSelect("traditional")}
          >
            <div className="absolute top-0 right-0 bg-secondary/20 text-secondary-foreground px-3 py-1 text-xs font-medium rounded-bl-md">
              Versatile
            </div>
            <div className="absolute inset-0 bg-gradient-to-b from-transparent to-primary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
            <CardContent className="p-6 pt-10">
              <div className="w-16 h-16 rounded-full bg-primary/20 flex items-center justify-center mb-4 mx-auto">
                <VideoIcon className="h-10 w-10 text-primary" />
              </div>
              <h3 className="font-bold text-xl text-center mb-2">Traditional Course</h3>
              <p className="text-muted-foreground text-sm text-center mb-4">
                Create a classic course with your own videos, slides, and media
              </p>
              
              <ul className="space-y-3 mt-4">
                <li className="flex items-start gap-2">
                  <div className="mt-0.5 text-primary">
                    <VideoIcon className="h-5 w-5" />
                  </div>
                  <div>
                    <span className="font-medium text-sm">Custom Video Uploads</span>
                    <p className="text-xs text-muted-foreground">
                      Upload your own pre-recorded videos or webcam content
                    </p>
                  </div>
                </li>
                <li className="flex items-start gap-2">
                  <div className="mt-0.5 text-primary">
                    <Presentation className="h-5 w-5" />
                  </div>
                  <div>
                    <span className="font-medium text-sm">Slide Presentations</span>
                    <p className="text-xs text-muted-foreground">
                      Create or upload professional slide presentations
                    </p>
                  </div>
                </li>
                <li className="flex items-start gap-2">
                  <div className="mt-0.5 text-primary">
                    <Film className="h-5 w-5" />
                  </div>
                  <div>
                    <span className="font-medium text-sm">Perfect for Demonstrations</span>
                    <p className="text-xs text-muted-foreground">
                      Ideal for showing real-world examples and techniques
                    </p>
                  </div>
                </li>
              </ul>
              
              <div className="mt-5 flex justify-center">
                <button 
                  onClick={() => handleFormatSelect("traditional")}
                  className="bg-primary hover:bg-primary-600 text-white px-4 py-2 rounded-md flex items-center gap-2 font-medium"
                >
                  <PlusIcon className="h-4 w-4" />
                  Create Traditional Course
                </button>
              </div>
            </CardContent>
          </Card>
        </div>
        
        <div className="p-6 bg-muted/50 border-t">
          <p className="text-xs text-muted-foreground text-center">
            Both formats support AI-assisted content generation, customization options, 
            and multi-platform publishing.
          </p>
        </div>
      </DialogContent>
    </Dialog>
  );
}