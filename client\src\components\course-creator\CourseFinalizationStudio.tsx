import { useState, useRef, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Play, 
  Pause, 
  RotateCcw, 
  Download, 
  Upload, 
  Image as ImageIcon, 
  Video as VideoIcon, 
  Volume2, 
  Eye, 
  Clock,
  ArrowLeft,
  ArrowRight,
  CheckCircle,
  AlertCircle,
  Loader2,
  Settings,
  Layers,
  FileVideo,
  Wand2
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';

interface LessonMedia {
  id: string;
  type: 'image' | 'video';
  url: string;
  thumbnail?: string;
  name: string;
  duration?: number;
}

interface LessonComponent {
  lessonId: string;
  lessonTitle: string;
  script: string;
  voiceUrl?: string;
  voiceDuration?: number;
  selectedMedia?: LessonMedia;
  videoStatus: 'pending' | 'processing' | 'completed' | 'error';
  videoUrl?: string;
  progress: number;
}

interface CourseFinalizationStudioProps {
  modules: any[];
  scripts: any;
  voices: any;
  onBack: () => void;
  onNext: () => void;
}

export function CourseFinalizationStudio({
  modules,
  scripts,
  voices,
  onBack,
  onNext
}: CourseFinalizationStudioProps) {
  const [activeLesson, setActiveLesson] = useState<string | null>(null);
  const [showMediaLibrary, setShowMediaLibrary] = useState(false);
  const [lessonComponents, setLessonComponents] = useState<LessonComponent[]>([]);
  const [selectedMediaType, setSelectedMediaType] = useState<'uploaded' | 'stock'>('uploaded');
  const [previewSettings, setPreviewSettings] = useState({
    videoTransition: 'fade',
    textAnimation: 'typewriter',
    backgroundMusic: false,
    voiceVolume: 80,
    musicVolume: 20
  });
  const [processingQueue, setProcessingQueue] = useState<Set<string>>(new Set());

  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Get available media from library
  const { data: mediaLibrary } = useQuery({
    queryKey: ['/api/media'],
    queryFn: async () => {
      const response = await apiRequest('GET', '/api/media');
      return response.json();
    }
  });

  // Initialize lesson components
  useEffect(() => {
    const components: LessonComponent[] = [];
    modules.forEach((module) => {
      module.lessons?.forEach((lesson: any) => {
        const lessonKey = `${module.id}-${lesson.id}`;
        const lessonScript = scripts[module.id]?.[lesson.id];
        const lessonVoice = voices[lessonKey];
        
        components.push({
          lessonId: lessonKey,
          lessonTitle: lesson.title,
          script: lessonScript || '',
          voiceUrl: lessonVoice?.url,
          voiceDuration: lessonVoice?.duration,
          videoStatus: 'pending',
          progress: 0
        });
      });
    });
    setLessonComponents(components);
  }, [modules, scripts, voices]);

  // Video generation mutation
  const generateVideoMutation = useMutation({
    mutationFn: async ({ lessonId, mediaUrl, voiceUrl, script, settings }: {
      lessonId: string;
      mediaUrl: string;
      voiceUrl: string;
      script: string;
      settings: any;
    }) => {
      const response = await apiRequest('POST', '/api/ai/generate-lesson-video', {
        lessonId,
        mediaUrl,
        voiceUrl,
        script,
        settings
      });
      return response.json();
    },
    onSuccess: (data, variables) => {
      setLessonComponents(prev => 
        prev.map(comp => 
          comp.lessonId === variables.lessonId 
            ? { ...comp, videoStatus: 'processing', progress: 0 }
            : comp
        )
      );
      
      // Start polling for video status
      pollVideoStatus(variables.lessonId, data.jobId);
    },
    onError: (error, variables) => {
      setLessonComponents(prev => 
        prev.map(comp => 
          comp.lessonId === variables.lessonId 
            ? { ...comp, videoStatus: 'error', progress: 0 }
            : comp
        )
      );
      toast({
        title: "Video Generation Failed",
        description: "There was an error generating the video. Please try again.",
        variant: "destructive"
      });
    }
  });

  // Poll video generation status
  const pollVideoStatus = async (lessonId: string, jobId: string) => {
    const poll = async () => {
      try {
        const response = await apiRequest('GET', `/api/ai/video-status/${jobId}`);
        const data = await response.json();
        
        setLessonComponents(prev => 
          prev.map(comp => 
            comp.lessonId === lessonId 
              ? { 
                  ...comp, 
                  progress: data.progress || 0,
                  videoStatus: data.status,
                  videoUrl: data.videoUrl 
                }
              : comp
          )
        );

        if (data.status === 'processing' && data.progress < 100) {
          setTimeout(poll, 2000); // Poll every 2 seconds
        } else if (data.status === 'completed') {
          toast({
            title: "Video Generated Successfully",
            description: `Lesson video for "${lessonComponents.find(c => c.lessonId === lessonId)?.lessonTitle}" is ready!`
          });
        }
      } catch (error) {
        console.error('Error polling video status:', error);
      }
    };
    
    poll();
  };

  const handleMediaSelect = (media: LessonMedia) => {
    if (!activeLesson) return;
    
    setLessonComponents(prev => 
      prev.map(comp => 
        comp.lessonId === activeLesson 
          ? { ...comp, selectedMedia: media }
          : comp
      )
    );
    
    setShowMediaLibrary(false);
    toast({
      title: "Media Selected",
      description: `${media.type === 'video' ? 'Video' : 'Image'} "${media.name}" has been selected for this lesson.`
    });
  };

  const handleGenerateVideo = (lessonId: string) => {
    const lesson = lessonComponents.find(c => c.lessonId === lessonId);
    if (!lesson || !lesson.selectedMedia || !lesson.voiceUrl) {
      toast({
        title: "Missing Components",
        description: "Please select media and ensure voice is generated before creating the video.",
        variant: "destructive"
      });
      return;
    }

    generateVideoMutation.mutate({
      lessonId,
      mediaUrl: lesson.selectedMedia.url,
      voiceUrl: lesson.voiceUrl,
      script: lesson.script,
      settings: previewSettings
    });
  };

  const handleGenerateAllVideos = () => {
    const readyLessons = lessonComponents.filter(
      lesson => lesson.selectedMedia && lesson.voiceUrl && lesson.videoStatus === 'pending'
    );

    if (readyLessons.length === 0) {
      toast({
        title: "No Lessons Ready",
        description: "Please ensure all lessons have media selected and voice generated.",
        variant: "destructive"
      });
      return;
    }

    readyLessons.forEach(lesson => {
      setTimeout(() => handleGenerateVideo(lesson.lessonId), Math.random() * 1000);
    });
  };

  const getCompletionStats = () => {
    const total = lessonComponents.length;
    const withMedia = lessonComponents.filter(c => c.selectedMedia).length;
    const withVoice = lessonComponents.filter(c => c.voiceUrl).length;
    const completed = lessonComponents.filter(c => c.videoStatus === 'completed').length;
    
    return { total, withMedia, withVoice, completed };
  };

  const stats = getCompletionStats();
  const overallProgress = stats.total > 0 ? (stats.completed / stats.total) * 100 : 0;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold mb-3">Course Video Production Studio</h2>
        <p className="text-muted-foreground text-lg">
          Combine your content, media, and voices into complete video lessons
        </p>
      </div>

      {/* Progress Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Layers className="w-5 h-5" />
            Production Progress
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{stats.total}</div>
              <div className="text-sm text-muted-foreground">Total Lessons</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{stats.withMedia}</div>
              <div className="text-sm text-muted-foreground">Media Selected</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">{stats.withVoice}</div>
              <div className="text-sm text-muted-foreground">Voice Generated</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">{stats.completed}</div>
              <div className="text-sm text-muted-foreground">Videos Complete</div>
            </div>
          </div>
          
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Overall Progress</span>
              <span>{Math.round(overallProgress)}%</span>
            </div>
            <Progress value={overallProgress} className="h-2" />
          </div>
        </CardContent>
      </Card>

      {/* Production Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="w-5 h-5" />
            Video Settings
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium mb-2">Video Transition</label>
              <Select 
                value={previewSettings.videoTransition} 
                onValueChange={(value) => setPreviewSettings(prev => ({ ...prev, videoTransition: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="fade">Fade</SelectItem>
                  <SelectItem value="slide">Slide</SelectItem>
                  <SelectItem value="zoom">Zoom</SelectItem>
                  <SelectItem value="none">None</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-2">Text Animation</label>
              <Select 
                value={previewSettings.textAnimation} 
                onValueChange={(value) => setPreviewSettings(prev => ({ ...prev, textAnimation: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="typewriter">Typewriter</SelectItem>
                  <SelectItem value="fade-in">Fade In</SelectItem>
                  <SelectItem value="slide-up">Slide Up</SelectItem>
                  <SelectItem value="none">None</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-2">Voice Volume: {previewSettings.voiceVolume}%</label>
              <Slider
                value={[previewSettings.voiceVolume]}
                onValueChange={([value]) => setPreviewSettings(prev => ({ ...prev, voiceVolume: value }))}
                max={100}
                step={5}
                className="w-full"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-2">Music Volume: {previewSettings.musicVolume}%</label>
              <Slider
                value={[previewSettings.musicVolume]}
                onValueChange={([value]) => setPreviewSettings(prev => ({ ...prev, musicVolume: value }))}
                max={100}
                step={5}
                className="w-full"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Lesson Production Timeline */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <FileVideo className="w-5 h-5" />
            Lesson Production Timeline
          </CardTitle>
          <Button 
            onClick={handleGenerateAllVideos}
            disabled={generateVideoMutation.isPending}
            className="gap-2"
          >
            <Wand2 className="w-4 h-4" />
            Generate All Videos
          </Button>
        </CardHeader>
        <CardContent className="space-y-4">
          {lessonComponents.map((lesson, index) => (
            <div
              key={lesson.lessonId}
              className="border rounded-lg p-4 space-y-4 hover:shadow-sm transition-shadow"
            >
              {/* Lesson Header */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-semibold">
                    {index + 1}
                  </div>
                  <div>
                    <h4 className="font-semibold">{lesson.lessonTitle}</h4>
                    <p className="text-sm text-muted-foreground">
                      {lesson.script ? `${lesson.script.length} characters` : 'No script'}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  {lesson.videoStatus === 'completed' && (
                    <Badge variant="default" className="bg-green-500">
                      <CheckCircle className="w-3 h-3 mr-1" />
                      Complete
                    </Badge>
                  )}
                  {lesson.videoStatus === 'processing' && (
                    <Badge variant="secondary">
                      <Loader2 className="w-3 h-3 mr-1 animate-spin" />
                      Processing
                    </Badge>
                  )}
                  {lesson.videoStatus === 'error' && (
                    <Badge variant="destructive">
                      <AlertCircle className="w-3 h-3 mr-1" />
                      Error
                    </Badge>
                  )}
                </div>
              </div>

              {/* Production Components */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* Media Selection */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Visual Media</label>
                  {lesson.selectedMedia ? (
                    <div className="border rounded-lg p-3 bg-green-50">
                      <div className="flex items-center gap-2 mb-2">
                        {lesson.selectedMedia.type === 'video' ? (
                          <VideoIcon className="w-4 h-4 text-green-600" />
                        ) : (
                          <ImageIcon className="w-4 h-4 text-green-600" />
                        )}
                        <span className="text-sm font-medium text-green-800">
                          {lesson.selectedMedia.name}
                        </span>
                      </div>
                      {lesson.selectedMedia.thumbnail && (
                        <img
                          src={lesson.selectedMedia.thumbnail}
                          alt={lesson.selectedMedia.name}
                          className="w-full h-20 object-cover rounded"
                        />
                      )}
                    </div>
                  ) : (
                    <Button
                      variant="outline"
                      className="w-full h-20 border-dashed"
                      onClick={() => {
                        setActiveLesson(lesson.lessonId);
                        setShowMediaLibrary(true);
                      }}
                    >
                      <Upload className="w-4 h-4 mr-2" />
                      Select Media
                    </Button>
                  )}
                </div>

                {/* Voice Status */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Voice Narration</label>
                  {lesson.voiceUrl ? (
                    <div className="border rounded-lg p-3 bg-blue-50">
                      <div className="flex items-center gap-2 mb-2">
                        <Volume2 className="w-4 h-4 text-blue-600" />
                        <span className="text-sm font-medium text-blue-800">
                          Voice Ready
                        </span>
                      </div>
                      {lesson.voiceDuration && (
                        <div className="flex items-center gap-1 text-xs text-blue-600">
                          <Clock className="w-3 h-3" />
                          {Math.round(lesson.voiceDuration)}s
                        </div>
                      )}
                      <audio
                        controls
                        className="w-full mt-2"
                        style={{ height: '32px' }}
                      >
                        <source src={lesson.voiceUrl} type="audio/mpeg" />
                      </audio>
                    </div>
                  ) : (
                    <div className="border rounded-lg p-3 bg-gray-50 text-center">
                      <AlertCircle className="w-4 h-4 text-gray-400 mx-auto mb-1" />
                      <span className="text-sm text-gray-600">No voice generated</span>
                    </div>
                  )}
                </div>

                {/* Video Generation */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Final Video</label>
                  {lesson.videoStatus === 'completed' && lesson.videoUrl ? (
                    <div className="space-y-2">
                      <div className="border rounded-lg p-3 bg-green-50">
                        <div className="flex items-center gap-2 mb-2">
                          <CheckCircle className="w-4 h-4 text-green-600" />
                          <span className="text-sm font-medium text-green-800">
                            Video Ready
                          </span>
                        </div>
                        <video
                          controls
                          className="w-full h-20 object-cover rounded"
                          poster={lesson.selectedMedia?.thumbnail}
                        >
                          <source src={lesson.videoUrl} type="video/mp4" />
                        </video>
                      </div>
                      <Button
                        size="sm"
                        variant="outline"
                        className="w-full"
                        onClick={() => {
                          const a = document.createElement('a');
                          a.href = lesson.videoUrl!;
                          a.download = `${lesson.lessonTitle}.mp4`;
                          a.click();
                        }}
                      >
                        <Download className="w-3 h-3 mr-1" />
                        Download
                      </Button>
                    </div>
                  ) : lesson.videoStatus === 'processing' ? (
                    <div className="border rounded-lg p-3 bg-yellow-50">
                      <div className="flex items-center gap-2 mb-2">
                        <Loader2 className="w-4 h-4 text-yellow-600 animate-spin" />
                        <span className="text-sm font-medium text-yellow-800">
                          Generating Video
                        </span>
                      </div>
                      <Progress value={lesson.progress} className="h-2" />
                      <div className="text-xs text-yellow-600 mt-1">
                        {lesson.progress}% complete
                      </div>
                    </div>
                  ) : (
                    <Button
                      className="w-full h-20"
                      onClick={() => handleGenerateVideo(lesson.lessonId)}
                      disabled={!lesson.selectedMedia || !lesson.voiceUrl || generateVideoMutation.isPending}
                    >
                      <Play className="w-4 h-4 mr-2" />
                      Generate Video
                    </Button>
                  )}
                </div>
              </div>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Media Library Dialog */}
      <Dialog open={showMediaLibrary} onOpenChange={setShowMediaLibrary}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle>Select Media for Lesson</DialogTitle>
            <DialogDescription>
              Choose an image or video to use as the visual component for this lesson
            </DialogDescription>
          </DialogHeader>
          
          <Tabs value={selectedMediaType} onValueChange={(value: any) => setSelectedMediaType(value)}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="uploaded">My Media</TabsTrigger>
              <TabsTrigger value="stock">Stock Media</TabsTrigger>
            </TabsList>
            
            <TabsContent value="uploaded" className="max-h-96 overflow-y-auto">
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                {mediaLibrary?.map((media: any) => (
                  <div
                    key={media.id}
                    className="border rounded-lg overflow-hidden cursor-pointer hover:shadow-lg transition-shadow"
                    onClick={() => handleMediaSelect({
                      id: media.id.toString(),
                      type: media.type,
                      url: media.url,
                      thumbnail: media.url,
                      name: media.name
                    })}
                  >
                    <div className="aspect-video bg-gray-100 flex items-center justify-center">
                      {media.type === 'video' ? (
                        <video
                          src={media.url}
                          className="w-full h-full object-cover"
                          poster={media.url}
                        />
                      ) : (
                        <img
                          src={media.url}
                          alt={media.name}
                          className="w-full h-full object-cover"
                        />
                      )}
                    </div>
                    <div className="p-2">
                      <div className="flex items-center gap-1 mb-1">
                        {media.type === 'video' ? (
                          <VideoIcon className="w-3 h-3" />
                        ) : (
                          <ImageIcon className="w-3 h-3" />
                        )}
                        <span className="text-xs font-medium truncate">
                          {media.name}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </TabsContent>
            
            <TabsContent value="stock">
              <div className="text-center py-8 text-muted-foreground">
                Stock media integration coming soon
              </div>
            </TabsContent>
          </Tabs>
        </DialogContent>
      </Dialog>

      {/* Navigation */}
      <div className="flex justify-between pt-6">
        <Button variant="outline" onClick={onBack}>
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Quizzes
        </Button>
        
        <Button 
          onClick={onNext} 
          disabled={stats.completed < stats.total}
          className="gap-2"
        >
          Continue to Publishing
          <ArrowRight className="w-4 h-4" />
        </Button>
      </div>
    </div>
  );
}