/**
 * Enhanced PostgreSQL Database Configuration
 * Optimized for AILearnMaster with improved connection handling, monitoring, and performance
 */

import { Pool, neonConfig, PoolConfig } from '@neondatabase/serverless';
import { drizzle } from 'drizzle-orm/neon-serverless';
import ws from "ws";
import * as schema from "@shared/schema";
import { sql } from 'drizzle-orm';

// Configure Neon database for serverless environment
neonConfig.webSocketConstructor = ws;

// Database configuration interface
interface DatabaseConfig {
  connectionString: string;
  maxConnections: number;
  idleTimeoutMs: number;
  connectionTimeoutMs: number;
  statementTimeoutMs: number;
  queryTimeoutMs: number;
  retryAttempts: number;
  retryDelayMs: number;
}

// Environment-specific database configurations
const getDatabaseConfig = (): DatabaseConfig => {
  const isProduction = process.env.NODE_ENV === 'production';
  const isDevelopment = process.env.NODE_ENV === 'development';

  // Get database URL from secure secrets manager
  const { getSecret } = require('./security-fixes/secure-secrets-manager');
  let connectionString = getSecret('DATABASE_URL') || process.env.DATABASE_URL || '';

  // Enforce SSL in production and ensure it's in the connection string
  if (isProduction && connectionString && !connectionString.includes('sslmode=')) {
    connectionString += connectionString.includes('?') ? '&sslmode=require' : '?sslmode=require';
  }

  // Validate connection string
  if (!connectionString) {
    throw new Error('DATABASE_URL is required');
  }

  return {
    connectionString,
    maxConnections: isProduction ? 15 : 8, // Reduced for security
    idleTimeoutMs: isProduction ? 30000 : 20000, // Shorter timeouts
    connectionTimeoutMs: isProduction ? 8000 : 5000,
    statementTimeoutMs: isProduction ? 20000 : 15000, // Prevent long queries
    queryTimeoutMs: isProduction ? 15000 : 10000,
    retryAttempts: isProduction ? 2 : 2, // Reduced retry attempts
    retryDelayMs: isProduction ? 1000 : 1000
  };
};

// Database connection state
interface DatabaseState {
  pool: Pool | null;
  db: ReturnType<typeof drizzle> | null;
  isConnected: boolean;
  lastHealthCheck: Date | null;
  connectionAttempts: number;
  lastError: Error | null;
}

class DatabaseManager {
  private state: DatabaseState = {
    pool: null,
    db: null,
    isConnected: false,
    lastHealthCheck: null,
    connectionAttempts: 0,
    lastError: null
  };

  private config: DatabaseConfig;
  private healthCheckInterval: NodeJS.Timeout | null = null;

  constructor() {
    this.config = getDatabaseConfig();
    this.initialize();
    this.startHealthChecking();
  }

  /**
   * Initialize database connection with retry logic
   */
  private async initialize(): Promise<void> {
    if (!this.config.connectionString) {
      console.error("❌ DATABASE_URL is not set. Database operations will be disabled.");
      return;
    }

    for (let attempt = 1; attempt <= this.config.retryAttempts; attempt++) {
      try {
        console.log(`🔄 Attempting database connection (${attempt}/${this.config.retryAttempts})...`);
        
        // Create connection pool with optimized settings
        const poolConfig: PoolConfig = {
          connectionString: this.config.connectionString,
          max: this.config.maxConnections,
          idleTimeoutMillis: this.config.idleTimeoutMs,
          connectionTimeoutMillis: this.config.connectionTimeoutMs,
          statementTimeoutMillis: this.config.statementTimeoutMs,
          queryTimeoutMillis: this.config.queryTimeoutMs,
          // Additional optimizations
          allowExitOnIdle: true,
          keepAlive: true,
          keepAliveInitialDelayMillis: 10000
        };

        this.state.pool = new Pool(poolConfig);
        
        // Initialize Drizzle ORM
        this.state.db = drizzle({ 
          client: this.state.pool, 
          schema,
          logger: process.env.NODE_ENV === 'development'
        });

        // Test connection
        await this.testConnection();
        
        this.state.isConnected = true;
        this.state.connectionAttempts = attempt;
        this.state.lastError = null;
        
        console.log(`✅ Database connection established successfully (attempt ${attempt})`);
        console.log(`📊 Pool config: max=${this.config.maxConnections}, timeout=${this.config.connectionTimeoutMs}ms`);
        
        return;
        
      } catch (error) {
        this.state.lastError = error as Error;
        console.error(`❌ Database connection attempt ${attempt} failed:`, error);
        
        if (attempt < this.config.retryAttempts) {
          console.log(`⏳ Retrying in ${this.config.retryDelayMs}ms...`);
          await this.delay(this.config.retryDelayMs);
        }
      }
    }

    console.error("❌ All database connection attempts failed. Running in fallback mode.");
    this.state.isConnected = false;
  }

  /**
   * Test database connection with a simple query
   */
  private async testConnection(): Promise<void> {
    if (!this.state.db) {
      throw new Error("Database not initialized");
    }

    const result = await this.state.db.execute(sql`SELECT 1 as test`);
    if (!result || result.length === 0) {
      throw new Error("Database test query failed");
    }
  }

  /**
   * Start periodic health checking
   */
  private startHealthChecking(): void {
    // Health check every 30 seconds
    this.healthCheckInterval = setInterval(async () => {
      await this.performHealthCheck();
    }, 30000);
  }

  /**
   * Perform database health check
   */
  private async performHealthCheck(): Promise<boolean> {
    try {
      if (!this.state.db) {
        return false;
      }

      await this.testConnection();
      this.state.lastHealthCheck = new Date();
      
      if (!this.state.isConnected) {
        console.log("✅ Database connection restored");
        this.state.isConnected = true;
      }
      
      return true;
    } catch (error) {
      if (this.state.isConnected) {
        console.error("❌ Database health check failed:", error);
        this.state.isConnected = false;
        this.state.lastError = error as Error;
      }
      return false;
    }
  }

  /**
   * Get database connection status
   */
  public getStatus() {
    return {
      isConnected: this.state.isConnected,
      lastHealthCheck: this.state.lastHealthCheck,
      connectionAttempts: this.state.connectionAttempts,
      lastError: this.state.lastError?.message,
      config: {
        maxConnections: this.config.maxConnections,
        environment: process.env.NODE_ENV || 'development'
      }
    };
  }

  /**
   * Get database instance (with null check)
   */
  public getDatabase() {
    if (!this.state.isConnected || !this.state.db) {
      throw new Error("Database not available. Check connection status.");
    }
    return this.state.db;
  }

  /**
   * Get pool instance (with null check)
   */
  public getPool() {
    if (!this.state.isConnected || !this.state.pool) {
      throw new Error("Database pool not available. Check connection status.");
    }
    return this.state.pool;
  }

  /**
   * Safely execute database operation with error handling
   */
  public async safeExecute<T>(operation: () => Promise<T>): Promise<T | null> {
    try {
      if (!this.state.isConnected) {
        console.warn("⚠️ Database not connected, skipping operation");
        return null;
      }
      return await operation();
    } catch (error) {
      console.error("❌ Database operation failed:", error);
      this.state.lastError = error as Error;
      
      // Try to reconnect if connection was lost
      if (this.isConnectionError(error)) {
        console.log("🔄 Attempting to reconnect...");
        await this.initialize();
      }
      
      return null;
    }
  }

  /**
   * Check if error is connection-related
   */
  private isConnectionError(error: any): boolean {
    const connectionErrors = [
      'ECONNREFUSED',
      'ENOTFOUND',
      'ETIMEDOUT',
      'connection terminated',
      'connection closed'
    ];
    
    const errorMessage = error?.message?.toLowerCase() || '';
    return connectionErrors.some(err => errorMessage.includes(err));
  }

  /**
   * Graceful shutdown
   */
  public async shutdown(): Promise<void> {
    console.log("🔄 Shutting down database connections...");
    
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }
    
    if (this.state.pool) {
      await this.state.pool.end();
    }
    
    this.state.isConnected = false;
    console.log("✅ Database connections closed");
  }

  /**
   * Utility delay function
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Create singleton database manager
const dbManager = new DatabaseManager();

// Export database instances (with backward compatibility)
export const pool = dbManager.getPool.bind(dbManager);
export const db = dbManager.getDatabase.bind(dbManager);

// Export enhanced database manager
export { dbManager };

// Export safe database operations
export const safeDbOperation = dbManager.safeExecute.bind(dbManager);

// Export database status
export const getDatabaseStatus = dbManager.getStatus.bind(dbManager);

// Graceful shutdown handler
process.on('SIGTERM', async () => {
  await dbManager.shutdown();
  process.exit(0);
});

process.on('SIGINT', async () => {
  await dbManager.shutdown();
  process.exit(0);
});
