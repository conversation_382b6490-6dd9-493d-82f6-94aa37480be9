{"name": "kokoro-js", "version": "1.2.1", "type": "module", "exports": {"types": "./types/kokoro.d.ts", "node": {"import": "./dist/kokoro.js", "require": "./dist/kokoro.cjs"}, "default": "./dist/kokoro.js"}, "scripts": {"build": "rm -rf dist types && rollup -c && tsc && cp ../LICENSE LICENSE", "format": "prettier --write . --print-width 1000", "test": "vitest run"}, "keywords": ["kokoro", "tts", "text-to-speech"], "author": {"name": "hexgrad", "email": "<EMAIL>"}, "browser": {"path": false, "fs/promises": false}, "contributors": ["<PERSON><PERSON><PERSON>"], "license": "Apache-2.0", "description": "High-quality text-to-speech for the web", "dependencies": {"@huggingface/transformers": "^3.5.1", "phonemizer": "^1.2.1"}, "devDependencies": {"@rollup/plugin-node-resolve": "^16.0.0", "@rollup/plugin-terser": "^0.4.4", "prettier": "3.4.2", "rollup": "^4.30.1", "typescript": "^5.7.3", "vitest": "^3.1.2"}, "files": ["types", "dist", "voices", "README.md", "LICENSE"], "homepage": "https://github.com/hexgrad/kokoro", "repository": {"type": "git", "url": "git+https://github.com/hexgrad/kokoro.git"}, "publishConfig": {"access": "public"}, "jsdelivr": "./dist/kokoro.web.js", "unpkg": "./dist/kokoro.web.js"}