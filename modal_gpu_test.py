import modal

# Create Modal app
app = modal.App("courseai-gpu-test")

# Define A100 GPU image
gpu_image = modal.Image.debian_slim(python_version="3.11").pip_install([
    "torch==2.1.0", "torchvision==0.16.0", "torchaudio==2.1.0"
])

@app.function(
    gpu="A100-80GB",
    timeout=300,
    memory=16384,
    image=gpu_image
)
def test_gpu():
    """Simple GPU test function"""
    import torch
    
    # Check GPU availability
    gpu_available = torch.cuda.is_available()
    gpu_count = torch.cuda.device_count() if gpu_available else 0
    
    gpu_info = {}
    if gpu_available:
        gpu_info = {
            "name": torch.cuda.get_device_name(0),
            "memory_gb": torch.cuda.get_device_properties(0).total_memory / 1024**3,
            "compute_capability": torch.cuda.get_device_capability(0)
        }
        
        # Test GPU computation
        test_tensor = torch.randn(1000, 1000, device='cuda')
        result = torch.mm(test_tensor, test_tensor.T)
        gpu_info["test_passed"] = result.shape == (1000, 1000)
    
    return {
        "gpu_available": gpu_available,
        "gpu_count": gpu_count,
        "gpu_info": gpu_info,
        "status": "success" if gpu_available else "no_gpu"
    }

@app.local_entrypoint()
def main():
    """Test the GPU deployment"""
    print("Testing A100 GPU...")
    result = test_gpu.remote()
    print(f"GPU Test Result: {result}")
    
    if result.get("gpu_available"):
        print("✅ A100 GPU is active and working!")
        print(f"GPU: {result['gpu_info']['name']}")
        print(f"Memory: {result['gpu_info']['memory_gb']:.1f}GB")
    else:
        print("❌ GPU not available")
    
    return result

if __name__ == "__main__":
    main()