import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage, FormDescription } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Card, CardContent } from "@/components/ui/card";
import { UserCircle, Video, User, PlaySquare } from "lucide-react";

interface CourseDetailsProps {
  onNext: (values: FormValues) => void;
  initialValues?: {
    title: string;
    category: string;
    description: string;
    targetAudience?: string;
    useAI: boolean;
    format?: 'avatar' | 'traditional';
  };
}

const formSchema = z.object({
  title: z.string().min(5, "Title must be at least 5 characters"),
  category: z.string().min(1, "Please select a category"),
  description: z.string().min(20, "Description must be at least 20 characters"),
  targetAudience: z.string().optional(),
  useAI: z.boolean().default(true),
  format: z.enum(['avatar', 'traditional']).default('traditional'),
  moduleCount: z.number().min(1, "At least one module is required").max(15, "Maximum 15 modules allowed").default(3)
});

type FormValues = z.infer<typeof formSchema>;

export function CourseDetails({ onNext, initialValues }: CourseDetailsProps) {
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: initialValues || {
      title: "",
      category: "",
      description: "",
      targetAudience: "",
      useAI: true,
      format: "traditional",
      moduleCount: 3
    }
  });

  const onSubmit = (values: FormValues) => {
    console.log("Course details:", values);
    onNext(values);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="title"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Course Title</FormLabel>
                <FormControl>
                  <Input 
                    placeholder="e.g. Digital Marketing Fundamentals" 
                    {...field} 
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="category"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Category</FormLabel>
                <Select 
                  onValueChange={field.onChange} 
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a category" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="Business">Business</SelectItem>
                    <SelectItem value="Technology">Technology</SelectItem>
                    <SelectItem value="Design">Design</SelectItem>
                    <SelectItem value="Marketing">Marketing</SelectItem>
                    <SelectItem value="Personal Development">Personal Development</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        
        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Course Description</FormLabel>
              <FormControl>
                <Textarea 
                  placeholder="Describe what your course is about..." 
                  className="resize-none" 
                  rows={4} 
                  {...field} 
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="targetAudience"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Target Audience</FormLabel>
              <FormControl>
                <Textarea 
                  placeholder="Who is this course for?" 
                  className="resize-none" 
                  rows={2} 
                  {...field} 
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="format"
          render={({ field }) => (
            <>
              <FormLabel>Course Format</FormLabel>
              <FormDescription>
                Choose how you want to present your course content
              </FormDescription>
              <FormControl>
                <RadioGroup
                  onValueChange={field.onChange}
                  value={field.value}
                  className="grid grid-cols-2 gap-4"
                >
                  <div className={`relative rounded-lg border-2 p-1 ${field.value === 'avatar' ? 'border-primary bg-primary/5' : 'border-muted'}`}>
                    <RadioGroupItem
                      value="avatar"
                      id="avatar"
                      className="sr-only"
                    />
                    <Card className="cursor-pointer h-full">
                      <CardContent className="p-4 flex flex-col items-center gap-2 text-center">
                        <div className="w-14 h-14 rounded-full bg-primary/20 flex items-center justify-center mb-2">
                          <UserCircle className="h-8 w-8 text-primary" />
                        </div>
                        <h3 className="font-semibold text-lg">Talking Avatar</h3>
                        <p className="text-sm text-muted-foreground">
                          Create an engaging video course with a virtual presenter using AI-generated avatar
                        </p>
                        <ul className="text-xs text-left w-full mt-2 space-y-1 text-muted-foreground">
                          <li className="flex items-center gap-1">
                            <span className="text-primary">✓</span> AI-generated voice narration
                          </li>
                          <li className="flex items-center gap-1">
                            <span className="text-primary">✓</span> Personalized talking avatar
                          </li>
                          <li className="flex items-center gap-1">
                            <span className="text-primary">✓</span> Animated presentations
                          </li>
                        </ul>
                      </CardContent>
                    </Card>
                  </div>
                  
                  <div className={`relative rounded-lg border-2 p-1 ${field.value === 'traditional' ? 'border-primary bg-primary/5' : 'border-muted'}`}>
                    <RadioGroupItem
                      value="traditional"
                      id="traditional"
                      className="sr-only"
                    />
                    <Card className="cursor-pointer h-full">
                      <CardContent className="p-4 flex flex-col items-center gap-2 text-center">
                        <div className="w-14 h-14 rounded-full bg-primary/20 flex items-center justify-center mb-2">
                          <Video className="h-8 w-8 text-primary" />
                        </div>
                        <h3 className="font-semibold text-lg">Traditional Format</h3>
                        <p className="text-sm text-muted-foreground">
                          Create a classic video course using slides, screen recordings, or uploaded media
                        </p>
                        <ul className="text-xs text-left w-full mt-2 space-y-1 text-muted-foreground">
                          <li className="flex items-center gap-1">
                            <span className="text-primary">✓</span> Upload your own videos
                          </li>
                          <li className="flex items-center gap-1">
                            <span className="text-primary">✓</span> Screen recordings
                          </li>
                          <li className="flex items-center gap-1">
                            <span className="text-primary">✓</span> Custom slide presentations
                          </li>
                        </ul>
                      </CardContent>
                    </Card>
                  </div>
                </RadioGroup>
              </FormControl>
              <FormMessage />
            </>
          )}
        />

        <FormField
          control={form.control}
          name="moduleCount"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Number of Modules</FormLabel>
              <FormDescription>
                Define how many modules your course will have. Each module will have its own script.
              </FormDescription>
              <FormControl>
                <div className="flex items-center space-x-2">
                  <Input
                    type="number"
                    min={1}
                    max={15}
                    className="w-24"
                    value={field.value}
                    onChange={e => {
                      const value = parseInt(e.target.value);
                      field.onChange(value > 0 && value <= 15 ? value : 3);
                    }}
                  />
                  <span className="text-sm text-muted-foreground">modules</span>
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="useAI"
          render={({ field }) => (
            <FormItem>
              <FormLabel>AI Content Generation</FormLabel>
              <div className="bg-secondary-50 border border-secondary-200 rounded-lg p-4">
                <div className="flex items-start">
                  <FormControl>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={field.onChange}
                      className="mt-1"
                    />
                  </FormControl>
                  <div className="ml-3 text-sm">
                    <label className="font-medium text-slate-700">Use AI to help generate course content</label>
                    <p className="text-slate-500">Our AI will analyze your course details and suggest a structure, modules, and content ideas.</p>
                  </div>
                </div>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <div className="flex justify-end">
          <button 
            type="submit" 
            className="bg-primary hover:bg-primary-700 text-white rounded-md px-6 py-2 font-medium"
          >
            Continue to Next Step
          </button>
        </div>
      </form>
    </Form>
  );
}
