/**
 * Marp Slide Generation Service
 * Integrates Modal A100 GPU-powered slide generation with Course AI Platform
 * Supports both Traditional and Avatar course workflows
 */

import fetch from 'cross-fetch';

interface SlideGenerationRequest {
  script_content: string;
  course_title: string;
  lesson_title?: string;
  style_theme?: string;
  slide_count_target?: number;
  include_animations?: boolean;
  custom_branding?: {
    primary_color?: string;
    text_color?: string;
    font_family?: string;
    logo_url?: string;
  };
}

interface BatchSlideRequest {
  course_modules: Array<{
    id: string;
    title: string;
    script: string;
    target_slides?: number;
  }>;
  course_title: string;
  global_theme?: string;
  custom_branding?: any;
}

interface SlideGenerationResult {
  success: boolean;
  slide_data?: {
    marp_markdown: string;
    slide_structure: any[];
    slide_count: number;
    analysis: any;
    theme: string;
  };
  rendered_outputs?: {
    html_base64: string;
    pdf_base64: string;
    pptx_base64?: string;
    slides_png: string[];
  };
  metadata?: {
    course_title: string;
    lesson_title?: string;
    generation_time_seconds: number;
    script_length: number;
    target_slide_count: number;
    actual_slide_count: number;
    theme_used: string;
    gpu_used: boolean;
    timestamp: number;
  };
  error?: string;
}

interface BatchSlideResult {
  batch_results: Array<{
    module_id: string;
    module_title: string;
    success: boolean;
    slide_data?: any;
    rendered_outputs?: any;
    metadata?: any;
    error?: string;
    processing_time: number;
    progress: number;
  }>;
  summary: {
    total_modules: number;
    successful_modules: number;
    failed_modules: number;
    total_processing_time: number;
    average_time_per_module: number;
    course_title: string;
    theme_used: string;
    error_occurred?: boolean;
  };
  error?: string;
}

export class MarpSlideService {
  private modalEndpoint: string;
  private modalToken: string;
  private fallbackEnabled: boolean;

  constructor() {
    this.modalEndpoint = process.env.MODAL_MARP_ENDPOINT || 'https://courseai-marp-slidegen.modal.run';
    this.modalToken = process.env.MODAL_TOKEN_SECRET || '';
    this.fallbackEnabled = true;
  }

  /**
   * Check health status of Marp slide generation service
   */
  async healthCheck(): Promise<{ status: string; service: string; [key: string]: any }> {
    try {
      const response = await fetch(`${this.modalEndpoint}/api_marp_health`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.modalToken}`
        },
        timeout: 30000
      });

      if (!response.ok) {
        throw new Error(`Health check failed: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Marp service health check failed:', error);
      return {
        status: 'error',
        service: 'Marp Slide Generation',
        error: error instanceof Error ? error.message : 'Unknown error',
        fallback_available: this.fallbackEnabled
      };
    }
  }

  /**
   * Generate slides from course script using A100 GPU acceleration
   */
  async generateSlides(request: SlideGenerationRequest): Promise<SlideGenerationResult> {
    try {
      // Validate input
      if (!request.script_content || request.script_content.trim().length < 50) {
        throw new Error('Script content must be at least 50 characters long');
      }

      if (!request.course_title || request.course_title.trim().length === 0) {
        throw new Error('Course title is required');
      }

      console.log(`Generating slides for: ${request.course_title} - ${request.lesson_title || 'Main Course'}`);

      // Attempt A100 GPU generation
      const response = await fetch(`${this.modalEndpoint}/api_generate_slides`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.modalToken}`
        },
        body: JSON.stringify({
          script_content: request.script_content,
          course_title: request.course_title,
          lesson_title: request.lesson_title || '',
          style_theme: request.style_theme || 'default',
          slide_count_target: request.slide_count_target || 10,
          include_animations: request.include_animations !== false,
          custom_branding: request.custom_branding
        }),
        timeout: 600000 // 10 minute timeout for slide generation
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`A100 slide generation failed: ${response.status} - ${errorText}`);
      }

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Slide generation failed');
      }

      return result;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error('A100 GPU slide generation failed, attempting fallback:', errorMessage);
      
      // Check for specific A100 GPU issues
      if (errorMessage.includes('Token validation failed') || 
          errorMessage.includes('billing') || 
          errorMessage.includes('quota') ||
          errorMessage.includes('payment method')) {
        console.warn('Modal A100 GPU unavailable due to authentication/billing issues');
      }

      // Fallback to local slide generation
      if (this.fallbackEnabled) {
        return await this.generateSlidesLocal(request);
      }

      return {
        success: false,
        error: errorMessage
      };
    }
  }

  /**
   * Generate slides for multiple course modules in batch
   */
  async generateBatchSlides(request: BatchSlideRequest): Promise<BatchSlideResult> {
    try {
      // Validate input
      if (!request.course_modules || request.course_modules.length === 0) {
        throw new Error('At least one course module is required');
      }

      if (!request.course_title || request.course_title.trim().length === 0) {
        throw new Error('Course title is required');
      }

      console.log(`Starting batch slide generation for ${request.course_modules.length} modules`);

      // Attempt A100 GPU batch generation
      const response = await fetch(`${this.modalEndpoint}/api_batch_slides`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.modalToken}`
        },
        body: JSON.stringify({
          course_modules: request.course_modules,
          course_title: request.course_title,
          global_theme: request.global_theme || 'default',
          custom_branding: request.custom_branding
        }),
        timeout: 1800000 // 30 minute timeout for batch processing
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`A100 batch slide generation failed: ${response.status} - ${errorText}`);
      }

      const result = await response.json();
      return result;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error('A100 GPU batch slide generation failed:', errorMessage);

      // Fallback to sequential local generation
      if (this.fallbackEnabled) {
        return await this.generateBatchSlidesLocal(request);
      }

      return {
        batch_results: [],
        summary: {
          total_modules: request.course_modules.length,
          successful_modules: 0,
          failed_modules: request.course_modules.length,
          total_processing_time: 0,
          average_time_per_module: 0,
          course_title: request.course_title,
          theme_used: request.global_theme || 'default',
          error_occurred: true
        },
        error: errorMessage
      };
    }
  }

  /**
   * Local fallback slide generation (simplified version)
   */
  private async generateSlidesLocal(request: SlideGenerationRequest): Promise<SlideGenerationResult> {
    try {
      console.log('Using local slide generation fallback');

      // Simple slide structure generation
      const slides = this.createBasicSlideStructure(
        request.script_content,
        request.course_title,
        request.lesson_title,
        request.slide_count_target || 10
      );

      // Generate basic Marp markdown
      const marpMarkdown = this.generateBasicMarpMarkdown(slides, request.style_theme || 'default');

      // Create simplified output (HTML only for local)
      const htmlContent = this.generateBasicHTML(marpMarkdown, request.course_title);
      const htmlBase64 = Buffer.from(htmlContent).toString('base64');

      return {
        success: true,
        slide_data: {
          marp_markdown: marpMarkdown,
          slide_structure: slides,
          slide_count: slides.length,
          analysis: { method: 'local_fallback' },
          theme: request.style_theme || 'default'
        },
        rendered_outputs: {
          html_base64: htmlBase64,
          pdf_base64: '', // Not available in local fallback
          slides_png: []
        },
        metadata: {
          course_title: request.course_title,
          lesson_title: request.lesson_title,
          generation_time_seconds: 2.0, // Estimated local generation time
          script_length: request.script_content.length,
          target_slide_count: request.slide_count_target || 10,
          actual_slide_count: slides.length,
          theme_used: request.style_theme || 'default',
          gpu_used: false,
          timestamp: Date.now() / 1000
        }
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Local generation failed';
      console.error('Local slide generation failed:', errorMessage);

      return {
        success: false,
        error: errorMessage
      };
    }
  }

  /**
   * Local fallback batch slide generation
   */
  private async generateBatchSlidesLocal(request: BatchSlideRequest): Promise<BatchSlideResult> {
    const results = [];
    const startTime = Date.now();

    for (let i = 0; i < request.course_modules.length; i++) {
      const module = request.course_modules[i];
      const moduleStart = Date.now();

      try {
        const slideRequest: SlideGenerationRequest = {
          script_content: module.script,
          course_title: request.course_title,
          lesson_title: module.title,
          style_theme: request.global_theme,
          slide_count_target: module.target_slides,
          custom_branding: request.custom_branding
        };

        const result = await this.generateSlidesLocal(slideRequest);

        results.push({
          module_id: module.id,
          module_title: module.title,
          success: result.success,
          slide_data: result.slide_data,
          rendered_outputs: result.rendered_outputs,
          metadata: result.metadata,
          error: result.error,
          processing_time: (Date.now() - moduleStart) / 1000,
          progress: ((i + 1) / request.course_modules.length) * 100
        });

      } catch (error) {
        results.push({
          module_id: module.id,
          module_title: module.title,
          success: false,
          processing_time: (Date.now() - moduleStart) / 1000,
          progress: ((i + 1) / request.course_modules.length) * 100,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    const totalTime = (Date.now() - startTime) / 1000;
    const successfulCount = results.filter(r => r.success).length;

    return {
      batch_results: results,
      summary: {
        total_modules: request.course_modules.length,
        successful_modules: successfulCount,
        failed_modules: request.course_modules.length - successfulCount,
        total_processing_time: totalTime,
        average_time_per_module: totalTime / request.course_modules.length,
        course_title: request.course_title,
        theme_used: request.global_theme || 'default'
      }
    };
  }

  /**
   * Create basic slide structure from script content
   */
  private createBasicSlideStructure(
    script: string, 
    courseTitle: string, 
    lessonTitle?: string, 
    targetCount: number = 10
  ): any[] {
    const slides = [];

    // Title slide
    slides.push({
      type: 'title',
      title: lessonTitle || courseTitle,
      subtitle: lessonTitle ? `Course: ${courseTitle}` : '',
      content: [],
      slide_number: 1
    });

    // Split script into sections
    const paragraphs = script.split('\n\n').filter(p => p.trim().length > 50);
    const sectionsPerSlide = Math.max(1, Math.floor(paragraphs.length / (targetCount - 2)));

    for (let i = 0; i < paragraphs.length; i += sectionsPerSlide) {
      const sectionGroup = paragraphs.slice(i, i + sectionsPerSlide);
      const slideTitle = this.extractSlideTitle(sectionGroup[0]);
      const bulletPoints = this.createBulletPoints(sectionGroup);

      slides.push({
        type: 'content',
        title: slideTitle,
        content: bulletPoints,
        slide_number: slides.length + 1
      });

      if (slides.length >= targetCount - 1) break;
    }

    // Summary slide
    slides.push({
      type: 'summary',
      title: 'Key Takeaways',
      content: ['Main concepts covered', 'Important insights', 'Next steps'],
      slide_number: slides.length + 1
    });

    return slides;
  }

  private extractSlideTitle(text: string): string {
    const sentences = text.split(/[.!?]+/);
    const firstSentence = sentences[0]?.trim() || '';
    const words = firstSentence.split(' ').slice(0, 6);
    return words.join(' ').replace(/[^\w\s]/g, '') || 'Course Content';
  }

  private createBulletPoints(sections: string[]): string[] {
    const points = [];
    for (const section of sections) {
      const sentences = section.split(/[.!?]+/).filter(s => s.trim().length > 20);
      points.push(...sentences.slice(0, 2).map(s => s.trim()));
      if (points.length >= 5) break;
    }
    return points.slice(0, 5);
  }

  /**
   * Generate basic Marp markdown
   */
  private generateBasicMarpMarkdown(slides: any[], theme: string): string {
    let markdown = `---
marp: true
theme: ${theme}
size: 16:9
paginate: true
backgroundColor: #ffffff
color: #333333
---

`;

    for (let i = 0; i < slides.length; i++) {
      const slide = slides[i];

      if (slide.type === 'title') {
        markdown += `# ${slide.title}\n\n`;
        if (slide.subtitle) {
          markdown += `## ${slide.subtitle}\n\n`;
        }
      } else {
        markdown += `## ${slide.title}\n\n`;
        if (slide.content && slide.content.length > 0) {
          for (const item of slide.content) {
            markdown += `- ${item}\n`;
          }
          markdown += '\n';
        }
      }

      if (i < slides.length - 1) {
        markdown += '---\n\n';
      }
    }

    return markdown;
  }

  /**
   * Generate basic HTML from Marp markdown
   */
  private generateBasicHTML(markdown: string, title: string): string {
    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${title} - Slides</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .slide { background: white; margin: 20px 0; padding: 40px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #1e3a8a; text-align: center; }
        h2 { color: #1e40af; border-bottom: 2px solid #3b82f6; padding-bottom: 10px; }
        ul { font-size: 18px; line-height: 1.6; }
        li { margin: 10px 0; }
    </style>
</head>
<body>
    <div class="presentation">
        ${this.convertMarkdownToHTML(markdown)}
    </div>
</body>
</html>`;
  }

  private convertMarkdownToHTML(markdown: string): string {
    const slides = markdown.split('---').filter(slide => slide.trim());
    return slides.map(slide => {
      let html = slide
        .replace(/^# (.*$)/gm, '<h1>$1</h1>')
        .replace(/^## (.*$)/gm, '<h2>$1</h2>')
        .replace(/^- (.*$)/gm, '<li>$1</li>')
        .replace(/(<li>.*<\/li>)/gs, '<ul>$1</ul>')
        .replace(/<\/li>\s*<li>/g, '</li><li>');
      
      return `<div class="slide">${html}</div>`;
    }).join('');
  }

  /**
   * List available slide themes
   */
  getAvailableThemes(): string[] {
    return [
      'default',
      'gaia',
      'uncover',
      'academic',
      'corporate',
      'minimal'
    ];
  }

  /**
   * Get slide generation statistics
   */
  async getServiceStats(): Promise<any> {
    try {
      const health = await this.healthCheck();
      return {
        service_status: health.status,
        available_themes: this.getAvailableThemes(),
        fallback_enabled: this.fallbackEnabled,
        a100_gpu_available: health.status === 'healthy',
        last_check: new Date().toISOString()
      };
    } catch (error) {
      return {
        service_status: 'error',
        available_themes: this.getAvailableThemes(),
        fallback_enabled: this.fallbackEnabled,
        a100_gpu_available: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        last_check: new Date().toISOString()
      };
    }
  }
}

// Export singleton instance
export const marpSlideService = new MarpSlideService();