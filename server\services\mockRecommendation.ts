// This is a temporary mock implementation of the recommendation service
// In a production environment, this would use real user data and AI-based recommendations

export interface Course {
  id: number;
  title: string;
  description: string;
  category: string;
  thumbnail?: string;
  rating?: number;
  enrollmentCount?: number;
  level?: string;
  tags?: string[];
  createdAt?: string;
  updatedAt?: string;
}

export interface AIInsight {
  type: 'trending' | 'recommended' | 'personalized' | 'similar';
  message: string;
  confidence: number;
}

export interface RecommendationGroup {
  title: string;
  insights: AIInsight[];
  courses: Course[];
}

/**
 * Generate mock recommendations (to be replaced with real recommendations in production)
 */
export function getMockRecommendations(): RecommendationGroup[] {
  return [
    {
      title: "Popular Courses",
      insights: getMockCourses().slice(0, 5).map(course => ({
        type: 'trending',
        message: `${course.enrollmentCount}+ students enrolled in the past month`,
        confidence: 0.85
      })),
      courses: getMockCourses().slice(0, 5)
    },
    {
      title: "New Releases",
      insights: getMockCourses().slice(5, 10).map(course => ({
        type: 'recommended',
        message: `Fresh content just added in ${course.category}`,
        confidence: 0.75
      })),
      courses: getMockCourses().slice(5, 10)
    },
    {
      title: "Editor's Choice",
      insights: getMockCourses().slice(10, 15).map(course => ({
        type: 'personalized',
        message: `Specially curated content for ${course.category} learners`,
        confidence: 0.9
      })),
      courses: getMockCourses().slice(10, 15)
    }
  ];
}

/**
 * Generate mock courses for development purposes
 */
function getMockCourses(): Course[] {
  return [
    {
      id: 1,
      title: "Introduction to AI and Machine Learning",
      description: "Learn the fundamentals of AI and ML algorithms",
      category: "Technology",
      thumbnail: "https://images.unsplash.com/photo-1591453089816-0fbb971b454c?w=800&auto=format&fit=crop&q=60&ixlib=rb-4.0.3",
      rating: 4.8,
      enrollmentCount: 1245,
      level: "Beginner",
      tags: ["AI", "Machine Learning", "Technology"],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: 2,
      title: "Advanced React Development",
      description: "Master React hooks, context API, and performance optimization",
      category: "Programming",
      thumbnail: "https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=800&auto=format&fit=crop&q=60&ixlib=rb-4.0.3",
      rating: 4.7,
      enrollmentCount: 890,
      level: "Advanced",
      tags: ["React", "JavaScript", "Frontend"],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: 3,
      title: "Digital Marketing Masterclass",
      description: "Comprehensive guide to modern digital marketing strategies",
      category: "Marketing",
      thumbnail: "https://images.unsplash.com/photo-1533750516457-a7f992034fec?w=800&auto=format&fit=crop&q=60&ixlib=rb-4.0.3",
      rating: 4.6,
      enrollmentCount: 1560,
      level: "Intermediate",
      tags: ["Marketing", "Social Media", "SEO"],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: 4,
      title: "Financial Planning for Entrepreneurs",
      description: "Essential financial skills for business founders",
      category: "Business",
      thumbnail: "https://images.unsplash.com/photo-1579621970590-9d624316904b?w=800&auto=format&fit=crop&q=60&ixlib=rb-4.0.3",
      rating: 4.9,
      enrollmentCount: 720,
      level: "Intermediate",
      tags: ["Finance", "Entrepreneurship", "Business"],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: 5,
      title: "Mobile App Development with Flutter",
      description: "Build cross-platform mobile apps with Flutter and Dart",
      category: "Programming",
      thumbnail: "https://images.unsplash.com/photo-1551650992-ee4fd47df41f?w=800&auto=format&fit=crop&q=60&ixlib=rb-4.0.3",
      rating: 4.5,
      enrollmentCount: 950,
      level: "Intermediate",
      tags: ["Flutter", "Mobile Development", "Dart"],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: 6,
      title: "Data Science with Python",
      description: "Comprehensive data analysis and visualization techniques",
      category: "Technology",
      thumbnail: "https://images.unsplash.com/photo-1599658880436-c61792e70672?w=800&auto=format&fit=crop&q=60&ixlib=rb-4.0.3",
      rating: 4.7,
      enrollmentCount: 1120,
      level: "Intermediate",
      tags: ["Python", "Data Science", "Analytics"],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: 7,
      title: "Full-Stack Web Development",
      description: "End-to-end web application development with MERN stack",
      category: "Programming",
      thumbnail: "https://images.unsplash.com/photo-1571171637578-41bc2dd41cd2?w=800&auto=format&fit=crop&q=60&ixlib=rb-4.0.3",
      rating: 4.6,
      enrollmentCount: 840,
      level: "Advanced",
      tags: ["JavaScript", "React", "Node.js", "MongoDB"],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: 8,
      title: "UX/UI Design Principles",
      description: "Master the art of creating engaging user experiences",
      category: "Design",
      thumbnail: "https://images.unsplash.com/photo-1541462608143-67571c6738dd?w=800&auto=format&fit=crop&q=60&ixlib=rb-4.0.3",
      rating: 4.8,
      enrollmentCount: 760,
      level: "Beginner",
      tags: ["UX", "UI", "Design", "Figma"],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: 9,
      title: "Content Marketing Strategy",
      description: "Develop effective content strategies for digital platforms",
      category: "Marketing",
      thumbnail: "https://images.unsplash.com/photo-1543269865-cbf427effbad?w=800&auto=format&fit=crop&q=60&ixlib=rb-4.0.3",
      rating: 4.5,
      enrollmentCount: 680,
      level: "Intermediate",
      tags: ["Content", "Marketing", "Strategy"],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: 10,
      title: "Cloud Computing with AWS",
      description: "Deploy and scale applications on Amazon Web Services",
      category: "Technology",
      thumbnail: "https://images.unsplash.com/photo-1607799279861-4dd421887fb3?w=800&auto=format&fit=crop&q=60&ixlib=rb-4.0.3",
      rating: 4.7,
      enrollmentCount: 910,
      level: "Advanced",
      tags: ["AWS", "Cloud", "DevOps"],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: 11,
      title: "Cybersecurity Fundamentals",
      description: "Essential security practices for the digital age",
      category: "Technology",
      thumbnail: "https://images.unsplash.com/photo-1550751827-4bd374c3f58b?w=800&auto=format&fit=crop&q=60&ixlib=rb-4.0.3",
      rating: 4.8,
      enrollmentCount: 830,
      level: "Beginner",
      tags: ["Security", "Networking", "Privacy"],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: 12,
      title: "Project Management Professional",
      description: "Prepare for PMP certification with comprehensive training",
      category: "Business",
      thumbnail: "https://images.unsplash.com/photo-1572177812156-58036aae439c?w=800&auto=format&fit=crop&q=60&ixlib=rb-4.0.3",
      rating: 4.9,
      enrollmentCount: 1050,
      level: "Advanced",
      tags: ["Project Management", "PMP", "Leadership"],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: 13,
      title: "Graphic Design Masterclass",
      description: "From beginner to professional in modern graphic design",
      category: "Design",
      thumbnail: "https://images.unsplash.com/photo-1626785774573-4b799315345d?w=800&auto=format&fit=crop&q=60&ixlib=rb-4.0.3",
      rating: 4.6,
      enrollmentCount: 940,
      level: "All Levels",
      tags: ["Graphic Design", "Illustrator", "Photoshop"],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: 14,
      title: "Blockchain Development",
      description: "Build decentralized applications and smart contracts",
      category: "Technology",
      thumbnail: "https://images.unsplash.com/photo-1639762681057-408e52192e55?w=800&auto=format&fit=crop&q=60&ixlib=rb-4.0.3",
      rating: 4.7,
      enrollmentCount: 780,
      level: "Advanced",
      tags: ["Blockchain", "Ethereum", "Solidity"],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: 15,
      title: "Social Media Marketing",
      description: "Grow your brand with effective social media strategies",
      category: "Marketing",
      thumbnail: "https://images.unsplash.com/photo-1611162617213-7d7a39e9b1d7?w=800&auto=format&fit=crop&q=60&ixlib=rb-4.0.3",
      rating: 4.5,
      enrollmentCount: 1280,
      level: "Intermediate",
      tags: ["Social Media", "Marketing", "Branding"],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  ];
}