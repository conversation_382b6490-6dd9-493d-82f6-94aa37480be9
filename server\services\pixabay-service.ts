const PIXABAY_API_KEY = process.env.PIXABAY_API_KEY;
const PIXABAY_BASE_URL = 'https://pixabay.com/api';

interface PixabayImage {
  id: number;
  webformatURL: string;
  largeImageURL: string;
  tags: string;
  user: string;
  views: number;
  downloads: number;
  likes: number;
}

interface PixabayVideo {
  id: number;
  tags: string;
  user: string;
  videos: {
    large: {
      url: string;
      width: number;
      height: number;
      size: number;
    };
    medium: {
      url: string;
      width: number;
      height: number;
      size: number;
    };
    small: {
      url: string;
      width: number;
      height: number;
      size: number;
    };
    tiny: {
      url: string;
      width: number;
      height: number;
      size: number;
    };
  };
}

export async function searchPixabayMedia(query: string, type: 'image' | 'video' = 'image', per_page: number = 15) {
  if (!PIXABAY_API_KEY) {
    throw new Error('Pixabay API key not configured');
  }

  try {
    const endpoint = type === 'video' ? '/videos/' : '/';
    const url = `${PIXABAY_BASE_URL}${endpoint}?key=${PIXABAY_API_KEY}&q=${encodeURIComponent(query)}&per_page=${per_page}&safesearch=true`;
    
    const response = await fetch(url);

    if (!response.ok) {
      throw new Error(`Pixabay API error: ${response.statusText}`);
    }

    const data = await response.json();
    
    if (type === 'video') {
      return {
        results: data.hits?.map((video: PixabayVideo) => ({
          id: video.id.toString(),
          type: 'video',
          url: video.videos?.medium?.url || video.videos?.small?.url || '',
          thumbnailUrl: video.videos?.tiny?.url || '',
          title: video.tags.split(',')[0] || 'Video',
          author: video.user,
          source: 'pixabay',
          description: video.tags
        })) || [],
        total: data.totalHits || 0
      };
    } else {
      return {
        results: data.hits?.map((image: PixabayImage) => ({
          id: image.id.toString(),
          type: 'image',
          url: image.largeImageURL,
          thumbnailUrl: image.webformatURL,
          title: image.tags.split(',')[0] || 'Image',
          author: image.user,
          source: 'pixabay',
          description: image.tags
        })) || [],
        total: data.totalHits || 0
      };
    }
  } catch (error) {
    console.error('Pixabay search error:', error);
    throw error;
  }
}