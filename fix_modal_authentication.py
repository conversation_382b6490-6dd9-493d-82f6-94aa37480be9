#!/usr/bin/env python3
"""
Modal A100 GPU Authentication Fix Script
This script helps resolve Modal authentication issues and sets up proper credentials.
"""

import os
import sys
import subprocess
import json
from pathlib import Path
import time

def print_header():
    """Print script header"""
    print("🔧 Modal A100 GPU Authentication Fix")
    print("=" * 50)
    print()

def check_modal_installation():
    """Check if Modal is properly installed"""
    print("📦 Checking Modal installation...")
    try:
        import modal
        print(f"✓ Modal is installed (version: {modal.__version__})")
        return True
    except ImportError:
        print("❌ Modal is not installed")
        print("Installing Modal...")
        try:
            subprocess.run([sys.executable, '-m', 'pip', 'install', 'modal'], check=True)
            print("✓ Modal installed successfully")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install Modal: {e}")
            return False

def check_current_auth():
    """Check current Modal authentication status"""
    print("\n🔐 Checking current authentication...")
    
    # Check environment variables
    token_id = os.getenv('MODAL_TOKEN_ID')
    token_secret = os.getenv('MODAL_TOKEN_SECRET')
    
    if token_id and token_secret:
        print(f"✓ Found environment variables:")
        print(f"  MODAL_TOKEN_ID: {token_id[:8]}...")
        print(f"  MODAL_TOKEN_SECRET: {token_secret[:8]}...")
        
        # Validate token format
        if validate_token_format(token_id, token_secret):
            return test_modal_connection()
        else:
            print("❌ Token format appears incorrect")
            return False
    else:
        print("❌ No Modal credentials found in environment variables")
        return False

def validate_token_format(token_id, token_secret):
    """Validate Modal token format"""
    print("\n🔍 Validating token format...")
    
    # Modal tokens typically have specific patterns
    # Token ID should be alphanumeric and around 20-30 characters
    # Token secret should be longer, around 40-60 characters
    
    if len(token_id) < 15 or len(token_secret) < 30:
        print("❌ Tokens appear too short for Modal format")
        return False
    
    # Check for common incorrect prefixes
    if token_id.startswith(('ak-', 'wk-', 'sk-')):
        print("❌ Token ID has incorrect prefix (appears to be OpenAI/other service format)")
        return False
    
    if token_secret.startswith(('ak-', 'wk-', 'sk-')):
        print("❌ Token secret has incorrect prefix (appears to be OpenAI/other service format)")
        return False
    
    print("✓ Token format appears valid")
    return True

def test_modal_connection():
    """Test Modal connection with current credentials"""
    print("\n🔗 Testing Modal connection...")
    try:
        import modal
        
        # Try to list apps to test authentication
        apps = modal.App.list()
        print("✓ Modal authentication successful")
        print(f"  Found {len(apps)} apps in your account")
        return True
        
    except Exception as e:
        print(f"❌ Modal authentication failed: {e}")
        return False

def setup_browser_auth():
    """Setup Modal authentication using browser"""
    print("\n🌐 Setting up Modal authentication via browser...")
    print("This will open your browser to authenticate with Modal.")
    print("Make sure you have:")
    print("1. A Modal account (sign up at https://modal.com)")
    print("2. A verified payment method for GPU billing")
    print()
    
    input("Press Enter to continue...")
    
    try:
        subprocess.run([sys.executable, '-m', 'modal', 'token', 'new'], check=True)
        print("✓ Browser authentication completed")
        return test_modal_connection()
    except subprocess.CalledProcessError as e:
        print(f"❌ Browser authentication failed: {e}")
        return False

def setup_manual_auth():
    """Setup Modal authentication manually with token input"""
    print("\n🔑 Manual Token Setup")
    print("=" * 30)
    print()
    print("To get your Modal API tokens:")
    print("1. Go to https://modal.com/settings/tokens")
    print("2. Create a new token if you don't have one")
    print("3. Copy the Token ID and Token Secret")
    print()
    print("⚠️  Important: Make sure these are Modal tokens, not OpenAI or other service tokens!")
    print()
    
    token_id = input("Enter your MODAL_TOKEN_ID: ").strip()
    token_secret = input("Enter your MODAL_TOKEN_SECRET: ").strip()
    
    if not token_id or not token_secret:
        print("❌ Both token ID and secret are required")
        return False
    
    if not validate_token_format(token_id, token_secret):
        print("❌ Token format validation failed")
        return False
    
    # Set environment variables
    os.environ['MODAL_TOKEN_ID'] = token_id
    os.environ['MODAL_TOKEN_SECRET'] = token_secret
    
    # Test the connection
    if test_modal_connection():
        print("✓ Manual authentication successful")
        save_credentials_to_file(token_id, token_secret)
        return True
    else:
        print("❌ Authentication test failed")
        return False

def save_credentials_to_file(token_id, token_secret):
    """Save credentials to environment file for persistence"""
    print("\n💾 Saving credentials...")
    
    # Create .env file
    env_file = Path('.env')
    env_content = f"""# Modal A100 GPU Credentials
MODAL_TOKEN_ID={token_id}
MODAL_TOKEN_SECRET={token_secret}

# Add other API keys as needed
# OPENAI_API_KEY=your_openai_key
# ELEVENLABS_API_KEY=your_elevenlabs_key
# GOOGLE_GENERATIVE_AI_API_KEY=your_google_key
"""
    
    with open(env_file, 'w') as f:
        f.write(env_content)
    
    print(f"✓ Credentials saved to {env_file}")
    print("  Add this file to your environment or load it in your application")

def main():
    """Main execution function"""
    print_header()
    
    # Step 1: Check Modal installation
    if not check_modal_installation():
        print("❌ Cannot proceed without Modal installation")
        return False
    
    # Step 2: Check current authentication
    if check_current_auth():
        print("\n✅ Modal authentication is working correctly!")
        print("Your A100 GPU backend is ready to deploy.")
        return True
    
    # Step 3: Setup authentication
    print("\n🔧 Authentication setup required")
    print("Choose authentication method:")
    print("1. Browser authentication (recommended)")
    print("2. Manual token entry")
    print()
    
    choice = input("Enter your choice (1 or 2): ").strip()
    
    if choice == "1":
        success = setup_browser_auth()
    elif choice == "2":
        success = setup_manual_auth()
    else:
        print("❌ Invalid choice")
        return False
    
    if success:
        print("\n🎉 Modal authentication setup complete!")
        print("Next steps:")
        print("1. Deploy the A100 GPU backend")
        print("2. Test the voice services integration")
        print("3. Validate course creation workflows")
        return True
    else:
        print("\n❌ Authentication setup failed")
        print("Please check your credentials and try again")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
