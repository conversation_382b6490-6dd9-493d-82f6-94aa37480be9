import React, { useState, useEffect } from 'react';
import { Tab, Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Tab<PERSON>anel } from 'react-tabs';
import 'react-tabs/style/react-tabs.css';
import './App.css';
import ImageGenerator from './components/ImageGenerator';
import VideoGenerator from './components/VideoGenerator';
import TTSChatterbox from './components/TTSChatterbox';
import TTSCoqui from './components/TTSCoqui';
import SlideGenerator from './components/SlideGenerator';
import CourseBuilder from './components/CourseBuilder';
import StatusBar from './components/StatusBar';
import apiService from './services/apiService';

function App() {
  const [gpuStatus, setGpuStatus] = useState({ status: 'checking', services: {} });
  const [activeTab, setActiveTab] = useState(0);

  useEffect(() => {
    checkGPUStatus();
    const interval = setInterval(checkGPUStatus, 30000); // Check every 30 seconds
    return () => clearInterval(interval);
  }, []);

  const checkGPUStatus = async () => {
    try {
      const status = await apiService.checkHealth();
      setGpuStatus(status);
    } catch (error) {
      setGpuStatus({ 
        status: 'error', 
        error: error.message,
        services: {
          stable_diffusion_xl: false,
          sadtalker: false,
          chatterbox_tts: false,
          coqui_tts: false,
          marp_slides: false
        }
      });
    }
  };

  return (
    <div className="App">
      <header className="App-header">
        <h1>AI Course Builder</h1>
        <p>GPU-Accelerated Content Creation Platform</p>
        <StatusBar status={gpuStatus} />
      </header>

      <main className="App-main">
        <Tabs selectedIndex={activeTab} onSelect={setActiveTab}>
          <TabList>
            <Tab>🎨 Images</Tab>
            <Tab>🎬 Videos</Tab>
            <Tab>🎙️ Chatterbox TTS</Tab>
            <Tab>🗣️ Coqui TTS</Tab>
            <Tab>📊 Slides</Tab>
            <Tab>🎓 Course Builder</Tab>
          </TabList>

          <TabPanel>
            <ImageGenerator />
          </TabPanel>
          
          <TabPanel>
            <VideoGenerator />
          </TabPanel>
          
          <TabPanel>
            <TTSChatterbox />
          </TabPanel>
          
          <TabPanel>
            <TTSCoqui />
          </TabPanel>
          
          <TabPanel>
            <SlideGenerator />
          </TabPanel>
          
          <TabPanel>
            <CourseBuilder />
          </TabPanel>
        </Tabs>
      </main>
      
      <footer className="App-footer">
        <p>Powered by Modal A100 80GB GPU Infrastructure</p>
      </footer>
    </div>
  );
}

export default App;