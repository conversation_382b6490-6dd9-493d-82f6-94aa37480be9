import { useState } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import { Loader2, Mail, Plus, X } from 'lucide-react';

interface CollaboratorsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  courseId: number;
  courseTitle: string;
}

type Collaborator = {
  id: number;
  name: string;
  email: string;
  role: 'viewer' | 'editor' | 'owner';
  avatarUrl?: string;
};

export function CollaboratorsDialog({
  open,
  onOpenChange,
  courseId,
  courseTitle
}: CollaboratorsDialogProps) {
  const { toast } = useToast();
  const [email, setEmail] = useState('');
  const [role, setRole] = useState<'viewer' | 'editor'>('viewer');
  const [isAdding, setIsAdding] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [collaborators, setCollaborators] = useState<Collaborator[]>([
    {
      id: 1,
      name: 'John Smith',
      email: '<EMAIL>',
      role: 'owner',
      avatarUrl: '/avatars/john.jpg'
    },
    {
      id: 2,
      name: 'Sarah Johnson',
      email: '<EMAIL>',
      role: 'editor',
      avatarUrl: '/avatars/sarah.jpg'
    },
    {
      id: 3,
      name: 'Michael Chen',
      email: '<EMAIL>',
      role: 'editor'
    },
    {
      id: 4,
      name: 'Emma Wilson',
      email: '<EMAIL>',
      role: 'viewer',
      avatarUrl: '/avatars/emma.jpg'
    }
  ]);

  // Load collaborators when dialog opens
  const loadCollaborators = async () => {
    if (!open) return;
    
    setIsLoading(true);
    try {
      const response = await apiRequest('GET', `/api/courses/${courseId}/collaborators`);
      if (!response.ok) {
        throw new Error('Failed to load collaborators');
      }
      
      const data = await response.json();
      setCollaborators(data);
    } catch (error) {
      console.error('Error loading collaborators:', error);
      toast({
        title: 'Error',
        description: 'Could not load collaborators. Please try again later.',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Add collaborator
  const handleAddCollaborator = async () => {
    if (!email || !role) {
      toast({
        title: 'Missing information',
        description: 'Please provide an email and select a role',
        variant: 'destructive'
      });
      return;
    }

    setIsAdding(true);
    try {
      const response = await apiRequest('POST', `/api/courses/${courseId}/collaborators`, {
        email,
        role
      });

      if (!response.ok) {
        throw new Error('Failed to add collaborator');
      }

      const newCollaborator = await response.json();
      setCollaborators(prev => [...prev, newCollaborator]);
      setEmail('');

      toast({
        title: 'Success',
        description: `Invitation sent to ${email}`
      });
    } catch (error) {
      console.error('Error adding collaborator:', error);
      toast({
        title: 'Error',
        description: 'Could not add collaborator. Please try again later.',
        variant: 'destructive'
      });
    } finally {
      setIsAdding(false);
    }
  };

  // Change collaborator role
  const handleRoleChange = async (collaboratorId: number, newRole: 'viewer' | 'editor' | 'owner') => {
    try {
      const response = await apiRequest('PATCH', `/api/courses/${courseId}/collaborators/${collaboratorId}`, {
        role: newRole
      });

      if (!response.ok) {
        throw new Error('Failed to update role');
      }

      setCollaborators(prev => 
        prev.map(c => c.id === collaboratorId ? { ...c, role: newRole } : c)
      );

      toast({
        title: 'Role updated',
        description: 'Collaborator role has been updated successfully'
      });
    } catch (error) {
      console.error('Error updating role:', error);
      toast({
        title: 'Error',
        description: 'Could not update role. Please try again later.',
        variant: 'destructive'
      });
    }
  };

  // Remove collaborator
  const handleRemoveCollaborator = async (collaboratorId: number) => {
    try {
      const response = await apiRequest('DELETE', `/api/courses/${courseId}/collaborators/${collaboratorId}`);

      if (!response.ok) {
        throw new Error('Failed to remove collaborator');
      }

      setCollaborators(prev => prev.filter(c => c.id !== collaboratorId));

      toast({
        title: 'Collaborator removed',
        description: 'Collaborator has been removed successfully'
      });
    } catch (error) {
      console.error('Error removing collaborator:', error);
      toast({
        title: 'Error',
        description: 'Could not remove collaborator. Please try again later.',
        variant: 'destructive'
      });
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Course Collaborators</DialogTitle>
          <DialogDescription>
            Invite team members to collaborate on "{courseTitle}"
          </DialogDescription>
        </DialogHeader>

        {/* Add collaborator form */}
        <div className="space-y-4 py-2">
          <div className="flex flex-col gap-2">
            <Label htmlFor="email">Add people by email</Label>
            <div className="flex gap-2">
              <Input
                id="email"
                placeholder="<EMAIL>"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="flex-1"
              />
              <Select value={role} onValueChange={(value) => setRole(value as 'viewer' | 'editor')}>
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="Select role" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="viewer">Can view</SelectItem>
                  <SelectItem value="editor">Can edit</SelectItem>
                </SelectContent>
              </Select>
              <Button type="button" onClick={handleAddCollaborator} disabled={isAdding}>
                {isAdding ? <Loader2 className="h-4 w-4 animate-spin" /> : <Plus className="h-4 w-4" />}
              </Button>
            </div>
          </div>

          {/* Current collaborators list */}
          <div className="space-y-2">
            <Label>People with access</Label>
            {isLoading ? (
              <div className="flex justify-center py-4">
                <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
              </div>
            ) : (
              <div className="rounded-md border">
                <div className="p-0">
                  {collaborators.map((collaborator) => (
                    <div
                      key={collaborator.id}
                      className="flex items-center justify-between p-4 border-b last:border-0"
                    >
                      <div className="flex items-center gap-3">
                        <Avatar className="h-8 w-8">
                          {collaborator.avatarUrl ? (
                            <AvatarImage src={collaborator.avatarUrl} alt={collaborator.name} />
                          ) : (
                            <AvatarFallback>
                              {collaborator.name.charAt(0)}
                            </AvatarFallback>
                          )}
                        </Avatar>
                        <div>
                          <p className="text-sm font-medium">{collaborator.name}</p>
                          <p className="text-xs text-muted-foreground flex items-center">
                            <Mail className="h-3 w-3 mr-1" />
                            {collaborator.email}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        {collaborator.role === 'owner' ? (
                          <span className="text-sm text-muted-foreground">Owner</span>
                        ) : (
                          <Select
                            defaultValue={collaborator.role}
                            onValueChange={(value) => handleRoleChange(collaborator.id, value as 'viewer' | 'editor')}
                          >
                            <SelectTrigger className="h-8 w-[110px]">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="viewer">Can view</SelectItem>
                              <SelectItem value="editor">Can edit</SelectItem>
                            </SelectContent>
                          </Select>
                        )}
                        {collaborator.role !== 'owner' && (
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8"
                            onClick={() => handleRemoveCollaborator(collaborator.id)}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}