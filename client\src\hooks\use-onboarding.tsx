import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { OnboardingAnimation } from '@/components/onboarding';

interface OnboardingContextType {
  showOnboarding: boolean;
  completeOnboarding: () => void;
  skipOnboarding: () => void;
  resetOnboarding: () => void;
}

const OnboardingContext = createContext<OnboardingContextType | undefined>(undefined);

export const ONBOARDING_STORAGE_KEY = 'course_creator_onboarding_completed';

interface OnboardingProviderProps {
  children: ReactNode;
}

export function OnboardingProvider({ children }: OnboardingProviderProps) {
  const [showOnboarding, setShowOnboarding] = useState(false);

  // Check if onboarding has been completed on mount
  useEffect(() => {
    const onboardingCompleted = localStorage.getItem(ONBOARDING_STORAGE_KEY);
    if (!onboardingCompleted) {
      // Delay showing the onboarding for a smoother initial experience
      const timer = setTimeout(() => {
        setShowOnboarding(true);
      }, 1000); // Wait 1 second
      
      return () => clearTimeout(timer);
    }
  }, []);

  const completeOnboarding = () => {
    localStorage.setItem(ONBOARDING_STORAGE_KEY, 'true');
    setShowOnboarding(false);
  };

  const skipOnboarding = () => {
    localStorage.setItem(ONBOARDING_STORAGE_KEY, 'true');
    setShowOnboarding(false);
  };

  const resetOnboarding = () => {
    localStorage.removeItem(ONBOARDING_STORAGE_KEY);
    setShowOnboarding(true);
  };

  return (
    <OnboardingContext.Provider
      value={{
        showOnboarding,
        completeOnboarding,
        skipOnboarding,
        resetOnboarding,
      }}
    >
      {children}
      {showOnboarding && (
        <OnboardingAnimation 
          onComplete={completeOnboarding} 
          onSkip={skipOnboarding} 
        />
      )}
    </OnboardingContext.Provider>
  );
}

export function useOnboarding() {
  const context = useContext(OnboardingContext);
  if (context === undefined) {
    throw new Error('useOnboarding must be used within a OnboardingProvider');
  }
  return context;
}