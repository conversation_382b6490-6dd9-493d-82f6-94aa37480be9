/**
 * Enhanced Course Generation API Routes
 * Unified endpoints for both traditional and avatar course generation
 */

import express, { Request, Response } from 'express';
import { unifiedCourseGenerationService, UnifiedCourseOptions, CourseType } from '../services/unified-course-generation-service';
import { dbManager, safeDbOperation } from '../db-enhanced';
import { courses, users } from '@shared/schema';
import { eq } from 'drizzle-orm';
import { z } from 'zod';

const router = express.Router();

// Validation schemas
const CourseGenerationSchema = z.object({
  type: z.enum(['traditional', 'avatar']),
  title: z.string().min(3).max(200),
  targetAudience: z.string().optional(),
  category: z.string().min(1),
  difficulty: z.enum(['beginner', 'intermediate', 'advanced']).optional(),
  duration: z.enum(['short', 'medium', 'long']).optional(),
  traditionalOptions: z.object({
    voiceSettings: z.object({
      voiceId: z.string().optional(),
      speed: z.number().min(0.5).max(2.0).optional(),
      pitch: z.number().min(0.5).max(2.0).optional()
    }).optional(),
    visualStyle: z.enum(['dynamic', 'educational', 'corporate']).optional(),
    mediaPreferences: z.object({
      videoQuality: z.enum(['standard', 'high', 'ultra']).optional(),
      sceneChangeFrequency: z.enum(['slow', 'medium', 'fast']).optional()
    }).optional()
  }).optional(),
  avatarOptions: z.object({
    avatarConfig: z.object({
      type: z.enum(['image', 'video']),
      sourceUrl: z.string().url(),
      style: z.enum(['professional', 'casual', 'animated']).optional(),
      background: z.enum(['office', 'classroom', 'studio', 'custom']).optional()
    }),
    voiceSettings: z.object({
      voiceId: z.string().optional(),
      speed: z.number().min(0.5).max(2.0).optional(),
      pitch: z.number().min(0.5).max(2.0).optional(),
      emotion: z.enum(['neutral', 'enthusiastic', 'calm']).optional()
    }).optional()
  }).optional()
});

/**
 * Start course generation
 */
router.post('/generate', async (req: Request, res: Response) => {
  try {
    // Validate request body
    const validationResult = CourseGenerationSchema.safeParse(req.body);
    if (!validationResult.success) {
      return res.status(400).json({
        error: 'Invalid request data',
        details: validationResult.error.errors
      });
    }

    const options = validationResult.data;
    
    // Get user ID from session/auth
    const userId = req.user?.id || req.body.userId;
    if (!userId) {
      return res.status(401).json({ error: 'User authentication required' });
    }

    // Verify user exists and has permissions
    const user = await safeDbOperation(async () => {
      const db = dbManager.getDatabase();
      const [user] = await db.select().from(users).where(eq(users.id, userId));
      return user;
    });

    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Check user's course generation limits
    const canGenerate = await checkGenerationLimits(userId, user.plan);
    if (!canGenerate.allowed) {
      return res.status(429).json({
        error: 'Generation limit exceeded',
        details: canGenerate.reason,
        upgradeRequired: canGenerate.upgradeRequired
      });
    }

    // Prepare unified options
    const unifiedOptions: UnifiedCourseOptions = {
      ...options,
      userId
    };

    // Start generation
    const result = await unifiedCourseGenerationService.generateCourse(unifiedOptions);

    res.json({
      success: true,
      jobId: result.jobId,
      estimatedTime: result.estimatedTime,
      courseType: options.type,
      message: `${options.type === 'avatar' ? 'Avatar' : 'Traditional'} course generation started`,
      progressUrl: `/api/course-generation/progress/${result.jobId}`
    });

  } catch (error) {
    console.error('Course generation start failed:', error);
    res.status(500).json({
      error: 'Failed to start course generation',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Get generation progress
 */
router.get('/progress/:jobId', async (req: Request, res: Response) => {
  try {
    const { jobId } = req.params;
    
    if (!jobId) {
      return res.status(400).json({ error: 'Job ID is required' });
    }

    const progress = unifiedCourseGenerationService.getProgress(jobId);
    
    if (!progress) {
      return res.status(404).json({ error: 'Generation job not found' });
    }

    // Calculate additional metrics
    const elapsedTime = Date.now() - progress.startTime.getTime();
    const progressPercentage = Math.round(progress.progress);
    
    res.json({
      jobId: progress.jobId,
      courseType: progress.courseType,
      status: progress.status,
      progress: progressPercentage,
      currentStep: progress.currentStep,
      estimatedTimeRemaining: progress.estimatedTimeRemaining,
      elapsedTime: Math.round(elapsedTime / 1000), // seconds
      courseId: progress.courseId,
      error: progress.error,
      lastUpdate: progress.lastUpdate,
      avatarProcessingStatus: progress.avatarProcessingStatus,
      qualityMetrics: progress.qualityMetrics,
      performanceMetrics: progress.performanceMetrics
    });

  } catch (error) {
    console.error('Failed to get generation progress:', error);
    res.status(500).json({
      error: 'Failed to retrieve progress',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Get all active generation jobs
 */
router.get('/active-jobs', async (req: Request, res: Response) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ error: 'User authentication required' });
    }

    const activeJobs = unifiedCourseGenerationService.getActiveJobs();
    
    // Filter jobs for current user (if needed)
    const userJobs = activeJobs.filter(job => {
      // Add user filtering logic here if needed
      return true; // For now, return all jobs
    });

    res.json({
      activeJobs: userJobs.map(job => ({
        jobId: job.jobId,
        courseType: job.courseType,
        status: job.status,
        progress: Math.round(job.progress),
        currentStep: job.currentStep,
        estimatedTimeRemaining: job.estimatedTimeRemaining,
        startTime: job.startTime,
        courseId: job.courseId
      }))
    });

  } catch (error) {
    console.error('Failed to get active jobs:', error);
    res.status(500).json({
      error: 'Failed to retrieve active jobs',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Cancel course generation
 */
router.post('/cancel/:jobId', async (req: Request, res: Response) => {
  try {
    const { jobId } = req.params;
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({ error: 'User authentication required' });
    }

    if (!jobId) {
      return res.status(400).json({ error: 'Job ID is required' });
    }

    const success = await unifiedCourseGenerationService.cancelGeneration(jobId);
    
    if (success) {
      res.json({
        success: true,
        message: 'Course generation cancelled successfully',
        jobId
      });
    } else {
      res.status(404).json({
        error: 'Generation job not found or cannot be cancelled',
        jobId
      });
    }

  } catch (error) {
    console.error('Failed to cancel generation:', error);
    res.status(500).json({
      error: 'Failed to cancel generation',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Validate course quality
 */
router.get('/validate/:courseId', async (req: Request, res: Response) => {
  try {
    const courseId = parseInt(req.params.courseId);
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({ error: 'User authentication required' });
    }

    if (isNaN(courseId)) {
      return res.status(400).json({ error: 'Invalid course ID' });
    }

    // Verify user owns the course
    const course = await safeDbOperation(async () => {
      const db = dbManager.getDatabase();
      const [course] = await db.select().from(courses).where(eq(courses.id, courseId));
      return course;
    });

    if (!course) {
      return res.status(404).json({ error: 'Course not found' });
    }

    if (course.userId !== userId) {
      return res.status(403).json({ error: 'Access denied' });
    }

    const validation = await unifiedCourseGenerationService.validateCourseQuality(courseId);

    res.json({
      courseId,
      isValid: validation.isValid,
      qualityScore: validation.qualityScore,
      issues: validation.issues,
      recommendations: generateQualityRecommendations(validation.issues),
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Course quality validation failed:', error);
    res.status(500).json({
      error: 'Failed to validate course quality',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Get generation statistics
 */
router.get('/statistics', async (req: Request, res: Response) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ error: 'User authentication required' });
    }

    const stats = await unifiedCourseGenerationService.getGenerationStatistics();

    res.json({
      statistics: stats,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Failed to get generation statistics:', error);
    res.status(500).json({
      error: 'Failed to retrieve statistics',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Get supported course types and options
 */
router.get('/options', async (req: Request, res: Response) => {
  try {
    res.json({
      courseTypes: [
        {
          type: 'traditional',
          name: 'Traditional Course',
          description: 'Video-based course with AI narration and dynamic visuals',
          estimatedTime: '10-15 minutes',
          features: [
            'AI-generated content structure',
            'Coqui TTS voice synthesis',
            'Dynamic background videos',
            'Auto-generated captions',
            'Scene transitions'
          ]
        },
        {
          type: 'avatar',
          name: 'Avatar Course',
          description: 'Personalized avatar-based course with talking head presentation',
          estimatedTime: '15-20 minutes',
          features: [
            'EchoMimic V2 avatar generation',
            'Lip-sync technology',
            'Custom backgrounds',
            'Educational slides',
            'Interactive presentation'
          ]
        }
      ],
      voiceOptions: [
        { id: 'tts_models/en/ljspeech/tacotron2-DDC', name: 'Professional Female', service: 'coqui' },
        { id: 'tts_models/en/vctk/vits', name: 'Professional Male', service: 'coqui' },
        { id: 'kokoro-v0_19', name: 'Natural Voice', service: 'kokoro' }
      ],
      categories: [
        'technology', 'business', 'education', 'health', 'science',
        'arts', 'language', 'personal-development', 'marketing', 'finance'
      ],
      difficulties: ['beginner', 'intermediate', 'advanced'],
      durations: [
        { value: 'short', name: 'Short (30-60 minutes)', lessons: '12-15' },
        { value: 'medium', name: 'Medium (1-2 hours)', lessons: '20-30' },
        { value: 'long', name: 'Long (3-5 hours)', lessons: '40-48' }
      ]
    });

  } catch (error) {
    console.error('Failed to get course options:', error);
    res.status(500).json({
      error: 'Failed to retrieve course options',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Check user's course generation limits
 */
async function checkGenerationLimits(userId: number, userPlan: string): Promise<{
  allowed: boolean;
  reason?: string;
  upgradeRequired?: boolean;
}> {
  try {
    // Get user's course count for current month
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();
    
    const monthlyCount = await safeDbOperation(async () => {
      const db = dbManager.getDatabase();
      const result = await db.execute(`
        SELECT COUNT(*) as count 
        FROM courses 
        WHERE user_id = ${userId} 
        AND EXTRACT(MONTH FROM created_at) = ${currentMonth + 1}
        AND EXTRACT(YEAR FROM created_at) = ${currentYear}
      `);
      return parseInt(result[0]?.count as string || '0');
    }) || 0;

    // Define limits by plan
    const limits: { [key: string]: number } = {
      'free': 2,
      'basic': 10,
      'premium': 50,
      'enterprise': 1000
    };

    const userLimit = limits[userPlan] || limits['free'];
    
    if (monthlyCount >= userLimit) {
      return {
        allowed: false,
        reason: `Monthly limit of ${userLimit} courses reached`,
        upgradeRequired: userPlan === 'free'
      };
    }

    return { allowed: true };
    
  } catch (error) {
    console.error('Failed to check generation limits:', error);
    return { allowed: true }; // Allow on error
  }
}

/**
 * Generate quality improvement recommendations
 */
function generateQualityRecommendations(issues: string[]): string[] {
  const recommendations: string[] = [];
  
  issues.forEach(issue => {
    if (issue.includes('title')) {
      recommendations.push('Consider using a more descriptive and engaging course title');
    }
    if (issue.includes('description')) {
      recommendations.push('Add a comprehensive course description with learning objectives');
    }
    if (issue.includes('lessons')) {
      recommendations.push('Increase the number of lessons for better content coverage');
    }
    if (issue.includes('media')) {
      recommendations.push('Ensure all lessons have proper media assets and narration');
    }
    if (issue.includes('video')) {
      recommendations.push('Complete video generation for all lessons');
    }
  });
  
  if (recommendations.length === 0) {
    recommendations.push('Course quality is good! Consider adding more interactive elements.');
  }
  
  return recommendations;
}

export default router;
