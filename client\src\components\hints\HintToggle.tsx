import React from 'react';
import { LightbulbIcon, LightbulbOff } from 'lucide-react';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useHints } from './HintProvider';

interface HintToggleProps {
  showLabel?: boolean;
}

export const HintToggle: React.FC<HintToggleProps> = ({ showLabel = true }) => {
  const { hintsEnabled, setHintsEnabled } = useHints();
  
  // Toggle function for the icon button
  const toggleHints = () => {
    setHintsEnabled(!hintsEnabled);
  };
  
  if (showLabel) {
    return (
      <div className="flex items-center space-x-2">
        <Switch 
          id="hint-toggle" 
          checked={hintsEnabled} 
          onCheckedChange={setHintsEnabled} 
        />
        <Label htmlFor="hint-toggle" className="text-sm cursor-pointer">
          Learning Hints
        </Label>
      </div>
    );
  }
  
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="ghost"
            size="icon"
            onClick={toggleHints}
            className="relative h-9 w-9 rounded-full"
          >
            {hintsEnabled ? (
              <LightbulbIcon className="h-5 w-5 text-primary" />
            ) : (
              <LightbulbOff className="h-5 w-5 text-muted-foreground" />
            )}
          </Button>
        </TooltipTrigger>
        <TooltipContent side="bottom">
          <p>
            {hintsEnabled 
              ? 'Learning hints are enabled' 
              : 'Learning hints are disabled'}
          </p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};