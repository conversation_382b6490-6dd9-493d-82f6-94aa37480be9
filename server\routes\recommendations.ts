import { Router, Request, Response } from 'express';
import { getMockRecommendations } from '../services/mockRecommendation';

const recommendationsRouter = Router();

// GET endpoint to fetch personalized course recommendations with AI insights
recommendationsRouter.get('/api/recommendations', async (req: Request, res: Response) => {
  try {
    // For this first iteration, we'll use mock data
    // In a production environment, this would check for authentication 
    // and use real user data for personalized recommendations
    const recommendations = getMockRecommendations();
    
    return res.status(200).json(recommendations);
  } catch (error) {
    console.error("Error fetching recommendations:", error);
    return res.status(500).json({ 
      message: "Failed to fetch recommendations",
      error: error instanceof Error ? error.message : "Unknown error" 
    });
  }
});

export default recommendationsRouter;