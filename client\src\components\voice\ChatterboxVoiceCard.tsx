import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Play, Pause, Crown } from 'lucide-react';

interface ChatterboxVoice {
  id: string;
  name: string;
  gender: string;
  accent: string;
  language: string;
  description: string;
  tier: string;
  tags: string[];
}

interface ChatterboxVoiceCardProps {
  voice: ChatterboxVoice;
  isSelected: boolean;
  isPlaying: boolean;
  isLoading: boolean;
  onSelect: () => void;
  onTogglePlayback: () => void;
}

export default function ChatterboxVoiceCard({
  voice,
  isSelected,
  isPlaying,
  isLoading,
  onSelect,
  onTogglePlayback
}: ChatterboxVoiceCardProps) {
  return (
    <Card 
      className={`cursor-pointer transition-all hover:shadow-md border-2 ${
        isSelected ? 'border-purple-500 bg-purple-50' : 'border-gray-200 hover:border-purple-300'
      }`}
      onClick={onSelect}
    >
      <CardContent className="p-4">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-2">
            <Crown className="h-4 w-4 text-purple-500" />
            <h4 className="font-semibold text-gray-900">{voice.name}</h4>
            <Badge className="bg-purple-100 text-purple-800 text-xs">Enterprise</Badge>
          </div>
          <Button
            size="sm"
            variant={isSelected ? "default" : "outline"}
            onClick={(e) => {
              e.stopPropagation();
              onTogglePlayback();
            }}
            disabled={isLoading}
            className={isSelected ? "bg-purple-600 hover:bg-purple-700" : ""}
          >
            {isPlaying ? (
              <Pause className="h-4 w-4" />
            ) : (
              <Play className="h-4 w-4" />
            )}
          </Button>
        </div>
        
        <div className="flex flex-wrap gap-1 mb-3">
          <Badge variant="outline" className="text-xs capitalize">
            {voice.gender}
          </Badge>
          <Badge variant="secondary" className="text-xs">
            {voice.accent}
          </Badge>
          <Badge variant="outline" className="text-xs uppercase">
            {voice.language}
          </Badge>
          {voice.tags.slice(0, 2).map((tag) => (
            <Badge key={tag} variant="default" className="text-xs bg-purple-100 text-purple-700">
              {tag}
            </Badge>
          ))}
        </div>
        
        <p className="text-sm text-gray-600 leading-relaxed line-clamp-2">
          {voice.description}
        </p>
      </CardContent>
    </Card>
  );
}