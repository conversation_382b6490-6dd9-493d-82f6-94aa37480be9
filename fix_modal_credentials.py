
#!/usr/bin/env python3
"""
Fix Modal Credentials and Deploy A100 GPU Service
This script helps you set up fresh Modal credentials and deploy the A100 service
"""

import os
import subprocess
import sys
import json
from pathlib import Path

def check_environment_credentials():
    """Check if Modal credentials are in environment"""
    token_id = os.getenv('MODAL_TOKEN_ID')
    token_secret = os.getenv('MODAL_TOKEN_SECRET')
    
    if token_id and token_secret:
        print(f"✓ Found Modal credentials in environment:")
        print(f"  Token ID: {token_id[:8]}...")
        print(f"  Token Secret: {token_secret[:8]}...")
        return True
    else:
        print("❌ Modal credentials not found in environment variables")
        return False

def setup_modal_auth():
    """Setup Modal authentication"""
    print("\n🔧 Setting up Modal authentication...")
    
    # Check if credentials are in environment
    if not check_environment_credentials():
        print("\n📋 Please add your Modal credentials to Replit Secrets:")
        print("1. Go to Modal.com and create/login to your account")
        print("2. Generate new API tokens in your Modal dashboard")
        print("3. Add these secrets in Replit:")
        print("   - MODAL_TOKEN_ID: your_token_id")
        print("   - MODAL_TOKEN_SECRET: your_token_secret")
        print("\nAfter adding secrets, run this script again.")
        return False
    
    # Set environment variables for Modal CLI
    token_id = os.getenv('MODAL_TOKEN_ID')
    token_secret = os.getenv('MODAL_TOKEN_SECRET')
    
    try:
        # Try to authenticate with Modal CLI
        result = subprocess.run([
            'modal', 'token', 'set',
            '--token-id', token_id,
            '--token-secret', token_secret
        ], capture_output=True, text=True, check=True)
        
        print("✓ Modal authentication successful")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Modal authentication failed: {e.stderr}")
        return False
    except FileNotFoundError:
        print("❌ Modal CLI not found. Installing...")
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", "modal"], check=True)
            print("✓ Modal installed successfully")
            return setup_modal_auth()  # Retry authentication
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install Modal: {e}")
            return False

def test_modal_connection():
    """Test Modal connection"""
    print("\n🧪 Testing Modal connection...")
    
    try:
        result = subprocess.run(['modal', 'token', 'current'], 
                              capture_output=True, text=True, check=True)
        print("✓ Modal connection successful")
        print(f"Current token: {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Modal connection failed: {e.stderr}")
        return False

def deploy_a100_app():
    """Deploy the A100 GPU application"""
    print("\n🚀 Deploying Modal A100 GPU application...")
    
    # Check if the app file exists
    app_file = "modal_a100_production.py"
    if not os.path.exists(app_file):
        print(f"❌ App file {app_file} not found")
        return False
    
    try:
        # Deploy the app
        result = subprocess.run([
            'modal', 'deploy', app_file
        ], capture_output=True, text=True, check=True)
        
        print("✓ A100 GPU app deployed successfully")
        print(f"Deployment output:\n{result.stdout}")
        
        # Get app URLs
        try:
            app_info = subprocess.run([
                'modal', 'app', 'list'
            ], capture_output=True, text=True, check=True)
            
            print(f"\nApp info:\n{app_info.stdout}")
            
        except subprocess.CalledProcessError:
            print("⚠️ Could not get app info, but deployment may have succeeded")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Deployment failed: {e.stderr}")
        return False

def test_deployed_app():
    """Test the deployed application"""
    print("\n🧪 Testing deployed A100 GPU functions...")
    
    try:
        # Test health check function
        result = subprocess.run([
            'modal', 'run', 'modal_a100_production.py::health_check_production'
        ], capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print("✓ A100 GPU health check successful")
            print(f"Health status: {result.stdout}")
            return True
        else:
            print(f"❌ Health check failed: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("⚠️ Health check timed out - this may be normal for first run")
        return True
    except Exception as e:
        print(f"❌ Error testing app: {e}")
        return False

def main():
    """Main setup function"""
    print("🔧 Modal A100 GPU Credentials & Deployment Fix")
    print("=" * 50)
    
    # Step 1: Setup authentication
    if not setup_modal_auth():
        return False
    
    # Step 2: Test connection
    if not test_modal_connection():
        return False
    
    # Step 3: Deploy app
    if not deploy_a100_app():
        return False
    
    # Step 4: Test deployment
    test_deployed_app()
    
    print("\n✅ Modal A100 GPU setup completed!")
    print("\nNext steps:")
    print("1. Your Modal A100 GPU service should now be accessible")
    print("2. Test the integration in your Course AI Platform")
    print("3. Monitor usage at https://modal.com/apps")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
