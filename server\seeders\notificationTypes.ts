import { notificationsService } from '../services/notificationsService';
import { InsertNotificationType } from '@shared/schema';
import { db } from '../db';
import { notificationTypes } from '@shared/schema';
import { sql } from 'drizzle-orm';

const defaultNotificationTypes: InsertNotificationType[] = [
  {
    type: 'course_published',
    displayName: 'Course Published',
    description: 'Notifications for when a course is successfully published',
    iconName: 'BookCheck',
    iconColor: 'text-green-500',
    importance: 'high',
    category: 'course',
    isDefault: true
  },
  {
    type: 'course_completed',
    displayName: 'Course Completed',
    description: 'Notifications for when a user completes a course',
    iconName: 'GraduationCap',
    iconColor: 'text-blue-500',
    importance: 'high',
    category: 'course',
    isDefault: true
  },
  {
    type: 'team_invite',
    displayName: 'Team Invite',
    description: 'Invitations to join a team',
    iconName: 'Users',
    iconColor: 'text-indigo-500',
    importance: 'high',
    category: 'team',
    isDefault: true
  },
  {
    type: 'course_collaboration',
    displayName: 'Course Collaboration',
    description: 'Notifications about course collaboration invites and updates',
    iconName: 'Users',
    iconColor: 'text-purple-500',
    importance: 'high',
    category: 'course',
    isDefault: true
  },
  {
    type: 'ai_generation_complete',
    displayName: 'AI Generation Complete',
    description: 'Notifications when AI content generation is complete',
    iconName: 'Sparkles',
    iconColor: 'text-amber-500',
    importance: 'medium',
    category: 'system',
    isDefault: true
  },
  {
    type: 'system_update',
    displayName: 'System Update',
    description: 'Important platform updates and announcements',
    iconName: 'Bell',
    iconColor: 'text-blue-500',
    importance: 'medium',
    category: 'system',
    isDefault: true
  },
  {
    type: 'subscription_expiring',
    displayName: 'Subscription Expiring',
    description: 'Notifications for subscription expiration',
    iconName: 'CreditCard',
    iconColor: 'text-yellow-500',
    importance: 'high',
    category: 'system',
    isDefault: true
  },
  {
    type: 'course_update',
    displayName: 'Course Update',
    description: 'Updates to courses you are enrolled in',
    iconName: 'BookOpen',
    iconColor: 'text-blue-500',
    importance: 'medium',
    category: 'course',
    isDefault: true
  },
  {
    type: 'comment_reply',
    displayName: 'Comment Reply',
    description: 'Someone replied to your comment',
    iconName: 'MessageSquare',
    iconColor: 'text-purple-500',
    importance: 'medium',
    category: 'social',
    isDefault: true
  },
  {
    type: 'quiz_result',
    displayName: 'Quiz Result',
    description: 'Results from quizzes you have taken',
    iconName: 'CheckSquare',
    iconColor: 'text-green-500',
    importance: 'medium',
    category: 'course',
    isDefault: true
  },
  {
    type: 'credit_low',
    displayName: 'Low AI Credits',
    description: 'Your AI credits are running low',
    iconName: 'AlertCircle',
    iconColor: 'text-red-500',
    importance: 'high',
    category: 'system',
    isDefault: true
  },
  {
    type: 'storage_full',
    displayName: 'Storage Full',
    description: 'Your storage is almost full',
    iconName: 'Database',
    iconColor: 'text-red-500',
    importance: 'high',
    category: 'system',
    isDefault: true
  }
];

export async function seedNotificationTypes() {
  try {
    // Check if any notification types already exist
    const existingCount = await db.select({ count: sql`COUNT(*)` }).from(notificationTypes);
    
    if (Number(existingCount[0]?.count) > 0) {
      console.log(`Notification types already seeded (${existingCount[0]?.count} types found)`);
      return;
    }
    
    console.log('Seeding notification types...');
    
    // Insert all the default types
    for (const type of defaultNotificationTypes) {
      await notificationsService.createNotificationType(type);
    }
    
    console.log(`Successfully seeded ${defaultNotificationTypes.length} notification types`);
  } catch (error) {
    console.error('Error seeding notification types:', error);
    // Don't throw the error, just log it and continue
    // This prevents the server from crashing on startup
  }
}
