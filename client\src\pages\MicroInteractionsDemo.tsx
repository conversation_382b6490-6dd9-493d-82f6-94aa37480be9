import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>H<PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import ProgressCelebration from '@/components/micro-interactions/ProgressCelebration';
import InteractiveButton from '@/components/micro-interactions/InteractiveButton';
import AnimatedProgressTracker from '@/components/micro-interactions/AnimatedProgressTracker';
import InteractiveHoverCard from '@/components/micro-interactions/InteractiveHoverCard';
import { useMicroInteractions } from '@/hooks/useMicroInteractions';
import { 
  Sparkles, 
  Trophy, 
  Star, 
  Zap, 
  PlayCircle,
  BookOpen,
  Target,
  Timer
} from 'lucide-react';

export default function MicroInteractionsDemo() {
  const microInteractions = useMicroInteractions();
  const [demoTimer, setDemoTimer] = useState(0);

  // Demo data for interactive cards
  const demoCards = [
    {
      title: "Introduction to AI Marketing",
      description: "Learn the fundamentals of AI-driven marketing strategies and how to implement them effectively in your business.",
      progress: 75,
      duration: "2h 30m",
      difficulty: 'beginner' as const,
      rating: 4.8,
      enrolled: 1250,
      status: 'in_progress' as const,
      tags: ['AI', 'Marketing', 'Strategy']
    },
    {
      title: "Advanced Data Analytics",
      description: "Dive deep into data analytics techniques and learn how to extract meaningful insights from complex datasets.",
      duration: "4h 15m",
      difficulty: 'advanced' as const,
      rating: 4.9,
      enrolled: 892,
      status: 'available' as const,
      tags: ['Analytics', 'Data Science', 'Python']
    },
    {
      title: "Digital Marketing Mastery",
      description: "Master the art of digital marketing with hands-on projects and real-world case studies.",
      duration: "3h 45m",
      difficulty: 'intermediate' as const,
      rating: 4.7,
      enrolled: 2150,
      status: 'completed' as const,
      tags: ['Digital Marketing', 'SEO', 'Social Media']
    }
  ];

  // Demo progress steps
  const demoSteps = [
    { id: '1', title: 'Watch Introduction Video', status: 'completed' as const },
    { id: '2', title: 'Read Course Materials', status: 'completed' as const },
    { id: '3', title: 'Complete Practice Quiz', status: 'active' as const },
    { id: '4', title: 'Submit Assignment', status: 'pending' as const },
    { id: '5', title: 'Participate in Discussion', status: 'pending' as const },
  ];

  // Initialize demo on mount
  useEffect(() => {
    microInteractions.initializeProgress(demoSteps);
  }, []);

  // Demo timer
  useEffect(() => {
    const timer = setInterval(() => {
      setDemoTimer(prev => {
        const newTime = prev + 1;
        microInteractions.updateTimeElapsed(newTime);
        return newTime;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [microInteractions]);

  const triggerLessonComplete = () => {
    microInteractions.completeStep(2, {
      type: 'lesson_complete',
      title: 'Lesson Completed!',
      description: 'Great job! You\'ve successfully completed the AI Marketing fundamentals lesson.',
      points: 25
    });
  };

  const triggerQuizPassed = () => {
    microInteractions.completeQuiz(85, true);
  };

  const triggerModuleComplete = () => {
    microInteractions.completeModule('Introduction to AI Marketing');
  };

  const triggerBadgeEarned = () => {
    microInteractions.earnBadge('Quick Learner', 'Completed 5 lessons in under 2 hours!');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 p-6">
      <div className="max-w-7xl mx-auto space-y-8">
        {/* Header */}
        <motion.div
          className="text-center space-y-4"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            Micro-Interaction Engagement Boosters
          </h1>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Experience delightful interactive elements that keep learners engaged and motivated throughout their learning journey
          </p>
        </motion.div>

        {/* Progress Tracker Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="w-5 h-5 text-blue-500" />
                Animated Progress Tracking
              </CardTitle>
            </CardHeader>
            <CardContent>
              <AnimatedProgressTracker
                steps={microInteractions.progressSteps}
                currentStepIndex={microInteractions.currentStepIndex}
                overallProgress={microInteractions.overallProgress}
                timeElapsed={microInteractions.timeElapsed}
                estimatedTimeRemaining={microInteractions.estimatedTimeRemaining || undefined}
                showCelebration={true}
              />
            </CardContent>
          </Card>
        </motion.div>

        {/* Interactive Buttons Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Sparkles className="w-5 h-5 text-purple-500" />
                Interactive Achievement Triggers
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <InteractiveButton
                  onClick={triggerLessonComplete}
                  className="bg-green-500 hover:bg-green-600 text-white"
                  rippleEffect={true}
                  bounceOnClick={true}
                  successFeedback={true}
                >
                  <BookOpen className="w-4 h-4 mr-2" />
                  Complete Lesson
                </InteractiveButton>

                <InteractiveButton
                  onClick={triggerQuizPassed}
                  className="bg-blue-500 hover:bg-blue-600 text-white"
                  rippleEffect={true}
                  glowEffect={true}
                >
                  <Star className="w-4 h-4 mr-2" />
                  Pass Quiz
                </InteractiveButton>

                <InteractiveButton
                  onClick={triggerModuleComplete}
                  className="bg-purple-500 hover:bg-purple-600 text-white"
                  rippleEffect={true}
                  bounceOnClick={true}
                >
                  <Target className="w-4 h-4 mr-2" />
                  Complete Module
                </InteractiveButton>

                <InteractiveButton
                  onClick={triggerBadgeEarned}
                  className="bg-orange-500 hover:bg-orange-600 text-white"
                  rippleEffect={true}
                  glowEffect={true}
                  successFeedback={true}
                >
                  <Trophy className="w-4 h-4 mr-2" />
                  Earn Badge
                </InteractiveButton>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Interactive Cards Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
        >
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <PlayCircle className="w-5 h-5 text-green-500" />
                Interactive Course Cards
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {demoCards.map((card, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.1 * index }}
                  >
                    <InteractiveHoverCard
                      {...card}
                      onClick={() => {
                        microInteractions.triggerCelebration({
                          type: 'lesson_complete',
                          title: 'Course Started!',
                          description: `You've started "${card.title}"`,
                          points: 10
                        });
                      }}
                    />
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Stats Dashboard */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8 }}
        >
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card className="text-center">
              <CardContent className="p-6">
                <motion.div
                  className="text-3xl font-bold text-blue-600"
                  key={microInteractions.totalXP}
                  initial={{ scale: 1.2, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  transition={{ duration: 0.3 }}
                >
                  {microInteractions.totalXP}
                </motion.div>
                <p className="text-sm text-gray-600 mt-1">Total XP Earned</p>
                <Sparkles className="w-6 h-6 text-yellow-500 mx-auto mt-2" />
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="p-6">
                <motion.div
                  className="text-3xl font-bold text-green-600"
                  key={microInteractions.streakCount}
                  initial={{ scale: 1.2, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  transition={{ duration: 0.3 }}
                >
                  {microInteractions.streakCount}
                </motion.div>
                <p className="text-sm text-gray-600 mt-1">Day Streak</p>
                <Zap className="w-6 h-6 text-orange-500 mx-auto mt-2" />
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="p-6">
                <motion.div
                  className="text-3xl font-bold text-purple-600"
                  key={Math.round(microInteractions.overallProgress)}
                  initial={{ scale: 1.2, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  transition={{ duration: 0.3 }}
                >
                  {Math.round(microInteractions.overallProgress)}%
                </motion.div>
                <p className="text-sm text-gray-600 mt-1">Progress</p>
                <Target className="w-6 h-6 text-purple-500 mx-auto mt-2" />
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="p-6">
                <motion.div
                  className="text-3xl font-bold text-red-600"
                  key={microInteractions.timeElapsed}
                  initial={{ scale: 1.2, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  transition={{ duration: 0.3 }}
                >
                  {Math.floor(microInteractions.timeElapsed / 60)}:{(microInteractions.timeElapsed % 60).toString().padStart(2, '0')}
                </motion.div>
                <p className="text-sm text-gray-600 mt-1">Time Elapsed</p>
                <Timer className="w-6 h-6 text-red-500 mx-auto mt-2" />
              </CardContent>
            </Card>
          </div>
        </motion.div>

        {/* Features List */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.0 }}
        >
          <Card>
            <CardHeader>
              <CardTitle>Engagement Features</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <Badge variant="outline" className="bg-green-50 text-green-700">
                      ✓ Progress Celebrations
                    </Badge>
                  </div>
                  <div className="flex items-center gap-3">
                    <Badge variant="outline" className="bg-blue-50 text-blue-700">
                      ✓ Interactive Buttons
                    </Badge>
                  </div>
                  <div className="flex items-center gap-3">
                    <Badge variant="outline" className="bg-purple-50 text-purple-700">
                      ✓ Animated Progress Tracking
                    </Badge>
                  </div>
                  <div className="flex items-center gap-3">
                    <Badge variant="outline" className="bg-orange-50 text-orange-700">
                      ✓ Hover Effects & Feedback
                    </Badge>
                  </div>
                </div>
                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <Badge variant="outline" className="bg-yellow-50 text-yellow-700">
                      ✓ Achievement Badges
                    </Badge>
                  </div>
                  <div className="flex items-center gap-3">
                    <Badge variant="outline" className="bg-pink-50 text-pink-700">
                      ✓ Streak Milestones
                    </Badge>
                  </div>
                  <div className="flex items-center gap-3">
                    <Badge variant="outline" className="bg-teal-50 text-teal-700">
                      ✓ Real-time Stats
                    </Badge>
                  </div>
                  <div className="flex items-center gap-3">
                    <Badge variant="outline" className="bg-indigo-50 text-indigo-700">
                      ✓ Confetti Animations
                    </Badge>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* Achievement Celebration Modal */}
      {microInteractions.showCelebration && microInteractions.currentAchievement && (
        <ProgressCelebration
          isVisible={microInteractions.showCelebration}
          achievement={microInteractions.currentAchievement}
          onClose={microInteractions.closeCelebration}
          onContinue={microInteractions.closeCelebration}
        />
      )}
    </div>
  );
}