import React from 'react';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Button } from "@/components/ui/button";
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Slider } from "@/components/ui/slider";
import { Label } from "@/components/ui/label";
import { 
  Sparkles, 
  Brain, 
  Clock, 
  Layers, 
  Share2, 
  BarChart, 
  MessageSquare, 
  FileCheck,
  Book,
  Zap,
  Info
} from "lucide-react";

export interface AdvancedOptionsSectionProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  children: React.ReactNode;
  defaultOpen?: boolean;
  badge?: string;
}

export function AdvancedOptionsSection({
  title,
  description,
  icon,
  children,
  defaultOpen = false,
  badge
}: AdvancedOptionsSectionProps) {
  return (
    <AccordionItem value={title.toLowerCase().replace(/\s+/g, '-')}>
      <AccordionTrigger>
        <div className="flex items-center gap-2 text-left">
          <div className="bg-primary/10 p-1.5 rounded text-primary">{icon}</div>
          <div>
            <div className="flex items-center gap-2">
              <span>{title}</span>
              {badge && (
                <Badge variant="outline" className="text-xs ml-2">
                  {badge}
                </Badge>
              )}
            </div>
            <p className="text-xs text-muted-foreground font-normal">
              {description}
            </p>
          </div>
        </div>
      </AccordionTrigger>
      <AccordionContent>
        <div className="pl-8 pt-2 space-y-4">
          {children}
        </div>
      </AccordionContent>
    </AccordionItem>
  );
}

export interface AdvancedOptionsPanelProps {
  step: 'details' | 'content' | 'media' | 'quiz' | 'micro-learning' | 'publishing';
  courseId?: number | undefined;
}

export function AdvancedOptionsPanel({ step, courseId }: AdvancedOptionsPanelProps) {
  return (
    <Card className="bg-white/50 backdrop-blur border-dashed shadow-sm">
      <div className="p-4 pb-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Sparkles className="h-4 w-4 text-primary" />
            <h3 className="text-sm font-medium">Advanced Options</h3>
          </div>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="ghost" className="h-7 w-7 p-0" size="icon">
                  <Info className="h-4 w-4" />
                  <span className="sr-only">More information</span>
                </Button>
              </TooltipTrigger>
              <TooltipContent className="max-w-xs">
                <p className="text-xs">
                  Advanced options provide additional tools and settings to enhance your course. 
                  Features vary depending on which step of course creation you're currently in.
                </p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
        <p className="text-xs text-muted-foreground mt-1">
          Enhance your course with additional tools and settings
        </p>
      </div>

      <Accordion type="multiple" className="px-4 pb-3">
        {step === 'details' && (
          <>
            <AdvancedOptionsSection
              title="SEO Optimization"
              description="Improve visibility and searchability"
              icon={<Layers className="h-4 w-4" />}
              badge="Recommended"
            >
              <div className="grid gap-4">
                <div className="flex flex-col gap-2">
                  <Label htmlFor="seo-keywords" className="text-xs">SEO Keywords</Label>
                  <input 
                    id="seo-keywords" 
                    className="border rounded p-2 text-sm" 
                    placeholder="Enter keywords separated by commas" 
                  />
                  <p className="text-xs text-muted-foreground">
                    Add keywords to help learners find your course
                  </p>
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="seo-auto" className="text-xs cursor-pointer">Auto-generate SEO metadata</Label>
                  <Switch id="seo-auto" />
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="social-preview" className="text-xs cursor-pointer">Generate social media preview</Label>
                  <Switch id="social-preview" />
                </div>
                <Button size="sm" variant="secondary" className="mt-2">
                  <Sparkles className="h-3.5 w-3.5 mr-2" />
                  Generate SEO Recommendations
                </Button>
              </div>
            </AdvancedOptionsSection>

            <AdvancedOptionsSection
              title="Accessibility Options"
              description="Make your course accessible to all learners"
              icon={<Book className="h-4 w-4" />}
            >
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <Label htmlFor="auto-captions" className="text-xs cursor-pointer">Auto-generate captions</Label>
                  <Switch id="auto-captions" defaultChecked />
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="transcript" className="text-xs cursor-pointer">Include full transcript</Label>
                  <Switch id="transcript" defaultChecked />
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="alt-text" className="text-xs cursor-pointer">Auto-generate alt text for images</Label>
                  <Switch id="alt-text" defaultChecked />
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="readability" className="text-xs cursor-pointer">Readability enhancements</Label>
                  <Switch id="readability" />
                </div>
              </div>
            </AdvancedOptionsSection>
          </>
        )}

        {step === 'content' && (
          <>
            <AdvancedOptionsSection
              title="AI Script Generation"
              description="Let AI help you create engaging content"
              icon={<Brain className="h-4 w-4" />}
              badge="Popular"
            >
              <div className="space-y-3">
                <Button variant="secondary" size="sm" className="w-full justify-start">
                  <Zap className="h-3.5 w-3.5 mr-2" /> Generate Full Script
                </Button>
                <Button variant="secondary" size="sm" className="w-full justify-start">
                  <Zap className="h-3.5 w-3.5 mr-2" /> Improve Existing Script
                </Button>
                <Button variant="secondary" size="sm" className="w-full justify-start">
                  <Zap className="h-3.5 w-3.5 mr-2" /> Create Section Summaries
                </Button>
                <Button variant="secondary" size="sm" className="w-full justify-start">
                  <Zap className="h-3.5 w-3.5 mr-2" /> Add Learning Objectives
                </Button>
                <Button variant="secondary" size="sm" className="w-full justify-start">
                  <Zap className="h-3.5 w-3.5 mr-2" /> Create Presentation Slides
                </Button>
              </div>
            </AdvancedOptionsSection>

            <AdvancedOptionsSection
              title="Quiz Generator"
              description="Create assessment questions from your content"
              icon={<MessageSquare className="h-4 w-4" />}
            >
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label className="text-xs">Number of Questions</Label>
                  <div className="flex items-center gap-4">
                    <Slider
                      defaultValue={[5]}
                      max={20}
                      min={1}
                      step={1}
                      className="flex-1"
                    />
                    <span className="text-xs font-medium w-4">5</span>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label className="text-xs">Question Types</Label>
                  <div className="flex flex-wrap gap-2">
                    <Badge variant="outline" className="cursor-pointer bg-primary/10 hover:bg-primary/20">Multiple Choice</Badge>
                    <Badge variant="outline" className="cursor-pointer">True/False</Badge>
                    <Badge variant="outline" className="cursor-pointer">Short Answer</Badge>
                    <Badge variant="outline" className="cursor-pointer">Fill in the Blank</Badge>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label className="text-xs">Difficulty Level</Label>
                  <div className="flex gap-2">
                    <Badge variant="outline" className="cursor-pointer bg-primary/10 hover:bg-primary/20">Easy</Badge>
                    <Badge variant="outline" className="cursor-pointer">Medium</Badge>
                    <Badge variant="outline" className="cursor-pointer">Hard</Badge>
                    <Badge variant="outline" className="cursor-pointer">Mixed</Badge>
                  </div>
                </div>
                
                <div className="flex flex-col gap-3 pt-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="flashcards" className="text-xs cursor-pointer">Include Flashcards</Label>
                    <Switch id="flashcards" defaultChecked />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label htmlFor="summary" className="text-xs cursor-pointer">Include Study Summary</Label>
                    <Switch id="summary" defaultChecked />
                  </div>
                </div>
                
                <Button className="w-full">
                  <Sparkles className="h-3.5 w-3.5 mr-2" />
                  Generate Quiz
                </Button>
              </div>
            </AdvancedOptionsSection>
          </>
        )}

        {step === 'media' && (
          <>
            <AdvancedOptionsSection
              title="Stock Media Library"
              description="Find professional images and videos"
              icon={<Layers className="h-4 w-4" />}
              badge="New"
            >
              <div className="space-y-3">
                <div className="grid grid-cols-2 gap-3">
                  <Button variant="outline" size="sm">
                    Search Stock Images
                  </Button>
                  <Button variant="outline" size="sm">
                    Search Stock Videos
                  </Button>
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="auto-suggest" className="text-xs cursor-pointer">Auto-suggest relevant media</Label>
                  <Switch id="auto-suggest" defaultChecked />
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="ai-captions" className="text-xs cursor-pointer">AI-generated captions</Label>
                  <Switch id="ai-captions" defaultChecked />
                </div>
              </div>
            </AdvancedOptionsSection>

            <AdvancedOptionsSection
              title="Text-to-Speech"
              description="Convert your script to spoken audio"
              icon={<MessageSquare className="h-4 w-4" />}
            >
              <div className="space-y-3">
                <div className="space-y-2">
                  <Label className="text-xs">Voice Style</Label>
                  <div className="flex flex-wrap gap-2">
                    <Badge variant="outline" className="cursor-pointer bg-primary/10 hover:bg-primary/20">Professional</Badge>
                    <Badge variant="outline" className="cursor-pointer">Friendly</Badge>
                    <Badge variant="outline" className="cursor-pointer">Energetic</Badge>
                    <Badge variant="outline" className="cursor-pointer">Calm</Badge>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label className="text-xs">Language Accent</Label>
                  <div className="flex flex-wrap gap-2">
                    <Badge variant="outline" className="cursor-pointer bg-primary/10 hover:bg-primary/20">American</Badge>
                    <Badge variant="outline" className="cursor-pointer">British</Badge>
                    <Badge variant="outline" className="cursor-pointer">Australian</Badge>
                    <Badge variant="outline" className="cursor-pointer">Indian</Badge>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label className="text-xs">Voice Speed</Label>
                  <div className="flex items-center gap-4">
                    <Slider
                      defaultValue={[100]}
                      max={150}
                      min={70}
                      step={5}
                      className="flex-1"
                    />
                    <span className="text-xs font-medium w-8">100%</span>
                  </div>
                </div>
                
                <Button className="w-full">
                  <Sparkles className="h-3.5 w-3.5 mr-2" />
                  Generate Speech
                </Button>
              </div>
            </AdvancedOptionsSection>
          </>
        )}

        {(step === 'micro-learning' || step === 'quiz') && (
          <>
            <AdvancedOptionsSection
              title="Micro-Learning Settings"
              description="Break content into bite-sized segments"
              icon={<Clock className="h-4 w-4" />}
              defaultOpen={step === 'micro-learning'}
              badge="Recommended"
            >
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="enable-micro" className="text-xs cursor-pointer">Enable Micro-Learning</Label>
                  <Switch id="enable-micro" defaultChecked={step === 'micro-learning'} />
                </div>
                
                <div className="space-y-2">
                  <Label className="text-xs">Number of Segments</Label>
                  <div className="flex items-center gap-4">
                    <Slider
                      defaultValue={[5]}
                      max={10}
                      min={2}
                      step={1}
                      className="flex-1"
                    />
                    <span className="text-xs font-medium w-4">5</span>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label className="text-xs">Break Interval Type</Label>
                  <div className="flex gap-2">
                    <Badge variant="outline" className="cursor-pointer bg-primary/10 hover:bg-primary/20">Time-based</Badge>
                    <Badge variant="outline" className="cursor-pointer">Content-based</Badge>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label className="text-xs">Knowledge Check Frequency</Label>
                  <div className="flex gap-2">
                    <Badge variant="outline" className="cursor-pointer">Low</Badge>
                    <Badge variant="outline" className="cursor-pointer bg-primary/10 hover:bg-primary/20">Medium</Badge>
                    <Badge variant="outline" className="cursor-pointer">High</Badge>
                  </div>
                </div>
                
                <div className="flex items-center justify-between">
                  <Label htmlFor="auto-summarize" className="text-xs cursor-pointer">Auto-summarize key points</Label>
                  <Switch id="auto-summarize" defaultChecked />
                </div>
                
                {step === 'micro-learning' && (
                  <Button className="w-full">
                    <Sparkles className="h-3.5 w-3.5 mr-2" />
                    Apply Micro-Learning
                  </Button>
                )}
              </div>
            </AdvancedOptionsSection>
            
            {step === 'quiz' && (
              <AdvancedOptionsSection
                title="Quiz Settings"
                description="Configure your assessment options"
                icon={<FileCheck className="h-4 w-4" />}
                defaultOpen={true}
              >
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="randomize" className="text-xs cursor-pointer">Randomize Questions</Label>
                    <Switch id="randomize" defaultChecked />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <Label htmlFor="show-answers" className="text-xs cursor-pointer">Show Correct Answers</Label>
                    <Switch id="show-answers" defaultChecked />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <Label htmlFor="retry" className="text-xs cursor-pointer">Allow Retries</Label>
                    <Switch id="retry" defaultChecked />
                  </div>
                  
                  <div className="space-y-2">
                    <Label className="text-xs">Passing Score (%)</Label>
                    <div className="flex items-center gap-4">
                      <Slider
                        defaultValue={[70]}
                        max={100}
                        min={50}
                        step={5}
                        className="flex-1"
                      />
                      <span className="text-xs font-medium w-8">70%</span>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <Label className="text-xs">Time Limit (minutes)</Label>
                    <div className="flex items-center gap-4">
                      <Slider
                        defaultValue={[10]}
                        max={60}
                        min={0}
                        step={5}
                        className="flex-1"
                      />
                      <span className="text-xs font-medium w-8">10</span>
                    </div>
                    <p className="text-xs text-muted-foreground">Set to 0 for no time limit</p>
                  </div>
                </div>
              </AdvancedOptionsSection>
            )}
          </>
        )}

        {step === 'publishing' && (
          <>
            <AdvancedOptionsSection
              title="Distribution Channels"
              description="Publish your course across platforms"
              icon={<Share2 className="h-4 w-4" />}
              badge="Pro"
            >
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Label htmlFor="platform" className="text-xs cursor-pointer">Native Platform</Label>
                    <Badge variant="secondary" className="text-xs">Default</Badge>
                  </div>
                  <Switch id="platform" defaultChecked disabled />
                </div>
                
                <div className="flex items-center justify-between">
                  <Label htmlFor="canvas" className="text-xs cursor-pointer">Canvas LMS</Label>
                  <Switch id="canvas" />
                </div>
                
                <div className="flex items-center justify-between">
                  <Label htmlFor="moodle" className="text-xs cursor-pointer">Moodle</Label>
                  <Switch id="moodle" />
                </div>
                
                <div className="flex items-center justify-between">
                  <Label htmlFor="marketplace" className="text-xs cursor-pointer">Course Marketplace</Label>
                  <Switch id="marketplace" />
                </div>
                
                <Button variant="outline" size="sm" className="w-full mt-2">
                  <Share2 className="h-3.5 w-3.5 mr-2" />
                  Configure Distribution Settings
                </Button>
              </div>
            </AdvancedOptionsSection>

            <AdvancedOptionsSection
              title="Analytics Setup"
              description="Track student engagement and progress"
              icon={<BarChart className="h-4 w-4" />}
            >
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <Label htmlFor="completion" className="text-xs cursor-pointer">Track Completion Rates</Label>
                  <Switch id="completion" defaultChecked />
                </div>
                
                <div className="flex items-center justify-between">
                  <Label htmlFor="retention" className="text-xs cursor-pointer">Knowledge Retention Metrics</Label>
                  <Switch id="retention" defaultChecked />
                </div>
                
                <div className="flex items-center justify-between">
                  <Label htmlFor="milestones" className="text-xs cursor-pointer">Learning Milestone Notifications</Label>
                  <Switch id="milestones" defaultChecked />
                </div>
                
                <div className="flex items-center justify-between">
                  <Label htmlFor="reports" className="text-xs cursor-pointer">Student Progress Reports</Label>
                  <Switch id="reports" />
                </div>
                
                <div className="flex items-center justify-between">
                  <Label htmlFor="feedback" className="text-xs cursor-pointer">Automated Feedback Collection</Label>
                  <Switch id="feedback" />
                </div>
              </div>
            </AdvancedOptionsSection>
          </>
        )}
      </Accordion>
    </Card>
  );
}