import express, { Request, Response } from 'express';
import { z } from 'zod';
import aiToolsService from '../services/aiToolsService';
import runpodService from '../services/runpodService';
import s3Service from '../services/awsS3Service';
import { db } from '../db';
import OpenAI from 'openai';
import { eq } from 'drizzle-orm';

const router = express.Router();

// Validation schemas
const textGenerationSchema = z.object({
  prompt: z.string().min(1).max(4000),
  model: z.enum(['mistral', 'mixtral']).optional(),
  courseId: z.number().optional(),
  lessonId: z.number().optional(),
  maxTokens: z.number().min(1).max(4000).optional(),
  temperature: z.number().min(0).max(2).optional(),
  topP: z.number().min(0).max(1).optional(),
});

const textToSpeechSchema = z.object({
  text: z.string().min(1).max(10000),
  model: z.enum(['kokoro', 'coqui']).optional(),
  voice: z.string().optional(),
  courseId: z.number().optional(),
  lessonId: z.number().optional(),
  speed: z.number().min(0.5).max(2).optional(),
  language: z.string().optional(),
});

const imageGenerationSchema = z.object({
  prompt: z.string().min(1).max(1000),
  model: z.enum(['kandinsky', 'wan']).optional(),
  courseId: z.number().optional(),
  lessonId: z.number().optional(),
  negativePrompt: z.string().optional(),
  width: z.number().min(64).max(1024).optional(),
  height: z.number().min(64).max(1024).optional(),
  numImages: z.number().min(1).max(4).optional(),
  guidanceScale: z.number().min(1).max(20).optional(),
  steps: z.number().min(10).max(150).optional(),
  seed: z.number().optional(),
});

const animationGenerationSchema = z.object({
  prompt: z.string().min(1).max(1000),
  courseId: z.number().optional(),
  lessonId: z.number().optional(),
  negativePrompt: z.string().optional(),
  inputImage: z.string().optional(), // base64 image
  motionStrength: z.number().min(0).max(1).optional(),
  frames: z.number().min(8).max(48).optional(),
  fps: z.number().min(1).max(30).optional(),
  seed: z.number().optional(),
});

// Helper function to check authentication
function isAuthenticated(req: Request, res: Response, next: express.NextFunction) {
  if (!req.session?.userId) {
    return res.status(401).json({ error: 'Not authenticated' });
  }
  next();
}

// Check configuration status
router.get('/status', isAuthenticated, (req: Request, res: Response) => {
  const status = {
    timestamp: new Date(),
    status: 'ok',
    services: {
      runpod: runpodService.isConfigured(),
      s3: s3Service.isConfigured(),
    },
    endpoints: {
      marp: runpodService.isEndpointConfigured('marp'),
      kokoro: runpodService.isEndpointConfigured('kokoro'),
      coqui: runpodService.isEndpointConfigured('coqui'),
      sadtalker: runpodService.isEndpointConfigured('sadtalker'),
    }
  };
  
  // Set overall status based on endpoint availability
  if (!status.services.runpod || !status.services.s3) {
    status.status = 'error';
  } else if (!status.endpoints.marp || 
             !status.endpoints.kokoro || 
             !status.endpoints.coqui || 
             !status.endpoints.sadtalker) {
    status.status = 'partial';
  }
  
  res.json(status);
});

// Health check endpoint
router.get('/health', async (req: Request, res: Response) => {
  const healthStatus = {
    timestamp: new Date(),
    status: 'ok',
    services: {
      runpod: runpodService.isConfigured(),
      s3: s3Service.isConfigured(),
    },
    endpoints: {
      marp: runpodService.isEndpointConfigured('marp'),
      kokoro: runpodService.isEndpointConfigured('kokoro'),
      coqui: runpodService.isEndpointConfigured('coqui'),
      sadtalker: runpodService.isEndpointConfigured('sadtalker'),
    }
  };
  
  // Set overall status based on endpoint availability
  if (!healthStatus.services.runpod || !healthStatus.services.s3) {
    healthStatus.status = 'error';
  } else if (!healthStatus.endpoints.marp || 
             !healthStatus.endpoints.kokoro || 
             !healthStatus.endpoints.coqui || 
             !healthStatus.endpoints.sadtalker) {
    healthStatus.status = 'partial';
  }
  
  res.json(healthStatus);
});

// Text generation endpoint
router.post('/text', isAuthenticated, async (req: Request, res: Response) => {
  try {
    // Validate request
    const validationResult = textGenerationSchema.safeParse(req.body);
    
    if (!validationResult.success) {
      return res.status(400).json({ 
        error: 'Invalid data provided', 
        details: validationResult.error.format() 
      });
    }
    
    const data = validationResult.data;
    
    // Generate text
    const result = await aiToolsService.generateText({
      ...data,
      userId: req.user!.id
    });
    
    res.json(result);
  } catch (error) {
    console.error('Error generating text:', error);
    res.status(500).json({ error: (error as Error).message });
  }
});

// Text to speech endpoint
router.post('/speech', isAuthenticated, async (req: Request, res: Response) => {
  try {
    // Validate request
    const validationResult = textToSpeechSchema.safeParse(req.body);
    
    if (!validationResult.success) {
      return res.status(400).json({ 
        error: 'Invalid data provided', 
        details: validationResult.error.format() 
      });
    }
    
    const data = validationResult.data;
    
    // Generate speech
    const result = await aiToolsService.generateSpeech({
      ...data,
      userId: req.user!.id
    });
    
    res.json(result);
  } catch (error) {
    console.error('Error generating speech:', error);
    res.status(500).json({ error: (error as Error).message });
  }
});

// Image generation endpoint
router.post('/image', isAuthenticated, async (req: Request, res: Response) => {
  try {
    // Validate request
    const validationResult = imageGenerationSchema.safeParse(req.body);
    
    if (!validationResult.success) {
      return res.status(400).json({ 
        error: 'Invalid data provided', 
        details: validationResult.error.format() 
      });
    }
    
    const data = validationResult.data;
    
    // Generate image
    const result = await aiToolsService.generateImage({
      ...data,
      userId: req.user!.id
    });
    
    res.json(result);
  } catch (error) {
    console.error('Error generating image:', error);
    res.status(500).json({ error: (error as Error).message });
  }
});

// Animation generation endpoint
router.post('/animation', isAuthenticated, async (req: Request, res: Response) => {
  try {
    // Validate request
    const validationResult = animationGenerationSchema.safeParse(req.body);
    
    if (!validationResult.success) {
      return res.status(400).json({ 
        error: 'Invalid data provided', 
        details: validationResult.error.format() 
      });
    }
    
    const data = validationResult.data;
    
    // Generate animation
    const result = await aiToolsService.generateAnimation({
      ...data,
      userId: req.user!.id
    });
    
    res.json(result);
  } catch (error) {
    console.error('Error generating animation:', error);
    res.status(500).json({ error: (error as Error).message });
  }
});

// Job status endpoint
router.get('/job/:jobId', isAuthenticated, async (req: Request, res: Response) => {
  try {
    const { jobId } = req.params;
    
    // Get job status
    const status = await aiToolsService.getJobStatus(jobId);
    
    res.json(status);
  } catch (error) {
    console.error('Error getting job status:', error);
    res.status(500).json({ error: (error as Error).message });
  }
});

// List user jobs
router.get('/jobs', isAuthenticated, async (req: Request, res: Response) => {
  try {
    const { type, courseId, lessonId, limit, offset } = req.query;
    
    // Get user jobs
    const jobs = await aiToolsService.getUserJobs(req.user!.id, {
      type: type as string | undefined,
      courseId: courseId ? parseInt(courseId as string) : undefined,
      lessonId: lessonId ? parseInt(lessonId as string) : undefined,
      limit: limit ? parseInt(limit as string) : 50,
      offset: offset ? parseInt(offset as string) : 0
    });
    
    res.json({ jobs });
  } catch (error) {
    console.error('Error listing user jobs:', error);
    res.status(500).json({ error: (error as Error).message });
  }
});

// API settings schema
const apiSettingsSchema = z.object({
  runpodApiKey: z.string().optional(),
  awsAccessKeyId: z.string().optional(),
  awsSecretAccessKey: z.string().optional(),
  s3BucketName: z.string().optional(),
  s3Region: z.string().optional(),
  cloudFrontDomain: z.string().optional(),
});

// Endpoint settings schema
const endpointSettingsSchema = z.object({
  kokoroEndpoint: z.string().optional(),
  coquiEndpoint: z.string().optional(),
  sadtalkerEndpoint: z.string().optional(),
  marpEndpoint: z.string().optional(),
});

// Get API settings
router.get('/settings/api', isAuthenticated, async (req: Request, res: Response) => {
  try {
    // In production, you would get this from a database
    const settings = {
      runpodApiKey: process.env.RUNPOD_API_KEY ? '••••••••••••••••' : '',
      awsAccessKeyId: process.env.AWS_ACCESS_KEY_ID || '',
      awsSecretAccessKey: process.env.AWS_SECRET_ACCESS_KEY ? '••••••••••••••••' : '',
      s3BucketName: process.env.AWS_S3_BUCKET || '',
      s3Region: process.env.AWS_REGION || '',
      cloudFrontDomain: process.env.AWS_CLOUDFRONT_DOMAIN || '',
    };
    
    res.json(settings);
  } catch (error) {
    console.error('Error retrieving API settings:', error);
    res.status(500).json({ error: (error as Error).message });
  }
});

// Save API settings
router.post('/settings/api', isAuthenticated, async (req: Request, res: Response) => {
  try {
    // Validate the request
    const validationResult = apiSettingsSchema.safeParse(req.body);
    
    if (!validationResult.success) {
      return res.status(400).json({ 
        error: 'Invalid data provided', 
        details: validationResult.error.format() 
      });
    }
    
    const settings = validationResult.data;
    
    // Update environment variables
    if (settings.runpodApiKey && settings.runpodApiKey !== '••••••••••••••••') {
      process.env.RUNPOD_API_KEY = settings.runpodApiKey;
    }
    
    if (settings.awsAccessKeyId) {
      process.env.AWS_ACCESS_KEY_ID = settings.awsAccessKeyId;
    }
    
    if (settings.awsSecretAccessKey && settings.awsSecretAccessKey !== '••••••••••••••••') {
      process.env.AWS_SECRET_ACCESS_KEY = settings.awsSecretAccessKey;
    }
    
    if (settings.s3BucketName) {
      process.env.AWS_S3_BUCKET = settings.s3BucketName;
    }
    
    if (settings.s3Region) {
      process.env.AWS_REGION = settings.s3Region;
    }
    
    if (settings.cloudFrontDomain) {
      process.env.AWS_CLOUDFRONT_DOMAIN = settings.cloudFrontDomain;
    }
    
    // In a production app, you would save these to a secure database
    // For this example, we'll just return success
    
    res.json({ success: true });
  } catch (error) {
    console.error('Error saving API settings:', error);
    res.status(500).json({ error: (error as Error).message });
  }
});

// Get endpoint settings
router.get('/settings/endpoints', isAuthenticated, async (req: Request, res: Response) => {
  try {
    // In production, you would get this from a database
    const settings = {
      kokoroEndpoint: process.env.RUNPOD_KOKORO_ENDPOINT || '',
      coquiEndpoint: process.env.RUNPOD_COQUI_ENDPOINT || 'https://8x1dneg4ifrfbc-5000.proxy.runpod.net/speak',
      sadtalkerEndpoint: process.env.RUNPOD_SADTALKER_ENDPOINT || '',
      marpEndpoint: process.env.RUNPOD_MARP_ENDPOINT || '',
    };
    
    res.json(settings);
  } catch (error) {
    console.error('Error retrieving endpoint settings:', error);
    res.status(500).json({ error: (error as Error).message });
  }
});

// Save endpoint settings
router.post('/settings/endpoints', isAuthenticated, async (req: Request, res: Response) => {
  try {
    // Validate the request
    const validationResult = endpointSettingsSchema.safeParse(req.body);
    
    if (!validationResult.success) {
      return res.status(400).json({ 
        error: 'Invalid data provided', 
        details: validationResult.error.format() 
      });
    }
    
    const settings = validationResult.data;
    
    // Update environment variables for the specific models you've installed
    if (settings.kokoroEndpoint) {
      process.env.RUNPOD_KOKORO_ENDPOINT = settings.kokoroEndpoint;
    }
    
    if (settings.coquiEndpoint) {
      process.env.RUNPOD_COQUI_ENDPOINT = settings.coquiEndpoint;
    }
    
    if (settings.sadtalkerEndpoint) {
      process.env.RUNPOD_SADTALKER_ENDPOINT = settings.sadtalkerEndpoint;
    }
    
    if (settings.marpEndpoint) {
      process.env.RUNPOD_MARP_ENDPOINT = settings.marpEndpoint;
    }
    
    // In a production app, you would save these to a secure database
    // For this example, we'll just return success
    
    res.json({ success: true });
  } catch (error) {
    console.error('Error saving endpoint settings:', error);
    res.status(500).json({ error: (error as Error).message });
  }
});

// Test connection to an endpoint
router.post('/test-connection', isAuthenticated, async (req: Request, res: Response) => {
  try {
    const { endpoint } = req.body;
    
    if (!endpoint) {
      return res.status(400).json({ error: 'Endpoint name is required' });
    }
    
    // Special handling for Coqui endpoint which has a default value
    if (endpoint === 'coqui') {
      // The Coqui endpoint is always considered available since we have a default
      return res.status(200).json({ success: true });
    }
    
    // Check if the endpoint is configured
    const isConfigured = runpodService.isEndpointConfigured(endpoint);
    
    if (!isConfigured) {
      return res.status(404).json({ error: `Endpoint ${endpoint} is not configured` });
    }
    
    // In a production app, you would actually test the connection
    // For now, we'll just return success if it's configured
    res.status(200).json({ success: true });
  } catch (error) {
    console.error('Error testing connection:', error);
    res.status(500).json({ error: (error as Error).message });
  }
});

// Module complexity balancing schema
const balanceModuleComplexitySchema = z.object({
  modules: z.array(z.object({
    id: z.string(),
    title: z.string(),
    lessonCount: z.number().optional(),
    lessons: z.array(z.object({
      id: z.string(),
      title: z.string(),
      type: z.string().optional()
    })).optional()
  }))
});

// Endpoint for balancing module complexity
router.post('/balance-module-complexity', async (req: Request, res: Response) => {
  try {
    // For simplicity and testing, we'll return a mocked balanced structure
    // This will help verify the frontend integration without API calls
    const balancedStructure = {
      modules: req.body.modules.map(module => ({
        title: module.title,
        complexity: ["beginner", "intermediate", "advanced"][Math.floor(Math.random() * 3)],
        lessons: (module.lessons || []).map(lesson => ({
          title: lesson.title,
          type: lesson.type || "video",
          complexity: ["low", "medium", "high"][Math.floor(Math.random() * 3)]
        }))
      }))
    };
    
    // Return the balanced module structure
    res.json(balancedStructure);
    
  } catch (error) {
    console.error('Error balancing module complexity:', error);
    res.status(500).json({ 
      error: 'Failed to balance module complexity',
      message: (error as Error).message 
    });
  }
});

export default router;