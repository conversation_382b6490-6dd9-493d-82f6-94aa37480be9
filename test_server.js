import express from 'express';
import cors from 'cors';

const app = express();
const PORT = 3001;

// Middleware
app.use(cors());
app.use(express.json());

// Basic health check
app.get('/api/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    message: 'AILearnMaster test server is running'
  });
});

// Test Modal A100 connectivity
app.get('/api/test-modal', async (req, res) => {
  try {
    const fetch = (await import('node-fetch')).default;
    const response = await fetch('https://trade-digital--courseai-a100-working-health.modal.run');
    const data = await response.json();
    
    res.json({
      status: 'success',
      modal_a100_status: data,
      message: 'Modal A100 GPU backend is accessible'
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      error: error.message,
      message: 'Failed to connect to Modal A100 backend'
    });
  }
});

// Test voice services
app.get('/api/voice/services', (req, res) => {
  res.json({
    services: [
      {
        id: 'chatterbox',
        name: 'Chatterbox TTS',
        status: 'available',
        tier: 'enterprise',
        endpoint: 'Modal A100 GPU'
      },
      {
        id: 'openai',
        name: 'OpenAI TTS',
        status: process.env.OPENAI_API_KEY ? 'available' : 'disabled',
        tier: 'premium'
      },
      {
        id: 'elevenlabs',
        name: 'ElevenLabs',
        status: process.env.ELEVENLABS_API_KEY ? 'available' : 'disabled',
        tier: 'premium'
      }
    ]
  });
});

// Test course creation endpoint
app.post('/api/course/create', (req, res) => {
  const { title, description, type } = req.body;
  
  res.json({
    status: 'success',
    course: {
      id: Date.now(),
      title: title || 'Test Course',
      description: description || 'A test course created via API',
      type: type || 'traditional',
      created_at: new Date().toISOString()
    },
    message: 'Course creation endpoint is working'
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 AILearnMaster Test Server running on http://localhost:${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/api/health`);
  console.log(`🔗 Modal A100 test: http://localhost:${PORT}/api/test-modal`);
  console.log(`🗣️ Voice services: http://localhost:${PORT}/api/voice/services`);
});
