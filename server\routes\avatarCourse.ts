/**
 * Avatar Course Generation Routes
 * Enhanced avatar course creation with individual lesson videos and FFmpeg assembly
 */

import { Router, Request, Response } from 'express';
import { avatarCourseGenerationService } from '../services/avatarCourseGenerationService';
import { authenticate } from '../middleware/auth';

const router = Router();

// Generate complete avatar course with individual lessons
router.post('/generate', authenticate, async (req: Request, res: Response) => {
  try {
    const { courseData, avatarSettings } = req.body;

    if (!courseData || !avatarSettings) {
      return res.status(400).json({
        error: 'Course data and avatar settings are required'
      });
    }

    console.log(`Starting avatar course generation: ${courseData.title}`);
    console.log(`Total modules: ${courseData.modules?.length || 0}`);

    // Generate the complete course
    const result = await avatarCourseGenerationService.generateCourse(
      courseData,
      avatarSettings
    );

    if (result.status === 'completed') {
      res.json({
        success: true,
        courseId: result.courseId,
        title: result.title,
        finalVideoPath: result.finalVideoPath,
        lessons: result.lessons,
        totalDuration: result.totalDuration,
        status: result.status
      });
    } else {
      res.status(500).json({
        success: false,
        error: result.error || 'Course generation failed',
        courseId: result.courseId,
        lessons: result.lessons
      });
    }

  } catch (error) {
    console.error('Avatar course generation error:', error);
    res.status(500).json({
      error: 'Course generation failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Get course generation status
router.get('/status/:courseId', authenticate, async (req: Request, res: Response) => {
  try {
    const { courseId } = req.params;
    
    // In a real implementation, this would check the generation status
    // For now, return a mock status
    res.json({
      courseId,
      status: 'completed',
      progress: 100,
      lessons: []
    });

  } catch (error) {
    console.error('Error getting course status:', error);
    res.status(500).json({
      error: 'Failed to get course status'
    });
  }
});

// Download course video
router.get('/download/:courseId', authenticate, async (req: Request, res: Response) => {
  try {
    const { courseId } = req.params;
    
    // In a real implementation, this would serve the generated video file
    res.status(404).json({
      error: 'Course video not found'
    });

  } catch (error) {
    console.error('Error downloading course:', error);
    res.status(500).json({
      error: 'Failed to download course'
    });
  }
});

// Clean up course files
router.delete('/cleanup/:courseId', authenticate, async (req: Request, res: Response) => {
  try {
    const { courseId } = req.params;
    
    await avatarCourseGenerationService.cleanupCourse(courseId);
    
    res.json({
      success: true,
      message: 'Course files cleaned up successfully'
    });

  } catch (error) {
    console.error('Error cleaning up course:', error);
    res.status(500).json({
      error: 'Failed to clean up course files'
    });
  }
});

export default router;