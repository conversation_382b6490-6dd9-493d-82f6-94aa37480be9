import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { toast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { Module, Lesson } from "./ContentStructure";
import { QuizBuilder } from "../quiz/QuizBuilder";
import { Progress } from "@/components/ui/progress";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { MediaLibrary } from "../media/MediaLibrary";
import PublishPlatformSelector from "@/components/integrations/publish-platform-selector";
import { SharedCourseEditingPanel, CollaborationNotification } from "../collaboration";
import CourseDownloadPdf from "./CourseDownloadPdf";
import { usePlayfulLoading } from "@/hooks/use-playful-loading";
import {
  AlertCircle,
  CheckCircle2,
  ArrowRight,
  Youtube, 
  ExternalLink,
  BookOpen,
  ChevronRight,
  FileText,
  Image,
  Loader2,
  Music,
  FileVideo,
  AlertTriangle,
  Globe,
  BarChart4,
  BrainCircuit,
  Video,
  Mic,
  Play,
  Download,
  Eye,
  Users
} from "lucide-react";

// Interface for a course element (representing a specific component in the lesson)
interface CourseElement {
  id: string;
  type: 'title' | 'text' | 'image' | 'video' | 'audio' | 'quiz' | 'slide' | 'file' | 'separator';
  content: string | null;
  mediaId?: number;
  mediaUrl?: string;
  order: number;
  duration?: number;
  config?: any;
}

// Object to store lesson elements by moduleId and lessonId
type LessonElements = {
  [moduleId: string]: {
    [lessonId: string]: CourseElement[];
  }
};

interface Media {
  id: number;
  name: string;
  type: string;
  url: string;
  mimeType: string;
  fileSize: number;
  courseId?: number | null;
  lessonId?: number | null;
  createdAt: string;
  originalFilename?: string | null;
  duration?: number | null;
  source?: string;
  sourceId?: string | null;
  sourceData?: any;
}

interface PreviewPublishProps {
  onPrevious: () => void;
  onFinish: () => void;
  courseDetails: {
    title: string;
    description: string;
    category: string;
    targetAudience?: string;
  };
  courseStructure: {
    modules: Module[];
  };
  courseScripts?: {
    [moduleKey: string]: {
      [lessonKey: string]: string;
    };
  };
  exportSettings?: {
    format: string;
    quality: string;
  };
  // Added for lesson elements from the assembly step
  lessonElements?: LessonElements;
  mediaLibrary?: Media[];
  // Added for edit mode support
  courseId?: number | null;
  isEditing?: boolean;
  // Added for tracking if voice narration has already been created
  generatedAudio?: Record<string, Media[]>;
}

interface Platform {
  id: string;
  name: string;
  icon: React.ReactNode;
  color: string;
  connected: boolean;
  description: string;
}

interface PublishStatus {
  isPending: boolean;
  isComplete: boolean;
  platform: string | null;
  error: string | null;
}

interface VideoGeneration {
  moduleIndex: number;
  lessonIndex: number;
  status: 'idle' | 'generating' | 'completed' | 'failed';
  jobId?: string;
  progress: number;
  mediaId?: number;
  videoUrl?: string;
  error?: string;
}

export function PreviewPublish({ 
  onPrevious, 
  onFinish, 
  courseDetails, 
  courseStructure, 
  courseScripts = {},
  exportSettings,
  lessonElements = {},
  mediaLibrary = [],
  courseId = null,
  isEditing = false,
  generatedAudio = {}
}: PreviewPublishProps) {
  const { showTaskLoading, hideLoading } = usePlayfulLoading();
  const [activeTab, setActiveTab] = useState<string>("review");
  const [expandedModules, setExpandedModules] = useState<Record<number, boolean>>({});
  const [publishStatus, setPublishStatus] = useState<PublishStatus>({
    isPending: false,
    isComplete: false,
    platform: null,
    error: null
  });
  // Updated to use numeric IDs for platform selection
  const [selectedPlatformIds, setSelectedPlatformIds] = useState<number[]>([]);
  
  // Track which platforms are connected
  const [connectedPlatforms, setConnectedPlatforms] = useState<Record<string, boolean>>({
    youtube: false,
    udemy: false,
    kajabi: false,
    teachable: false
  });
  
  // Track connection loading state for UI feedback
  const [connectingPlatform, setConnectingPlatform] = useState<string | null>(null);
  // Store quiz data when generated
  const [quizData, setQuizData] = useState<any>(null);
  
  // Video generation state
  const [videoGenerations, setVideoGenerations] = useState<Record<string, VideoGeneration>>({});
  const [selectedVoice, setSelectedVoice] = useState<string>("Adam");
  const [selectedModule, setSelectedModule] = useState<number | null>(null);
  const [selectedLesson, setSelectedLesson] = useState<number | null>(null);
  const [selectedMediaIds, setSelectedMediaIds] = useState<number[]>([]);
  const [pollingIntervals, setPollingIntervals] = useState<Record<string, number>>({});

  // Updated to use the connectedPlatforms state
  const platforms: Platform[] = [
    {
      id: "youtube",
      name: "YouTube",
      icon: <Youtube size={24} />,
      color: "bg-red-600",
      connected: connectedPlatforms.youtube,
      description: "Upload your course videos directly to YouTube as a playlist"
    },
    {
      id: "udemy",
      name: "Udemy",
      icon: <BookOpen size={24} />,
      color: "bg-purple-600",
      connected: connectedPlatforms.udemy,
      description: "Publish your course on Udemy's global learning marketplace"
    },
    {
      id: "kajabi",
      name: "Kajabi",
      icon: <Globe size={24} />,
      color: "bg-blue-600",
      connected: connectedPlatforms.kajabi,
      description: "Create a professional knowledge product on Kajabi's platform"
    },
    {
      id: "teachable",
      name: "Teachable",
      icon: <BookOpen size={24} />,
      color: "bg-green-600",
      connected: connectedPlatforms.teachable,
      description: "Publish your course on Teachable's easy-to-use platform"
    }
  ];

  const toggleModule = (moduleIndex: number) => {
    setExpandedModules(prev => ({
      ...prev,
      [moduleIndex]: !prev[moduleIndex]
    }));
  };

  // New handler for platform selection through the PublishPlatformSelector
  const handlePlatformSelection = (platformId: number, selected: boolean) => {
    if (selected) {
      setSelectedPlatformIds(prev => [...prev, platformId]);
    } else {
      setSelectedPlatformIds(prev => prev.filter(id => id !== platformId));
    }
  };
  
  // Connect to a platform for integration
  const connectToPlatform = async (platformId: string) => {
    // Check if already connecting
    if (connectingPlatform) {
      return;
    }

    setConnectingPlatform(platformId);
    
    try {
      const response = await apiRequest("POST", "/api/integrations/connect", {
        platform: platformId
      });
      
      if (!response.ok) {
        throw new Error("Failed to connect to platform");
      }
      
      const data = await response.json();
      
      // If the platform requires authorization via redirect
      if (data.authUrl) {
        // Open the auth URL in a new window
        window.open(data.authUrl, "_blank", "width=600,height=700");
        
        // Show a message to the user about checking the other window
        toast({
          title: `Connecting to ${platformId.charAt(0).toUpperCase() + platformId.slice(1)}`,
          description: "Please complete the authorization process in the new window."
        });
        
        // Poll for connection status every 3 seconds
        const checkStatusInterval = setInterval(async () => {
          try {
            const statusResponse = await apiRequest("GET", `/api/integrations/${platformId}/status`);
            
            if (statusResponse.ok) {
              const statusData = await statusResponse.json();
              
              if (statusData.connected) {
                // Clear the interval and update connection status
                clearInterval(checkStatusInterval);
                setConnectedPlatforms(prev => ({ ...prev, [platformId]: true }));
                setConnectingPlatform(null);
                
                toast({
                  title: "Connection successful",
                  description: `Successfully connected to ${platformId.charAt(0).toUpperCase() + platformId.slice(1)}`
                });
              }
            }
          } catch (error) {
            console.error("Error checking connection status", error);
          }
        }, 3000);
        
        // Stop checking after 2 minutes
        setTimeout(() => {
          clearInterval(checkStatusInterval);
          
          // If still not connected, show an error
          if (!connectedPlatforms[platformId]) {
            setConnectingPlatform(null);
            
            toast({
              title: "Connection timeout",
              description: "The connection process took too long. Please try again.",
              variant: "destructive"
            });
          }
        }, 120000);
      } else {
        // Direct connection successful
        setConnectedPlatforms(prev => ({ ...prev, [platformId]: true }));
        setConnectingPlatform(null);
        
        toast({
          title: "Connection successful",
          description: `Successfully connected to ${platformId.charAt(0).toUpperCase() + platformId.slice(1)}`
        });
      }
    } catch (error) {
      console.error("Error connecting to platform", error);
      setConnectingPlatform(null);
      
      toast({
        title: "Connection failed",
        description: error instanceof Error ? error.message : "An unknown error occurred",
        variant: "destructive"
      });
    }
  };
  
  // Disconnect from a platform
  const disconnectPlatform = async (platformId: string) => {
    // Check if already connecting or not connected
    if (connectingPlatform || !connectedPlatforms[platformId]) {
      return;
    }

    setConnectingPlatform(platformId);
    
    try {
      const response = await apiRequest("DELETE", `/api/integrations/${platformId}`, {});
      
      if (!response.ok) {
        throw new Error("Failed to disconnect from platform");
      }
      
      // Update connection status
      setConnectedPlatforms(prev => ({ ...prev, [platformId]: false }));
      setConnectingPlatform(null);
      
      toast({
        title: "Disconnected successfully",
        description: `Your account has been disconnected from ${platformId.charAt(0).toUpperCase() + platformId.slice(1)}`
      });
    } catch (error) {
      console.error("Error disconnecting from platform", error);
      setConnectingPlatform(null);
      
      toast({
        title: "Disconnection failed",
        description: error instanceof Error ? error.message : "An unknown error occurred",
        variant: "destructive"
      });
    }
  };
  
  // Generate the unique key for a lesson's video generation
  const getVideoKey = (moduleIndex: number, lessonIndex: number) => `${moduleIndex}-${lessonIndex}`;
  
  // Get media IDs from lesson elements for a selected lesson
  const getMediaIdsFromLessonElements = (moduleIndex: number, lessonIndex: number): number[] => {
    const elements = lessonElements[moduleIndex]?.[lessonIndex] || [];
    const mediaIds: number[] = [];
    
    elements.forEach(element => {
      if (element.mediaId && !mediaIds.includes(element.mediaId)) {
        mediaIds.push(element.mediaId);
      }
    });
    
    return mediaIds;
  };
  
  // Check if audio has already been generated for this lesson in the MediaCreation step
  const hasGeneratedAudio = (moduleIndex: number, lessonIndex: number): boolean => {
    const key = `module-${moduleIndex}-lesson-${lessonIndex}`;
    
    // Check if we have generated audio for this lesson
    if (generatedAudio && generatedAudio[key] && generatedAudio[key].length > 0) {
      // Found audio files for this lesson
      return true;
    }
    
    // Check mediaLibrary for audio files with specific naming pattern
    const module = courseStructure.modules[moduleIndex];
    const lesson = module?.lessons[lessonIndex];
    
    if (!module || !lesson) return false;
    
    // Look for audio files in the media library that match the lesson
    const audioFiles = mediaLibrary.filter(media => 
      media.type === 'audio' && 
      media.name.includes(`${module.title}-${lesson.title}`)
    );
    
    return audioFiles.length > 0;
  };
  
  // Start video generation for a lesson
  const startVideoGeneration = async (moduleIndex: number, lessonIndex: number) => {
    const videoKey = getVideoKey(moduleIndex, lessonIndex);
    const script = courseScripts[`module-${moduleIndex}`]?.[`lesson-${lessonIndex}`];
    const lesson = courseStructure.modules[moduleIndex]?.lessons[lessonIndex];
    
    if (!script || !lesson) {
      toast({
        title: "Cannot generate video",
        description: "This lesson doesn't have a script.",
        variant: "destructive"
      });
      return;
    }
    
    // Show a playful loading screen with working expression
    showTaskLoading('processing-video', {
      text: 'Preparing Video Generation',
      subText: `Setting up video generation for "${lesson.title}"`,
      expression: 'working',
      estimatedTime: 2500,
    });
    
    // Update video generation status to generating
    setVideoGenerations(prev => ({
      ...prev,
      [videoKey]: {
        moduleIndex,
        lessonIndex,
        status: 'generating',
        progress: 0,
      }
    }));
    
    try {
      // First check if we have media IDs from user selection, otherwise use media IDs from lesson elements
      let mediaIds = selectedMediaIds.length > 0 
        ? selectedMediaIds 
        : getMediaIdsFromLessonElements(moduleIndex, lessonIndex);
      
      // Show appropriate toast message based on where the media came from
      if (mediaIds.length > 0) {
        if (selectedMediaIds.length > 0) {
          toast({
            title: "Using selected media",
            description: `${mediaIds.length} media item${mediaIds.length !== 1 ? 's' : ''} will be used in the video.`
          });
        } else {
          toast({
            title: "Using media from lesson elements",
            description: `${mediaIds.length} media item${mediaIds.length !== 1 ? 's' : ''} from the course assembly will be used in the video.`
          });
        }
      } else {
        toast({
          title: "No media selected",
          description: "The video will be generated with default visuals. You can add media to enhance the video."
        });
      }
      
      // Check if we already have generated audio for this lesson
      const key = `module-${moduleIndex}-lesson-${lessonIndex}`;
      let preGeneratedAudioId: number | undefined;
      
      // If we have pre-generated audio, use it instead of generating a new one
      if (hasGeneratedAudio(moduleIndex, lessonIndex)) {
        // Check generatedAudio first
        if (generatedAudio[key] && generatedAudio[key].length > 0) {
          preGeneratedAudioId = generatedAudio[key][0].id;
          toast({
            title: "Using pre-generated audio",
            description: "Using voice narration created in the Media Creation step."
          });
        } else {
          // Otherwise look for it in the media library
          const module = courseStructure.modules[moduleIndex];
          const lesson = module?.lessons[lessonIndex];
          
          if (module && lesson) {
            const audioFiles = mediaLibrary.filter(media => 
              media.type === 'audio' && 
              media.name.includes(`${module.title}-${lesson.title}`)
            );
            
            if (audioFiles.length > 0) {
              preGeneratedAudioId = audioFiles[0].id;
              toast({
                title: "Using existing audio",
                description: "Using voice narration found in your Media Library."
              });
            }
          }
        }
      }
      
      const response = await apiRequest("POST", "/api/video-lessons/generate", {
        lessonTitle: lesson.title,
        script: script,
        voiceId: preGeneratedAudioId ? undefined : selectedVoice,
        mediaIds: mediaIds.length > 0 ? mediaIds : undefined,
        preGeneratedAudioId: preGeneratedAudioId,
        subtitles: true
      });
      
      if (!response.ok) {
        throw new Error("Failed to start video generation");
      }
      
      const data = await response.json();
      
      // Update status with job ID
      setVideoGenerations(prev => ({
        ...prev,
        [videoKey]: {
          ...prev[videoKey],
          jobId: data.id,
          progress: 10,
        }
      }));
      
      // Start polling for status
      startPolling(videoKey, data.id);
      
      toast({
        title: "Video generation started",
        description: `Your video for "${lesson.title}" is being generated. This may take a few minutes.`
      });
    } catch (error) {
      console.error("Error starting video generation:", error);
      
      setVideoGenerations(prev => ({
        ...prev,
        [videoKey]: {
          ...prev[videoKey],
          status: 'failed',
          error: error instanceof Error ? error.message : "Unknown error occurred"
        }
      }));
      
      toast({
        title: "Failed to start video generation",
        description: error instanceof Error ? error.message : "An unknown error occurred",
        variant: "destructive"
      });
    }
  };
  
  // Poll for video generation status
  const startPolling = (videoKey: string, jobId: string) => {
    // Clear existing polling interval for this video
    if (pollingIntervals[videoKey]) {
      window.clearInterval(pollingIntervals[videoKey]);
    }
    
    // Poll every 5 seconds
    const interval = window.setInterval(async () => {
      try {
        const response = await apiRequest("GET", `/api/video-lessons/status/${jobId}`);
        
        if (!response.ok) {
          throw new Error("Failed to check video status");
        }
        
        const data = await response.json();
        
        // Update video generation status
        setVideoGenerations(prev => ({
          ...prev,
          [videoKey]: {
            ...prev[videoKey],
            status: data.status === 'completed' ? 'completed' : 
                    data.status === 'failed' ? 'failed' : 'generating',
            progress: data.progress || prev[videoKey].progress,
            mediaId: data.mediaId,
            videoUrl: data.videoUrl,
            error: data.error
          }
        }));
        
        // Stop polling if complete or failed
        if (data.status === 'completed' || data.status === 'failed') {
          window.clearInterval(pollingIntervals[videoKey]);
          setPollingIntervals(prev => {
            const newPolling = { ...prev };
            delete newPolling[videoKey];
            return newPolling;
          });
          
          if (data.status === 'completed') {
            // Show a happy mascot message for success
            showTaskLoading('processing-video', {
              text: 'Video Created Successfully!',
              subText: 'Your video lesson is ready to view and download',
              expression: 'happy',
              estimatedTime: 2000,
            });
            
            toast({
              title: "Video generated successfully",
              description: "Your video lesson is ready to view and download."
            });
          } else {
            // Show a surprised mascot message for failure
            showTaskLoading('processing-video', {
              text: 'Video Generation Issue',
              subText: data.error || "An unexpected error occurred during video generation",
              expression: 'surprised',
              estimatedTime: 2000,
            });
            
            toast({
              title: "Video generation failed",
              description: data.error || "An unexpected error occurred",
              variant: "destructive"
            });
          }
        }
      } catch (error) {
        console.error("Error polling for video status:", error);
        
        // Don't stop polling on temporary errors
        if (error instanceof Error && error.message.includes("Failed to check video status")) {
          console.warn("Temporary error checking video status, will retry...");
          return;
        }
        
        // Stop polling on persistent errors
        window.clearInterval(pollingIntervals[videoKey]);
        setPollingIntervals(prev => {
          const newPolling = { ...prev };
          delete newPolling[videoKey];
          return newPolling;
        });
        
        setVideoGenerations(prev => ({
          ...prev,
          [videoKey]: {
            ...prev[videoKey],
            status: 'failed',
            error: error instanceof Error ? error.message : "Unknown error during status check"
          }
        }));
      }
    }, 5000);
    
    // Store the interval ID
    setPollingIntervals(prev => ({
      ...prev,
      [videoKey]: interval
    }));
  };
  
  // Clear all polling intervals when component unmounts
  React.useEffect(() => {
    return () => {
      Object.values(pollingIntervals).forEach(interval => {
        window.clearInterval(interval);
      });
    };
  }, [pollingIntervals]);

  const handlePublish = async () => {
    // Check if any platform is selected
    const hasSelectedPlatform = selectedPlatformIds.length > 0;
    
    if (!hasSelectedPlatform) {
      toast({
        title: "No publishing platform selected",
        description: "Please select at least one platform to publish your course",
        variant: "destructive"
      });
      return;
    }

    // Set publishing status to pending
    setPublishStatus({
      isPending: true,
      isComplete: false,
      platform: null,
      error: null
    });

    // Show playful loading screen with mascot
    showTaskLoading('saving-course', {
      text: isEditing ? 'Updating Your Course' : 'Publishing Your Course',
      subText: `${courseDetails.title} is being ${isEditing ? 'updated' : 'published'}. This may take a moment...`,
      expression: 'excited',
      estimatedTime: 5000,
    });

    try {
      // Use the selected platform IDs directly for API request
      const platformsToPublish = selectedPlatformIds;

      // Prepare payload for API request
      const courseData = {
        title: courseDetails.title,
        description: courseDetails.description,
        category: courseDetails.category,
        targetAudience: courseDetails.targetAudience || "General audience",
        structure: courseStructure,
        scripts: courseScripts,
        platforms: platformsToPublish,
        exportSettings: exportSettings || { format: "video", quality: "hd" },
        quiz: quizData // Include the quiz data if it exists
      };

      let response;
      
      // If in edit mode and we have a courseId, use PATCH to update
      if (isEditing && courseId) {
        response = await apiRequest("PATCH", `/api/courses/${courseId}`, courseData);
        
        if (!response.ok) {
          throw new Error("Failed to update course");
        }
        
        // Hide loading screen
        hideLoading();
        
        toast({
          title: "Course updated successfully",
          description: `Your course has been updated and is ready to publish`,
        });
      } else {
        // Otherwise, create a new course
        response = await apiRequest("POST", "/api/courses", courseData);
        
        if (!response.ok) {
          throw new Error("Failed to create course");
        }
        
        // Hide loading screen
        hideLoading();
        
        toast({
          title: "Course created successfully",
          description: `Your course has been created and is ready to publish to ${platformsToPublish.join(", ")}`,
        });
      }

      // Update publish status to complete
      setPublishStatus({
        isPending: false,
        isComplete: true,
        platform: platformsToPublish.join(", "),
        error: null
      });

      // Wait 2 seconds before calling onFinish to allow the user to see the success state
      setTimeout(() => {
        onFinish();
      }, 2000);
    } catch (error) {
      console.error("Error publishing course:", error);
      
      // Hide loading screen in case of error
      hideLoading();
      
      setPublishStatus({
        isPending: false,
        isComplete: false,
        platform: null,
        error: error instanceof Error ? error.message : "Unknown error occurred"
      });

      // Show a playful loading screen with a surprised expression briefly
      showTaskLoading('analyzing-content', {
        text: 'Something Went Wrong',
        subText: error instanceof Error ? error.message : "An unknown error occurred",
        expression: 'surprised',
        estimatedTime: 1500,
      });

      toast({
        title: isEditing ? "Failed to update course" : "Failed to create course",
        description: error instanceof Error ? error.message : "An unknown error occurred",
        variant: "destructive"
      });
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-xl font-semibold">Preview & Publish</h3>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={onPrevious}>Back</Button>
          <Button 
            variant="outline" 
            className="border-indigo-300 bg-indigo-50 text-indigo-700 hover:bg-indigo-100 hover:text-indigo-800"
            onClick={() => setActiveTab("review")}
          >
            <Eye className="h-4 w-4 mr-2" />
            Preview Course
          </Button>
          <Button
            onClick={handlePublish}
            disabled={publishStatus.isPending}
            className="bg-green-600 hover:bg-green-700 text-white"
          >
            {publishStatus.isPending ? (
              <>
                <span className="animate-spin mr-2">&#10227;</span>
                {isEditing ? "Updating..." : "Publishing..."}
              </>
            ) : (
              <>{isEditing ? "Update Course" : "Publish Course"}</>
            )}
          </Button>
        </div>
      </div>

      {/* Status alerts */}
      {publishStatus.isComplete && (
        <Alert className="bg-green-50 border-green-200">
          <CheckCircle2 className="h-5 w-5 text-green-600" />
          <AlertTitle className="text-green-800">
            {isEditing ? "Course updated successfully!" : "Course published successfully!"}
          </AlertTitle>
          <AlertDescription className="text-green-700">
            {isEditing 
              ? "Your course has been updated and can be published to your selected platforms."
              : `Your course has been published to ${publishStatus.platform}.`}
          </AlertDescription>
        </Alert>
      )}

      {publishStatus.error && (
        <Alert variant="destructive">
          <AlertCircle className="h-5 w-5" />
          <AlertTitle>{isEditing ? "Update failed" : "Publishing failed"}</AlertTitle>
          <AlertDescription>{publishStatus.error}</AlertDescription>
        </Alert>
      )}

      <Tabs defaultValue={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="mb-4 grid w-full grid-cols-5">
          <TabsTrigger value="review">Course Review</TabsTrigger>
          <TabsTrigger value="videos">Video Lessons</TabsTrigger>
          <TabsTrigger value="quiz">Course Quiz</TabsTrigger>
          <TabsTrigger value="collaboration">Collaboration</TabsTrigger>
          <TabsTrigger value="platforms">Publishing</TabsTrigger>
        </TabsList>

        <TabsContent value="review" className="space-y-6">
          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-center space-x-2">
                <FileText className="h-5 w-5 text-primary" />
                <CardTitle className="text-lg">Course Overview</CardTitle>
              </div>
              <CardDescription>
                Review your course details before publishing
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-1">
                <h3 className="text-lg font-semibold">{courseDetails.title}</h3>
                <p className="text-sm text-slate-600">{courseDetails.description}</p>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-1">
                  <h4 className="text-sm font-medium">Category</h4>
                  <p className="text-sm">{courseDetails.category}</p>
                </div>
                <div className="space-y-1">
                  <h4 className="text-sm font-medium">Target Audience</h4>
                  <p className="text-sm">{courseDetails.targetAudience || "General audience"}</p>
                </div>
              </div>

              <Separator />

              <div className="space-y-3">
                <h4 className="text-sm font-medium">Course Structure</h4>
                <Accordion type="multiple" className="w-full">
                  {courseStructure.modules.map((module, moduleIndex) => (
                    <AccordionItem key={moduleIndex} value={`module-${moduleIndex}`}>
                      <AccordionTrigger>
                        <div className="flex items-center space-x-2">
                          <span className="font-medium">{module.title}</span>
                          <Badge variant="outline" className="ml-2 text-xs">
                            {module.lessons.length} Lessons
                          </Badge>
                        </div>
                      </AccordionTrigger>
                      <AccordionContent>
                        <div className="space-y-2 pl-4">
                          {module.lessons.map((lesson, lessonIndex) => (
                            <div key={lessonIndex} className="flex items-center py-1">
                              <ChevronRight className="h-3 w-3 mr-2 text-slate-400" />
                              <span className="text-sm">{lesson.title}</span>
                            </div>
                          ))}
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  ))}
                </Accordion>
              </div>
            </CardContent>
          </Card>
          
          {/* Add PDF Download Card */}
          {courseId && (
            <CourseDownloadPdf courseId={courseId} courseTitle={courseDetails.title} />
          )}
        </TabsContent>

        <TabsContent value="videos" className="space-y-6">
          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-center space-x-2">
                <Video className="h-5 w-5 text-primary" />
                <CardTitle className="text-lg">Automated Video Lessons</CardTitle>
              </div>
              <CardDescription>
                Create professional video lessons by converting scripts to narrated videos
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <div className="flex justify-between items-center mb-2">
                    <Label htmlFor="lesson-selector" className="block text-sm font-medium">Select Lesson</Label>
                    <div className="flex items-center">
                      <span className="text-xs text-gray-500 mr-1">🖼️ = Has media</span>
                      <AlertCircle className="h-4 w-4 text-blue-400" />
                    </div>
                  </div>
                  <div className="space-y-4">
                    <Select 
                      onValueChange={(value) => {
                        const [moduleIndex, lessonIndex] = value.split('-').map(Number);
                        setSelectedModule(moduleIndex);
                        setSelectedLesson(lessonIndex);
                        
                        // Get media IDs from lesson elements and set them as selected media
                        const mediaIds = getMediaIdsFromLessonElements(moduleIndex, lessonIndex);
                        if (mediaIds.length > 0) {
                          setSelectedMediaIds(mediaIds);
                          
                          // Show a toast to notify that media was auto-selected
                          toast({
                            title: `Media auto-selected`,
                            description: `${mediaIds.length} media item${mediaIds.length !== 1 ? 's' : ''} from this lesson will be used in the video.`,
                          });
                        }
                      }}
                    >
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="Choose a lesson" />
                      </SelectTrigger>
                      <SelectContent>
                        {courseStructure.modules.map((module, moduleIndex) => {
                          return (
                            <div key={moduleIndex}>
                              <div className="px-2 py-1.5 text-sm font-semibold text-muted-foreground">
                                {module.title}
                              </div>
                              {module.lessons.map((lesson, lessonIndex) => {
                                const hasScript = !!courseScripts[`module-${moduleIndex}`]?.[`lesson-${lessonIndex}`];
                                const hasMedia = getMediaIdsFromLessonElements(moduleIndex, lessonIndex).length > 0;
                                
                                return (
                                  <SelectItem 
                                    key={`${moduleIndex}-${lessonIndex}`} 
                                    value={`${moduleIndex}-${lessonIndex}`}
                                    disabled={!hasScript}
                                  >
                                    {lesson.title} {!hasScript && "(No script)"} {hasMedia && "🖼️"}
                                  </SelectItem>
                                );
                              })}
                            </div>
                          );
                        })}
                      </SelectContent>
                    </Select>

                    <div className="space-y-3">
                      <Label htmlFor="voice-selector" className="text-sm font-medium">Select Voice</Label>
                      {selectedModule !== null && selectedLesson !== null && hasGeneratedAudio(selectedModule, selectedLesson) ? (
                        <div className="flex items-center space-x-2 p-2 bg-green-50 border border-green-200 rounded-md">
                          <CheckCircle2 className="h-4 w-4 text-green-500" />
                          <span className="text-sm text-green-700">Voice narration already generated in Media Creation</span>
                        </div>
                      ) : (
                        <Select 
                          value={selectedVoice}
                          onValueChange={setSelectedVoice}
                        >
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="Choose a voice" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="Adam">Adam (Male, American)</SelectItem>
                            <SelectItem value="Emily">Emily (Female, British)</SelectItem>
                            <SelectItem value="Maria">Maria (Female, Spanish)</SelectItem>
                            <SelectItem value="Hiroshi">Hiroshi (Male, Japanese)</SelectItem>
                            <SelectItem value="Sarah">Sarah (Female, Australian)</SelectItem>
                          </SelectContent>
                        </Select>
                      )}
                    </div>

                    <Button 
                      className="mt-4 w-full" 
                      disabled={selectedModule === null || selectedLesson === null}
                      onClick={() => {
                        if (selectedModule !== null && selectedLesson !== null) {
                          startVideoGeneration(selectedModule, selectedLesson);
                        }
                      }}
                    >
                      <Mic className="h-4 w-4 mr-2" />
                      Generate Video
                    </Button>
                  </div>
                </div>

                <div>
                  <Label className="mb-2 block text-sm font-medium">Select Media</Label>
                  <MediaLibrary 
                    onSelect={(selectedMedia) => {
                      // Extract just the IDs from the selected media items
                      const mediaIds = Array.isArray(selectedMedia) 
                        ? selectedMedia.map(media => typeof media === 'number' ? media : media.id)
                        : [];
                      setSelectedMediaIds(mediaIds);
                    }}
                  />
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <h4 className="text-sm font-medium">Generated Videos</h4>
                <div className="space-y-3">
                  {Object.entries(videoGenerations).map(([key, generation]) => {
                    const module = courseStructure.modules[generation.moduleIndex];
                    const lesson = module?.lessons[generation.lessonIndex];
                    if (!module || !lesson) return null;

                    return (
                      <div key={key} className="border rounded p-3 space-y-2">
                        <div className="flex justify-between items-center">
                          <div>
                            <h5 className="font-medium text-sm">{lesson.title}</h5>
                            <p className="text-xs text-slate-500">{module.title}</p>
                          </div>
                          <div className="flex items-center space-x-2">
                            {generation.status === 'generating' && (
                              <Badge variant="outline" className="bg-blue-50 text-blue-700">
                                Generating
                              </Badge>
                            )}
                            {generation.status === 'completed' && (
                              <Badge variant="outline" className="bg-green-50 text-green-700">
                                Completed
                              </Badge>
                            )}
                            {generation.status === 'failed' && (
                              <Badge variant="outline" className="bg-red-50 text-red-700">
                                Failed
                              </Badge>
                            )}
                          </div>
                        </div>

                        {generation.status === 'generating' && (
                          <div className="space-y-1">
                            <div className="flex items-center">
                              <Loader2 className="h-3 w-3 mr-2 animate-spin text-blue-500" />
                              <span className="text-xs text-blue-600">
                                Generating your video... {generation.progress}%
                              </span>
                            </div>
                            <Progress value={generation.progress} max={100} className="h-1" />
                          </div>
                        )}

                        {generation.status === 'completed' && generation.videoUrl && (
                          <div className="space-y-3">
                            <div className="aspect-video bg-slate-100 rounded overflow-hidden">
                              <video 
                                src={generation.videoUrl} 
                                controls 
                                className="w-full h-full"
                              ></video>
                            </div>
                            <div className="flex justify-end space-x-2">
                              <Button size="sm" variant="outline">
                                <Play className="h-4 w-4 mr-1" /> Preview
                              </Button>
                              <Button size="sm" variant="outline">
                                <Download className="h-4 w-4 mr-1" /> Download
                              </Button>
                            </div>
                          </div>
                        )}

                        {generation.status === 'failed' && (
                          <div className="text-xs text-red-600">
                            {generation.error || "An unexpected error occurred during video generation."}
                          </div>
                        )}
                      </div>
                    );
                  })}

                  {Object.keys(videoGenerations).length === 0 && (
                    <div className="text-center py-6 text-slate-500 border rounded-md border-dashed">
                      <Video className="h-10 w-10 mx-auto mb-2 text-slate-300" />
                      <p>No videos generated yet</p>
                      <p className="text-xs mt-1">
                        Select a lesson and voice to generate your first video.
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="quiz" className="space-y-6">
          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-center space-x-2">
                <CheckCircle2 className="h-5 w-5 text-primary" />
                <CardTitle className="text-lg">Course Quiz</CardTitle>
              </div>
              <CardDescription>
                Create an assessment quiz for your course
              </CardDescription>
            </CardHeader>
            <CardContent>
              <QuizBuilder 
                courseId={courseId || 0}
                courseTitle={courseDetails.title}
                courseDescription={courseDetails.description}
                onSave={setQuizData}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="collaboration" className="space-y-6">
          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-center space-x-2">
                <Users className="h-5 w-5 text-primary" />
                <CardTitle className="text-lg">Team Collaboration</CardTitle>
              </div>
              <CardDescription>
                Invite team members to collaborate on your course
              </CardDescription>
            </CardHeader>
            <CardContent>
              {courseId ? (
                <>
                  <SharedCourseEditingPanel 
                    courseId={courseId} 
                    courseTitle={courseDetails.title} 
                  />
                  
                  {/* Notification component for collaboration invites */}
                  <CollaborationNotification courseId={courseId} />
                </>
              ) : (
                <div className="flex flex-col items-center justify-center py-8 text-center">
                  <Users className="h-12 w-12 text-slate-300 mb-3" />
                  <h4 className="text-base font-medium mb-1">Collaboration available after publishing</h4>
                  <p className="text-sm text-slate-500 mb-4 max-w-md">
                    You'll be able to invite team members to collaborate on this course after it's published.
                  </p>
                  <div className="flex space-x-2">
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => setActiveTab("platforms")}
                    >
                      <Globe className="h-4 w-4 mr-2" />
                      Go to Publishing
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="platforms" className="space-y-6">
          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-center space-x-2">
                <Globe className="h-5 w-5 text-primary" />
                <CardTitle className="text-lg">Publishing Platforms</CardTitle>
              </div>
              <CardDescription>
                Select platforms where you want to publish your course
              </CardDescription>
            </CardHeader>
            <CardContent>
              {/* Using the new PublishPlatformSelector component */}
              <PublishPlatformSelector 
                selectedPlatforms={selectedPlatformIds}
                onSelectPlatform={handlePlatformSelection}
                disabled={publishStatus.isPending}
              />
              
              <div className="mt-6 flex items-center justify-between">
                <div className="flex items-center text-sm text-slate-600 space-x-1">
                  <AlertCircle className="h-4 w-4 text-blue-500" />
                  <span>Not seeing your preferred platform? Visit the <Button variant="link" className="h-auto p-0 text-blue-500" onClick={() => window.open('/platform-connections', '_blank')}>Platform Connections</Button> page to connect more platforms.</span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg">Publishing Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between mb-4">
                <div className="space-y-0.5">
                  <Label htmlFor="option-drafts">Save as Draft</Label>
                  <p className="text-xs text-muted-foreground">Publish as draft first to review before making public</p>
                </div>
                <Switch id="option-drafts" defaultChecked />
              </div>
              
              <Separator />

              <div className="flex items-center justify-between mb-4">
                <div className="space-y-0.5">
                  <Label htmlFor="option-analytics">Enable Analytics</Label>
                  <p className="text-xs text-muted-foreground">Track views, engagement and student progress</p>
                </div>
                <Switch id="option-analytics" defaultChecked />
              </div>
              
              <Separator />

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="option-public">Make Course Public</Label>
                  <p className="text-xs text-muted-foreground">Allow your course to be discoverable in search</p>
                </div>
                <Switch id="option-public" defaultChecked />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="bg-amber-50 border-b border-amber-100">
              <AlertTriangle className="h-5 w-5 text-amber-600 mb-2" />
              <CardTitle className="text-amber-800 text-lg">Important Notes</CardTitle>
            </CardHeader>
            <CardContent className="pt-4">
              <ul className="space-y-2 text-sm">
                <li className="flex items-start">
                  <ArrowRight className="h-4 w-4 mr-2 mt-0.5 text-amber-600" />
                  <span>Publishing to external platforms may require additional verification steps.</span>
                </li>
                <li className="flex items-start">
                  <ArrowRight className="h-4 w-4 mr-2 mt-0.5 text-amber-600" />
                  <span>Make sure you have the necessary rights for all content included in your course.</span>
                </li>
                <li className="flex items-start">
                  <ArrowRight className="h-4 w-4 mr-2 mt-0.5 text-amber-600" />
                  <span>To track analytics across all platforms, ensure your integrations are properly configured.</span>
                </li>
              </ul>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}