import React, { useState, useCallback, useEffect } from 'react';
import { DragDropContext, Droppable, Draggable, DropResult } from '@hello-pangea/dnd';
import { 
  ArrowRight, 
  Plus, 
  Trash2, 
  Edit, 
  Image, 
  Video, 
  FileText, 
  Mic, 
  Maximize, 
  Minimize
} from 'lucide-react';
import { useHints } from '@/components/hints/HintProvider';
import { StoryboardHint } from '@/components/hints/contextual-hints';

import { 
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle
} from "@/components/ui/card";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from "@/components/ui/dialog";

import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { ScrollA<PERSON> } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

// Define types for storyboard scenes
export interface Scene {
  id: string;
  title: string;
  description: string;
  imagePrompt?: string; // For AI-generated visuals
  imageSrc?: string;    // For uploaded visuals
  narration: string;    // Script text
  duration: number;     // In seconds
  type: 'title' | 'content' | 'summary' | 'quiz' | 'transition';
  order: number;
}

interface StoryboardEditorProps {
  initialScenes?: Scene[];
  onSave?: (scenes: Scene[]) => void;
  onGenerateVideo?: (scenes: Scene[]) => void;
}

// Helper function to get color for scene type
const getSceneTypeColor = (type: Scene['type']) => {
  switch (type) {
    case 'title':
      return 'var(--primary)';
    case 'content':
      return 'var(--blue-500, #3b82f6)';
    case 'summary':
      return 'var(--green-500, #22c55e)';
    case 'quiz':
      return 'var(--purple-500, #a855f7)';
    case 'transition':
      return 'var(--orange-500, #f97316)';
    default:
      return 'var(--muted-foreground)';
  }
};

// Helper function to get scene type icon
const getSceneIcon = (type: Scene['type']) => {
  switch (type) {
    case 'title':
      return <FileText className="h-4 w-4" />;
    case 'content':
      return <Video className="h-4 w-4" />;
    case 'summary':
      return <FileText className="h-4 w-4" />;
    case 'quiz':
      return <FileText className="h-4 w-4" />;
    case 'transition':
      return <ArrowRight className="h-4 w-4" />;
    default:
      return <FileText className="h-4 w-4" />;
  }
};

interface SceneCardProps {
  scene: Scene;
  index: number;
  moveScene: (dragIndex: number, hoverIndex: number) => void;
  deleteScene: (id: string) => void;
  editScene: (scene: Scene) => void;
  isExpanded: boolean;
  toggleExpand: (id: string) => void;
}

const SceneCard: React.FC<SceneCardProps> = ({
  scene,
  index,
  moveScene,
  deleteScene,
  editScene,
  isExpanded,
  toggleExpand
}) => {
  return (
    <Draggable draggableId={scene.id} index={index}>
      {(provided, snapshot) => (
        <Card
          ref={provided.innerRef}
          {...provided.draggableProps}
          {...provided.dragHandleProps}
          className={`mb-4 cursor-move ${snapshot.isDragging ? 'opacity-50' : 'opacity-100'}`}
          style={{
            ...provided.draggableProps.style,
            borderLeft: `4px solid ${getSceneTypeColor(scene.type)}`
          }}
        >
          <CardHeader className="p-3 pb-0">
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <div className="bg-secondary p-1 rounded-md">
                  {getSceneIcon(scene.type)}
                </div>
                <CardTitle className="text-sm font-semibold line-clamp-1">
                  {scene.title || `Scene ${index + 1}`}
                </CardTitle>
              </div>
              <div className="flex items-center gap-1">
                <Button variant="ghost" size="icon" onClick={() => toggleExpand(scene.id)}>
                  {isExpanded ?
                    <Minimize className="h-4 w-4" /> :
                    <Maximize className="h-4 w-4" />
                  }
                </Button>
                <Button variant="ghost" size="icon" onClick={() => editScene(scene)}>
                  <Edit className="h-4 w-4" />
                </Button>
                <Button variant="ghost" size="icon" onClick={() => deleteScene(scene.id)}>
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardHeader>

          {isExpanded && (
            <CardContent className="p-3">
              <div className="space-y-2">
                {(scene.imageSrc || scene.imagePrompt) && (
                  <div className="flex flex-col">
                    <span className="text-xs text-muted-foreground mb-1">Visual</span>
                    <div className="bg-secondary/50 rounded-md h-16 flex items-center justify-center">
                      {scene.imageSrc ? (
                        <img
                          src={scene.imageSrc}
                          alt={scene.title}
                          className="max-h-full object-contain"
                        />
                      ) : (
                        <div className="text-xs text-muted-foreground p-2 text-center">
                          {scene.imagePrompt}
                        </div>
                      )}
                    </div>
                  </div>
                )}

                <div className="flex flex-col">
                  <span className="text-xs text-muted-foreground mb-1">Narration</span>
                  <div className="text-xs border rounded-md p-2 max-h-20 overflow-y-auto">
                    {scene.narration || "No narration added"}
                  </div>
                </div>

                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>Type: {scene.type}</span>
                  <span>Duration: {scene.duration}s</span>
                </div>
              </div>
            </CardContent>
          )}

          {!isExpanded && (
            <CardFooter className="p-3 pt-0">
              <div className="flex justify-between w-full text-xs text-muted-foreground">
                <div className="flex items-center gap-1">
                  <Mic className="h-3 w-3" />
                  <span className="line-clamp-1">
                    {scene.narration ? `"${scene.narration.substring(0, 20)}${scene.narration.length > 20 ? '...' : ''}"` : "No narration"}
                  </span>
                </div>
                <span>{scene.duration}s</span>
              </div>
            </CardFooter>
          )}
        </Card>
      )}
    </Draggable>
  );
};

interface SceneEditorDialogProps {
  scene: Scene | null;
  onSave: (scene: Scene) => void;
  onClose: () => void;
  isOpen: boolean;
}

const SceneEditorDialog: React.FC<SceneEditorDialogProps> = ({ scene, onSave, onClose, isOpen }) => {
  const [editedScene, setEditedScene] = useState<Scene>(scene || {
    id: '',
    title: '',
    description: '',
    narration: '',
    duration: 5,
    type: 'content',
    order: 0
  });
  
  useEffect(() => {
    if (scene) {
      setEditedScene(scene);
    }
  }, [scene]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setEditedScene(prev => ({ ...prev, [name]: value }));
  };

  const handleDurationChange = (value: number[]) => {
    setEditedScene(prev => ({ ...prev, duration: value[0] }));
  };

  const handleSubmit = () => {
    onSave(editedScene);
    onClose();
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = () => {
        setEditedScene(prev => ({ ...prev, imageSrc: reader.result as string }));
      };
      reader.readAsDataURL(file);
    }
  };

  if (!isOpen) return null;

  return (
    <Dialog open={isOpen} onOpenChange={() => onClose()}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>{scene?.id ? 'Edit Scene' : 'Add New Scene'}</DialogTitle>
          <DialogDescription>
            Configure the details for this scene in your storyboard.
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-2">
          <Tabs defaultValue="basic">
            <TabsList className="grid grid-cols-4">
              <TabsTrigger value="basic">Basic</TabsTrigger>
              <TabsTrigger value="visual">Visual</TabsTrigger>
              <TabsTrigger value="narration">Narration</TabsTrigger>
              <TabsTrigger value="advanced">Advanced</TabsTrigger>
            </TabsList>

            <TabsContent value="basic" className="space-y-4 mt-4">
              <div className="grid gap-2">
                <Label htmlFor="title">Title</Label>
                <Input
                  id="title"
                  name="title"
                  value={editedScene.title}
                  onChange={handleChange}
                  placeholder="Scene title"
                />
              </div>

              <div className="grid gap-2">
                <Label htmlFor="type">Scene Type</Label>
                <select
                  id="type"
                  name="type"
                  value={editedScene.type}
                  onChange={handleChange}
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                >
                  <option value="title">Title</option>
                  <option value="content">Content</option>
                  <option value="summary">Summary</option>
                  <option value="quiz">Quiz</option>
                  <option value="transition">Transition</option>
                </select>
              </div>

              <div className="grid gap-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  name="description"
                  value={editedScene.description}
                  onChange={handleChange}
                  placeholder="Describe this scene"
                  rows={2}
                />
              </div>

              <div className="grid gap-2">
                <Label htmlFor="duration">Duration (seconds)</Label>
                <div className="flex items-center gap-4">
                  <Slider
                    id="duration"
                    min={1}
                    max={30}
                    step={1}
                    value={[editedScene.duration]}
                    onValueChange={handleDurationChange}
                    className="flex-1"
                  />
                  <span className="w-12 text-center">{editedScene.duration}s</span>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="visual" className="space-y-4 mt-4">
              <div className="grid gap-4">
                <Label>Visual Content</Label>

                <div
                  className="border-2 border-dashed rounded-md p-4 text-center cursor-pointer hover:bg-muted/50 transition-colors"
                  onClick={() => document.getElementById('file-upload')?.click()}
                >
                  <input
                    id="file-upload"
                    type="file"
                    accept="image/*"
                    className="hidden"
                    onChange={handleFileChange}
                  />
                  {editedScene.imageSrc ? (
                    <div className="flex flex-col items-center">
                      <img
                        src={editedScene.imageSrc}
                        alt="Preview"
                        className="max-h-40 object-contain mb-2"
                      />
                      <p className="text-sm text-muted-foreground">Click to replace</p>
                    </div>
                  ) : (
                    <div className="flex flex-col items-center py-4">
                      <Image className="h-10 w-10 text-muted-foreground mb-2" />
                      <p className="text-sm font-medium">Click to upload an image</p>
                      <p className="text-xs text-muted-foreground mt-1">SVG, PNG, JPG or GIF</p>
                    </div>
                  )}
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="imagePrompt">Image Generation Prompt (AI)</Label>
                  <Textarea
                    id="imagePrompt"
                    name="imagePrompt"
                    value={editedScene.imagePrompt}
                    onChange={handleChange}
                    placeholder="Describe the image you want to generate"
                    rows={3}
                  />
                  <p className="text-xs text-muted-foreground">
                    If you provide both an image and a prompt, the uploaded image will take precedence.
                  </p>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="narration" className="space-y-4 mt-4">
              <div className="grid gap-2">
                <Label htmlFor="narration">Narration Script</Label>
                <Textarea
                  id="narration"
                  name="narration"
                  value={editedScene.narration}
                  onChange={handleChange}
                  placeholder="Enter the narration text for this scene"
                  rows={8}
                />
              </div>

              <div className="flex items-center gap-2 text-sm">
                <Mic className="h-4 w-4" />
                <span>
                  Estimated narration time: {Math.ceil(editedScene.narration.split(' ').length / 3)}s
                </span>
              </div>
            </TabsContent>

            <TabsContent value="advanced" className="space-y-4 mt-4">
              <div className="grid gap-2">
                <Label htmlFor="order">Scene Order</Label>
                <Input
                  id="order"
                  name="order"
                  type="number"
                  value={editedScene.order}
                  onChange={handleChange}
                  min={0}
                />
              </div>

              <div className="grid gap-2">
                <Label>Scene ID</Label>
                <p className="text-sm text-muted-foreground border rounded-md p-2 bg-muted/50">
                  {editedScene.id || 'Will be generated automatically'}
                </p>
              </div>
            </TabsContent>
          </Tabs>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>Cancel</Button>
          <Button onClick={handleSubmit}>Save Scene</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

const StoryboardEditor: React.FC<StoryboardEditorProps> = ({
  initialScenes = [],
  onSave,
  onGenerateVideo
}) => {
  const [scenes, setScenes] = useState<Scene[]>(initialScenes);
  const [expandedScenes, setExpandedScenes] = useState<Set<string>>(new Set());
  const [editingScene, setEditingScene] = useState<Scene | null>(null);
  const [isEditorOpen, setIsEditorOpen] = useState(false);
  const { showHint } = useHints();
  
  useEffect(() => {
    // Show storyboard hint when component mounts
    showHint("storyboard");
  }, [showHint]);

  // Set up initial scenes with any provided
  useEffect(() => {
    if (initialScenes.length > 0) {
      setScenes(initialScenes);
    }
  }, [initialScenes]);

  const toggleExpandScene = (sceneId: string) => {
    setExpandedScenes(prev => {
      const newSet = new Set(prev);
      if (newSet.has(sceneId)) {
        newSet.delete(sceneId);
      } else {
        newSet.add(sceneId);
      }
      return newSet;
    });
  };

  const addNewScene = () => {
    const newId = `scene_${Date.now()}`;
    setEditingScene({
      id: newId,
      title: '',
      description: '',
      narration: '',
      duration: 5,
      type: 'content',
      order: scenes.length
    });
    setIsEditorOpen(true);
  };

  const editScene = (scene: Scene) => {
    setEditingScene({ ...scene });
    setIsEditorOpen(true);
  };

  const deleteScene = (sceneId: string) => {
    setScenes(scenes.filter(s => s.id !== sceneId));
  };

  const saveScene = (scene: Scene) => {
    if (scenes.some(s => s.id === scene.id)) {
      setScenes(scenes.map(s => s.id === scene.id ? scene : s));
    } else {
      setScenes([...scenes, scene]);
    }

    setEditingScene(null);
  };

  const moveScene = useCallback((dragIndex: number, hoverIndex: number) => {
    setScenes(prevScenes => {
      const result = [...prevScenes];
      const [removed] = result.splice(dragIndex, 1);
      result.splice(hoverIndex, 0, removed);

      // Update order property
      return result.map((scene, idx) => ({
        ...scene,
        order: idx
      }));
    });
  }, []);

  const handleSave = () => {
    const sortedScenes = [...scenes].sort((a, b) => a.order - b.order);
    if (onSave) onSave(sortedScenes);
  };

  const handleGenerateVideo = () => {
    const sortedScenes = [...scenes].sort((a, b) => a.order - b.order);
    if (onGenerateVideo) onGenerateVideo(sortedScenes);
  };

  const handleDragEnd = (result: DropResult) => {
    if (!result.destination) {
      return;
    }

    const sourceIndex = result.source.index;
    const destinationIndex = result.destination.index;

    if (sourceIndex === destinationIndex) {
      return;
    }

    moveScene(sourceIndex, destinationIndex);
  };

  const totalDuration = scenes.reduce((total, scene) => total + scene.duration, 0);

  return (
    <DragDropContext onDragEnd={handleDragEnd}>
      <div className="flex flex-col h-full">
        <div className="flex justify-between items-center mb-4">
          <div>
            <h2 className="text-xl font-bold">Storyboard Editor</h2>
            <p className="text-sm text-muted-foreground">
              Create and organize your video scenes
            </p>
          </div>

          <div className="flex gap-2">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="outline" onClick={handleSave}>
                    Save Storyboard
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  Save your storyboard progress
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button onClick={handleGenerateVideo}>
                    Generate Video
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  Create a video based on your storyboard
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>

        <div className="flex gap-6 h-[calc(100%-4rem)]">
          <div className="w-80 flex flex-col">
            <div className="flex justify-between items-center mb-2">
              <h3 className="text-sm font-semibold">Scenes</h3>
              <div className="flex items-center gap-1 text-sm text-muted-foreground">
                <span>{scenes.length} scenes</span>
                <span>·</span>
                <span>{totalDuration}s</span>
              </div>
            </div>

            <Button
              variant="outline"
              onClick={addNewScene}
              className="mb-4 flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              Add Scene
            </Button>

            <Droppable droppableId="scenes">
              {(provided) => (
                <ScrollArea className="flex-1 pr-4">
                  <div 
                    ref={provided.innerRef}
                    {...provided.droppableProps}
                  >
                    {scenes.length === 0 ? (
                      <div className="text-center p-8 border border-dashed rounded-md text-muted-foreground">
                        <p>No scenes yet</p>
                        <p className="text-sm">Add a scene to get started</p>
                      </div>
                    ) : (
                      scenes
                        .sort((a, b) => a.order - b.order)
                        .map((scene, index) => (
                          <SceneCard
                            key={scene.id}
                            scene={scene}
                            index={index}
                            moveScene={moveScene}
                            deleteScene={deleteScene}
                            editScene={editScene}
                            isExpanded={expandedScenes.has(scene.id)}
                            toggleExpand={toggleExpandScene}
                          />
                        ))
                    )}
                    {provided.placeholder}
                  </div>
                </ScrollArea>
              )}
            </Droppable>
          </div>

          <Separator orientation="vertical" />

          <div className="flex-1 bg-muted/30 rounded-lg p-4">
            <div className="mb-4">
              <h3 className="text-sm font-semibold">Preview</h3>
              <p className="text-xs text-muted-foreground">
                This is a visual representation of your storyboard
              </p>
            </div>

            <div className="flex flex-wrap gap-2">
              {scenes.length === 0 ? (
                <div className="w-full text-center p-12 border border-dashed rounded-md text-muted-foreground">
                  <p>Your storyboard preview will appear here</p>
                  <p className="text-sm">Add scenes to visualize your video flow</p>
                </div>
              ) : (
                scenes
                  .sort((a, b) => a.order - b.order)
                  .map((scene, index) => (
                    <React.Fragment key={scene.id}>
                      <div
                        className="w-40 h-24 border rounded-md overflow-hidden relative cursor-pointer hover:ring-2 hover:ring-primary/50 transition-all"
                        onClick={() => editScene(scene)}
                        style={{ borderColor: getSceneTypeColor(scene.type) }}
                      >
                        <div className="absolute inset-0 flex items-center justify-center bg-background/80 backdrop-blur-sm">
                          {scene.imageSrc ? (
                            <img
                              src={scene.imageSrc}
                              alt={scene.title}
                              className="w-full h-full object-cover"
                            />
                          ) : (
                            <div className="bg-secondary w-12 h-12 rounded-full flex items-center justify-center">
                              {getSceneIcon(scene.type)}
                            </div>
                          )}
                        </div>
                        <div className="absolute bottom-0 left-0 right-0 bg-background/80 backdrop-blur-sm p-1 text-xs font-medium truncate">
                          {scene.title || `Scene ${index + 1}`}
                        </div>
                      </div>

                      {index < scenes.length - 1 && (
                        <div className="flex items-center">
                          <ArrowRight className="h-4 w-4 text-muted-foreground" />
                        </div>
                      )}
                    </React.Fragment>
                  ))
              )}
            </div>
          </div>
        </div>
      </div>

      <SceneEditorDialog
        scene={editingScene}
        isOpen={isEditorOpen}
        onSave={saveScene}
        onClose={() => {
          setIsEditorOpen(false);
          setEditingScene(null);
        }}
      />
    </DragDropContext>
  );
};

export default StoryboardEditor;