import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Link } from 'wouter';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { 
  ChevronLeft, 
  BarChart4, 
  Settings2, 
  Upload, 
  GanttChart, 
  MoreHorizontal,
  CheckCircle,
  AlertCircle,
  RefreshCw
} from 'lucide-react';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { SiUdemy } from 'react-icons/si';
import { FaChalkboardTeacher, FaGraduationCap, FaBookOpen } from 'react-icons/fa';

// Interface definitions
interface PublishedCourse {
  id: number;
  courseId: number;
  platformId: string;
  platformCourseId: string;
  courseTitle: string;
  status: 'published' | 'draft' | 'pending' | 'error';
  lastSynced: string;
  analytics: {
    enrollments: number;
    revenue: number;
    rating: number;
  };
  error?: string;
}

interface Platform {
  id: number;
  name: string;
  slug: string;
  description: string;
  status: 'active' | 'pending' | 'error';
}

interface MarketAnalytics {
  totalEnrollments: number;
  totalRevenue: number;
  averageRating: number;
  platforms: {
    platform: string;
    enrollments: number;
    revenue: number;
  }[];
  topCourses: {
    id: number;
    title: string;
    enrollments: number;
    revenue: number;
    platform: string;
  }[];
}

export default function MarketplacesPage() {
  const { toast } = useToast();
  const [publishDialogOpen, setPublishDialogOpen] = useState(false);
  const [selectedCourse, setSelectedCourse] = useState<number | null>(null);
  
  // Fetch platforms data
  const { data: platforms, isLoading: isLoadingPlatforms } = useQuery({
    queryKey: ['/api/platforms/marketplaces'],
    queryFn: async () => {
      try {
        const response = await apiRequest('GET', '/api/platforms/marketplaces');
        return await response.json();
      } catch (error) {
        console.error('Error fetching marketplace platforms:', error);
        // Return example data if API fails
        return [
          { id: 1, name: 'Udemy', slug: 'udemy', description: 'Global marketplace for online learning', status: 'active' },
          { id: 2, name: 'Teachable', slug: 'teachable', description: 'Platform for creating and selling online courses', status: 'pending' },
          { id: 3, name: 'Thinkific', slug: 'thinkific', description: 'All-in-one platform for online courses', status: 'error' }
        ];
      }
    }
  });
  
  // Fetch published courses
  const { data: publishedCourses, isLoading: isLoadingCourses } = useQuery({
    queryKey: ['/api/publishing/courses'],
    queryFn: async () => {
      try {
        const response = await apiRequest('GET', '/api/publishing/courses');
        return await response.json();
      } catch (error) {
        console.error('Error fetching published courses:', error);
        // Return example data if API fails
        return [
          { 
            id: 1, 
            courseId: 101, 
            platformId: 'udemy', 
            platformCourseId: 'ud12345', 
            courseTitle: 'Complete Web Development Bootcamp', 
            status: 'published', 
            lastSynced: '2025-04-20T14:30:00Z',
            analytics: {
              enrollments: 1245,
              revenue: 24900,
              rating: 4.7
            }
          },
          { 
            id: 2, 
            courseId: 102, 
            platformId: 'teachable', 
            platformCourseId: 'tc67890', 
            courseTitle: 'Advanced React Patterns', 
            status: 'published', 
            lastSynced: '2025-04-18T09:15:00Z',
            analytics: {
              enrollments: 879,
              revenue: 31644,
              rating: 4.9
            }
          },
          { 
            id: 3, 
            courseId: 103, 
            platformId: 'udemy', 
            platformCourseId: 'ud54321', 
            courseTitle: 'Python for Data Science', 
            status: 'draft', 
            lastSynced: '2025-04-15T16:45:00Z',
            analytics: {
              enrollments: 0,
              revenue: 0,
              rating: 0
            }
          }
        ];
      }
    }
  });
  
  // Fetch marketplace analytics
  const { data: analytics, isLoading: isLoadingAnalytics } = useQuery({
    queryKey: ['/api/analytics/marketplaces'],
    queryFn: async () => {
      try {
        const response = await apiRequest('GET', '/api/analytics/marketplaces');
        return await response.json();
      } catch (error) {
        console.error('Error fetching marketplace analytics:', error);
        // Return example data if API fails
        return {
          totalEnrollments: 2124,
          totalRevenue: 56544,
          averageRating: 4.8,
          platforms: [
            { platform: 'udemy', enrollments: 1245, revenue: 24900 },
            { platform: 'teachable', enrollments: 879, revenue: 31644 },
          ],
          topCourses: [
            { id: 102, title: 'Advanced React Patterns', enrollments: 879, revenue: 31644, platform: 'teachable' },
            { id: 101, title: 'Complete Web Development Bootcamp', enrollments: 1245, revenue: 24900, platform: 'udemy' },
          ]
        };
      }
    }
  });
  
  const handleSyncCourse = async (courseId: number) => {
    toast({
      title: 'Syncing course',
      description: 'Your course is being synced with the marketplace.',
    });
  };
  
  const handlePublishCourse = (courseId: number) => {
    setSelectedCourse(courseId);
    setPublishDialogOpen(true);
  };
  
  const getPlatformIcon = (slug: string, size = 24) => {
    switch (slug) {
      case 'udemy':
        return <SiUdemy size={size} className="text-[#ea5252]" />;
      case 'teachable':
        return <FaChalkboardTeacher size={size} className="text-[#29b2fe]" />;
      case 'thinkific':
        return <FaGraduationCap size={size} className="text-[#1caee6]" />;
      case 'kajabi':
        return <FaBookOpen size={size} className="text-[#7719aa]" />;
      default:
        return null;
    }
  };
  
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };
  
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'published':
        return <Badge variant="default" className="bg-green-500">Published</Badge>;
      case 'draft':
        return <Badge variant="outline">Draft</Badge>;
      case 'pending':
        return <Badge variant="secondary">Pending</Badge>;
      case 'error':
        return <Badge variant="destructive">Error</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };
  
  return (
    <div className="container mx-auto p-6">
      <div className="mb-8">
        <Link href="/platform-integrations">
          <Button variant="ghost" className="pl-0 mb-4">
            <ChevronLeft className="mr-2 h-4 w-4" />
            Back to All Platforms
          </Button>
        </Link>
        
        <h1 className="text-3xl font-bold mb-2 gradient-heading">Course Marketplaces</h1>
        <p className="text-slate-600">Publish and manage your courses on global online learning marketplaces</p>
      </div>
      
      {/* Analytics Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-slate-500">Total Enrollments</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">
              {analytics?.totalEnrollments.toLocaleString() || '0'}
            </div>
            <p className="text-xs text-slate-500 mt-1">Across all platforms</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-slate-500">Total Revenue</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-green-600">
              {analytics ? formatCurrency(analytics.totalRevenue) : '$0'}
            </div>
            <p className="text-xs text-slate-500 mt-1">Across all platforms</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-slate-500">Average Rating</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">
              {analytics?.averageRating ? analytics.averageRating.toFixed(1) : '0.0'}
            </div>
            <div className="flex mt-1">
              {[1, 2, 3, 4, 5].map((star) => (
                <svg
                  key={star}
                  className={`h-4 w-4 ${
                    star <= Math.round(analytics?.averageRating || 0)
                      ? 'text-yellow-400'
                      : 'text-gray-300'
                  }`}
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                </svg>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
      
      {/* Connected Platforms */}
      <h2 className="text-xl font-semibold mb-4">Connected Marketplaces</h2>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        {platforms?.map((platform: Platform) => (
          <Card key={platform.id}>
            <CardHeader>
              <div className="flex items-center">
                {getPlatformIcon(platform.slug)}
                <CardTitle className="ml-2">{platform.name}</CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-slate-600">{platform.description}</p>
              <div className="mt-4 flex items-center">
                <div className={`w-3 h-3 rounded-full mr-2 ${
                  platform.status === 'active' ? 'bg-green-500' : 
                  platform.status === 'pending' ? 'bg-amber-500' : 'bg-red-500'
                }`}></div>
                <span className="text-sm font-medium">
                  {platform.status === 'active' ? 'Active' : 
                   platform.status === 'pending' ? 'Pending' : 'Error'}
                </span>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline" size="sm">
                <Settings2 className="h-4 w-4 mr-2" />
                Settings
              </Button>
              <Button variant="outline" size="sm">
                <BarChart4 className="h-4 w-4 mr-2" />
                Analytics
              </Button>
            </CardFooter>
          </Card>
        ))}
        
        <Card className="border-dashed border-2 flex flex-col items-center justify-center p-6">
          <div className="p-3 rounded-full bg-primary/10 mb-4">
            <Plus className="h-6 w-6 text-primary" />
          </div>
          <h3 className="text-lg font-medium mb-2">Add New Marketplace</h3>
          <p className="text-sm text-slate-500 text-center mb-4">
            Connect to additional course marketplaces to expand your reach
          </p>
          <Link href="/platform-integrations">
            <Button>Connect Platform</Button>
          </Link>
        </Card>
      </div>
      
      {/* Published Courses */}
      <div className="mb-8">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">Published Courses</h2>
          <Button>
            <Upload className="h-4 w-4 mr-2" />
            Publish New Course
          </Button>
        </div>
        
        <Card>
          <CardContent className="p-0">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Course</TableHead>
                  <TableHead>Platform</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Enrollments</TableHead>
                  <TableHead>Revenue</TableHead>
                  <TableHead>Rating</TableHead>
                  <TableHead>Last Synced</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {publishedCourses?.map((course: PublishedCourse) => (
                  <TableRow key={course.id}>
                    <TableCell className="font-medium">{course.courseTitle}</TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        {getPlatformIcon(course.platformId, 18)}
                        <span className="ml-2">
                          {platforms?.find((p: Platform) => p.slug === course.platformId)?.name || course.platformId}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>{getStatusBadge(course.status)}</TableCell>
                    <TableCell>{course.analytics.enrollments.toLocaleString()}</TableCell>
                    <TableCell>{formatCurrency(course.analytics.revenue)}</TableCell>
                    <TableCell>
                      {course.analytics.rating > 0 ? (
                        <div className="flex items-center">
                          <span className="mr-1">{course.analytics.rating.toFixed(1)}</span>
                          <svg className="h-4 w-4 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                          </svg>
                        </div>
                      ) : (
                        <span className="text-slate-400">—</span>
                      )}
                    </TableCell>
                    <TableCell>{new Date(course.lastSynced).toLocaleDateString()}</TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <DropdownMenuItem onClick={() => handleSyncCourse(course.id)}>
                            <RefreshCw className="h-4 w-4 mr-2" />
                            Sync Now
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <BarChart4 className="h-4 w-4 mr-2" />
                            View Analytics
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem>
                            <Settings2 className="h-4 w-4 mr-2" />
                            Manage Settings
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>
      
      {/* Publish Dialog */}
      <Dialog open={publishDialogOpen} onOpenChange={setPublishDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Publish Course to Marketplace</DialogTitle>
            <DialogDescription>
              Select the marketplace where you'd like to publish your course.
            </DialogDescription>
          </DialogHeader>
          
          <div className="grid gap-4 py-4">
            <div className="space-y-4">
              {platforms?.filter((p: Platform) => p.status === 'active').map((platform: Platform) => (
                <Card key={platform.id} className="cursor-pointer hover:border-primary transition-colors">
                  <CardContent className="p-4 flex items-center">
                    {getPlatformIcon(platform.slug, 24)}
                    <div className="ml-3">
                      <h3 className="font-medium">{platform.name}</h3>
                      <p className="text-xs text-slate-500">{platform.description}</p>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setPublishDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={() => setPublishDialogOpen(false)}>
              Continue
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

const Plus = ({ className }: { className?: string }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    className={className}
  >
    <path d="M12 5v14M5 12h14" />
  </svg>
);