import React from "react";
import { cn } from "@/lib/utils";
import { Link, useLocation } from "wouter";
import { VideoIcon, BookOpen, HomeIcon, Users, Settings, MessageSquare } from "lucide-react";

export function MainNav({
  className,
  ...props
}: React.HTMLAttributes<HTMLElement>) {
  const [location] = useLocation();

  const routes = [
    {
      href: "/",
      label: "Home",
      icon: <HomeIcon className="mr-2 h-4 w-4" />,
      active: location === "/",
    },
    {
      href: "/courses",
      label: "Courses",
      icon: <BookOpen className="mr-2 h-4 w-4" />,
      active: location === "/courses" || location.startsWith("/courses/"),
    },
    {
      href: "/animated-video-generator",
      label: "Video Generator",
      icon: <VideoIcon className="mr-2 h-4 w-4" />,
      active: location === "/animated-video-generator",
    },
    {
      href: "/chatbot",
      label: "AI Assistant",
      icon: <MessageSquare className="mr-2 h-4 w-4" />,
      active: location === "/chatbot",
    },
    {
      href: "/teams",
      label: "Teams",
      icon: <Users className="mr-2 h-4 w-4" />,
      active: location === "/teams" || location.startsWith("/teams/"),
    },
    {
      href: "/settings",
      label: "Settings",
      icon: <Settings className="mr-2 h-4 w-4" />,
      active: location === "/settings",
    },
  ];

  return (
    <nav
      className={cn("flex items-center space-x-4 lg:space-x-6", className)}
      {...props}
    >
      {routes.map((route) => (
        <Link
          key={route.href}
          href={route.href}
          className={cn(
            "flex items-center text-sm font-medium transition-colors hover:text-primary",
            route.active
              ? "text-primary"
              : "text-muted-foreground"
          )}
        >
          {route.icon}
          {route.label}
        </Link>
      ))}
    </nav>
  );
}