"""
Production Modal A100 GPU Application - Fixed Authentication and Billing
Comprehensive A100 80G GPU setup with proper authentication, billing management, and fallback systems
"""

import modal
import os
import base64
import json
import time
from typing import Dict, List, Any, Optional
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create Modal app with proper authentication handling
try:
    app = modal.App("courseai-a100-production")
    logger.info("Modal app created successfully")
except Exception as e:
    logger.error(f"Failed to create Modal app: {e}")
    raise

# Define A100 80G GPU image with comprehensive dependencies
gpu_image = modal.Image.debian_slim().pip_install([
    # Core ML libraries
    "torch>=2.0.0",
    "torchvision>=0.15.0",
    "torchaudio>=2.0.0",
    
    # NLP and TTS libraries
    "transformers>=4.30.0",
    "accelerate>=0.20.0",
    "datasets>=2.12.0",
    "tokenizers>=0.13.0",
    
    # Audio processing
    "librosa>=0.10.0",
    "soundfile>=0.12.0",
    "scipy>=1.10.0",
    "numpy>=1.24.0",
    
    # TTS specific
    "TTS>=0.22.0",
    "bark-ai>=1.0.0",
    "espeak-ng",
    
    # Utilities
    "requests>=2.30.0",
    "Pillow>=9.5.0",
    "matplotlib>=3.7.0",
    "tqdm>=4.65.0",
    
    # Video processing for SadTalker
    "opencv-python>=4.7.0",
    "imageio>=2.28.0",
    "imageio-ffmpeg>=0.4.8",
    "face-alignment>=1.4.0",
    "scikit-image>=0.20.0",
]).apt_install([
    "ffmpeg",
    "libsndfile1",
    "espeak-ng",
    "git",
    "wget",
    "curl"
]).run_commands([
    "pip install --no-deps bark-ai",
    "python -c 'import nltk; nltk.download(\"punkt\")'",
])

# GPU configuration with billing optimization
A100_GPU_CONFIG = modal.gpu.A100(count=1, memory=80)

# Cost management settings
MAX_CONCURRENT_REQUESTS = 3
IDLE_TIMEOUT = 300  # 5 minutes
KEEP_WARM = 0  # Don't keep warm to save costs

@app.function(
    image=gpu_image,
    gpu=A100_GPU_CONFIG,
    timeout=600,
    concurrency_limit=MAX_CONCURRENT_REQUESTS,
    keep_warm=KEEP_WARM,
    container_idle_timeout=IDLE_TIMEOUT
)
def health_check_a100() -> Dict[str, Any]:
    """
    Comprehensive A100 GPU health check
    Returns detailed GPU status and capabilities
    """
    import torch
    import psutil
    import time
    
    start_time = time.time()
    
    try:
        # Basic system info
        system_info = {
            "timestamp": time.time(),
            "container_startup_time": start_time,
            "cpu_count": psutil.cpu_count(),
            "memory_total_gb": round(psutil.virtual_memory().total / 1024**3, 2),
            "disk_free_gb": round(psutil.disk_usage('/').free / 1024**3, 2)
        }
        
        # GPU information
        if torch.cuda.is_available():
            gpu_info = {
                "gpu_available": True,
                "gpu_count": torch.cuda.device_count(),
                "gpu_name": torch.cuda.get_device_name(0),
                "gpu_memory_total": round(torch.cuda.get_device_properties(0).total_memory / 1024**3, 2),
                "gpu_memory_allocated": round(torch.cuda.memory_allocated(0) / 1024**3, 4),
                "gpu_memory_reserved": round(torch.cuda.memory_reserved(0) / 1024**3, 4),
                "cuda_version": torch.version.cuda,
                "pytorch_version": torch.__version__,
                "gpu_compute_capability": torch.cuda.get_device_capability(0)
            }
            
            # Test GPU computation
            try:
                device = torch.device("cuda:0")
                test_tensor = torch.randn(1000, 1000, device=device)
                result = torch.matmul(test_tensor, test_tensor.T)
                torch.cuda.synchronize()
                
                gpu_info["gpu_compute_test"] = "passed"
                gpu_info["test_tensor_shape"] = list(result.shape)
                
            except Exception as e:
                gpu_info["gpu_compute_test"] = f"failed: {e}"
                
        else:
            gpu_info = {
                "gpu_available": False,
                "error": "CUDA not available"
            }
        
        # TTS libraries check
        tts_status = {}
        try:
            import TTS
            tts_status["TTS"] = TTS.__version__
        except ImportError as e:
            tts_status["TTS"] = f"not available: {e}"
            
        try:
            from bark import SAMPLE_RATE
            tts_status["bark"] = "available"
        except ImportError as e:
            tts_status["bark"] = f"not available: {e}"
        
        return {
            "status": "healthy",
            "service": "CourseAI A100 Production",
            "response_time_ms": round((time.time() - start_time) * 1000, 2),
            "system": system_info,
            "gpu": gpu_info,
            "tts_libraries": tts_status
        }
        
    except Exception as e:
        return {
            "status": "error",
            "service": "CourseAI A100 Production",
            "error": str(e),
            "response_time_ms": round((time.time() - start_time) * 1000, 2)
        }

@app.function(
    image=gpu_image,
    gpu=A100_GPU_CONFIG,
    timeout=900,
    concurrency_limit=MAX_CONCURRENT_REQUESTS,
    keep_warm=KEEP_WARM,
    container_idle_timeout=IDLE_TIMEOUT
)
def chatterbox_tts_a100(
    text: str,
    voice_preset: str = "v2/en_speaker_6",
    temperature: float = 0.8,
    silence_duration: float = 0.25
) -> Dict[str, Any]:
    """
    High-performance Chatterbox TTS using A100 80G GPU
    Optimized for quality and speed with proper error handling
    """
    import torch
    import time
    import base64
    import io
    import soundfile as sf
    from scipy.io import wavfile
    
    start_time = time.time()
    
    try:
        # Validate inputs
        if not text or len(text.strip()) < 1:
            raise ValueError("Text is required and cannot be empty")
            
        if len(text) > 5000:
            raise ValueError("Text too long (max 5000 characters)")
        
        logger.info(f"Generating TTS for {len(text)} characters with voice {voice_preset}")
        
        # Initialize GPU
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        if device.type == "cpu":
            logger.warning("GPU not available, using CPU")
        
        # Try Bark TTS first (highest quality)
        try:
            from bark import SAMPLE_RATE, generate_audio, preload_models
            from bark.generation import set_seed
            
            # Preload models for faster generation
            preload_models()
            
            # Set seed for reproducibility
            set_seed(42)
            
            # Generate audio
            logger.info("Generating audio with Bark TTS")
            audio_array = generate_audio(text, history_prompt=voice_preset)
            
            # Convert to proper format
            audio_array = (audio_array * 32767).astype('int16')
            
            # Save to bytes
            audio_buffer = io.BytesIO()
            wavfile.write(audio_buffer, SAMPLE_RATE, audio_array)
            audio_data = audio_buffer.getvalue()
            
            generation_method = "bark_a100"
            sample_rate = SAMPLE_RATE
            
        except Exception as bark_error:
            logger.error(f"Bark TTS failed: {bark_error}")
            
            # Fallback to TTS library
            try:
                from TTS.api import TTS as TTSApi
                
                logger.info("Falling back to TTS library")
                tts = TTSApi(model_name="tts_models/en/ljspeech/tacotron2-DDC")
                
                # Generate audio to temporary file
                import tempfile
                with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
                    tts.tts_to_file(text=text, file_path=temp_file.name)
                    
                    # Read audio data
                    with open(temp_file.name, 'rb') as f:
                        audio_data = f.read()
                
                generation_method = "tts_library_a100"
                sample_rate = 22050
                
            except Exception as tts_error:
                logger.error(f"TTS library also failed: {tts_error}")
                raise Exception(f"All TTS methods failed. Bark: {bark_error}, TTS: {tts_error}")
        
        # Encode audio to base64
        audio_b64 = base64.b64encode(audio_data).decode('utf-8')
        
        generation_time = time.time() - start_time
        
        return {
            "success": True,
            "audio_data": audio_b64,
            "audio_format": "wav",
            "sample_rate": sample_rate,
            "generation_method": generation_method,
            "voice_preset": voice_preset,
            "text_length": len(text),
            "generation_time_seconds": round(generation_time, 3),
            "gpu_used": device.type == "cuda",
            "timestamp": time.time()
        }
        
    except Exception as e:
        logger.error(f"Chatterbox TTS A100 error: {e}")
        return {
            "success": False,
            "error": str(e),
            "generation_method": "failed",
            "generation_time_seconds": round(time.time() - start_time, 3),
            "timestamp": time.time()
        }

@app.function(
    image=gpu_image,
    gpu=A100_GPU_CONFIG,
    timeout=1800,  # 30 minutes for batch processing
    concurrency_limit=1,  # Limit to 1 for batch processing
    keep_warm=KEEP_WARM,
    container_idle_timeout=IDLE_TIMEOUT
)
def batch_course_narration_a100(
    lessons: List[Dict[str, Any]],
    voice_preset: str = "v2/en_speaker_6",
    optimize_quality: bool = True
) -> List[Dict[str, Any]]:
    """
    Batch TTS generation optimized for course content with A100 GPU
    Processes multiple lessons efficiently with progress tracking
    """
    import time
    
    start_time = time.time()
    results = []
    
    logger.info(f"Starting batch narration for {len(lessons)} lessons")
    
    try:
        for i, lesson in enumerate(lessons):
            lesson_start = time.time()
            
            # Extract lesson data
            lesson_id = lesson.get('id', f'lesson_{i}')
            lesson_title = lesson.get('title', f'Lesson {i+1}')
            lesson_text = lesson.get('text', lesson.get('script', ''))
            
            if not lesson_text:
                results.append({
                    "lesson_id": lesson_id,
                    "lesson_title": lesson_title,
                    "success": False,
                    "error": "No text content provided",
                    "processing_time": 0
                })
                continue
            
            logger.info(f"Processing lesson {i+1}/{len(lessons)}: {lesson_title}")
            
            # Generate TTS for this lesson
            tts_result = chatterbox_tts_a100.local(
                text=lesson_text,
                voice_preset=voice_preset,
                temperature=0.8 if optimize_quality else 0.6
            )
            
            # Prepare result
            lesson_result = {
                "lesson_id": lesson_id,
                "lesson_title": lesson_title,
                "success": tts_result.get("success", False),
                "processing_time": round(time.time() - lesson_start, 3),
                "text_length": len(lesson_text),
                "progress": round((i + 1) / len(lessons) * 100, 1)
            }
            
            if tts_result.get("success"):
                lesson_result.update({
                    "audio_data": tts_result["audio_data"],
                    "audio_format": tts_result["audio_format"],
                    "sample_rate": tts_result["sample_rate"],
                    "generation_method": tts_result["generation_method"]
                })
            else:
                lesson_result["error"] = tts_result.get("error", "Unknown error")
            
            results.append(lesson_result)
            
            # Log progress
            if (i + 1) % 5 == 0 or i == len(lessons) - 1:
                logger.info(f"Completed {i + 1}/{len(lessons)} lessons")
        
        total_time = time.time() - start_time
        successful_count = sum(1 for r in results if r["success"])
        
        logger.info(f"Batch processing complete: {successful_count}/{len(lessons)} successful in {total_time:.2f}s")
        
        return {
            "batch_results": results,
            "summary": {
                "total_lessons": len(lessons),
                "successful_lessons": successful_count,
                "failed_lessons": len(lessons) - successful_count,
                "total_processing_time": round(total_time, 3),
                "average_time_per_lesson": round(total_time / len(lessons), 3) if lessons else 0,
                "voice_preset": voice_preset,
                "optimization_enabled": optimize_quality
            }
        }
        
    except Exception as e:
        logger.error(f"Batch processing error: {e}")
        return {
            "batch_results": results,
            "error": str(e),
            "summary": {
                "total_lessons": len(lessons),
                "successful_lessons": sum(1 for r in results if r.get("success", False)),
                "failed_lessons": len(lessons) - sum(1 for r in results if r.get("success", False)),
                "total_processing_time": round(time.time() - start_time, 3),
                "voice_preset": voice_preset,
                "error_occurred": True
            }
        }

@app.function(
    image=gpu_image,
    gpu=A100_GPU_CONFIG,
    timeout=1200,
    concurrency_limit=MAX_CONCURRENT_REQUESTS,
    keep_warm=KEEP_WARM,
    container_idle_timeout=IDLE_TIMEOUT
)
def large_model_inference_a100(
    model_name: str,
    prompt: str,
    max_tokens: int = 1024,
    temperature: float = 0.7,
    system_prompt: Optional[str] = None
) -> Dict[str, Any]:
    """
    Large model inference optimized for A100 80G GPU
    Supports models up to 70B+ parameters with proper memory management
    """
    import torch
    import time
    from transformers import AutoTokenizer, AutoModelForCausalLM, BitsAndBytesConfig
    
    start_time = time.time()
    
    try:
        # Validate GPU availability
        if not torch.cuda.is_available():
            raise Exception("GPU not available for large model inference")
        
        device = torch.device("cuda:0")
        
        # Configure quantization for memory efficiency
        quantization_config = BitsAndBytesConfig(
            load_in_4bit=True,
            bnb_4bit_compute_dtype=torch.float16,
            bnb_4bit_use_double_quant=True,
            bnb_4bit_quant_type="nf4"
        )
        
        logger.info(f"Loading model: {model_name}")
        
        # Load tokenizer and model
        tokenizer = AutoTokenizer.from_pretrained(model_name)
        model = AutoModelForCausalLM.from_pretrained(
            model_name,
            quantization_config=quantization_config,
            device_map="auto",
            torch_dtype=torch.float16,
            trust_remote_code=True
        )
        
        # Prepare input
        if system_prompt:
            full_prompt = f"System: {system_prompt}\n\nUser: {prompt}\n\nAssistant:"
        else:
            full_prompt = prompt
        
        # Tokenize input
        inputs = tokenizer(full_prompt, return_tensors="pt").to(device)
        
        # Generate response
        with torch.no_grad():
            outputs = model.generate(
                **inputs,
                max_new_tokens=max_tokens,
                temperature=temperature,
                do_sample=True,
                pad_token_id=tokenizer.eos_token_id,
                repetition_penalty=1.1
            )
        
        # Decode response
        response = tokenizer.decode(outputs[0][inputs['input_ids'].shape[1]:], skip_special_tokens=True)
        
        generation_time = time.time() - start_time
        
        return {
            "success": True,
            "response": response.strip(),
            "model_name": model_name,
            "prompt_tokens": inputs['input_ids'].shape[1],
            "generated_tokens": outputs.shape[1] - inputs['input_ids'].shape[1],
            "generation_time_seconds": round(generation_time, 3),
            "gpu_memory_used_gb": round(torch.cuda.memory_allocated() / 1024**3, 2),
            "timestamp": time.time()
        }
        
    except Exception as e:
        logger.error(f"Large model inference error: {e}")
        return {
            "success": False,
            "error": str(e),
            "model_name": model_name,
            "generation_time_seconds": round(time.time() - start_time, 3),
            "timestamp": time.time()
        }

# REST API endpoints
@app.web_endpoint(method="GET", docs=True)
def api_health():
    """Health check endpoint"""
    return health_check_a100.remote()

@app.web_endpoint(method="POST", docs=True)
def api_tts_generate(item: Dict[str, Any]):
    """Generate TTS audio"""
    return chatterbox_tts_a100.remote(
        text=item.get("text", ""),
        voice_preset=item.get("voice_preset", "v2/en_speaker_6"),
        temperature=item.get("temperature", 0.8),
        silence_duration=item.get("silence_duration", 0.25)
    )

@app.web_endpoint(method="POST", docs=True)
def api_batch_tts(item: Dict[str, Any]):
    """Batch TTS generation"""
    return batch_course_narration_a100.remote(
        lessons=item.get("lessons", []),
        voice_preset=item.get("voice_preset", "v2/en_speaker_6"),
        optimize_quality=item.get("optimize_quality", True)
    )

@app.web_endpoint(method="POST", docs=True)
def api_llm_inference(item: Dict[str, Any]):
    """Large language model inference"""
    return large_model_inference_a100.remote(
        model_name=item.get("model_name", "microsoft/DialoGPT-medium"),
        prompt=item.get("prompt", ""),
        max_tokens=item.get("max_tokens", 1024),
        temperature=item.get("temperature", 0.7),
        system_prompt=item.get("system_prompt")
    )

if __name__ == "__main__":
    print("🚀 CourseAI A100 Production App Ready")
    print("Available functions:")
    print("- health_check_a100: Comprehensive GPU health check")
    print("- chatterbox_tts_a100: High-quality TTS generation")
    print("- batch_course_narration_a100: Batch TTS for courses")
    print("- large_model_inference_a100: Large model inference")
    print("\nCost optimization features:")
    print(f"- Max concurrent requests: {MAX_CONCURRENT_REQUESTS}")
    print(f"- Container idle timeout: {IDLE_TIMEOUT}s")
    print(f"- Keep warm containers: {KEEP_WARM}")
    print("\nDeploy with: modal deploy modal_a100_production_fixed.py")