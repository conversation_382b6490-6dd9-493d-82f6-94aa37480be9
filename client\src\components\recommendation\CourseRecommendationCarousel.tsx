import { useEffect, useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight, Star, Users, Clock, Zap, TrendingUp, Sparkles, BookOpen } from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import { Link } from 'wouter';

// Types from our server implementation
interface Course {
  id: number;
  title: string;
  description: string;
  category: string;
  thumbnail?: string;
  rating?: number;
  enrollmentCount?: number;
  level?: string;
  tags?: string[];
}

interface AIInsight {
  type: 'trending' | 'recommended' | 'personalized' | 'similar';
  message: string;
  confidence: number;
}

interface RecommendationGroup {
  title: string;
  insights: AIInsight[];
  courses: Course[];
}

const CourseCard = ({ course, insight }: { course: Course, insight: AIInsight }) => {
  // Function to determine icon based on insight type
  const getInsightIcon = (type: AIInsight['type']) => {
    switch (type) {
      case 'trending':
        return <TrendingUp className="w-4 h-4 text-rose-500" />;
      case 'recommended':
        return <Sparkles className="w-4 h-4 text-amber-500" />;
      case 'personalized':
        return <Zap className="w-4 h-4 text-indigo-500" />;
      case 'similar':
        return <BookOpen className="w-4 h-4 text-emerald-500" />;
      default:
        return <Sparkles className="w-4 h-4 text-amber-500" />;
    }
  };

  return (
    <Card className="w-full min-w-[280px] max-w-[320px] shadow-md hover:shadow-lg transition-shadow duration-300">
      <div className="relative">
        <img 
          src={course.thumbnail || 'https://via.placeholder.com/320x180?text=Course+Image'} 
          alt={course.title}
          className="w-full h-[180px] object-cover rounded-t-lg"
        />
        <div className="absolute bottom-0 left-0 w-full bg-gradient-to-t from-black/70 to-transparent p-3">
          <Badge variant="secondary" className="bg-white/20 backdrop-blur-sm text-white">
            {course.category}
          </Badge>
        </div>
      </div>
      <CardContent className="p-4">
        <h3 className="text-lg font-semibold line-clamp-1 mb-1">{course.title}</h3>
        <p className="text-sm text-muted-foreground line-clamp-2 mb-3">{course.description}</p>
        
        <div className="flex items-center gap-2 text-sm mb-4">
          {course.rating && (
            <div className="flex items-center text-amber-500">
              <Star className="w-4 h-4 fill-current mr-1" />
              <span>{course.rating.toFixed(1)}</span>
            </div>
          )}
          {course.enrollmentCount && (
            <div className="flex items-center text-muted-foreground">
              <Users className="w-4 h-4 mr-1" />
              <span>{course.enrollmentCount}+ enrolled</span>
            </div>
          )}
          {course.level && (
            <div className="flex items-center text-muted-foreground ml-auto">
              <Clock className="w-4 h-4 mr-1" />
              <span>{course.level}</span>
            </div>
          )}
        </div>

        <div className="flex items-center mt-auto">
          <div className="flex items-center bg-muted p-2 rounded-md text-xs mr-2">
            {getInsightIcon(insight.type)}
            <span className="ml-1 line-clamp-1">{insight.message}</span>
          </div>
          <Link href={`/courses/${course.id}`}>
            <Button variant="ghost" size="sm" className="ml-auto">View</Button>
          </Link>
        </div>
      </CardContent>
    </Card>
  );
};

const CourseRecommendationCarousel = () => {
  const [activeGroup, setActiveGroup] = useState<number>(0);
  const [visibleCourses, setVisibleCourses] = useState<number>(3);
  
  // Responsive adjustments
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 640) {
        setVisibleCourses(1);
      } else if (window.innerWidth < 1024) {
        setVisibleCourses(2);
      } else {
        setVisibleCourses(3);
      }
    };
    
    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Fetch recommendations from the API
  const { data: recommendations, isLoading, error } = useQuery({
    queryKey: ['/api/recommendations'],
    staleTime: 1000 * 60 * 5, // 5 minutes
  });

  if (isLoading) {
    return (
      <div className="my-8">
        <Skeleton className="h-12 w-48 mb-4" />
        <div className="flex gap-4">
          {Array(3).fill(0).map((_, i) => (
            <Skeleton key={i} className="h-[350px] w-[320px]" />
          ))}
        </div>
      </div>
    );
  }

  if (error || !recommendations || recommendations.length === 0) {
    return null; // Hide the component if there are no recommendations or an error
  }

  const currentGroup = recommendations[activeGroup];
  const totalGroups = recommendations.length;

  // Calculate which courses to display based on the current state
  const displayedCourses = currentGroup.courses.slice(0, visibleCourses);
  
  const nextGroup = () => {
    setActiveGroup((prev) => (prev + 1) % totalGroups);
  };

  const prevGroup = () => {
    setActiveGroup((prev) => (prev - 1 + totalGroups) % totalGroups);
  };

  return (
    <div className="w-full py-6">
      <div className="flex justify-between items-center mb-4">
        <div>
          <h2 className="text-2xl font-bold">{currentGroup.title}</h2>
          <p className="text-sm text-muted-foreground">Personalized recommendations for you</p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline" 
            size="icon"
            onClick={prevGroup}
            disabled={totalGroups <= 1}
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <Button
            variant="outline" 
            size="icon"
            onClick={nextGroup}
            disabled={totalGroups <= 1}
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <div className="relative overflow-hidden">
        <div className="flex gap-4 pb-4 overflow-x-auto scrollbar-hide">
          {displayedCourses.map((course, index) => (
            <CourseCard 
              key={course.id} 
              course={course} 
              insight={currentGroup.insights[index]} 
            />
          ))}
        </div>
      </div>
      
      {totalGroups > 1 && (
        <div className="flex justify-center mt-4">
          {Array(totalGroups).fill(0).map((_, i) => (
            <Button
              key={i}
              variant="ghost"
              size="sm"
              className={`w-2 h-2 rounded-full mx-1 p-0 ${i === activeGroup ? 'bg-primary' : 'bg-muted'}`}
              onClick={() => setActiveGroup(i)}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default CourseRecommendationCarousel;